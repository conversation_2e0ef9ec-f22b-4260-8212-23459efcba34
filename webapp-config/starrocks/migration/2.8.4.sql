SET global time_zone = 'Asia/Shanghai' ;

SET PASSWORD = PASSWORD('Databuff@123');

CREATE DATABASE IF NOT EXISTS databuff;

use databuff;

ALTER TABLE dc_span RENAME dc_span_delete;
CREATE TABLE IF NOT EXISTS `dc_span`
(
    `minutes`                      bigint(20) NOT NULL COMMENT "分钟",
    `is_service_flow`              tinyint(4) NOT NULL COMMENT "是否是参与服务流计算的数据",
    `entrypoint_path_id`           bigint(20) NULL COMMENT "服务级别入口服务pathId",
    `entrypoint_interface_path_id` bigint(20) NULL COMMENT "接口级别入口服务pathId",
    `error`                        tinyint(4) NOT NULL COMMENT "错误数",
    `hours`                        bigint(20) NOT NULL COMMENT "小时",
    `span_id`                      bigint(20) NOT NULL COMMENT "spanId",
    `startTime`                    datetime      NOT NULL COMMENT "开始时间",
    `is_parent`                    tinyint(4) NOT NULL COMMENT "是否是父亲节点",
    `trace_id`                     bigint(20) NOT NULL COMMENT "traceId",
    `parent_id`                    bigint(20) NOT NULL COMMENT "父spanId",
    `path_id`                      bigint(20) NULL COMMENT "pathId",
    `parent_path_id`               bigint(20) NULL COMMENT "父pathId",
    `interface_path_id`            bigint(20) NULL COMMENT "interfacePathId",
    `parent_interface_path_id`     bigint(20) NULL COMMENT "父interfacePathId",
    `df-api-key`                   varchar(255)  NOT NULL COMMENT "apiKey",
    `resource`                     varchar(500)  NOT NULL COMMENT "资源",
    `src_call`                     int(11) NULL COMMENT "来源调用",
    `serviceId`                    varchar(255)  NOT NULL COMMENT "服务id",
    `serviceInstance`              varchar(255) NULL COMMENT "服务实例",
    `clientService`                varchar(255) NULL COMMENT "客户端服务",
    `client_service_id`            varchar(255) NULL COMMENT "客户端服务id",
    `clientServiceInstance`        varchar(255) NULL COMMENT "客户端服务实例",
    `end`                          bigint(20) NOT NULL COMMENT "结束时间",
    `hostName`                     varchar(255)  NOT NULL COMMENT "主机名",
    `type`                         varchar(50)   NOT NULL COMMENT "类型",
    `isIn`                         tinyint(4) NOT NULL COMMENT "是否入口",
    `duration`                     bigint(20) NOT NULL COMMENT "耗时",
    `df-hostname`                  varchar(255)  NOT NULL COMMENT "主机名",
    `start`                        bigint(20) NOT NULL COMMENT "开始时间",
    `host_id`                      varchar(100)  NOT NULL COMMENT "主机id",
    `reportService`                varchar(255) NULL COMMENT "上报服务名",
    `service`                      varchar(255)  NOT NULL COMMENT "服务名",
    `meta`                         varchar(10000) NULL COMMENT "meta信息",
    `name`                         varchar(255)  NOT NULL COMMENT "name",
    `isOut`                        tinyint(4) NOT NULL COMMENT "是否出口",
    `metrics`                      varchar(1000) NOT NULL COMMENT "metrics信息",
    `user-agent`                   varchar(255) NULL COMMENT "agent信息",
    `meta.http.status_code`        smallint(6) NULL COMMENT "http状态码",
    `meta.error.type`              varchar(100) NULL COMMENT "错误类型",
    `meta.peer.hostname`           varchar(255) NULL COMMENT "httpHost",
    `meta.http.method`             varchar(30) NULL COMMENT "httpMethod",
    `meta.http.url`                varchar(500) NULL COMMENT "httpUrl",
    `meta.db.type`                 varchar(50) NULL COMMENT "",
    `meta.db.instance`             varchar(100) NULL COMMENT "",
    `meta.db.operation`            varchar(50) NULL COMMENT "",
    `meta.mq.topic`                varchar(100) NULL COMMENT "",
    `meta.mq.group`                varchar(100) NULL COMMENT "",
    `meta.mq.broker`               varchar(50) NULL COMMENT "",
    `metrics.mq.partition`         varchar(10) NULL COMMENT "",
    `server_service_id`            varchar(255) NULL COMMENT "服务端服务id",
    `serverServiceInstance`        varchar(255) NULL COMMENT "服务端实例",
    `serverService`                varchar(255) NULL COMMENT "服务端服务名"
    ) ENGINE=OLAP
    DUPLICATE KEY(`minutes`, `is_service_flow`, `entrypoint_path_id`, `entrypoint_interface_path_id`)
    PARTITION BY (`hours`)
    DISTRIBUTED BY HASH(`trace_id`) BUCKETS 1
    PROPERTIES (
                   "replication_num" = "1",
                   "replicated_storage" = "true",
                   "partition_live_number" = "168",
                   "compression" = "ZSTD"
               );

CREATE TABLE IF NOT EXISTS databuff.dc_root (
                                                `apiKey` varchar(100) NULL COMMENT "API密钥",
    `serviceId` INT NULL COMMENT "服务ID",
    `fromTime` BIGINT NULL COMMENT "根因定位分析的开始时间",
    `toTime` BIGINT NULL COMMENT "根因定位分析的结束时间",
    `eventId` array<varchar(200)> NULL COMMENT "事件ID",
    `monitorId` array<varchar(200)> NULL COMMENT "监控规则ID",
    `rootType` varchar(255) NULL COMMENT "根因的类型",
    `timestamp` DATETIME NOT NULL COMMENT "根因生成时间",
    `message` varchar(10000) NOT NULL COMMENT "根因内容"
    )
    PARTITION BY RANGE(`timestamp`) ()
    DISTRIBUTED BY HASH(`apiKey` ,`serviceId` )
    PROPERTIES (
                   "replication_num" = "1",
                   "dynamic_partition.enable" = "true",
                   "dynamic_partition.time_unit" = "DAY",
                   "dynamic_partition.start" = "-7",
                   "dynamic_partition.end" = "1",
                   "dynamic_partition.prefix" = "p"
               );


ALTER TABLE databuff.dc_alarm
    ADD COLUMN pname array< varchar (200) > null comment '进程名称列表',
    ADD COLUMN containerId array< varchar (200) > null comment '容器id列表',
    ADD COLUMN containerName array< varchar (200) > null comment '容器名称列表';

-- -- 添加root.resource es索引 字段
alter table databuff.dc_span
    ADD COLUMN `meta.indices` varchar(500) null comment '索引',
    ADD COLUMN `meta.root.resource` varchar(500) null comment '根接口';

-- 2.7.10.sql已经执行过  目前没考虑升级
-- -- 添加容器排序 字段
-- alter table databuff.dc_latest_containers
--    ADD COLUMN `created` bigint(20) null comment '创建时间',
--    ADD COLUMN `rbps` DOUBLE null comment '每秒读取字节数',
--    ADD COLUMN `wbps` DOUBLE null comment '每秒写入字节数';


CREATE TABLE IF Not Exists `dc_rum_slow_page_span`
(
    `startTime`               datetime      NOT NULL COMMENT "开始时间",
    `app_id`                  smallint      NOT NULL COMMENT "应用id",
    `page_id`                 bigint(20)    NOT NULL COMMENT "页面id",
    `trace_id`                bigint(20)    NOT NULL COMMENT "traceId",
    `span_id`                 bigint(20)    NOT NULL COMMENT "spanId",
    `parent_id`               bigint(20)    NOT NULL COMMENT "父spanId",
    `session_id`              bigint(20)    NOT NULL COMMENT "会话id",
    `service`                 varchar(255)  NULL COMMENT "服务名",
    `location_href`           VARCHAR(1024) NOT NULL COMMENT '页面来源URL',
    `processed_location_href` VARCHAR(512)  NOT NULL COMMENT '处理过的页面来源URL',
    `http_url`                VARCHAR(1024) NOT NULL COMMENT '请求URL',
    `df_api_key_id`           tinyint       NOT NULL COMMENT "租户 apiKeyId",
    `start`                   bigint(20)    NOT NULL COMMENT "开始时间",
    `end`                     bigint(20)    NOT NULL COMMENT "结束时间",
    `duration`                bigint(20)    NOT NULL COMMENT "耗时"
    ) DUPLICATE KEY(`startTime`,`app_id`, `page_id`)
    PARTITION BY RANGE(`startTime`) (
                                    )
    DISTRIBUTED BY HASH(`page_id`) BUCKETS 3
    PROPERTIES(
                  "replication_num" = "1",
                  "dynamic_partition.enable" = "true",
                  "dynamic_partition.time_unit" = "HOUR",
                  "dynamic_partition.start" = "-168",
                  "dynamic_partition.end" = "1",
                  "dynamic_partition.prefix" = "p",
                  "dynamic_partition.time_zone" = "Asia/Shanghai",
                  "dynamic_partition.history_partition_num" = "3"
              );

CREATE TABLE IF Not Exists `dc_rum_action_span`
(
    `startTime`     datetime      NOT NULL COMMENT "开始时间",
    `app_id`        smallint      NOT NULL COMMENT "应用id",
    `action_id`     bigint(20)    NOT NULL COMMENT "操作id",
    `action_name`   varchar(100)  NOT NULL COMMENT "操作名称",
    `trace_id`      bigint(20)    NOT NULL COMMENT "traceId",
    `span_id`       bigint(20)    NOT NULL COMMENT "spanId",
    `parent_id`     bigint(20)    NOT NULL COMMENT "父spanId",
    `session_id`    bigint(20)    NOT NULL COMMENT "会话id",
    `service`       varchar(255)  NULL COMMENT "服务名",
    `location_href` VARCHAR(512)  NOT NULL COMMENT '页面来源URL',
    `http_url`      VARCHAR(1024) NOT NULL COMMENT '请求URL',
    `df_api_key_id` tinyint       NOT NULL COMMENT "租户 apiKeyId",
    `start`         bigint(20)    NOT NULL COMMENT "开始时间",
    `end`           bigint(20)    NOT NULL COMMENT "结束时间",
    `duration`      bigint(20)    NOT NULL COMMENT "耗时"

    ) DUPLICATE KEY(`startTime`,`app_id`,`action_id`)
    PARTITION BY RANGE(`startTime`) (
                                    )
    DISTRIBUTED BY HASH(`action_id`) BUCKETS 3
    PROPERTIES(
                  "replication_num" = "1",
                  "dynamic_partition.enable" = "true",
                  "dynamic_partition.time_unit" = "HOUR",
                  "dynamic_partition.start" = "-168",
                  "dynamic_partition.end" = "1",
                  "dynamic_partition.prefix" = "p",
                  "dynamic_partition.time_zone" = "Asia/Shanghai",
                  "dynamic_partition.history_partition_num" = "3"
              );


CREATE TABLE IF NOT EXISTS dc_rum_page
(
    `startTime`               DATETIME      NOT NULL COMMENT "事件发生时间",
    `app_id`                  smallint      NOT NULL COMMENT "应用id",
    `is_slow_page`            TINYINT       NOT NULL COMMENT "是否是慢页面 0:否 1:是",
    `processed_location_href` VARCHAR(512)  NOT NULL COMMENT '处理过的页面来源URL',
    `page_id`                 bigint(20)    NOT NULL COMMENT "页面id",
    `location_href`           VARCHAR(1024) NOT NULL COMMENT '页面来源URL',
    `is_slow_full_load_time`  TINYINT       NOT NULL COMMENT "是否是慢完全加载 0:否 1:是",
    `is_slow_lcp`             TINYINT       NOT NULL COMMENT "是否是慢lcp 0:否 1:是",
    `is_slow_fcp`             TINYINT       NOT NULL COMMENT "是否是慢fcp 0:否 1:是",
    `is_slow_dcl`             TINYINT       NOT NULL COMMENT "是否是慢dcl 0:否 1:是",
    `user_id`                 VARCHAR(50)   NOT NULL COMMENT "用户ID",
    `ip`                      VARCHAR(15)   NOT NULL COMMENT "公网IP",
    `session_id`              bigint(20)    NOT NULL COMMENT "会话ID",
    `isp`                     VARCHAR(50)   NOT NULL COMMENT "运营商",
    `browser`                 VARCHAR(20)   NOT NULL COMMENT "浏览器",
    `operating_system`        VARCHAR(50)   NOT NULL COMMENT "操作系统",
    `region`                  VARCHAR(100)  NOT NULL COMMENT "地域",
    `probe_version`           VARCHAR(20)   NOT NULL COMMENT "探针版本",
    `full_load_time`          BIGINT COMMENT "完全加载时间(ms)",
    `lcp`                     BIGINT COMMENT "Largest Contentful Paint(ns)",
    `fcp`                     BIGINT COMMENT "First Contentful Paint(ns)",
    `dcl`                     BIGINT COMMENT "DomContentLoaded(ns)",
    `fid`                     BIGINT COMMENT "First Input Delay(ns)",
    `cls`                     DOUBLE COMMENT "Cumulative Layout Shift",
    `ttfb`                    BIGINT COMMENT "Time to First Byte(ns)",
    `tti`                     BIGINT COMMENT "Time to Interactive(ns)",
    `tbt`                     BIGINT COMMENT "Total Blocking Time(ns)",
    `user_agent`              VARCHAR(255)  NOT NULL COMMENT "User Agent",
    `df_api_key_id`           tinyint       NOT NULL COMMENT "租户 apiKeyId"
    ) DUPLICATE KEY(`startTime`,`app_id`,`is_slow_page`)
    PARTITION BY RANGE(`startTime`) (
                                    )
    DISTRIBUTED BY HASH(`page_id`) BUCKETS 3
    PROPERTIES(
                  "replication_num" = "1",
                  "dynamic_partition.enable" = "true",
                  "dynamic_partition.time_unit" = "HOUR",
                  "dynamic_partition.start" = "-168",
                  "dynamic_partition.end" = "1",
                  "dynamic_partition.prefix" = "p",
                  "dynamic_partition.time_zone" = "Asia/Shanghai",
                  "dynamic_partition.history_partition_num" = "3"
              );

CREATE TABLE IF NOT EXISTS dc_rum_page_uv
(
    `startTime`               DATETIME COMMENT "事件发生时间",
    `app_id`                  smallint     NOT NULL COMMENT "应用id",
    `processed_location_href` VARCHAR(512) NOT NULL COMMENT '处理过的页面来源URL',
    `is_slow_page`            TINYINT      NOT NULL COMMENT "是否是慢页面 0:否 1:是",
    `df_api_key_id`           tinyint      NOT NULL COMMENT "租户 apiKeyId",
    `uv` HLL HLL_UNION COMMENT "用来计算uv ,慢页面影响用户数"
    )
    PARTITION BY RANGE (`startTime`) (
                                     ) DISTRIBUTED BY HASH(`processed_location_href`) BUCKETS 3
    PROPERTIES(
                  "replication_num" = "1",
                  "dynamic_partition.enable" = "true",
                  "dynamic_partition.time_unit" = "HOUR",
                  "dynamic_partition.start" = "-168",
                  "dynamic_partition.end" = "1",
                  "dynamic_partition.prefix" = "p",
                  "dynamic_partition.time_zone" = "Asia/Shanghai",
                  "dynamic_partition.history_partition_num" = "3"
              );

CREATE TABLE IF NOT EXISTS dc_rum_action
(
    `startTime`               DATETIME     NOT NULL COMMENT "操作开始时间",
    `app_id`                  smallint     NOT NULL COMMENT "应用id",
    `action_name`             varchar(100) NOT NULL COMMENT "操作名称",
    `action_id`               bigint(20)   NOT NULL COMMENT "操作id",
    `action_duration`         BIGINT COMMENT "操作时间(耗时ns)",
    `action_request_duration` BIGINT COMMENT "操作请求耗时(ns)",
    `action_service_duration` BIGINT COMMENT "服务端平均耗时(ns)",
    `user_id`                 VARCHAR(50)  NOT NULL COMMENT "用户ID",
    `ip`                      VARCHAR(15)  NOT NULL COMMENT "公网IP",
    `session_id`              bigint(20)   NOT NULL COMMENT "会话ID",
    `browser`                 VARCHAR(20)  NOT NULL COMMENT "浏览器",
    `operating_system`        VARCHAR(50)  NOT NULL COMMENT "操作系统",
    `region`                  VARCHAR(100) NOT NULL COMMENT "地域",
    `isp`                     VARCHAR(50)  NOT NULL COMMENT "运营商",
    `probe_version`           VARCHAR(20)  NOT NULL COMMENT "探针版本",
    `user_agent`              VARCHAR(255) NOT NULL COMMENT "User Agent",
    `df_api_key_id`           tinyint      NOT NULL COMMENT "租户 apiKeyId"
    ) DUPLICATE KEY(`startTime`,`app_id`,`action_name`)
    PARTITION BY RANGE(`startTime`) (
                                    )
    DISTRIBUTED BY HASH(`action_id`) BUCKETS 3
    PROPERTIES(
                  "replication_num" = "1",
                  "dynamic_partition.enable" = "true",
                  "dynamic_partition.time_unit" = "HOUR",
                  "dynamic_partition.start" = "-168",
                  "dynamic_partition.end" = "1",
                  "dynamic_partition.prefix" = "p",
                  "dynamic_partition.time_zone" = "Asia/Shanghai",
                  "dynamic_partition.history_partition_num" = "3"
              );

CREATE TABLE IF NOT EXISTS dc_rum_action_uv
(
    `startTime`     DATETIME     NOT NULL COMMENT "操作开始时间",
    `app_id`        smallint     NOT NULL COMMENT "应用id",
    `action_name`   varchar(100) NOT NULL COMMENT "操作名称",
    `df_api_key_id` tinyint      NOT NULL COMMENT "租户 apiKeyId",
    `uv` HLL HLL_UNION COMMENT "uv"
    )
    PARTITION BY RANGE (`startTime`) (
                                     ) DISTRIBUTED BY HASH(`action_name`) BUCKETS 3
    PROPERTIES(
                  "replication_num" = "1",
                  "dynamic_partition.enable" = "true",
                  "dynamic_partition.time_unit" = "HOUR",
                  "dynamic_partition.start" = "-168",
                  "dynamic_partition.end" = "1",
                  "dynamic_partition.prefix" = "p",
                  "dynamic_partition.time_zone" = "Asia/Shanghai",
                  "dynamic_partition.history_partition_num" = "3"
              );


CREATE TABLE IF NOT EXISTS dc_rum_error_logs
(
    `error_time`              DATETIME      NOT NULL COMMENT "错误发生时间",
    `app_id`                  smallint      NOT NULL COMMENT "应用id",
    `error_message`           VARCHAR(750)  NOT NULL COMMENT "错误信息",
    `error_id`                bigint(20)    NOT NULL COMMENT "错误id",
    `page_id`                 bigint(20)    NOT NULL COMMENT "页面id",
    `user_id`                 VARCHAR(50)   NOT NULL COMMENT "用户ID",
    `ip`                      VARCHAR(15)   NOT NULL COMMENT "公网IP",
    `session_id`              bigint(20)    NOT NULL COMMENT "会话ID",
    `browser`                 VARCHAR(20)   NOT NULL COMMENT "浏览器",
    `operating_system`        VARCHAR(50)   NOT NULL COMMENT "操作系统",
    `region`                  VARCHAR(100)  NOT NULL COMMENT "地域",
    `isp`                     VARCHAR(50)   NOT NULL COMMENT "运营商",
    `probe_version`           VARCHAR(20)   NOT NULL COMMENT "探针版本",
    `user_agent`              VARCHAR(255)  NOT NULL COMMENT "UA",
    `page_url`                VARCHAR(1024) NOT NULL COMMENT "发生页面",
    `processed_location_href` VARCHAR(512)  NOT NULL COMMENT '处理过的页面来源URL',
    `error_file`              VARCHAR(2048) COMMENT "错误文件",
    `error_stack`             VARCHAR(5000) COMMENT "错误堆栈",
    `df_api_key_id`           tinyint       NOT NULL COMMENT "租户 apiKeyId"
    ) DUPLICATE KEY(`error_time`,`app_id`,`error_message`)
    PARTITION BY RANGE(`error_time`) (
                                     )
    DISTRIBUTED BY HASH(`error_id`) BUCKETS 3
    PROPERTIES(
                  "replication_num" = "1",
                  "dynamic_partition.enable" = "true",
                  "dynamic_partition.time_unit" = "HOUR",
                  "dynamic_partition.start" = "-168",
                  "dynamic_partition.end" = "1",
                  "dynamic_partition.prefix" = "p",
                  "dynamic_partition.time_zone" = "Asia/Shanghai",
                  "dynamic_partition.history_partition_num" = "3"
              );


CREATE TABLE IF NOT EXISTS dc_rum_error_logs_uv
(
    `error_time`              DATETIME     NOT NULL COMMENT "错误发生时间",
    `app_id`                  smallint     NOT NULL COMMENT "应用id",
    `error_message`           VARCHAR(750) NOT NULL COMMENT "错误信息",
    `processed_location_href` VARCHAR(512) NOT NULL COMMENT '处理过的页面来源URL',
    `df_api_key_id`           tinyint      NOT NULL COMMENT "租户 apiKeyId",
    `uv` HLL HLL_UNION COMMENT "影响用户数"
    )
    PARTITION BY RANGE (`error_time`) (
                                      ) DISTRIBUTED BY HASH(`error_message`) BUCKETS 3
    PROPERTIES(
                  "replication_num" = "1",
                  "dynamic_partition.enable" = "true",
                  "dynamic_partition.time_unit" = "HOUR",
                  "dynamic_partition.start" = "-168",
                  "dynamic_partition.end" = "1",
                  "dynamic_partition.prefix" = "p",
                  "dynamic_partition.time_zone" = "Asia/Shanghai",
                  "dynamic_partition.history_partition_num" = "3"
              );

DROP TABLE IF EXISTS databuff.`dc_event_2_8_3`;
CREATE TABLE IF NOT EXISTS databuff.`dc_event_2_8_3` (
                                                         `apiKey` varchar(100) NOT NULL COMMENT "API密钥",
    `id` varchar(50) NOT NULL COMMENT "事件ID",
    `triggerTime` bigint(20) NULL COMMENT "本次触发时间(异常点)",
    `createDt` date NOT NULL COMMENT "生命周期TTL分区键",
    `monitorId` varchar(50) NULL COMMENT "监控规则ID",
    `value` double NULL COMMENT "值",
    `silence` tinyint(4) NULL COMMENT "是否静默",
    `level` tinyint(4) NULL COMMENT "事件最终等级",
    `creatorId` varchar(50) NULL COMMENT "规则创建者ID",
    `editorId` varchar(50) NULL COMMENT "规则修改者ID",
    `source` varchar(50) NULL COMMENT "事件来源",
    `classification` varchar(50) NULL COMMENT "分类",
    `trgTrd` varchar(500) NULL COMMENT "触发消息",
    `triggerObjType` varchar(500) NULL COMMENT "触发字段",
    `group` varchar(500) NULL COMMENT "触发分组",
    `message` varchar(5000) NULL COMMENT "事件消息",
    `metrics` array<varchar(255)> NULL COMMENT "度量标准",
    `host` array<varchar(255)> NULL COMMENT "主机列表",
    `serviceId` array<varchar(200)> NULL COMMENT "服务ID列表",
    `serviceInstance` array<varchar(255)> NULL COMMENT "服务实例列表",
    `deviceName` array<varchar(100)> NULL COMMENT "磁盘分区列表",
    `eventStatus` varchar(50) NULL COMMENT "事件状态",
    `eventMsg` varchar(500) NULL COMMENT "事件消息",
    `trigger` json NULL COMMENT "触发对象",
    `tags` json NULL COMMENT "标签",
    `query` json NULL COMMENT "规则查询参数",
    `thresholds` json NULL COMMENT "单指标阈值",
    `multithresholds` json NULL COMMENT "多指标阈值",
    `busName` array<varchar(100)> NULL COMMENT "业务系统列表",
    `ruleName` varchar(1000) NULL COMMENT "规则名称",
    `type` varchar(50) NULL COMMENT "检测方法",
    `createTime` bigint(20) NULL COMMENT "事件创建时间"
    ) ENGINE=OLAP
    DUPLICATE KEY(`apiKey`,`id`, `triggerTime`, `createDt`)
    PARTITION BY RANGE(`createDt`)()
    DISTRIBUTED BY HASH(`id`) BUCKETS 1
    PROPERTIES (
                   "replication_num" = "1",
                   "dynamic_partition.enable" = "true",
                   "dynamic_partition.time_unit" = "DAY",
                   "dynamic_partition.time_zone" = "Asia/Shanghai",
                   "dynamic_partition.start" = "-7",
                   "dynamic_partition.end" = "1",
                   "dynamic_partition.prefix" = "p",
                   "dynamic_partition.history_partition_num" = "7",
                   "in_memory" = "false",
                   "enable_persistent_index" = "true",
                   "replicated_storage" = "true",
                   "compression" = "LZ4"
               );
ALTER TABLE dc_event SWAP WITH dc_event_2_8_3;

DROP TABLE IF EXISTS databuff.`dc_event_system_2_8_3`;
CREATE TABLE IF NOT EXISTS databuff.`dc_event_system_2_8_3` (
                                                                `apiKey` varchar(100) NOT NULL COMMENT "API密钥",
    `id` varchar(50) NOT NULL COMMENT "事件ID",
    `triggerTime` bigint(20) NULL COMMENT "本次触发时间(异常点)",
    `createDt` date NOT NULL COMMENT "生命周期TTL分区键",
    `monitorId` varchar(50) NULL COMMENT "监控规则ID",
    `value` double NULL COMMENT "值",
    `silence` tinyint(4) NULL COMMENT "是否静默",
    `level` tinyint(4) NULL COMMENT "事件最终等级",
    `creatorId` varchar(50) NULL COMMENT "规则创建者ID",
    `editorId` varchar(50) NULL COMMENT "规则修改者ID",
    `source` varchar(50) NULL COMMENT "事件来源",
    `classification` varchar(50) NULL COMMENT "分类",
    `trgTrd` varchar(500) NULL COMMENT "触发消息",
    `triggerObjType` varchar(500) NULL COMMENT "触发字段",
    `group` varchar(500) NULL COMMENT "触发分组",
    `message` varchar(5000) NULL COMMENT "事件消息",
    `metrics` array<varchar(255)> NULL COMMENT "度量标准",
    `host` array<varchar(255)> NULL COMMENT "主机列表",
    `serviceId` array<varchar(200)> NULL COMMENT "服务ID列表",
    `serviceInstance` array<varchar(255)> NULL COMMENT "服务实例列表",
    `deviceName` array<varchar(100)> NULL COMMENT "磁盘分区列表",
    `eventStatus` varchar(50) NULL COMMENT "事件状态",
    `eventMsg` varchar(500) NULL COMMENT "事件消息",
    `trigger` json NULL COMMENT "触发对象",
    `tags` json NULL COMMENT "标签",
    `query` json NULL COMMENT "规则查询参数",
    `thresholds` json NULL COMMENT "单指标阈值",
    `multithresholds` json NULL COMMENT "多指标阈值",
    `busName` array<varchar(100)> NULL COMMENT "业务系统列表",
    `ruleName` varchar(1000) NULL COMMENT "规则名称",
    `type` varchar(50) NULL COMMENT "检测方法",
    `createTime` bigint(20) NULL COMMENT "事件创建时间"
    ) ENGINE=OLAP
    DUPLICATE KEY(`apiKey`,`id`, `triggerTime`, `createDt`)
    PARTITION BY RANGE(`createDt`)()
    DISTRIBUTED BY HASH(`id`) BUCKETS 1
    PROPERTIES (
                   "replication_num" = "1",
                   "dynamic_partition.enable" = "true",
                   "dynamic_partition.time_unit" = "DAY",
                   "dynamic_partition.time_zone" = "Asia/Shanghai",
                   "dynamic_partition.start" = "-7",
                   "dynamic_partition.end" = "1",
                   "dynamic_partition.prefix" = "p",
                   "dynamic_partition.history_partition_num" = "7",
                   "in_memory" = "false",
                   "enable_persistent_index" = "true",
                   "replicated_storage" = "true",
                   "compression" = "LZ4"
               );
ALTER TABLE dc_event_system SWAP WITH dc_event_system_2_8_3;

DROP TABLE IF EXISTS `dc_profiling_hotspot`;
CREATE TABLE `dc_profiling_hotspot` (
                                        `time` datetime NOT NULL COMMENT "标准时间YYYY-MM-DD HH:MM:SS",
                                        `nanoTimestamp` bigint(20) NOT NULL COMMENT "纳秒时间戳",
                                        `excerptId` varchar(32) NOT NULL COMMENT "摘录ID=创建日期（精确到日）+服务ID+服务实例+资源名称+资源类型+热点方法名+所在层级",
                                        `samples` int(11) NULL COMMENT "本次采集样例数",
                                        `tid` int(11) NULL COMMENT "线程ID"
) ENGINE=OLAP
DUPLICATE KEY(`time`, `nanoTimestamp`, `excerptId`)
PARTITION BY RANGE(`time`)()
DISTRIBUTED BY RANDOM
PROPERTIES (
"replication_num" = "1",
"dynamic_partition.enable" = "true",
"dynamic_partition.time_unit" = "DAY",
"dynamic_partition.time_zone" = "Asia/Shanghai",
"dynamic_partition.start" = "-7",
"dynamic_partition.end" = "1",
"dynamic_partition.prefix" = "p",
"dynamic_partition.history_partition_num" = "7",
"in_memory" = "false",
"enable_persistent_index" = "true",
"replicated_storage" = "true",
"compression" = "LZ4"
);

DROP TABLE IF EXISTS `dc_profiling_stack`;
CREATE TABLE `dc_profiling_stack` (
                                      `excerptId` varchar(32) NOT NULL COMMENT "摘录ID=创建日期（精确到日）+服务ID+服务实例+资源名称+资源类型+热点方法名+所在层级",
                                      `createDt` date NOT NULL COMMENT "生命周期TTL分区键",
                                      `observerTool` varchar(200) NULL COMMENT "观察工具",
                                      `service` varchar(255) NULL COMMENT "服务名称",
                                      `serviceId` varchar(200) NULL COMMENT "服务ID",
                                      `serviceInstance` varchar(255) NULL COMMENT "服务实例",
                                      `host` varchar(255) NULL COMMENT "主机名",
                                      `resource` varchar(1000) NULL COMMENT "资源名称",
                                      `resourceType` varchar(50) NULL COMMENT "资源类型",
                                      `rsFlagIndex` int(11) NULL COMMENT "业务方法对应堆栈索引",
                                      `hotspotMethod` varchar(1000) NULL COMMENT "热点方法名",
                                      `stackTrace` array<varchar(1000)> NOT NULL COMMENT "堆栈文本",
                                      `apiKey` varchar(255) NULL COMMENT "apiKey"
) ENGINE=OLAP
PRIMARY KEY(`excerptId`, `createDt`)
PARTITION BY RANGE(`createDt`)()
DISTRIBUTED BY HASH(`excerptId`) BUCKETS 10
PROPERTIES (
"replication_num" = "1",
"dynamic_partition.enable" = "true",
"dynamic_partition.time_unit" = "DAY",
"dynamic_partition.time_zone" = "Asia/Shanghai",
"dynamic_partition.start" = "-7",
"dynamic_partition.end" = "1",
"dynamic_partition.prefix" = "p",
"dynamic_partition.history_partition_num" = "7",
"in_memory" = "false",
"enable_persistent_index" = "true",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `dc_data_collect`
(
    `hashcode`              bigint(20)    NOT NULL COMMENT "hash值后8位",
    `minutes`               bigint(20)    NOT NULL COMMENT "分钟",
    `startTime`             datetime      NOT NULL COMMENT "开始时间",
    `hours`                 bigint(20)    NOT NULL COMMENT "小时",
    `tag`                   varchar(1000) NOT NULL COMMENT "key",
    `error`                 tinyint(4)    NOT NULL COMMENT "错误数",
    `span_id`               bigint(20)    NOT NULL COMMENT "spanId",
    `is_parent`             tinyint(4)    NOT NULL COMMENT "是否是父亲节点",
    `trace_id`              bigint(20)    NOT NULL COMMENT "traceId",
    `meta.http.status_code` smallint(6)   NULL COMMENT "http状态码",
    `duration`              bigint(20)    NOT NULL COMMENT "耗时",
    `hostName`              varchar(255)  NOT NULL COMMENT "主机名",
    `type`                  varchar(50)   NOT NULL COMMENT "类型",
    `serviceId`             varchar(255)  NOT NULL COMMENT "服务id",
    `serviceInstance`       varchar(255)  NULL COMMENT "服务实例",
    `resource`              varchar(500)  NOT NULL COMMENT "资源",
    `meta.http.method`      varchar(30)   NULL COMMENT "httpMethod",
    `meta.error.type`       varchar(100)  NULL COMMENT "错误类型"
) ENGINE = OLAP DUPLICATE KEY(`hashcode`, `minutes`, `startTime`)
PARTITION BY (`hours`)
DISTRIBUTED BY HASH(`trace_id`) BUCKETS 1
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"partition_live_number" = "168",
"compression" = "ZSTD"
);




CREATE TABLE IF NOT EXISTS `dc_rum_web_request_uv` (
                                                       `startTime` DATETIME NOT NULL COMMENT "请求发生时间",
                                                       `app_id` smallint NOT NULL COMMENT "应用id",
                                                       `is_error` TINYINT NOT NULL COMMENT "是否错误 0:否 1:是",
                                                       `request_type` TINYINT NOT NULL COMMENT "请求类型 0:ajax请求 1:静态资源请求",
                                                       `domain` VARCHAR(255) NOT NULL COMMENT "域名",
    `processed_path` VARCHAR(512) NOT NULL COMMENT "处理过的路径",
    `processed_http_url` varchar(512) NOT NULL COMMENT '处理过的http url',
    `isp` VARCHAR(50) NOT NULL COMMENT "运营商",
    `status_code` VARCHAR(3) NULL COMMENT "状态码",
    `service` VARCHAR(255) NULL COMMENT "调用服务",
    `df_api_key_id` tinyint NOT NULL COMMENT "租户 apiKeyId",
    `uv` HLL HLL_UNION COMMENT "用来计算uv"
    )
    PARTITION BY RANGE(`startTime`) (
                                    )
    DISTRIBUTED BY HASH(`processed_path`) BUCKETS 3
    PROPERTIES(
                  "replication_num" = "1",
                  "dynamic_partition.enable" = "true",
                  "dynamic_partition.time_unit" = "HOUR",
                  "dynamic_partition.start" = "-168",
                  "dynamic_partition.end" = "1",
                  "dynamic_partition.prefix" = "p",
                  "dynamic_partition.time_zone" = "Asia/Shanghai",
                  "dynamic_partition.history_partition_num" = "3"
              );


CREATE TABLE IF NOT EXISTS `dc_rum_web_request_span` (
                                                         `startTime` datetime NOT NULL COMMENT "请求发生时间",
                                                         `app_id` smallint NOT NULL COMMENT "应用id",
                                                         `processed_http_url` varchar(512) NOT NULL COMMENT '处理过的http url',
    `span_id` bigint(20) NOT NULL COMMENT "请求前端span_id",
    `request_type` TINYINT NOT NULL COMMENT "请求类型 0:ajax请求 1:静态资源请求",
    `user_id` VARCHAR(255) NULL COMMENT "用户ID",
    `status_code` int NULL COMMENT "状态码",
    `session_id` bigint(20) NOT NULL COMMENT "会话ID",
    `ip` VARCHAR(50) NOT NULL COMMENT "公网IP",
    `server_time` BIGINT NOT NULL COMMENT "服务端耗时(ns)",
    `network_time` BIGINT NOT NULL COMMENT "网络耗时(ns)",
    `transfer_size` int  NULL COMMENT "传输数据量(Byte)",
    `region` VARCHAR(50) NULL COMMENT "地域",
    `isp` VARCHAR(50) NULL COMMENT "运营商",
    `location_href` VARCHAR(1024) NOT NULL COMMENT '页面来源URL',
    `processed_location_href` VARCHAR(512) NOT NULL COMMENT '处理过的页面来源URL',
    `http_url` VARCHAR(1024) NOT NULL COMMENT '请求URL',
    `backend_span_id` bigint(20) NULL COMMENT "后端spanID",
    `service` VARCHAR(255) NULL COMMENT "调用服务名称",
    `df_api_key_id` tinyint NOT NULL COMMENT "租户 apiKeyId",
    `start` bigint(20) NOT NULL COMMENT "开始时间",
    `end` bigint(20) NOT NULL COMMENT "结束时间",
    `duration` bigint(20) NOT NULL COMMENT "请求耗时(ns)",
    `url_param` VARCHAR(8192) NULL COMMENT "URL参数",
    `http_request_header` VARCHAR(8192) NULL COMMENT "HTTP请求头",
    `http_request_body` VARCHAR(8192) NULL COMMENT "HTTP请求体",
    `http_response_header` VARCHAR(8192) NULL COMMENT "HTTP响应头",
    `http_response_body` VARCHAR(8192) NULL COMMENT "HTTP响应体"

    )
    DUPLICATE KEY(`startTime`, `app_id`, `processed_http_url`)
    PARTITION BY RANGE(`startTime`) ()
    DISTRIBUTED BY HASH(`span_id`) BUCKETS 3
    PROPERTIES(
                  "replication_num" = "1",
                  "dynamic_partition.enable" = "true",
                  "dynamic_partition.time_unit" = "HOUR",
                  "dynamic_partition.start" = "-168",
                  "dynamic_partition.end" = "1",
                  "dynamic_partition.prefix" = "p",
                  "dynamic_partition.time_zone" = "Asia/Shanghai",
                  "dynamic_partition.history_partition_num" = "3",
                  "bloom_filter_columns" = "span_id,user_id,session_id,ip"
              );

ALTER TABLE `dc_profiling_stack`
ADD COLUMN `frameTypeIds` varchar(2048) NULL COMMENT "堆栈方法类型，index和stackTrace相同",
ADD COLUMN `hotspotJavaMethod` varchar(1000) NULL COMMENT "Java热点方法名" AFTER `hotspotMethod`;

ALTER TABLE `dc_profiling_hotspot`
ADD COLUMN `traceId` bigint(20) NULL COMMENT 'traceId',
DROP COLUMN `tid`,
DROP COLUMN `samples`;

ALTER TABLE databuff.dc_rum_page SET ("bloom_filter_columns" = "user_id, ip, session_id");
ALTER TABLE databuff.dc_rum_error_logs SET ("bloom_filter_columns" = "user_id, ip, session_id");
ALTER TABLE databuff.dc_rum_action SET ("bloom_filter_columns" = "user_id, ip, session_id");