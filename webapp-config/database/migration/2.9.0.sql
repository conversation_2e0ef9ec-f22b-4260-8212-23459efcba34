-- databuff mysql v2.8.6
SET NAMES utf8mb4;
use dc_databuff;
SET FOREIGN_KEY_CHECKS = 0;

DROP TABLE IF EXISTS `df_space_map_layout`;
CREATE TABLE `df_space_map_layout` (
                                       `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                       `type` VARCHAR(20) NOT NULL COMMENT '空间地图坐标类型',
                                       `account` VARCHAR(255) NOT NULL COMMENT '用户账号（唯一标识）',
                                       `api_key` VARCHAR(255) NOT NULL COMMENT 'API访问密钥',
                                       `gid` VARCHAR(20) NOT NULL COMMENT '管理域ID',
                                       `layout_data` longtext NOT NULL COMMENT '空间布局坐标数据（JSON格式）',
                                       `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                       PRIMARY KEY (`id`),
                                       UNIQUE KEY `uniq_id` (`account`,`type`,`gid`),
                                       INDEX `idx_gid` (`gid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='空间地图坐标表';


ALTER TABLE dc_data_collector_source
ADD COLUMN java_methods json DEFAULT NULL COMMENT 'Java方法描述列表',
ADD COLUMN source_key varchar(256) DEFAULT NULL COMMENT 'Java方法参数表达式';

alter table dc_databuff_service
    add datasource varchar(256) null comment '数据来源' after virtual_service;

SET FOREIGN_KEY_CHECKS = 1;