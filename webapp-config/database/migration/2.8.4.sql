-- databuff mysql v2.8.4
SET NAMES utf8mb4;
use dc_databuff;
SET FOREIGN_KEY_CHECKS = 0;

    -- ----------------------------
-- Table structure for dc_resources
-- ----------------------------
DROP TABLE IF EXISTS `dc_resources`;
CREATE TABLE `dc_resources` (
                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                `name` varchar(100) DEFAULT NULL COMMENT '菜单名称',
                                `url` varchar(200) DEFAULT NULL COMMENT 'url',
                                `parent_id` int(11) DEFAULT '0' COMMENT '父菜单id',
                                `hidden` tinyint(1) DEFAULT NULL COMMENT '是否显示在菜单栏 1-true 0-false',
                                `module_function` text COMMENT '页面功能 json',
                                `order` int(8) DEFAULT NULL COMMENT '菜单排列顺序',
                                `leaf` tinyint(1) DEFAULT NULL COMMENT '节点是否包含子集1-true 0-false',
                                `path` varchar(200) DEFAULT NULL COMMENT '路径',
                                `icon` varchar(100) DEFAULT NULL COMMENT '图标路径',
                                `is_license` tinyint(1) DEFAULT NULL COMMENT '是否授权 0-false 1-true',
                                `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                PRIMARY KEY (`id`) USING BTREE,
                                KEY `idx_sys_resource_parent_id` (`parent_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2605 ROW_FORMAT=COMPACT COMMENT='资源表';

-- ----------------------------
-- Records of dc_resources
-- ----------------------------
BEGIN;
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1, '驾驶舱', NULL, 0, 1, NULL, 1, 1, '/cockpit', NULL, 1, '2024-05-20 13:44:53', '2024-05-20 13:44:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (2, '数据报表', NULL, 0, 1, NULL, 2, 0, '/dataReport', NULL, 1, '2024-05-20 13:44:53', '2024-05-20 13:44:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (3, '智能告警', NULL, 0, 1, NULL, 3, 0, '/alarmCenter', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (4, '应用性能', NULL, 0, 1, NULL, 4, 0, '/appMonitor', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (5, '基础设施', NULL, 0, 1, NULL, 5, 0, '/infrastructure', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (6, '指标体系', NULL, 0, 1, NULL, 3, 0, '/metrics', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (7, '网络性能', NULL, 0, 1, NULL, 1, 0, '/npm', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (8, '访问体验', NULL, 0, 1, NULL, 8, 0, '/rum', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (9, '日志分析', NULL, 0, 1, NULL, 9, 0, '/log', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (10, '部署配置', NULL, 0, 1, NULL, 10, 0, '/config', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (11, '帮助中心', NULL, 0, 1, NULL, 11, 0, '/help', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (201, '仪表盘', NULL, 2, 1, NULL, 1, 1, '/dashboard', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (202, '报告', NULL, 2, 1, NULL, 2, 1, '/report', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (301, '告警列表', NULL, 3, 1, NULL, 1, 0, '/alarmCenter/alarm', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (302, '通知记录', NULL, 3, 1, NULL, 2, 1, '/alarmCenter/notice', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (303, '智能根因', NULL, 3, 1, NULL, 3, 1, '/alarmCenter/rootCause', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (401, '空间地图', NULL, 4, 1, NULL, 1, 1, '/appMonitor/relationMap', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (402, '业务系统', NULL, 4, 1, NULL, 2, 1, '/appMonitor/businessSystem', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (403, '服务', NULL, 4, 1, NULL, 3, 1, '/appMonitor/service', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (404, '数据库', NULL, 4, 1, NULL, 4, 1, '/appMonitor/database', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (405, '消息队列', NULL, 4, 1, NULL, 5, 1, '/appMonitor/msgQueue', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (406, '缓存', NULL, 4, 1, NULL, 6, 1, '/appMonitor/cache', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (407, '详细分析', NULL, 4, 1, NULL, 7, 1, '/appMonitor/serviceAnalysis', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (408, '错误分析', NULL, 4, 1, NULL, 8, 1, '/appMonitor/errors', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (409, '链路追踪', NULL, 4, 1, NULL, 9, 1, '/appMonitor/trace', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (501, '主机', NULL, 5, 1, NULL, 1, 1, '/infrastructure/host', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (502, 'Docker容器', NULL, 5, 1, NULL, 2, 1, '/infrastructure/docker', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (503, 'Kubernetes', NULL, 5, 1, NULL, 3, 1, '/infrastructure/cluster', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (504, '进程组', NULL, 5, 1, NULL, 4, 1, '/infrastructure/process', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (505, '网络设备', NULL, 5, 1, NULL, 5, 1, '/infrastructure/ndm', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (601, '探索', NULL, 6, 1, NULL, 1, 1, '/metrics/explore', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (602, '列表', NULL, 6, 1, NULL, 2, 1, '/metrics/list', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (702, '网络分析', NULL, 7, 1, NULL, 2, 1, '/npm/analysis', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (703, '网络拓扑', NULL, 7, 1, NULL, 3, 1, '/npm/topology', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (704, 'DNS分析', NULL, 7, 1, NULL, 4, 1, '/npm/dns', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (801, 'Web应用', NULL, 8, 1, NULL, 1, 1, '/rum/web', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (802, '多维分析', NULL, 8, 1, NULL, 2, 1, '/rum/analysis', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (803, '会话检索', NULL, 8, 1, NULL, 3, 1, '/rum/session', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1001, '安装部署', NULL, 10, 1, NULL, 1, 1, '/config/install', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1002, '部署状态', NULL, 10, 1, NULL, 2, 1, '/config/status', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1003, '配置管理', NULL, 10, 1, NULL, 3, 0, '/config/manage', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1010, '实体监控', NULL, 1003, 1, NULL, 1, 0, '/config/entity', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1011, '服务监控', NULL, 1010, 1, NULL, 1, 1, '/config/service', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1020, '告警配置', NULL, 1003, 1, NULL, 2, 0, '/config/alarm', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1021, '检测规则', NULL, 1020, 1, NULL, 1, 1, '/config/rule', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1022, '收敛策略', NULL, 1020, 1, NULL, 2, 1, '/config/convergence', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1023, '响应策略', NULL, 1020, 1, NULL, 3, 1, '/config/response', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1024, '静默计划', NULL, 1020, 1, NULL, 4, 1, '/config/silence', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1081, '用户管理', NULL, 1003, 1, NULL, 3, 1, '/sysManage/account', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1082, '角色管理', NULL, 1003, 1, NULL, 4, 1, '/sysManage/role', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1083, 'License管理', NULL, 1003, 1, NULL, 5, 1, '/sysManage/license', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1084, '通知管理', NULL, 1003, 1, NULL, 6, 1, '/sysManage/notice', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1085, '系统设置', NULL, 1003, 1, NULL, 7, 0, '/sysManage/setting', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1086, '基础设置', NULL, 1085, 1, NULL, 1, 1, '/sysManage/basic', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1087, '系统事件', NULL, 1085, 1, NULL, 2, 1, '/sysManage/systemEvent', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1101, '入门指南', NULL, 11, 1, NULL, 1, 1, '/help/startGuide', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1102, '时间同步', NULL, 11, 1, NULL, 2, 1, '/help/timeSync', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1103, 'NTP服务', NULL, 11, 1, NULL, 3, 1, '/help/setupNTP', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO `dc_resources` (`id`, `name`, `url`, `parent_id`, `hidden`, `module_function`, `order`, `leaf`, `path`, `icon`, `is_license`, `create_time`, `update_time`) VALUES (1104, '负载均衡', NULL, 11, 1, NULL, 4, 1, '/help/nginxTracing', NULL, 1, '2024-05-20 13:45:52', '2024-05-20 13:45:55');
INSERT INTO dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(16, '业务观测', 'observe', 0, 1, '', 6.05, 0, '/observe', 'menu-scene', NULL, '2022-04-07 17:18:10', '2022-04-07 17:18:10');
INSERT INTO dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(1601, '业务场景', 'scene', 16, 1, '', 1, 1, '/observe/scene', '', NULL, '2022-04-07 17:20:27', '2022-04-07 17:20:27');
INSERT INTO dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(1602, '业务事件', 'event', 16, 1, '', 2, 1, '/observe/event', '', NULL, '2022-04-07 17:20:27', '2022-04-07 17:20:27');

COMMIT;




-- ----------------------------
-- Table structure for dc_role_resources
-- ----------------------------
DROP TABLE IF EXISTS `dc_role_resources`;
CREATE TABLE `dc_role_resources` (
                                     `id` int(20) NOT NULL AUTO_INCREMENT,
                                     `role_id` int(11) NOT NULL COMMENT '角色id',
                                     `resources_id` int(11) NOT NULL COMMENT '菜单id',
                                     `create_time` datetime DEFAULT NULL,
                                     `update_time` datetime DEFAULT NULL,
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=493 ROW_FORMAT=DYNAMIC COMMENT='资源、角色中间表';

-- ----------------------------
-- Records of dc_role_resources
-- ----------------------------
BEGIN;
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 2, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 3, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 4, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 5, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 6, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 7, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 8, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 9, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 10, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 11, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 201, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 202, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 301, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 302, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 303, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 401, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 402, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 403, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 404, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 405, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 406, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 407, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 408, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 409, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 501, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 502, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 503, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 504, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 505, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 601, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 602, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 702, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 703, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 704, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 801, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 802, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 803, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1001, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1002, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1003, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1010, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1011, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1020, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1021, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1022, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1023, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1024, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1081, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1082, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1083, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1084, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1085, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1086, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1087, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1101, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1102, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1103, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 1, 1104, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 1, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 2, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 3, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 4, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 5, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 6, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 7, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 8, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 9, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 11, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 201, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 202, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 301, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 302, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 303, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 401, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 402, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 403, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 404, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 405, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 406, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 407, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 408, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 409, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 501, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 502, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 503, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 504, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 505, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 601, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 602, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 702, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 703, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 704, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 801, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 802, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 803, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 1101, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 1102, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 1103, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO `dc_role_resources` (`id`, `role_id`, `resources_id`, `create_time`, `update_time`) VALUES (null, 3, 1104, '2024-05-20 14:25:34', '2024-05-20 14:25:36');
INSERT INTO dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 16, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(3, 16, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 1601, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(3, 1601, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 1602, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(3, 1602, '2021-01-12 14:25:34', '2021-01-12 14:25:36');

COMMIT;

DROP TABLE IF EXISTS `dc_business_service_rule`;
create table dc_business_service_rule
(
    id          int auto_increment comment '规则ID'
        primary key,
    sys_id      int                                  not null comment '业务系统ID或子系统ID',
    rule_name   varchar(255)                         not null comment '规则名称',
    query       text                                 not null comment '规则查询条件',
    enabled     tinyint(1) default 1                 not null comment '是否启用,0:停用,1:启用',
    api_key     varchar(64)                          not null comment '租户 apiKey',
    create_time timestamp  default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time timestamp  default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '业务系统监控服务规则配置表';

-- dc_databuff.dc_databuff_traffic_light definition
DROP TABLE IF EXISTS `dc_databuff_traffic_light`;
CREATE TABLE `dc_databuff_traffic_light` (
                                             `type` varchar(255) NOT NULL COMMENT '红绿灯的排序类型',
                                             `red` bigint(20) NOT NULL COMMENT '红灯的阈值',
                                             `yellow` bigint(20) NOT NULL COMMENT '黄灯的阈值',
                                             `open` tinyint(4) DEFAULT '0' COMMENT '是否启用',
                                             PRIMARY KEY (`type`)
) ENGINE=InnoDB ;
INSERT INTO dc_databuff.dc_databuff_traffic_light (`type`,red,yellow,`open`) VALUES ('alarm',2,1,1),('exception',100,10,0);


-- 增加了菜单

INSERT INTO dc_databuff.dc_resources (id,name,url,parent_id,hidden,module_function,`order`,leaf,`path`,icon,is_license,create_time,update_time) VALUES
    (1012,'业务系统监控',NULL,1010,1,NULL,2,1,'/config/business',NULL,1,'2024-06-25 13:50:15','2024-06-25 13:50:15');
INSERT INTO dc_databuff.dc_resources (id,name,url,parent_id,hidden,module_function,`order`,leaf,`path`,icon,is_license,create_time,update_time) VALUES
    (1013,'进程监控',NULL,1010,1,NULL,3,1,'/config/process',NULL,1,'2024-06-25 13:51:54','2024-06-25 13:51:54');
INSERT INTO dc_databuff.dc_role_resources (role_id,resources_id,create_time,update_time) VALUES
    (1,1012,'2024-05-20 14:25:34','2024-05-20 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id,resources_id,create_time,update_time) VALUES
    (1,1013,'2024-05-20 14:25:34','2024-05-20 14:25:36');
INSERT INTO dc_databuff.dc_resources (id,name,url,parent_id,hidden,module_function,`order`,leaf,`path`,icon,is_license,create_time,update_time) VALUES
    (410,'服务流',NULL,4,1,NULL,3.5,1,'/appMonitor/serviceFlow',NULL,1,'2024-06-25 13:51:54','2024-06-25 13:51:54');
INSERT INTO dc_databuff.dc_role_resources (role_id,resources_id,create_time,update_time) VALUES
    (1,410,'2024-05-20 14:25:34','2024-05-20 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id,resources_id,create_time,update_time) VALUES
    (3,410,'2024-05-20 14:25:34','2024-05-20 14:25:36');


-- 新增进程采集和进程识别的两张表
DROP TABLE IF EXISTS `dc_process_collect_rules`;
CREATE TABLE `dc_process_collect_rules` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `process_name` varchar(255) DEFAULT NULL COMMENT '进程名称',
                                            `process_path` varchar(1024) DEFAULT NULL COMMENT '进程路径',
                                            `match_process` varchar(128) DEFAULT NULL COMMENT '进程匹配规则',
                                            `hostname` varchar(64) DEFAULT NULL COMMENT '主机名',
                                            `hostnames` varchar(1024) DEFAULT NULL,
                                            `ip` varchar(64) DEFAULT NULL COMMENT 'ip',
                                            `ips` varchar(1024) DEFAULT NULL,
                                            `tags` varchar(1024) DEFAULT NULL COMMENT '标签',
                                            `match_host` varchar(128) DEFAULT NULL COMMENT '主机匹配规则',
                                            `type` int(1) NOT NULL COMMENT '0 白名单 1黑名单',
                                            `status` int(11) DEFAULT NULL COMMENT '1生效 0不生效',
                                            `inner` int(11) NOT NULL DEFAULT '1' COMMENT '内置规则',
                                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 ;

DROP TABLE IF EXISTS `dc_process_identify_rules`;
CREATE TABLE `dc_process_identify_rules` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT,
                                             `process_name` varchar(255) DEFAULT NULL COMMENT '进程名称',
                                             `match_process` varchar(128) DEFAULT NULL COMMENT '进程匹配规则',
                                             `type` int(1) NOT NULL COMMENT '0 预置 1 非预置',
                                             `status` int(11) DEFAULT NULL COMMENT '1生效 0不生效',
                                             `inner` int(11) NOT NULL DEFAULT '1' COMMENT '内置规则',
                                             `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                             `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                             PRIMARY KEY (`id`)
) ENGINE=InnoDB ;

DROP TABLE IF EXISTS `dc_databuff_issue_log`;
CREATE TABLE `dc_databuff_issue_log` (
                                         `issue_id` varchar(32) NOT NULL COMMENT '问题ID',
                                         `alarm_id` varchar(32) DEFAULT NULL COMMENT '告警ID',
                                         `policy_id` int(11) DEFAULT NULL COMMENT '告警策略ID',
                                         `source` enum('告警触发','手动触发','其他') NOT NULL COMMENT '问题来源',
                                         `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `description` longtext COMMENT '问题描述',
                                         KEY `idx_alarm_id` (`alarm_id`),
                                         KEY `idx_issue_id` (`issue_id`)
) ENGINE=InnoDB ;
CREATE INDEX idx_create_time ON dc_databuff_issue_log (create_time);

DROP TABLE IF EXISTS `dc_databuff_issue_service`;
CREATE TABLE `dc_databuff_issue_service` (
                                             `issue_id` varchar(32) NOT NULL,
                                             `service` varchar(255) NOT NULL,
                                             `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                             PRIMARY KEY (`issue_id`,`service`)
) ENGINE=InnoDB ;
CREATE INDEX idx_create_time ON dc_databuff_issue_service (create_time);

DROP TABLE IF EXISTS `dc_databuff_issue_detail`;
CREATE TABLE `dc_databuff_issue_detail` (
                                            `id` varchar(32) NOT NULL,
                                            `description` text,
                                            `root_cause_node` varchar(255) DEFAULT NULL,
                                            `root_cause_type` varchar(255) DEFAULT NULL,
                                            `source` enum('告警触发','手动触发','其他') NOT NULL,
                                            `status` enum('分析中','分析成功','分析失败','打开','关闭','未知') NOT NULL,
                                            `start_time` datetime DEFAULT NULL,
                                            `end_time` datetime DEFAULT NULL,
                                            `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
                                            `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                            `root_analyse` json DEFAULT NULL,
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB ;

ALTER TABLE `dc_res_host` ADD COLUMN `features` text COMMENT '特征信息';

DROP TABLE IF EXISTS `dc_databuff_traffic_light`;

SET FOREIGN_KEY_CHECKS = 1;



DELETE FROM dc_databuff.dc_resources WHERE id=22;
DELETE FROM dc_databuff.dc_resources WHERE id=2201;
DELETE FROM dc_databuff.dc_resources WHERE id=2202;
DELETE FROM dc_databuff.dc_resources WHERE id=2203;
DELETE FROM dc_databuff.dc_resources WHERE id=2204;

DELETE FROM dc_databuff.dc_role_resources WHERE resources_id=22;
DELETE FROM dc_databuff.dc_role_resources WHERE resources_id=2201;
DELETE FROM dc_databuff.dc_role_resources WHERE resources_id=2202;
DELETE FROM dc_databuff.dc_role_resources WHERE resources_id=2203;
DELETE FROM dc_databuff.dc_role_resources WHERE resources_id=2204;

INSERT INTO dc_databuff.dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(22, 'RUM', 'rum', 0, 1, '', 8, 0, '/rum', 'menu-rum', NULL, '2022-04-07 17:18:10', '2022-04-07 17:18:10');
INSERT INTO dc_databuff.dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(2201, 'RUMApplication', 'application', 22, 1, '', 1, 1, '/rum/application', '', NULL, '2022-04-07 17:18:10', '2022-04-07 17:18:10');
INSERT INTO dc_databuff.dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(2202, 'RUMPage', 'page', 22, 1, '', 2, 1, '/rum/page', '', NULL, '2022-04-07 17:18:10', '2022-04-07 17:18:10');
INSERT INTO dc_databuff.dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(2203, 'RUMAction', 'action', 22, 1, '', 3, 1, '/rum/action', '', NULL, '2022-04-07 17:20:27', '2022-04-07 17:20:27');
INSERT INTO dc_databuff.dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(2204, 'RUMJSError', 'jsError', 22, 1, '', 4, 1, '/rum/jsError', '', NULL, '2022-04-07 17:20:27', '2022-04-07 17:20:27');



INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 22, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(3, 22, '2021-01-12 14:25:34', '2021-01-12 14:25:36');

INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 2201, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(3, 2201, '2021-01-12 14:25:34', '2021-01-12 14:25:36');

INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 2202, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(3, 2202, '2021-01-12 14:25:34', '2021-01-12 14:25:36');

INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 2203, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(3, 2203, '2021-01-12 14:25:34', '2021-01-12 14:25:36');

INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 2204, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(3, 2204, '2021-01-12 14:25:34', '2021-01-12 14:25:36');


ALTER TABLE dc_rum_app_settings ADD COLUMN security_settings JSON COMMENT '安全设置';
ALTER TABLE dc_rum_app_settings ADD COLUMN threshold_settings JSON COMMENT '阈值设置';
ALTER TABLE dc_rum_app_settings ADD COLUMN url_aggregation_settings JSON COMMENT 'URL聚合设置';

CREATE TABLE dc_databuff.`dc_rum_web_request_alias` (
                                                        `app_id` int(11) NOT NULL COMMENT '应用ID',
                                                        `processed_http_url` varchar(512) NOT NULL COMMENT '处理过的http url',
                                                        `alias` varchar(200) NOT NULL COMMENT '别名',
                                                        `create_time` datetime DEFAULT current_timestamp() COMMENT '创建时间',
                                                        PRIMARY KEY (`app_id`,`processed_http_url`)
) ENGINE=InnoDB COMMENT='rum请求别名表';



INSERT INTO dc_databuff.dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(1014, '请求监控', 'request', 1010, 1, '', 4, 1, '/config/request', '', 1, '2022-04-07 17:20:27', '2022-04-07 17:20:27');

INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 1014, '2021-01-12 14:25:34', '2021-01-12 14:25:36');

INSERT INTO dc_databuff.dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(1050, '空间地图配置', 'relation', 1003, 1, '', 1, 1, '/config/relationest', '', 1, '2022-04-07 17:20:27', '2022-04-07 17:20:27');

INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 1050, '2021-01-12 14:25:34', '2021-01-12 14:25:36');

CREATE TABLE `dc_data_collector_property` (
                                              `id` int(11) NOT NULL AUTO_INCREMENT,
                                              `property` varchar(32) NOT NULL COMMENT '属性',
                                              `data_type` int(4) NOT NULL COMMENT '数据类型',
                                              `values_handle` int(4) NOT NULL COMMENT '多值处理',
                                              `upper_lower` int(4) NOT NULL COMMENT '大小写处理方式',
                                              `state` tinyint(1) DEFAULT '0' COMMENT '是否开启',
                                              PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 COMMENT='数据采集器属性配置';

CREATE TABLE `dc_data_collector_source` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `property_id` int(11) NOT NULL COMMENT '属性ID',
                                            `name` varchar(32) NOT NULL COMMENT '数据源名称',
                                            `description` varchar(256) DEFAULT NULL COMMENT '描述',
                                            `service_id` varchar(64) NOT NULL COMMENT '服务ID',
                                            `service_name` varchar(64) NOT NULL COMMENT '服务名称',
                                            `property_source` int(4) NOT NULL COMMENT '属性来源',
                                            `property_name` varchar(32) NOT NULL COMMENT '参数名',
                                            `get_value_method` int(4) NOT NULL COMMENT '取值方式',
                                            `state` tinyint(1) DEFAULT '0' COMMENT '是否开启',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=22 COMMENT='数据采集器数据源配置';

CREATE TABLE `dc_databuff_problem` (
                                       `id` varchar(100) NOT NULL,
                                       `problemService` varchar(100) NOT NULL,
                                       `problemServiceType` varchar(45) DEFAULT NULL,
                                       `problemCauseType` varchar(100) DEFAULT NULL,
                                       `problemDesc` varchar(1000) DEFAULT NULL,
                                       `problemStartTime` timestamp NULL DEFAULT NULL,
                                       `problemEndTime` timestamp NULL DEFAULT NULL,
                                       `influence` json DEFAULT NULL,
                                       `analyseStartTime` timestamp NULL DEFAULT NULL,
                                       `analyseEndTime` timestamp NULL DEFAULT NULL,
                                       `influenceServiceCount` int(11) DEFAULT NULL,
                                       `influenceAlarmCount` int(11) DEFAULT NULL,
                                       `status` varchar(45) DEFAULT NULL,
                                       `isRoot` tinyint(4) DEFAULT NULL,
                                       `beginToActionTime` timestamp NULL DEFAULT NULL,
                                       PRIMARY KEY (`id`),
                                       KEY `idx_dc_databuff_problem_problemEndTime` (`problemEndTime`),
                                       KEY `idx_dc_databuff_problem_problemService` (`problemService`),
                                       KEY `idx_dc_databuff_problem_problemCauseType` (`problemCauseType`),
                                       KEY `idx_dc_databuff_problem_isRoot` (`isRoot`)
) ENGINE=InnoDB;

ALTER TABLE dc_databuff.dc_databuff_issue_detail
ADD COLUMN problemId varchar(32) comment "issue关联的problemId",
MODIFY COLUMN source enum('事件触发','告警触发','手动触发','其他') CHARACTER SET utf8mb4 NOT NULL;

CREATE INDEX idx_start_time ON dc_databuff.dc_databuff_issue_detail (start_time);

-- 增加告警清理定时任务
INSERT INTO `dc_databuff`.`xxl_job_info`(  `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES ( 2, 'sharding-alarmClean-task', '2022-11-02 15:45:35', '2022-11-02 15:45:35', 'fm', '', 'CRON', '50 * * * * ?', 'DO_NOTHING', 'ROUND', 'sharding-alarmClean-task', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2022-11-02 15:45:35', '', 1, 1711590645000, 1711590705000);
-- 删除无用定时任务
DELETE FROM `dc_databuff`.`xxl_job_info` where executor_handler = 'sharding-defConvergenceTimer-task';
-- 服务列表更新时间字段为空的默认更改为当前时间，后续会自动过期删除；如果为空则不回删除
update `dc_databuff_service` t set t.update_time = now() where t.update_time is null;

-- 增加告警描述模板和变量列表
ALTER TABLE `dc_databuff_convergence_policy`
ADD COLUMN `pattern` varchar(400) DEFAULT NULL COMMENT '告警描述模板',
ADD COLUMN `variables` json DEFAULT NULL COMMENT '告警描述变量列表',
ADD COLUMN sameRootCause BOOLEAN COMMENT '是否为同根因收敛';


-- 添加内置收敛策略
-- 【预置】同服务收敛
-- 【预置】同主机事件收敛
-- 【预置】智能AI收敛
-- 【预置】同业务事件收敛

DELETE FROM dc_databuff.dc_databuff_convergence_policy WHERE policy_name = '【预置】同服务收敛';
DELETE FROM dc_databuff.dc_databuff_convergence_policy WHERE policy_name = '【预置】同主机事件收敛';
DELETE FROM dc_databuff.dc_databuff_convergence_policy WHERE policy_name = '【预置】智能AI收敛';
DELETE FROM dc_databuff.dc_databuff_convergence_policy WHERE policy_name = '【预置】同业务事件收敛';

INSERT IGNORE INTO dc_databuff.dc_databuff_convergence_policy (policy_name, is_ai, enabled, fixed_duration_window, end_judgment_window, filter_conditions, convergence_conditions, alarm_description_template, created_time, updated_time, creator_id, editor_id, creator, editor, api_key, sameRootCause, pattern, variables) VALUES('【预置】同服务收敛', 0, 1, 5, NULL, '[]', '[{"left": "service", "right": "", "operator": "same", "connector": "AND"}]', '服务【{{服务名称}}】产生告警，收敛{{收敛事件数量}}个事件，持续{{告警持续时间}}。', '2024-04-02 18:04:56', '2024-11-20 18:54:06', 1, 1, 'Admin', 'Admin', 'NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4', NULL, '服务【{2}】产生告警，收敛{1}个事件，持续{0}。', '["duration", "eventCnt", "service"]');
INSERT IGNORE INTO dc_databuff.dc_databuff_convergence_policy (policy_name, is_ai, enabled, fixed_duration_window, end_judgment_window, filter_conditions, convergence_conditions, alarm_description_template, created_time, updated_time, creator_id, editor_id, creator, editor, api_key, sameRootCause, pattern, variables) VALUES('【预置】同主机事件收敛', 0, 1, 5, NULL, '[]', '[{"left": "host", "right": "", "operator": "same", "connector": "AND"}]', '主机【{{主机}}】产生告警，ip为【{{主机IP}}】，共收敛{{收敛事件数量}}个事件，持续{{告警持续时间}}。', '2024-04-02 18:06:40', '2024-11-20 18:54:06', 1, 1, 'Admin', 'Admin', 'NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4', NULL, '主机【{2}】产生告警，ip为【{3}】，共收敛{1}个事件，持续{0}。', '["duration", "eventCnt", "host", "hostIp"]');
INSERT IGNORE INTO dc_databuff.dc_databuff_convergence_policy (policy_name, is_ai, enabled, fixed_duration_window, end_judgment_window, filter_conditions, convergence_conditions, alarm_description_template, created_time, updated_time, creator_id, editor_id, creator, editor, api_key, sameRootCause, pattern, variables) VALUES('【预置】智能AI收敛', 0, 0, NULL, NULL, NULL, NULL, '{{问题描述}}', '2024-04-02 18:04:56', '2024-11-20 18:54:06', 1, 1, 'Admin', 'Admin', 'NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4', 1, '{0}', '["problemDesc"]');
INSERT IGNORE INTO dc_databuff.dc_databuff_convergence_policy (policy_name, is_ai, enabled, fixed_duration_window, end_judgment_window, filter_conditions, convergence_conditions, alarm_description_template, created_time, updated_time, creator_id, editor_id, creator, editor, api_key, pattern, variables, sameRootCause) VALUES('【预置】同业务事件收敛', 0, 1, 5, NULL, '[]', '[{"left": "bizEventName", "right": "", "operator": "same", "connector": "AND"}]', '业务事件【{{业务事件名称}}】中服务【{{服务名称}}】产生告警，收敛{{收敛事件数量}}个事件，持续{{告警持续时间}}。', '2025-01-20 13:55:23', '2025-01-20 14:05:22', 1, 1, 'Admin', 'Admin', 'NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4', '业务事件【{2}】中服务【{3}】产生告警，收敛{1}个事件，持续{0}。', '["duration", "eventCnt", "bizEventName", "service"]', NULL);

-- 增加rum web菜单
INSERT INTO dc_databuff.dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(304, '问题分析', 'problemAnalysis', 3, 1, '', 4, 1, '/alarmCenter/problemAnalysis', '', NULL, '2022-04-07 17:20:27', '2022-04-07 17:20:27');
INSERT INTO dc_databuff.dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(2205, 'Web请求', 'request', 22, 1, '', 5, 1, '/rum/request', '', NULL, '2022-04-07 17:20:27', '2022-04-07 17:20:27');
INSERT INTO dc_databuff.dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(2206, '用户追踪', 'trace', 22, 1, '', 6, 1, '/rum/trace', '', NULL, '2022-04-07 17:20:27', '2022-04-07 17:20:27');

INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 304, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(3, 304, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 2205, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(3, 2205, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 2206, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(3, 2206, '2021-01-12 14:25:34', '2021-01-12 14:25:36');



CREATE TABLE IF NOT EXISTS dc_rum_source_maps (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    app_id int(11) NOT NULL COMMENT '应用ID',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_path VARCHAR(512) NOT NULL COMMENT 'Source Map文件路径',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unique_app_file (app_id, file_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='rum js source map映射表';


SET FOREIGN_KEY_CHECKS = 1;