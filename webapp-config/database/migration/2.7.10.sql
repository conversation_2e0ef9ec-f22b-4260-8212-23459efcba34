-- v2.7.10  业务观测
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
use dc_databuff;


-- dc_databuff.df_biz_event definition
DROP TABLE IF EXISTS `df_biz_event`;
CREATE TABLE `df_biz_event` (
                                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '业务事件id',
                                `enabled` tinyint(1) DEFAULT '1' COMMENT '策略状态，布尔类型，默认为开启状态',
                                `biz_name` varchar(255) NOT NULL COMMENT '业务事件名称',
                                `biz_type` int(11) NOT NULL COMMENT '1前端业务 2后端业务',
                                `biz_sub_type` int(11) NOT NULL COMMENT '1服务应用级 2接口级',
                                `biz_reqs` json DEFAULT NULL COMMENT '关联请求',
                                `remark` text COMMENT '注释',
                                `api_key` varchar(255) NOT NULL COMMENT 'api_key',
                                `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create_time',
                                `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'update_time',
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 ;


-- dc_databuff.df_biz_scenario definition
DROP TABLE IF EXISTS `df_biz_scenario`;
CREATE TABLE `df_biz_scenario` (
                                   `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '业务场景id',
                                   `biz_scenario_name` varchar(255) NOT NULL COMMENT '业务场景名称',
                                   `biz_group_id` int(11) NOT NULL COMMENT '业务分组id',
                                   `biz_group_name` varchar(255) NOT NULL COMMENT '业务分组名',
                                   `biz_events` json DEFAULT NULL COMMENT '业务场景关联业务事件',
                                   `remark` text COMMENT '注释',
                                   `api_key` varchar(255) NOT NULL COMMENT 'api_key',
                                   `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create_time',
                                   `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'update_time',
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 ;


-- dc_databuff.df_biz_group definition
DROP TABLE IF EXISTS `df_biz_group`;
CREATE TABLE `df_biz_group` (
                                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '业务分组id',
                                `group_name` varchar(255) NOT NULL COMMENT '业务分组名称',
                                `order` int(11) NOT NULL COMMENT '排序号',
                                `api_key` varchar(255) NOT NULL COMMENT 'api_key',
                                PRIMARY KEY (`id`),
                                UNIQUE KEY `df_biz_group_UN` (`group_name`,`api_key`)
) ENGINE=InnoDB AUTO_INCREMENT=0 ;


-- 业务事件指标生成调度任务，不需要的可以删除
INSERT INTO dc_databuff.xxl_job_info (job_group,job_desc,add_time,update_time,author,alarm_email,schedule_type,schedule_conf,misfire_strategy,executor_route_strategy,executor_handler,executor_param,executor_block_strategy,executor_timeout,executor_fail_retry_count,glue_type,glue_source,glue_remark,glue_updatetime,child_jobid,trigger_status,trigger_last_time,trigger_next_time) VALUES
    (2,'sharding-bizObservability-task','2024-08-27 15:45:35','2024-08-27 15:45:35','fm','','CRON','0 0/1 * * * ?','DO_NOTHING','SHARDING_BROADCAST','sharding-bizObservability-task','','SERIAL_EXECUTION',0,0,'BEAN','','GLUE代码初始化','2024-09-01 15:45:35','',1,1724944800000,1724944860000);

INSERT INTO dc_databuff.dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(16, '业务观测', 'observe', 0, 1, '', 6.05, 0, 'observe', 'menu-scene', NULL, '2022-04-07 17:18:10', '2022-04-07 17:18:10');
INSERT INTO dc_databuff.dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(1601, '业务场景', 'scene', 16, 1, '', 1, 1, 'observe/scene', '', NULL, '2022-04-07 17:20:27', '2022-04-07 17:20:27');
INSERT INTO dc_databuff.dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(1602, '业务事件', 'event', 16, 1, '', 2, 1, 'observe/event', '', NULL, '2022-04-07 17:20:27', '2022-04-07 17:20:27');


INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 16, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(3, 16, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 1601, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(3, 1601, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 1602, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(3, 1602, '2021-01-12 14:25:34', '2021-01-12 14:25:36');