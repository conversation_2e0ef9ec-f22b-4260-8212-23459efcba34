-- databuff mysql v2.8.6
SET NAMES utf8mb4;
use dc_databuff;
SET
FOREIGN_KEY_CHECKS = 0;

DELETE FROM dc_databuff.dc_agent_updata  ;
ALTER TABLE dc_databuff.dc_agent_updata ADD operation INT NULL COMMENT '操作类型，0更新，1重启，2停止，3启动';
ALTER TABLE dc_databuff.dc_agent_updata ADD msg longtext NULL COMMENT '失败原因等信息';
ALTER TABLE dc_databuff.dc_agent_updata MODIFY COLUMN status int NULL COMMENT '状态 0待更新/重启/停止/启动，1更新/重启/停止/启动成功，2更新/重启/停止/启动失败，3更新/重启/停止/启动中';

ALTER TABLE dc_databuff.dc_agent ADD goos varchar(100) NULL COMMENT '操作系统';
UPDATE dc_agent da LEFT JOIN dc_res_host drh ON da.host_name = drh.host_name AND da.api_key = drh.api_key SET da.goos = drh.goos where da.goos is null;


-- 新增有关于DataHub配置表和集群表
CREATE TABLE IF NOT EXISTS dc_databuff.`dc_databuff_datahub_pipeline`
(
    id                  INT UNSIGNED NOT NULL PRIMARY KEY AUTO_INCREMENT,
    pipeline_name       VARCHAR(100) NOT NULL DEFAULT '' COMMENT '管道名称',
    cluster_id          INT UNSIGNED NOT NULL COMMENT '所属的集群id',
    remark              VARCHAR(255) DEFAULT '' COMMENT '备注',
    data_type           VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'Pipeline所要处理的数据类型，部分算子不支持部分类型，要做校验',
    create_time         TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time         TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    create_user_id      VARCHAR(100) NOT NULL COMMENT '用户id',
    update_user_id      VARCHAR(100) DEFAULT '' COMMENT '更新用户id',
    enabled             TINYINT      NOT NULL DEFAULT 1 COMMENT '是否启用，0不启用，1启用',
    is_delete           BIGINT      NOT NULL DEFAULT 0 COMMENT '是否删除，0未删除，大于0 已删除',
    -- 唯一索引, 这个集群clusterId下，这个datatype下，这个名字唯一。
    UNIQUE INDEX unique_pipeline_name (cluster_id, data_type, pipeline_name, is_delete)
) COMMENT='DataHub pipeline 表';

-- 配置表
CREATE TABLE IF NOT EXISTS dc_databuff.`dc_databuff_datahub_pipeline_configurations`
(
    id                  INT UNSIGNED NOT NULL PRIMARY KEY AUTO_INCREMENT,
    pipeline_id         INT UNSIGNED NOT NULL COMMENT '所属的管道id',
    name                VARCHAR(100) NOT NULL DEFAULT '' COMMENT '配置名称',
    remark              VARCHAR(255) DEFAULT '' COMMENT '备注',
    type                VARCHAR(100) NOT NULL COMMENT '配置所属的类型',
    parent_type         VARCHAR(100) DEFAULT '' COMMENT '父类型',
    config              TEXT NOT NULL COMMENT '配置内容',
    enabled             TINYINT      NOT NULL DEFAULT 1 COMMENT '是否启用，0不启用，1启用',
    is_delete           BIGINT      NOT NULL DEFAULT 0 COMMENT '是否删除，0未删除，大于0 已删除',
    create_user_id      VARCHAR(100) NOT NULL COMMENT '用户id',
    update_user_id      VARCHAR(100) DEFAULT '' COMMENT '更新用户id',
    create_time         TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time         TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    -- 唯一索引, 这个pipeline下，这个parentType下，这个type 这个状态，这个名字唯一。
    UNIQUE INDEX unique_name (pipeline_id, parent_type, type, name, is_delete)
) COMMENT='DataHub pipeline配置表';

-- 集群表
CREATE TABLE IF NOT EXISTS dc_databuff.`dc_databuff_datahub_cluster` (
    id              INT UNSIGNED NOT NULL PRIMARY KEY AUTO_INCREMENT,
    cluster_name    VARCHAR(100) NOT NULL COMMENT '集群名称',
    remark          VARCHAR(255) DEFAULT '' COMMENT '备注',
    enabled         TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用，0不启用，1启用',
    create_user_id  VARCHAR(100) NOT NULL COMMENT '用户id',
    update_user_id  VARCHAR(100) DEFAULT '' COMMENT '更新用户id',
    is_delete       BIGINT NOT NULL DEFAULT 0 COMMENT '是否删除，0未删除，大于0 已删除',
    create_time     TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time     TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    port            INT UNSIGNED NOT NULL DEFAULT 10000 COMMENT '该集群分配的端口号资源',
    -- 唯一索引, 是否删除状态下，这个集群名称唯一
    UNIQUE INDEX unique_cluster_name (cluster_name, is_delete)
) COMMENT 'DataHub集群表';

-- 集群节点表
CREATE TABLE IF NOT EXISTS dc_databuff.`dc_databuff_datahub_cluster_nodes` (
    id              INT UNSIGNED NOT NULL PRIMARY KEY AUTO_INCREMENT,
    cluster_id      INT UNSIGNED NOT NULL COMMENT '所属的集群id',
    node_name       VARCHAR(100) NOT NULL COMMENT '节点名称',
    status          VARCHAR(100) NOT NULL COMMENT '节点状态',
    create_time     TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time     TIMESTAMP  NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    other_info      TEXT COMMENT '其他信息'
) COMMENT 'DataHub集群节点表';

-- 新增一些默认的配置
INSERT IGNORE INTO dc_databuff.dc_databuff_datahub_cluster (id, cluster_name, remark, create_user_id, update_user_id) VALUES (1, 'datahub', '默认集群', 'Admin', 'Admin');

-- dc_databuff.dc_agent_log definition
DROP TABLE IF EXISTS `dc_agent_log`;
CREATE TABLE `dc_agent_log` (
                                `id` int NOT NULL AUTO_INCREMENT,
                                `log_name` varchar(255) NOT NULL COMMENT '日志文件名称',
                                `path` varchar(255) NOT NULL COMMENT '日志文件路径',
                                `host` varchar(255) NOT NULL COMMENT '主机名',
                                `is_new` int NOT NULL COMMENT '是否新上传',
                                `upload_time` datetime NOT NULL COMMENT '上传时间',
                                `log_file_size` int(11) NOT NULL COMMENT '日志文件大小，bytes字节',
                                `log_file_create_time` datetime NULL COMMENT '日志创建时间',
                                `log_file_update_time` datetime NULL COMMENT '日志修改时间',
                                `api_key` varchar(255) NOT NULL COMMENT 'api_key',
                                PRIMARY KEY (`id`),
                                UNIQUE KEY `p_key` (`log_name`,`host`,`api_key`) USING BTREE
) ENGINE=InnoDB ;


-- 修改 df_notify_record 表结构
ALTER TABLE `df_notify_record`
    -- 修改字段类型
    CHANGE COLUMN `notice_time` `notice_time` DATETIME NULL COMMENT '通知时间', -- 修改为 `DATETIME` 类型
    CHANGE COLUMN `alert_start_time` `alert_start_time` DATETIME NULL COMMENT '告警开始时间', -- 修改为 `DATETIME` 类型
    CHANGE COLUMN `resend_time` `resend_time` DATETIME NULL COMMENT '重新发送时间', -- 修改为 `DATETIME` 类型
    -- 添加索引
    ADD INDEX `idx_api_key` (`api_key`),
    ADD INDEX `idx_resp_policy_id` (`resp_policy_id`),
    ADD INDEX `idx_notice_time` (`notice_time`),
    ADD INDEX `idx_alarm_id` (`alarm_id`),
    ADD INDEX `idx_method` (`method`),
    ADD INDEX `idx_result` (`result`),
    ADD INDEX `idx_alert_type` (`alert_type`),
    ADD INDEX `idx_api_key_notice_time` (`api_key`, `notice_time`);


ALTER TABLE dc_databuff.dc_role CHANGE data_auth_level pid int DEFAULT 0 NULL COMMENT '父id，默认0';
ALTER TABLE dc_databuff.dc_role MODIFY COLUMN pid int DEFAULT 0 NULL COMMENT '父id，默认0';


DROP TABLE IF EXISTS `df_role_group_relation`;
CREATE TABLE `df_role_group_relation` (
                                          `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                          `role_id` int NOT NULL COMMENT '角色ID',
                                          `gid` int NOT NULL COMMENT '管理域ID',
                                          `config_auth` tinyint(1) NOT NULL DEFAULT '0' COMMENT '配置管理权限：0-false 1-true',
                                          `data_auth` tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据查看权限：0-false 1-true',
                                          `api_key` varchar(64) NOT NULL COMMENT 'API Key',
                                          `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                          PRIMARY KEY (`id`),
                                          KEY `idx_role_gid` (`role_id`,`gid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色-管理域关系表';


RENAME TABLE dc_databuff.df_notify_revice_user TO dc_databuff.df_notify_receive_user;
ALTER TABLE dc_databuff.df_notify_receive_user DROP COLUMN creator_id;
ALTER TABLE dc_databuff.df_notify_receive_user DROP COLUMN editor_id;
ALTER TABLE dc_databuff.df_notify_receive_user ADD wechat_webhook varchar(255) NULL COMMENT '微信群机器人Webhook调用地址';
ALTER TABLE dc_databuff.df_notify_receive_user ADD ding_webhook varchar(255) NULL COMMENT '钉钉群机器人Webhook调用地址';
ALTER TABLE dc_databuff.df_notify_receive_user ADD ding_secret varchar(255) NULL COMMENT '签名秘钥（安全设置使用 加签 的方式）';
ALTER TABLE dc_databuff.df_notify_receive_user ADD CONSTRAINT receive_name_UN UNIQUE KEY (rcv_name);

INSERT INTO df_notify_receive_user (
    rcv_name,
    enabled,
    phone,
    email,
    wechat_uid,
    dingtalk_uid,
    api_key,
    wechat_webhook,
    ding_webhook,
    ding_secret
)
SELECT
    account AS rcv_name,
    1 AS enabled,
    mobile AS phone,
    email_addr AS email,
    NULL AS wechat_uid,
    NULL AS dingtalk_uid,
    'NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4' AS api_key,
    NULL AS wechat_webhook,
    NULL AS ding_webhook,
    NULL AS ding_secret
FROM dc_user
    ON DUPLICATE KEY UPDATE
                         enabled = VALUES(enabled),
                         phone = VALUES(phone),
                         email = VALUES(email),
                         api_key = VALUES(api_key),
                         wechat_uid = IFNULL(wechat_uid, VALUES(wechat_uid)),
                         dingtalk_uid = IFNULL(dingtalk_uid, VALUES(dingtalk_uid)),
                         wechat_webhook = IFNULL(wechat_webhook, VALUES(wechat_webhook)),
                         ding_webhook = IFNULL(ding_webhook, VALUES(ding_webhook)),
                         ding_secret = IFNULL(ding_secret, VALUES(ding_secret));

ALTER TABLE dc_databuff_monitor
MODIFY COLUMN `type` varchar(200) NOT NULL COMMENT '检测类型',
ADD COLUMN gid VARCHAR(32) COMMENT '域ID',
DROP INDEX `dc_databuff_monitor_UN`,
ADD UNIQUE KEY `dc_databuff_monitor_UN` (`api_key`,`rule_name`, `gid`) USING BTREE,
ADD INDEX idx_monitor_creator (creator),
ADD INDEX idx_monitor_gid (gid);

DELETE FROM dc_databuff.dc_databuff_monitor
WHERE rule_name IN ('flink-CPU使用率过高', 'dts-CPU使用率过高', 'task-executor-CPU使用率过高', 'webapp-CPU使用率过高');

INSERT INTO dc_databuff.dc_databuff_monitor (name, tags, classification, `type`, priority, multi, time_out, message, query, `options`, params, monitor_notice, create_time, update_time, api_key, rule_name, service_ids, service_instances, hosts, pnames, businessNames, target, `system`, enabled, creator_id, editor_id, creator, editor, gid) VALUES(NULL, NULL, 'singleMetric', 'threshold', NULL, NULL, 0, NULL, '{"1":{"A":{"types":["自监控","flink","CPU"],"metric":"flink.system.cpu.usage","by":["ip"],"from":[],"aggs":"avg"},"expr":"A","way":"threshold","unit":"percent","period":300,"no_data_timeframe":5,"require_full_window":false,"evaluation_delay":0,"thresholds":{"critical":0.5,"warning":null},"view_unit":"%","_scale":0.01,"time_aggregator":"avg","comparison":">","continuous":true,"continuous_n":3},"critical":"AND","warning":"AND","noData":"AND"}', NULL, NULL, NULL, '2024-03-14 11:05:44', '2025-02-11 14:28:23', 'NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4', 'flink-CPU使用率过高', NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 'Admin', 'Admin', NULL);
INSERT INTO dc_databuff.dc_databuff_monitor (name, tags, classification, `type`, priority, multi, time_out, message, query, `options`, params, monitor_notice, create_time, update_time, api_key, rule_name, service_ids, service_instances, hosts, pnames, businessNames, target, `system`, enabled, creator_id, editor_id, creator, editor, gid) VALUES(NULL, NULL, 'singleMetric', 'threshold', NULL, NULL, 0, NULL, '{"1":{"A":{"types":["自监控","dts","CPU"],"metric":"dts.system.cpu.usage","by":["ip"],"from":[],"aggs":"avg"},"expr":"A","way":"threshold","unit":"percent","period":300,"no_data_timeframe":5,"require_full_window":false,"evaluation_delay":0,"thresholds":{"critical":0.5,"warning":null},"view_unit":"%","_scale":0.01,"time_aggregator":"avg","comparison":">","continuous":true,"continuous_n":3},"critical":"AND","warning":"AND","noData":"AND"}', NULL, NULL, NULL, '2024-03-20 20:24:33', '2025-02-11 14:28:23', 'NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4', 'dts-CPU使用率过高', NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 'Admin', 'Admin', NULL);
INSERT INTO dc_databuff.dc_databuff_monitor (name, tags, classification, `type`, priority, multi, time_out, message, query, `options`, params, monitor_notice, create_time, update_time, api_key, rule_name, service_ids, service_instances, hosts, pnames, businessNames, target, `system`, enabled, creator_id, editor_id, creator, editor, gid) VALUES(NULL, NULL, 'singleMetric', 'threshold', NULL, NULL, 0, NULL, '{"1":{"A":{"types":["自监控","task-executor","CPU"],"metric":"task-executor.system.cpu.usage","by":["ip"],"from":[],"aggs":"avg"},"expr":"A","way":"threshold","unit":"percent","period":300,"no_data_timeframe":5,"require_full_window":false,"evaluation_delay":0,"thresholds":{"critical":0.5,"warning":null},"view_unit":"%","_scale":0.01,"time_aggregator":"avg","comparison":">","continuous":true,"continuous_n":3},"critical":"AND","warning":"AND","noData":"AND"}', NULL, NULL, NULL, '2024-03-20 20:29:56', '2025-02-11 14:28:24', 'NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4', 'task-executor-CPU使用率过高', NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 'Admin', 'Admin', NULL);
INSERT INTO dc_databuff.dc_databuff_monitor (name, tags, classification, `type`, priority, multi, time_out, message, query, `options`, params, monitor_notice, create_time, update_time, api_key, rule_name, service_ids, service_instances, hosts, pnames, businessNames, target, `system`, enabled, creator_id, editor_id, creator, editor, gid) VALUES(NULL, NULL, 'singleMetric', 'threshold', NULL, NULL, 0, NULL, '{"1":{"A":{"types":["自监控","webapp","CPU"],"metric":"webapp.system.cpu.usage","by":["ip"],"from":[],"aggs":"avg"},"expr":"A","way":"threshold","unit":"percent","period":300,"no_data_timeframe":5,"require_full_window":false,"evaluation_delay":0,"thresholds":{"critical":0.5,"warning":null},"view_unit":"%","_scale":0.01,"time_aggregator":"avg","comparison":">","continuous":true,"continuous_n":3},"critical":"AND","warning":"AND","noData":"AND"}', NULL, NULL, NULL, '2024-03-20 20:32:32', '2025-02-11 14:28:24', 'NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4', 'webapp-CPU使用率过高', NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1, 1, 'Admin', 'Admin', NULL);

ALTER TABLE dc_databuff_convergence_policy
ADD COLUMN gid VARCHAR(32) NULL COMMENT '域ID',
ADD COLUMN def tinyint(1) NULL DEFAULT 0 COMMENT '是否为默认收敛策略，0-否，1-是',
DROP INDEX `policy_name`,
ADD UNIQUE KEY `policy_name` (`policy_name`, `gid`) USING BTREE;

-- 添加内置收敛策略
-- 默认收敛策略
INSERT IGNORE INTO dc_databuff.dc_databuff_convergence_policy (policy_name, is_ai, enabled, fixed_duration_window, end_judgment_window, filter_conditions, convergence_conditions, alarm_description_template, created_time, updated_time, creator_id, editor_id, creator, editor, api_key, pattern, variables, sameRootCause, gid, def) VALUES('默认收敛策略', 0, 1, 5, NULL, '[]', '[]', NULL, '2025-02-13 14:43:36', '2025-02-13 14:45:08', NULL, NULL, 'Admin', 'Admin', 'NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4', NULL, NULL, NULL, NULL, 1);

ALTER TABLE dc_databuff_monitor_silence
ADD COLUMN gid VARCHAR(32) COMMENT '域ID';

ALTER TABLE df_notify_record
ADD COLUMN gid VARCHAR(32) COMMENT '域ID';

ALTER TABLE `dc_databuff_resp_policy`
ADD COLUMN gid VARCHAR(32) COMMENT '域ID',
DROP INDEX `policy_key`,
ADD UNIQUE KEY `policy_key` (`policy_name`, `api_key`, `gid`) USING BTREE;

ALTER TABLE dc_databuff_problem
ADD COLUMN gid VARCHAR(32) COMMENT '域ID',
ADD COLUMN problemShowId VARCHAR(50) COMMENT 'Problem的展示ID';

ALTER TABLE dc_databuff_issue_detail
ADD COLUMN gid VARCHAR(32) COMMENT '域ID';

DROP TABLE IF EXISTS `dc_databuff_monitor_template`;


CREATE TABLE dc_rum_ios_symbol_file (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    app_id INT NOT NULL COMMENT '应用ID',
    version VARCHAR(16) NOT NULL COMMENT '应用版本号',
    build_version VARCHAR(30) NOT NULL COMMENT '构建版本号',
    uuid VARCHAR(50) NOT NULL COMMENT '符号表UUID',
    file_path VARCHAR(500) NOT NULL COMMENT '符号表压缩文件路径',
    unzip_path VARCHAR(500) NOT NULL COMMENT '符号表解压后路径',
    dsym_path VARCHAR(500) NOT NULL COMMENT '符号表解压后dsym路径',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_uuid (uuid),
    UNIQUE KEY uk_app_version_build (app_id, version, build_version),
    KEY idx_app_version (app_id, version)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='iOS符号表文件信息';


CREATE TABLE dc_rum_android_symbol_file (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    app_id INT NOT NULL COMMENT '应用ID',
    version_code VARCHAR(16) NOT NULL COMMENT '应用版本代码',
    version_name VARCHAR(30) NOT NULL COMMENT '应用版本名称',
    file_path VARCHAR(500) NOT NULL COMMENT 'Mapping文件路径',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_app_version_build (app_id, version_code, version_name),
    KEY idx_app_version (app_id, version_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Android Mapping文件信息';


CREATE TABLE IF NOT EXISTS `dc_rum_license` (
    `license_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID，导入顺序决定优先级',
    `month_rule` TINYINT NOT NULL DEFAULT 0 COMMENT '月划分规则:0=自然月,1=完整月(仅影响license自身分配)',
    `cooperation_model` TINYINT NOT NULL COMMENT '商务合作方式：0=订阅,1=买断',
    `auth_distribution` TINYINT NULL COMMENT '授权分配方式：0=月均,1=年包(若cooperation_model=1则此字段无意义)',
    `auth_mau` BIGINT NOT NULL DEFAULT 0 COMMENT '授权月活,订阅=一年内总量,买断=整期限总量',
    `auth_pv` BIGINT NOT NULL DEFAULT 0 COMMENT '授权PV数,同auth_mau作用',
    `start_time` BIGINT NOT NULL COMMENT '授权起始时间戳',
    `end_time` BIGINT NOT NULL COMMENT '授权截止时间戳',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间(导入时间)',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='RUM License表,存多条授权记录(多授权并行)';


CREATE TABLE IF NOT EXISTS `dc_rum_resource_config` (
    `app_id` INT PRIMARY KEY COMMENT '应用ID,一对一配置',
    `is_unlimited` TINYINT NOT NULL DEFAULT 1 COMMENT '1=不限制,0=自定义采集限制',
    `month_limit_mau` BIGINT NULL COMMENT '每月授权活跃设备数限制(1-99999999)',
    `day_limit_mau` BIGINT NULL  COMMENT '每日授权活跃设备数限制(1-99999999)',
    `sampling_rate` INT NULL COMMENT '应用采样率(0-100)',
    `month_limit_pv` BIGINT NULL COMMENT '每月授权PV数限制(1-99999999)',
    `day_limit_pv` BIGINT NULL COMMENT '每日授权PV数限制(1-99999999)',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='RUM资源管理配置表';


-- 2.8.6菜单sql
INSERT INTO dc_databuff.dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(17, '全局拓扑', 'topology', 0, 1, '', 1.5, 0, '/topology', '', NULL, '2022-04-07 17:18:10', '2022-04-07 17:18:10');

INSERT INTO dc_databuff.dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(2208, 'RUM资源管理', 'resource', 22, 1, '', 7, 1, '/rum/resource', '', NULL, '2022-04-07 17:20:27', '2022-04-07 17:20:27');
INSERT INTO dc_databuff.dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(2209, '用户画像', 'user', 22, 1, '', 8, 1, '/rum/user', '', NULL, '2022-04-07 17:20:27', '2022-04-07 17:20:27');

INSERT INTO dc_databuff.dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(1088, '操作审计', 'operationAudit', 1003, 1, '', 8, 1, '/sysManage/operationAudit', '', NULL, '2022-04-07 17:20:27', '2022-04-07 17:20:27');
INSERT INTO dc_databuff.dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(1092, '管理域', 'group', 1003, 1, '', 4.5, 0, '/sysManage/group', '', NULL, '2022-04-07 17:18:10', '2022-04-07 17:18:10');
INSERT INTO dc_databuff.dc_resources (id, name, url, parent_id, hidden, module_function, `order`, leaf, `path`, icon, is_license, create_time, update_time) VALUES(411, '诊断分析', 'diagnostic', 4, 1, '', 11, 1, '/appMonitor/diagnostic', '', NULL, '2022-04-07 17:20:27', '2022-04-07 17:20:27');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 17, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(3, 17, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 2208, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(3, 2208, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 2209, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(3, 2209, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 1088, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 1092, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(1, 411, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
INSERT INTO dc_databuff.dc_role_resources (role_id, resources_id, create_time, update_time) VALUES(3, 411, '2021-01-12 14:25:34', '2021-01-12 14:25:36');
-- 更新role名Admin -> Administrator
update dc_databuff.dc_role set role_name = 'Administrator' where role_name = 'Admin';

-- 新增agent_dump表
DROP TABLE IF EXISTS `dc_agent_dump`;
CREATE TABLE `dc_agent_dump` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID，自增',
  `api_key` varchar(32) NOT NULL COMMENT 'API密钥',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小',
  `path` varchar(255) DEFAULT NULL COMMENT '文件路径',
  `service_id` varchar(255) NOT NULL COMMENT '服务ID',
  `service` varchar(255) NOT NULL COMMENT '服务名称',
  `service_instance` varchar(255) NOT NULL COMMENT '服务实例名称',
  `host` varchar(255) DEFAULT NULL COMMENT '主机名称',
  `ip` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `status` tinyint DEFAULT NULL COMMENT '状态枚举',
  `progress` varchar(32) DEFAULT NULL COMMENT '更新进度',
  `operation` int DEFAULT NULL COMMENT '操作类型',
  `pack_name` varchar(255) DEFAULT NULL COMMENT '更新包名称',
  `account` varchar(32) DEFAULT NULL COMMENT '账户',
  `version` varchar(32) DEFAULT NULL COMMENT '更新版本',
  `old_version` varchar(32) DEFAULT NULL COMMENT '老版本',
  `upload_time` datetime DEFAULT NULL COMMENT '上传时间',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `msg` varchar(255) DEFAULT NULL COMMENT '失败原因等信息',
  `gid` varchar(32) DEFAULT NULL COMMENT '域ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4   COMMENT='agent dump内存任务表';

-- 删除无用定时任务
DELETE FROM `dc_databuff`.`xxl_job_info` where executor_handler = 'business-service-task';

-- 增加Dump文件清理定时任务
INSERT INTO `dc_databuff`.`xxl_job_info`(  `job_group`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `schedule_type`, `schedule_conf`, `misfire_strategy`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `trigger_status`, `trigger_last_time`, `trigger_next_time`) VALUES ( 2, 'sharding-dumpClean-task', '2022-11-02 15:45:35', '2022-11-02 15:45:35', 'fm', '', 'CRON', '50 * * * * ?', 'DO_NOTHING', 'ROUND', 'sharding-dumpClean-task', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2022-11-02 15:45:35', '', 1, 1711590645000, 1711590705000);

UPDATE dc_databuff.dc_role SET define_type='自定义' WHERE role_name ='Operator';

INSERT INTO dc_databuff.dc_sys_meta_config
( code, `describe`, params, enabled, api_key)
VALUES('GROUP_AUTH_CONFIG', '是否开启管理域权限控制：0-不启用 1-启用', '{}', 0, 'NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4');


ALTER TABLE dc_databuff.df_notify_record ADD is_single INT NULL COMMENT '钉钉，微信区分个人还是机器人通知,1个人0机器人';
ALTER TABLE dc_databuff.dc_databuff_group MODIFY COLUMN description varchar(1200) CHARACTER SET utf8mb4   NULL COMMENT '描述';


-- dc_databuff.dc_databuff_datahub_pipeline 表新增字段
ALTER TABLE dc_databuff.dc_databuff_datahub_pipeline ADD COLUMN `total_receiver` varchar(32) NULL DEFAULT "" COMMENT '接入的数据总量';

ALTER TABLE dc_databuff.dc_databuff_datahub_pipeline ADD COLUMN `last_update_time` bigint NULL DEFAULT 0 COMMENT '数据最新上报时间';

SET FOREIGN_KEY_CHECKS = 1;