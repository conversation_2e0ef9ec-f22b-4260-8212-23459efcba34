#!/bin/bash

count=0

while true; do
    result=$(docker exec -it webapp curl 127.0.0.1:18080/health/checkAll)

    if [[ $result != "true" ]]; then
        count=$((count+1))
        echo "Health check failed ($count times)"

        if [[ $count -eq 3 ]]; then
            echo "Restarting all ..."
            docker-compose -f /data/dc/dcgl/docker-compose.yml down
            docker-compose -f /data/dc/dcgl/docker-compose.yml up -d
            break
        fi
    else
        echo "Health check passed"
        break
    fi

    sleep 60
done
