package com.databuff.engine.sink;

import com.alibaba.fastjson.JSON;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.engine.resource.fun.OlapRich2SinkFunction;
import com.databuff.util.StreamLoadUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.databuff.common.constants.MetricName.RINGBUFFER_FULL;
import static com.databuff.common.constants.OlapDB.OLAP_DATABASE;

@Slf4j
public class RumOlapSinkFunction<T> extends OlapRich2SinkFunction<T> {
    private String tableName;
    private String columns;
    private String jsonPaths;
    private Class<T> valueClass;
    private StreamLoadUtil.ColumnConfig[] columnConfigs;

    public RumOlapSinkFunction(int interval, int random, long size, String fePorts, String user, String password,
                               String jobName, String option, String maxFilterRatio, String tableName) {
        super(interval, random, size, fePorts, user, password, jobName, option, maxFilterRatio);
        this.tableName = tableName;
    }

    public RumOlapSinkFunction(int interval, int random, long size, String fePorts, String user, String password,
                               String jobName, String option, String tableName, String maxFilterRatio, Class<T> valueClass,
                               StreamLoadUtil.ColumnConfig... columnConfigs) {
        super(interval, random, size, fePorts, user, password, jobName, option, maxFilterRatio);
        this.tableName = tableName;
        this.valueClass = valueClass;
        this.columnConfigs = columnConfigs;
        initializeColumnsAndJsonPaths();
    }

    private void initializeColumnsAndJsonPaths() {
//        Map.Entry<String, String> columnsAndJsonPaths = StreamLoadUtil.generateColumnsAndJsonPaths(
//                valueClass,
//                new StreamLoadUtil.ColumnConfig("uv", "user_id")
//        );
        Map.Entry<String, String> columnsAndJsonPaths;
        if (columnConfigs != null) {

            columnsAndJsonPaths = StreamLoadUtil.generateColumnsAndJsonPaths(
                    valueClass,
                    columnConfigs
            );
        } else {
            columnsAndJsonPaths = StreamLoadUtil.generateColumnsAndJsonPaths(
                    valueClass);
        }
        this.columns = columnsAndJsonPaths.getKey();
        this.jsonPaths = columnsAndJsonPaths.getValue();
    }

    @Override
    public void invoke(T value, Context context) {
        if (value == null) {
            log.warn("Received null value in RumOlapSinkFunction");
            return;
        }

        if (olapWriterEventRingBuffer == null) {
            log.error("Cannot process value: RingBuffer is null. This might indicate improper initialization or shutdown in progress");
            return;  // 直接返回而不是抛出异常
        }

        boolean success = false;
        int maxAttempts = 2;
        int attempt = 0;

        while (!success && attempt < maxAttempts) {
            try {
                success = olapWriterEventRingBuffer.tryPublishEvent((event, sequence) -> {
                    event.setDatabase(OLAP_DATABASE);
                    event.setTable(tableName);
                    event.setData(JSON.toJSONBytes(value));
                    if (columns != null) {
                        event.setColumns(columns);
                    }
                    if (jsonPaths != null) {
                        event.setJsonpaths(jsonPaths);
                    }
                });

                if (!success) {
                    OtelMetricUtil.logCounter(RINGBUFFER_FULL, getTags(), 1);
                    attempt++;
                    if (attempt < maxAttempts) {
                        Thread.sleep(100); // 添加短暂延迟
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Interrupted while trying to publish event");
                break;
            } catch (Exception e) {
                log.error("Error publishing event", e);
                break;
            }
        }

        if (!success) {
            log.error("Failed to publish event after {} attempts for value: {}", maxAttempts, value);
        }
    }

    @Override
    public void close() throws Exception {
        try {
            // 清理特定于RumOlapSinkFunction的资源
            columns = null;
            jsonPaths = null;
            valueClass = null;
            columnConfigs = null;
            tableName = null;

            // 调用父类的close
            super.close();
        } catch (Exception e) {
            log.error("Error while closing RumOlapSinkFunction", e);
            throw e;
        }
    }
}

