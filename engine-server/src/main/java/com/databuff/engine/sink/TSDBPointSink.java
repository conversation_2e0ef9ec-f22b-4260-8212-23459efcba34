package com.databuff.engine.sink;

import com.databuff.common.constants.Constant;
import com.databuff.common.tsdb.model.TSDBPoint;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;

import java.util.Collections;
import java.util.List;

@Slf4j
public class TSDBPointSink extends AbstractBatchTSDBSink<TSDBPoint> {

    public TSDBPointSink(String tsdb, String tsdbUrl, ParameterTool params) {
        super(
                tsdb,
                tsdbUrl,
                params.get("tsdb.api", Constant.MORE_DB.API),
                params.get("tsdb.user", Constant.MORE_DB.USER),
                params.get("tsdb.password", Constant.MORE_DB.PASSWORD),
                params.getInt("tsdb.queryTimeout", Constant.MORE_DB.QUERY_TIMEOUT),
                params.get("tsdb.duration", Constant.MORE_DB.DURATION),
                params.getInt("tsdb.shard", Constant.MORE_DB.SHARD),
                params.getInt("tsdb.replication", Constant.MORE_DB.REPLICATION),
                Constant.MORE_DB.INTERVAL,
                Constant.MORE_DB.JMX_ENABLED,
                null
        );
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters); // 调用父类的 open 初始化 tsdbOperateUtil
        log.info("TSDBPointSink (Extending AbstractBatchTSDBSink, No Default DB) opened.");
        // FlinkOtelMetricUtil.initOtelMetric(getRuntimeContext()); // 如果需要
    }

    /**
     * 将输入的单个 TSDBPoint 包装成列表返回。
     * 这是 AbstractBatchTSDBSink 要求必须实现的方法。
     * @param point Flink 窗口聚合后传入的单个 TSDBPoint
     * @return 包含单个 TSDBPoint 的列表，或空列表（如果输入为 null）
     */
    @Override
    protected List<TSDBPoint> processElements(TSDBPoint point) {
        if (point != null) {
            if (point.getDatabase() == null || point.getDatabase().isEmpty()) {
                // **重要:** 确保 Flink Processor 总是设置了 Database
                log.error("Received TSDBPoint without a database name! Point: {}", point);
                return Collections.emptyList(); // 不处理没有数据库名的点
            }
            return Collections.singletonList(point);
        } else {
            log.warn("Received null TSDBPoint in processElements.");
            return Collections.emptyList();
        }
    }

    // invoke 方法继承自 AbstractBatchTSDBSink，它会调用 processElements
    // 然后调用 writePoints(List<TSDBPoint> points)

    @Override
    protected void writePoints(List<TSDBPoint> points) {
        // 调用父类的 writePoints 实现（它会处理 databaseName 并调用 util 的批量写入）
        // 这里可以添加额外的日志或监控
        if (points != null && !points.isEmpty()) {
            if (points.size() > 1) {
                // 这个情况理论上不应该发生，因为 processElements 只返回大小为1的list
                log.warn("writePoints received a list with size > 1 ({}), expected 1.", points.size());
            }
            // 调用父类（或直接调用Util）进行写入
            try {
                super.writePoints(points); // 复用父类的写入逻辑
                // FlinkOtelMetricUtil.logCounter("tsdb_points_written", points.size(), tags...);
            } catch (Exception e) {
                log.error("Error in super.writePoints for {} points", points.size(), e);
                // 如果父类没处理异常，这里需要处理或再次抛出
                // 注意：如果写入失败，这里的点就丢失了，没有重试或缓冲
            }
        }
    }

    @Override
    public void close() throws Exception {
        log.info("Closing TSDBPointSink (Extending AbstractBatchTSDBSink)...");
        // 这里不需要额外操作，父类的 close 会关闭 tsdbOperateUtil
        super.close();
        log.info("TSDBPointSink closed.");
    }
}