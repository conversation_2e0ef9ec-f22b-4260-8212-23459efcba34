package com.databuff.engine.sink;

import com.databuff.common.utils.ScheduledMetricUtil;
import com.databuff.engine.resource.fun.OlapRich2SinkFunction;
import com.databuff.engine.util.FlinkOtelMetricUtil;
import org.apache.flink.configuration.Configuration;

import static com.databuff.common.constants.MetricName.RINGBUFFER_FULL;
import static com.databuff.common.constants.OlapDB.OLAP_DATABASE;
import static com.databuff.engine.constant.MetricName.TAGS_TRACE_ANALYSE_FOR_FILL;

public class DCTraceOlapSinkFunction extends OlapRich2SinkFunction<byte[]> {

    private ScheduledMetricUtil.Counter counter;
    private String table;

    public DCTraceOlapSinkFunction(int interval, int random, long size, int initSize, String fePorts, String user, String password, String jobName, String option, String maxFilterRatio, int maxPending, String table, int connectionRequestTimeout) {
        super(interval, random, size, initSize, fePorts, user, password, jobName, option, maxFilterRatio, maxPending, connectionRequestTimeout);
        this.table = table;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        FlinkOtelMetricUtil.initOtelMetric(getRuntimeContext());
        counter = new ScheduledMetricUtil.Counter(RINGBUFFER_FULL, TAGS_TRACE_ANALYSE_FOR_FILL);
    }

    @Override
    public void invoke(byte[] dcTraceArr, Context context) {
        boolean success = olapWriterEventRingBuffer.tryPublishEvent((event, sequence) -> {
            event.setDatabase(OLAP_DATABASE);
            event.setTable(table);
            event.setDcTraceArr(dcTraceArr);
        });
        if (!success) {
            counter.add(1);
        }
    }
}
