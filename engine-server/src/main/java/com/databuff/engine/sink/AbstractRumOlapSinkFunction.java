package com.databuff.engine.sink;

import com.alibaba.fastjson.JSON;
import com.databuff.common.constants.OlapDB;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.engine.resource.fun.OlapRich2SinkFunction;
import com.databuff.util.StreamLoadUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.databuff.common.constants.MetricName.RINGBUFFER_FULL;

@Slf4j
public abstract class AbstractRumOlapSinkFunction<T> extends OlapRich2SinkFunction<T> {
    protected final Map<Class<?>, StarRocksTableInfo> starRocksTableMapping = new HashMap<>();

    protected AbstractRumOlapSinkFunction(ParameterTool params, String defaultJobName) {
        super(
                params.getInt("interval", 30),
                params.getInt("random", 10),
                params.getInt("size", 100),
                params.get("olap.be"),
                "root",
                params.get("olap.be.pwd", ""),
                params.get("job.name", defaultJobName),
                "dc_rum",
                params.get("olap.be.maxFilterRatio", "1")
        );
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        initStarRocksMapping();
        log.info("{} open() success, starRocksTableMapping size={}",
                this.getClass().getSimpleName(),
                starRocksTableMapping.size());
    }

    protected abstract void initStarRocksMapping();

    protected void publishEventToStarRocks(Object value, Class<?> entityClass, Context ctx) {
        if (value == null) return;

        StarRocksTableInfo tableInfo = starRocksTableMapping.get(entityClass);
        if (tableInfo == null) {
            log.warn("No tableInfo found for class={}", entityClass.getName());
            return;
        }
//        else {
//            log.info("StarRocksTableInfo :{}",tableInfo);
//        }

        publishToRingBuffer(value, tableInfo);
    }

    private void publishToRingBuffer(Object value, StarRocksTableInfo tableInfo) {
        boolean success = false;
        int attempt = 0;
        int maxRetries = 2;

        while (!success && attempt < maxRetries) {
            try {
                success = olapWriterEventRingBuffer.tryPublishEvent((event, seq) -> {
                    event.setDatabase(OlapDB.OLAP_DATABASE);
                    event.setTable(tableInfo.getTableName());
                    event.setData(JSON.toJSONBytes(value));
                    if (tableInfo.getColumns() != null) {
                        event.setColumns(tableInfo.getColumns());
                    }
                    if (tableInfo.getJsonPaths() != null) {
                        event.setJsonpaths(tableInfo.getJsonPaths());
                    }
                });

                if (!success) {
                    OtelMetricUtil.logCounter(RINGBUFFER_FULL, getTags(), 1);
                    attempt++;
                    if (attempt < maxRetries) {
                        Thread.sleep(50L);
                    }
                }
            } catch (Exception e) {
                log.error("publishEvent error, table={}, value={}",
                        tableInfo.getTableName(), value, e);
                break;
            }
        }

        if (!success) {
            log.error("Failed to publish event after {} attempts => table={} value={}",
                    maxRetries, tableInfo.getTableName(), value);
        }
    }

    @Override
    public void close() throws Exception {
        super.close();
        starRocksTableMapping.clear();
        log.info("{} closed successfully.", this.getClass().getSimpleName());
    }

    /**
     * 内部类: 保存 “StarRocks表” + “生成好的 columns/jsonPaths”
     */
    @Data
    protected static class StarRocksTableInfo {
        private final String tableName;
        private final String columns;
        private final String jsonPaths;

        public StarRocksTableInfo(
                String tableName,
                Class<?> entityClass,
                List<StreamLoadUtil.ColumnConfig> columnConfigs
        ) {
            this.tableName = tableName;
            Map.Entry<String, String> entry;
            if (columnConfigs != null) {
                entry = StreamLoadUtil.generateColumnsAndJsonPaths(
                        entityClass,
                        columnConfigs.toArray(new StreamLoadUtil.ColumnConfig[0])
                );
            } else {
                entry = StreamLoadUtil.generateColumnsAndJsonPaths(entityClass);
            }
            this.columns = entry.getKey();     // e.g. "appId, pageId, uv=hll_hash(userId), ..."
            this.jsonPaths = entry.getValue();   // e.g. ["$.appId","$.pageId","$.userId",...]
        }
    }
}
