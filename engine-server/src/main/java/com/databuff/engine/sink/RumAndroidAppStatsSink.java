package com.databuff.engine.sink;

import com.databuff.common.constants.Constant;
import com.databuff.common.constants.TSDBIndex;
import com.databuff.common.tsdb.model.TSDBPoint;
import com.databuff.entity.rum.moredb.RumAndroidAppStats;
import org.apache.flink.api.java.utils.ParameterTool;

import java.util.HashMap;
import java.util.Map;

import static com.databuff.common.utils.PointUtil.DEFAULT_FIELD_TYPES;

public class RumAndroidAppStatsSink extends AbstractTSDBSink<RumAndroidAppStats> {


    public RumAndroidAppStatsSink(String tsdb, String tsdbUrl, ParameterTool params) {
        super(
                tsdb,
                tsdbUrl,
                params.get("tsdb.api", Constant.MORE_DB.API),
                params.get("tsdb.user", Constant.MORE_DB.USER),
                params.get("tsdb.password", Constant.MORE_DB.PASSWORD),
                params.getInt("tsdb.queryTimeout", Constant.MORE_DB.QUERY_TIMEOUT),
                params.get("tsdb.duration", Constant.MORE_DB.DURATION),
                params.getInt("tsdb.shard", Constant.MORE_DB.SHARD),
                params.getInt("tsdb.replication", Constant.MORE_DB.REPLICATION),
                Constant.MORE_DB.INTERVAL,
                Constant.MORE_DB.JMX_ENABLED,
                TSDBIndex.TSDB_RUM_METRIC_DATABASE_NAME
        );
    }

    @Override
    protected TSDBPoint processElement(RumAndroidAppStats stats) {
        if (stats == null || stats.getDfApiKey() == null) {
            return null;
        }

        String dbName = stats.getDfApiKey() + "_" + TSDBIndex.TSDB_RUM_METRIC_DATABASE_NAME;

        Map<String, String> tags = new HashMap<>();
        tags.put("appId", String.valueOf(stats.getAppId()));
        tags.put("appName", stats.getAppName());

        Map<String, Object> fields = new HashMap<>();
        fields.put("launchCount", stats.getLaunchCount());
        fields.put("pageCount", stats.getPageCount());
        fields.put("actionCount", stats.getActionCount());
        fields.put("requestCount", stats.getRequestCount());
        fields.put("anrCount", stats.getAnrCount());
        fields.put("crashCount", stats.getCrashCount());
        fields.put("launchDuration", stats.getLaunchDuration());
        fields.put("pageDuration", stats.getPageDuration());
        fields.put("actionDuration", stats.getActionDuration());
        fields.put("requestDuration", stats.getRequestDuration());

        return new TSDBPoint(dbName, TSDBIndex.TSDB_METRIC_RUM_ANDROID_APP_STATS, stats.getStartTime(), tags, fields, DEFAULT_FIELD_TYPES);
    }
}
