package com.databuff.engine.sink;

import com.databuff.engine.metric.model.RumSpanAction;
import com.databuff.engine.metric.model.RumSpanPage;
import com.databuff.engine.metric.model.RumWebRequestData;
import com.databuff.entity.rum.starrocks.*;
import com.databuff.entity.rum.web.IRumData;
import com.databuff.util.StreamLoadUtil.ColumnConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;

import java.util.Collections;

import static com.databuff.common.constants.OlapDB.*;
import static com.databuff.engine.constant.MetricName.JOB_NAME_RUM_ANALYSE;

/**
 * 使用一个 Disruptor / OlapWriterHandler 的 SinkFunction, 通过 instanceof 判断, 分别写入到不同 StarRocks 表。
 * 支持对 UV 列使用 "hll_hash" 等特殊操作，避免"uv, uv=hll_hash(...)"重复列问题。
 */
@Slf4j
public class RumWebOlapSinkFunction extends AbstractRumOlapSinkFunction<IRumData> {

    public RumWebOlapSinkFunction(ParameterTool params) {
        super(params, JOB_NAME_RUM_ANALYSE);
    }

    /**
     * 初始化表映射：
     *   tableName => (entityClass + columnConfigs)
     *   这些 “实体类” 是：RumAction, RumActionUV, RumPage, RumPageUV, RumPageSpan, ...
     *   UV 列需要 "uv=hll_hash(user_id)" => 构造 ColumnConfig("uv","user_id")即可
     */
    @Override
    protected void initStarRocksMapping() {
        // ---------- 1. Page 相关 ----------
        // RumPage => DC_RUM_PAGE
        starRocksTableMapping.put(
                RumPage.class,
                new StarRocksTableInfo(
                        DC_RUM_PAGE,
                        RumPage.class,
                        null
                )
        );
        // RumPageUV => DC_RUM_PAGE_UV (uv列)
        starRocksTableMapping.put(
                RumPageUV.class,
                new StarRocksTableInfo(
                        DC_RUM_PAGE_UV,
                        RumPageUV.class,
                        Collections.singletonList(new ColumnConfig("uv","user_id"))
                )
        );
        // RumPageSpan => DC_RUM_PAGE_SPAN
        starRocksTableMapping.put(
                RumPageSpan.class,
                new StarRocksTableInfo(
                        DC_RUM_PAGE_SPAN,
                        RumPageSpan.class,
                        null
                )
        );

        // ---------- 2. Action 相关 ----------
        // RumAction => DC_RUM_ACTION
        starRocksTableMapping.put(
                RumAction.class,
                new StarRocksTableInfo(
                        DC_RUM_ACTION,
                        RumAction.class,
                        null
                )
        );
        // RumActionUV => DC_RUM_ACTION_UV (uv列)
        starRocksTableMapping.put(
                RumActionUV.class,
                new StarRocksTableInfo(
                        DC_RUM_ACTION_UV,
                        RumActionUV.class,
                        Collections.singletonList(new ColumnConfig("uv", "user_id"))
                )
        );
        // RumActionSpan => DC_RUM_ACTION_SPAN
        starRocksTableMapping.put(
                RumActionSpan.class,
                new StarRocksTableInfo(
                        DC_RUM_ACTION_SPAN,
                        RumActionSpan.class,
                        null
                )
        );

        // ---------- 3. Error logs ----------
        starRocksTableMapping.put(
                RumErrorLogs.class,
                new StarRocksTableInfo(
                        DC_RUM_ERROR_LOGS,
                        RumErrorLogs.class,
                        null
                )
        );
        starRocksTableMapping.put(
                RumErrorLogsUV.class,
                new StarRocksTableInfo(
                        DC_RUM_ERROR_LOGS_UV,
                        RumErrorLogsUV.class,
                        Collections.singletonList(new ColumnConfig("uv","user_id"))
                )
        );

        // ---------- 4. WebRequest 相关 ----------
        starRocksTableMapping.put(
                RumWebRequestSpan.class,
                new StarRocksTableInfo(
                        DC_RUM_WEB_REQUEST_SPAN,
                        RumWebRequestSpan.class,
                        null
                )
        );
        starRocksTableMapping.put(
                RumWebRequestUV.class,
                new StarRocksTableInfo(
                        DC_RUM_WEB_REQUEST_UV,
                        RumWebRequestUV.class,
                        Collections.singletonList(new ColumnConfig("uv","user_id"))
                )
        );

        // ---------- 5. WebSession ----------
        starRocksTableMapping.put(
                RumWebSession.class,
                new StarRocksTableInfo(
                        DC_RUM_WEB_SESSION,
                        RumWebSession.class,
                        null
                )
        );

    }

    @Override
    public void invoke(IRumData value, SinkFunction.Context context) throws Exception {
        if (value == null) {
            return;
        }
        try {
            // 根据实际类型进行分发
            if (value instanceof RumPage) {
                publishEventToStarRocks(value, RumPage.class, context);
            } else if (value instanceof RumPageUV) {
                publishEventToStarRocks(value, RumPageUV.class, context);
            } else if (value instanceof RumSpanPage) {
                handleRumSpanPage((RumSpanPage) value, context);
            } else if (value instanceof RumSpanAction) {
                handleRumSpanAction((RumSpanAction) value, context);
            } else if (value instanceof RumErrorLogs) {
                handleRumErrorLogs((RumErrorLogs) value, context);
            } else if (value instanceof RumWebRequestData) {
                handleRumWebRequest((RumWebRequestData) value, context);
            }else if (value instanceof RumWebSession) {
                handleRumWebSession((RumWebSession) value, context);
            }
            // ... 还可以补充其他类型

        } catch (Exception e) {
            log.error("RumWebOlapSinkFunction invoke error, value={}", value, e);
            throw e;
        }
    }

    // =============== 各种处理方法 ===============


    /**
     * RumSpanPage
     */
    private void handleRumSpanPage(RumSpanPage pageSpan, Context ctx) {
        // 如果 isMetricData => Combined, 否则 => RumPageSpan
        if (!pageSpan.isMetricData() && pageSpan.getSpanId() != null) {
            RumPageSpan rps = pageSpan.toRumPageSpan();
            if (rps != null) {
                publishEventToStarRocks(rps, RumPageSpan.class, ctx);
            }
        }
    }

    /**
     * RumSpanAction
     * isMetricData => (RumAction, RumActionUV)，否则 => RumActionSpan
     */
    private void handleRumSpanAction(RumSpanAction action, Context ctx) {
        if (action.getActionId() == null) {
            return;
        }
        if (action.isMetricData()) {
            RumAction ra = action.toRumAction();
            if (ra != null && ra.getActionId() != null) {
                publishEventToStarRocks(ra, RumAction.class, ctx);
            }
            RumActionUV uv = action.toRumActionUV();
            if (uv != null && uv.getAppId() != null) {
                publishEventToStarRocks(uv, RumActionUV.class, ctx);
            }
        } else {
            RumActionSpan ras = action.toRumActionSpan();
            if (ras != null) {
                publishEventToStarRocks(ras, RumActionSpan.class, ctx);
            }
        }
    }

    /**
     * RumErrorLogs => (RumErrorLogs, RumErrorLogsUV)
     */
    private void handleRumErrorLogs(RumErrorLogs logs, Context ctx) {
        publishEventToStarRocks(logs, RumErrorLogs.class, ctx);
        RumErrorLogsUV uv = logs.toRumErrorLogsUV();
        if (uv != null) {
            publishEventToStarRocks(uv, RumErrorLogsUV.class, ctx);
        }
    }

    /**
     * RumWebRequestData => (RumWebRequestSpan, RumWebRequestUV)
     */
    private void handleRumWebRequest(RumWebRequestData webReq, Context ctx) {
        RumWebRequestSpan span = webReq.toRumWebRequestSpan();
        if (span != null && span.getStartTime() != null) {
            publishEventToStarRocks(span, RumWebRequestSpan.class, ctx);
        }
        RumWebRequestUV uv = webReq.toRumWebRequestUV();
        if (uv != null && uv.getStartTime() != null) {
            publishEventToStarRocks(uv, RumWebRequestUV.class, ctx);
        }
    }

    /**
     * RumWebSession
     */
    private void handleRumWebSession(RumWebSession webSession, Context ctx) {
        if (webSession != null && webSession.getHour() != null) {
            publishEventToStarRocks(webSession, RumWebSession.class, ctx);
        }
    }



}
