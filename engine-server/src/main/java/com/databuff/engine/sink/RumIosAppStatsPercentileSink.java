package com.databuff.engine.sink;

import com.databuff.common.constants.Constant;
import com.databuff.common.constants.TSDBIndex;
import com.databuff.common.tsdb.model.TSDBPoint;
import com.databuff.engine.util.TraceUtil;
import com.databuff.entity.rum.moredb.RumIosAppStatsPercentile;
import org.apache.flink.api.java.utils.ParameterTool;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.databuff.common.utils.PointUtil.DEFAULT_FIELD_TYPES;

public class RumIosAppStatsPercentileSink extends AbstractBatchTSDBSink<List<RumIosAppStatsPercentile>> {

    public RumIosAppStatsPercentileSink(String tsdb, String tsdbUrl, ParameterTool params) {
        super(
                tsdb,
                tsdbUrl,
                params.get("tsdb.api", Constant.MORE_DB.API),
                params.get("tsdb.user", Constant.MORE_DB.USER),
                params.get("tsdb.password", Constant.MORE_DB.PASSWORD),
                params.getInt("tsdb.queryTimeout", Constant.MORE_DB.QUERY_TIMEOUT),
                params.get("tsdb.duration", Constant.MORE_DB.DURATION),
                params.getInt("tsdb.shard", Constant.MORE_DB.SHARD),
                params.getInt("tsdb.replication", Constant.MORE_DB.REPLICATION),
                60,
                false,
                TSDBIndex.TSDB_RUM_METRIC_DATABASE_NAME
        );
    }

    @Override
    protected List<TSDBPoint> processElements(List<RumIosAppStatsPercentile> percentiles) {
        if (percentiles == null || percentiles.isEmpty()) {
            return null;
        }

        List<TSDBPoint> points = new ArrayList<>();
        for (RumIosAppStatsPercentile statsPercentile : percentiles) {
            if (statsPercentile.getDfApiKey() == null) {
                continue;
            }

            String dbName = statsPercentile.getDfApiKey() + "_" + TSDBIndex.TSDB_RUM_METRIC_DATABASE_NAME;

            Map<String, String> tags = new HashMap<>();
            tags.put("appId", String.valueOf(statsPercentile.getAppId()));
            tags.put("appName", statsPercentile.getAppName());
            tags.put("type", statsPercentile.getType());

            Map<String, Object> fields = new HashMap<>();
            fields.put("metric", statsPercentile.getMetric());
            fields.put("histogramCount", statsPercentile.getHistogramCount());
            fields.put("histogramMin", statsPercentile.getHistogramMin());
            fields.put("histogramMax", statsPercentile.getHistogramMax());
            fields.put("histogramSum", statsPercentile.getHistogramSum());

            Map<Integer, Long> percentileAggMap = statsPercentile.getPercentileAgg();
            TraceUtil.addPercentileField(fields, percentileAggMap, statsPercentile.getHistogramCount(), statsPercentile.getHistogramMax());

            points.add(new TSDBPoint(dbName, TSDBIndex.TSDB_METRIC_RUM_IOS_APP_STATS_PERCENTILE, statsPercentile.getStartTime(), tags, fields, DEFAULT_FIELD_TYPES));
        }

        return points;
    }
}

