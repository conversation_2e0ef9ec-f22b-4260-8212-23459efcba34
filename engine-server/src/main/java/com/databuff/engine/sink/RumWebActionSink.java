package com.databuff.engine.sink;

import com.databuff.common.constants.Constant;
import com.databuff.common.constants.TSDBIndex;
import com.databuff.common.tsdb.model.TSDBPoint;
import com.databuff.common.utils.PointUtil;
import com.databuff.engine.metric.model.RumSpanAction;
import com.databuff.entity.rum.moredb.RumWebAction;
import org.apache.flink.api.java.utils.ParameterTool;

import java.util.HashMap;
import java.util.Map;

public class RumWebActionSink extends AbstractTSDBSink<RumSpanAction> {


    public RumWebActionSink(String tsdb, String tsdbUrl, ParameterTool params) {
        super(
                tsdb,
                tsdbUrl,
                params.get("tsdb.api", Constant.MORE_DB.API),
                params.get("tsdb.user", Constant.MORE_DB.USER),
                params.get("tsdb.password", Constant.MORE_DB.PASSWORD),
                params.getInt("tsdb.queryTimeout", Constant.MORE_DB.QUERY_TIMEOUT),
                params.get("tsdb.duration", Constant.MORE_DB.DURATION),
                params.getInt("tsdb.shard", Constant.MORE_DB.SHARD),
                params.getInt("tsdb.replication", Constant.MORE_DB.REPLICATION),
                60,
                false,
                TSDBIndex.TSDB_RUM_METRIC_DATABASE_NAME
        );
    }

    @Override
    protected TSDBPoint processElement(RumSpanAction rumSpanAction) {
        if (rumSpanAction == null){
            return null;
        }
        RumWebAction action = rumSpanAction.toRumWebAction();
        String apikey = action.getDfApiKey();
        if (apikey == null) {
            return null;
        }

        String dbName = apikey + "_" + TSDBIndex.TSDB_RUM_METRIC_DATABASE_NAME;

        Map<String, String> tags = new HashMap<>();
        // Tags (索引)
        tags.put("appId", action.getAppId());
        tags.put("appName", action.getAppName());
        tags.put("actionName", action.getActionName());
        tags.put("actionDurationTier", action.getActionDurationTier());
        tags.put("actionRequestDurationTier", action.getActionRequestDurationTier());
        tags.put("actionServiceDurationTier", action.getActionServiceDurationTier());

        Map<String, Object> fields = new HashMap<>();
        // Fields (度量值)
        fields.put("actionCount", action.getActionCount());
        fields.put("actionDuration", action.getActionDuration());
        fields.put("actionServiceDuration", action.getActionServiceDuration());
        fields.put("actionRequestDuration", action.getActionRequestDuration());
        fields.put("ajaxRequestCount", action.getAjaxRequestCount());
        fields.put("ajaxErrorCount", action.getAjaxErrorCount());
        fields.put("serverResponseCount", action.getServerResponseCount());
        fields.put("successfulActionCount", action.getSuccessfulActionCount());

        return new TSDBPoint(dbName, TSDBIndex.TSDB_METRIC_RUM_WEB_ACTION, action.getStartTime(), tags, fields, PointUtil.DEFAULT_FIELD_TYPES);
    }
}
