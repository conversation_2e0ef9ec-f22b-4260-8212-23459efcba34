package com.databuff.engine.trace.fun;

import org.apache.flink.table.functions.AggregateFunction;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @author:TianMing
 * @date: 2022/7/27
 * @time: 16:14
 */
public class CollectListSetFun extends AggregateFunction<String[], List<String>> {
    public void accumulate(List acc,List<String> columns){
        acc.addAll(columns);
    }

    public void retract(List acc,List<String> columns){
        acc.removeAll(columns);
    }

    @Override
    public String[] getValue(List list) {
        Set set= new HashSet(list);
        return (String[]) set.toArray(new String[0]);
    }

    @Override
    public List createAccumulator() {
        List list = new ArrayList();
        return list;
    }


    public void resetAccumulator(List acc){
        acc.clear();
    }

}
