package com.databuff.engine.trace;

import com.databuff.common.constants.Constant;
import com.databuff.common.constants.TSDBIndex;
import com.databuff.common.model.DCTrace;
import com.databuff.common.tsdb.model.TSDBPoint;
import com.databuff.common.utils.BytesUtil;
import com.databuff.common.utils.DCTraceUtil;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.engine.metric.BoundedOutOfOrdernessWatermarksOnEventTime;
import com.databuff.engine.npm.TSDBSink;
import com.databuff.engine.sink.TraceOlapSinkFunction;
import com.databuff.engine.trace.fun.FillAndSplitDCSpanFunction;
import com.databuff.engine.trace.model.ServiceFlow;
import com.databuff.engine.trace.model.ServiceFlowAggregate;
import com.databuff.engine.util.FlinkEnv;
import com.databuff.engine.util.ServiceFlowUtil;
import org.apache.flink.api.common.eventtime.SerializableTimestampAssigner;
import org.apache.flink.api.common.eventtime.WatermarkGeneratorSupplier;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.AggregateFunction;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.api.windowing.assigners.EventTimeSessionWindows;
import org.apache.flink.streaming.api.windowing.assigners.TumblingEventTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;

import static com.databuff.common.constants.OlapDB.DC_SPAN;
import static com.databuff.engine.constant.MetricName.JOB_NAME_TRACE_ANALYSE;

public class AnalyseTrace {

    private static final Logger LOGGER = LoggerFactory.getLogger(AnalyseTrace.class);

    public static void main(String[] args) {
        final ParameterTool params = ParameterTool.fromArgs(args);
        String jobName = params.get("job.name", JOB_NAME_TRACE_ANALYSE);
        String tsdbUrl = params.get("tsdb.url", Constant.MORE_DB.URL);
        String tsdb = params.get("tsdb.db", Constant.TS_DB.MOREDB);
        final String tsdbApi = params.get("tsdb.api", Constant.MORE_DB.API);
        final String tsdbUser = params.get("tsdb.user", Constant.MORE_DB.USER);
        final String tsdbPassword = params.get("tsdb.password", Constant.MORE_DB.PASSWORD);
        final int queryTimeout = params.getInt("tsdb.queryTimeout", Constant.MORE_DB.QUERY_TIMEOUT);
        final String duration = params.get("tsdb.duration", Constant.MORE_DB.DURATION);
        final int shard = params.getInt("tsdb.shard", Constant.MORE_DB.SHARD);
        final int replication = params.getInt("tsdb.replication", Constant.MORE_DB.REPLICATION);
        final boolean jmxEnabled = Constant.MORE_DB.JMX_ENABLED;
        StreamExecutionEnvironment executionEnvironment = FlinkEnv.getFlinkStreamEnv(params);
        int parallelism = params.getInt("parallelism", 8);
        int fillParallelism = params.getInt("fillParallelism", 8);
        int sinkParallelism = params.getInt("sinkParallelism", 8);
        executionEnvironment.setParallelism(parallelism);
        String traceTopic = params.get("consumer.topic", Constant.Kafka.TRACE_TOPIC);
        int interval = params.getInt("interval", 10);
        int random = params.getInt("random", 2);
        int size = params.getInt("size", 100);
        int initSize = params.getInt("initSize", 100);
        int maxPending = params.getInt("maxPending", 5);
        int connectionRequestTimeout = params.getInt("connectionRequestTimeout", 20000);
        String olapBe = params.get("olap.be");
        String password = params.get("olap.be.pwd", "");
        String maxFilterRatio = params.get("olap.be.maxFilterRatio", "1");
        long forBoundedOutOfOrdernessSeconds = params.getInt("forBoundedOutOfOrdernessSeconds", 10);
        boolean logLastData = params.getBoolean("logLastData", false);

        // 从kafka读取数据
        KafkaSource<byte[]> source = KafkaSource.<byte[]>builder()
                .setBootstrapServers(params.get("bootstrap.servers", Constant.Kafka.CONSUMER))
                .setTopics(traceTopic)
                .setGroupId(Constant.Kafka.TRACE_GROUP_ID)
                .setStartingOffsets(OffsetsInitializer.latest())
                .setValueOnlyDeserializer(new DeserializationSchema<byte[]>() {

                    @Override
                    public TypeInformation<byte[]> getProducedType() {
                        return TypeInformation.of(byte[].class);
                    }

                    @Override
                    public byte[] deserialize(byte[] bytes) {
                        return bytes;
                    }

                    @Override
                    public boolean isEndOfStream(byte[] bytes) {
                        return false;
                    }
                })
                .build();

        DataStream<byte[]> traceDataStream = executionEnvironment.fromSource(source, WatermarkStrategy.noWatermarks(), "Kafka Source")
                .setParallelism(parallelism)
                .slotSharingGroup(jobName)
                .name("kafka");

        WatermarkStrategy<byte[]> watermarkStrategy = WatermarkStrategy
                // 使用自定义水位线策略，允许乱序时间为forBoundedOutOfOrdernessSeconds秒，15秒无新数据进入窗口，则间隔200毫秒触发一次onPeriodicEmit函数
                .forGenerator((WatermarkGeneratorSupplier<byte[]>) context -> new BoundedOutOfOrdernessWatermarksOnEventTime(Duration.ofSeconds(forBoundedOutOfOrdernessSeconds), Duration.ofSeconds(15), 200L))
                // 设置窗口最大空闲时间，允许60s等待其他分区触发计算，如果还没有触发，直接计算当前分区
                .withIdleness(Duration.ofSeconds(60))
                // 提取数据中的时间戳作为EventTime
                .withTimestampAssigner((SerializableTimestampAssigner<byte[]>) (element, recordTimestamp) -> BytesUtil.bytesToLong(element));

        OutputTag<byte[]> late = new OutputTag<byte[]>("late") {
        };
        OutputTag<byte[]> flow = new OutputTag<byte[]>("flow") {
        };

        // 处理主数据流
        SingleOutputStreamOperator<byte[]> dcTraceDataStream = traceDataStream.assignTimestampsAndWatermarks(watermarkStrategy)
                .keyBy((KeySelector<byte[], String>) dcTrace -> DCTraceUtil.getTraceId(dcTrace))
                .window(EventTimeSessionWindows.withGap(Time.minutes(1)))
                .sideOutputLateData(late)
                .trigger(new SessionTrigger(Time.minutes(3).toMilliseconds()))
                .allowedLateness(Time.minutes(0))
                .process(new FillAndSplitDCSpanFunction(flow))
                .setParallelism(fillParallelism)
                .slotSharingGroup(jobName)
                .name("fillAndSplitDCSpan");

        // 写入 dc_span
        dcTraceDataStream.addSink(new TraceOlapSinkFunction(interval, random, size, initSize, olapBe, "root", password, jobName, "dc_span", maxFilterRatio, maxPending, DC_SPAN, connectionRequestTimeout, false))
                .slotSharingGroup(jobName)
                .setParallelism(sinkParallelism)
                .name("traceOlapSink");

        WatermarkStrategy<byte[]> flowWatermarkStrategy = WatermarkStrategy
                // 使用自定义水位线策略，允许乱序时间为forBoundedOutOfOrdernessSeconds秒，15秒无新数据进入窗口，则间隔200毫秒触发一次onPeriodicEmit函数
                .forGenerator((WatermarkGeneratorSupplier<byte[]>) context -> new BoundedOutOfOrdernessWatermarksOnEventTime(Duration.ofSeconds(forBoundedOutOfOrdernessSeconds), Duration.ofSeconds(15), 200L))
                // 设置窗口最大空闲时间，允许60s等待其他分区触发计算，如果还没有触发，直接计算当前分区
                .withIdleness(Duration.ofSeconds(60))
                // 提取数据中的时间戳作为EventTime
                .withTimestampAssigner((SerializableTimestampAssigner<byte[]>) (element, recordTimestamp) -> BytesUtil.bytesToLong(element));

        //构建service.flow指标聚合
        dcTraceDataStream.getSideOutput(flow)
                .assignTimestampsAndWatermarks(flowWatermarkStrategy)
                .keyBy((KeySelector<byte[], String>) value -> ServiceFlowUtil.getKeyBy(value))
                .window(TumblingEventTimeWindows.of(Time.minutes(1)))
                .allowedLateness(Time.minutes(0))
                .aggregate(new AggregateFunction<byte[], ServiceFlowAggregate, TSDBPoint>() {

                    @Override
                    public ServiceFlowAggregate createAccumulator() {
                        return new ServiceFlowAggregate();
                    }

                    @Override
                    public ServiceFlowAggregate add(byte[] value, ServiceFlowAggregate accumulator) {
                        ServiceFlow serviceFlow = ServiceFlowUtil.deserialize(value);
                        return accumulator.add(serviceFlow);
                    }

                    @Override
                    public TSDBPoint getResult(ServiceFlowAggregate accumulator) {
                        return accumulator.getTSDBPoint();
                    }

                    @Override
                    public ServiceFlowAggregate merge(ServiceFlowAggregate a, ServiceFlowAggregate b) {
                        return a.merge(b);
                    }
                })
                .addSink(new TSDBSink(tsdb, tsdbUrl, tsdbApi, tsdbUser, tsdbPassword, queryTimeout, duration, shard, replication, interval, jmxEnabled, Constant.DF_API_KEY_VALUE + "_" + TSDBIndex.TSDB_APM_METRIC_DATABASE_NAME));

        // 处理迟到数据
        dcTraceDataStream.getSideOutput(late)
                .process(new ProcessFunction<byte[], byte[]>() {
                    @Override
                    public void processElement(byte[] bytes, ProcessFunction<byte[], byte[]>.Context context, Collector<byte[]> collector) throws Exception {
                        OtelMetricUtil.logCounter("trace_late_data", 1);
                        if (logLastData) {
                            LOGGER.warn("trace late data: {}", DCTraceUtil.getTraceId(bytes));
                        }
                        DCTrace dcTrace = DCTraceUtil.deserialize(bytes);
                        for (byte[] span : dcTrace.getSpans()) {
                            collector.collect(span);
                        }
                    }
                })
                .addSink(new TraceOlapSinkFunction(interval, random, size, initSize, olapBe, "root", password, jobName, "dc_span", maxFilterRatio, maxPending, DC_SPAN, connectionRequestTimeout, false))
                .slotSharingGroup(jobName)
                .setParallelism(1)
                .name("getSideOutput");

        try {
            executionEnvironment.execute(jobName);
        } catch (Exception e) {
            LOGGER.error("启动SpanAnalyse error:{}", e);
        }
    }

}
