package com.databuff.engine.trace.model;

import com.databuff.common.tsdb.model.TSDBPoint;
import lombok.Data;

@Data
public class ServiceFlowAggregate {

    private ServiceFlow local;

    public ServiceFlowAggregate add(ServiceFlow serviceFlow) {
        if (local == null) {
            local = serviceFlow;
        } else {
            local.cnt += serviceFlow.cnt;
            local.slow += serviceFlow.slow;
            local.error += serviceFlow.error;
            local.srcCall += serviceFlow.srcCall;
            local.sumDuration += serviceFlow.sumDuration;
        }
        return this;
    }

    public ServiceFlowAggregate merge(ServiceFlowAggregate b) {
        if (b.local == null) {
            return this;
        }
        if (local == null) {
            local = b.local;
            return this;
        }
        local.cnt += b.local.cnt;
        local.error += b.local.error;
        local.srcCall += b.local.srcCall;
        local.sumDuration += b.local.sumDuration;
        return this;
    }

    public TSDBPoint getTSDBPoint() {
        if (local == null) {
            return null;
        }
        return local.getTSDBPoint();
    }
}
