package com.databuff.engine.trace.fun;

import com.alibaba.fastjson.JSON;
import com.databuff.common.model.DCSpan;
import com.databuff.common.model.DCTrace;
import com.databuff.common.utils.DCTraceUtil;
import com.databuff.common.utils.ScheduledMetricUtil;
import com.databuff.engine.trace.model.ServiceFlow;
import com.databuff.engine.util.FillPathAndRelationUtil;
import com.databuff.engine.util.FlinkOtelMetricUtil;
import com.databuff.engine.util.ServiceFlowUtil;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Vector;
import java.util.concurrent.ConcurrentHashMap;

import static com.databuff.engine.constant.MetricName.*;

public class FillAndSplitDCTraceFunction extends ProcessWindowFunction<byte[], byte[], String, TimeWindow> {

    public ScheduledMetricUtil.Timer kafkaDelay;
    public ScheduledMetricUtil.Timer dataDelay;
    public ScheduledMetricUtil.Counter kafkaConsume;
    public ScheduledMetricUtil.Counter traceNoRoot;
    private OutputTag<byte[]> flowOutPutTag;

    public FillAndSplitDCTraceFunction(OutputTag<byte[]> flowOutPutTag) {
        this.flowOutPutTag = flowOutPutTag;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        FlinkOtelMetricUtil.initOtelMetric(getRuntimeContext());
        kafkaDelay = new ScheduledMetricUtil.Timer(KAFKA_DELAY, TAGS_TRACE_ANALYSE_FOR_FILL);
        dataDelay = new ScheduledMetricUtil.Timer(DATA_DELAY, TAGS_TRACE_ANALYSE_FOR_FILL);
        kafkaConsume = new ScheduledMetricUtil.Counter(KAFKA_CONSUME, TAGS_TRACE_ANALYSE_FOR_FILL);
        traceNoRoot = new ScheduledMetricUtil.Counter(TRACE_NO_ROOT, TAGS_TRACE_ANALYSE);
    }

    @Override
    public void process(String s, ProcessWindowFunction<byte[], byte[], String, TimeWindow>.Context context, Iterable<byte[]> elements, Collector<byte[]> collector) throws Exception {
        Map<String, List<DCSpan>> traceIdAndSpans = new ConcurrentHashMap<>();
        Map<String, DCTrace> traceIds = new ConcurrentHashMap<>();
        List<ServiceFlow> serviceFlows = new Vector<>();

        FillPathAndRelationUtil.initDCTraceFromBytes(elements, traceIdAndSpans, traceIds, kafkaDelay, dataDelay, kafkaConsume);
        FillPathAndRelationUtil.fill(traceIdAndSpans, serviceFlows, traceNoRoot);

        Vector<byte[]> serviceFlowBytes = new Vector<>();
        serviceFlows.parallelStream().forEach(serviceFlow -> {
            serviceFlow.generateSeriesKey();
            serviceFlowBytes.add(ServiceFlowUtil.serialize(serviceFlow));
        });
        serviceFlowBytes.forEach(bytes -> context.output(flowOutPutTag, bytes));

        Vector<byte[]> dcTraceList = new Vector<>();
        traceIdAndSpans.entrySet().parallelStream().forEach(entry -> {
            DCTrace dcTrace = traceIds.get(entry.getKey());
            int length = 24;
            length += entry.getKey().length();
            List<byte[]> dcSpanList = new ArrayList<>();
            for (DCSpan dcSpan : entry.getValue()) {
                byte[] data = JSON.toJSONBytes(dcSpan);
                dcSpanList.add(data);
                length += 4;
                length += data.length;
            }
            dcTrace.setSpans(dcSpanList);
            dcTraceList.add(DCTraceUtil.serialize(dcTrace, length));
        });
        dcTraceList.stream().forEach(bytes -> collector.collect(bytes));
    }
}
