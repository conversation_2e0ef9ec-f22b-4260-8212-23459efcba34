package com.databuff.engine.metric.model;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class RumWebRequestPercentileAccumulator {
    private String apiKey;
    private long windowStartTime;
    private Integer appId;
    private String appName;
    private Integer requestType;
    private String processedHttpUrl;
    private String domain;
    private String processedPath;
    private String isp;
    private String statusCode;
    private String service;
    private Map<String, PercentileData> metricsData = new HashMap<>();

    public void add(RumWebRequestData data) {
        addMetric("duration", data.getDuration(), 1);
//        addMetric("serverTime", data.getServerTime(), 1);
//        addMetric("networkTime", data.getNetworkTime(), 1);
    }

    private void addMetric(String metricName, Long metricValue, long count) {
        if (metricValue == null) return;
        metricsData.computeIfAbsent(metricName, k -> new PercentileData())
                .add(metricValue, count);
    }

    public void merge(RumWebRequestPercentileAccumulator other) {
        for (Map.Entry<String, PercentileData> entry : other.metricsData.entrySet()) {
            metricsData.merge(entry.getKey(), entry.getValue(), PercentileData::merge);
        }
        if (this.windowStartTime > other.windowStartTime) {
            this.windowStartTime = other.windowStartTime;
        }
        if (this.appId == null) {
            this.apiKey = other.apiKey;
            this.appId = other.appId;
            this.requestType = other.requestType;
            this.processedHttpUrl = other.processedHttpUrl;
            this.domain = other.domain;
            this.processedPath = other.processedPath;
            this.isp = other.isp;
            this.statusCode = other.statusCode;
            this.service = other.service;
        }
    }

    @Data
    public static class PercentileData {
        private long allCount = 0;
        private long sum = 0;
        private long max = Long.MIN_VALUE;
        private long min = Long.MAX_VALUE;
        private PercentileAccumulator percentile = new PercentileAccumulator();

        public void add(long value, long count) {
            allCount += count;
            sum += value * count;
            max = Math.max(max, value);
            min = Math.min(min, value);
            for (int i = 0; i < count; i++) {
                percentile.add(value);
            }
        }

        public PercentileData merge(PercentileData other) {
            allCount += other.allCount;
            sum += other.sum;
            max = Math.max(max, other.max);
            min = Math.min(min, other.min);
            percentile.merge(other.percentile);
            return this;
        }
    }
}
