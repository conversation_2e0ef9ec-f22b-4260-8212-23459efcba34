package com.databuff.engine.metric.model;

import lombok.Data;

import java.util.Map;

@Data
public class CustomFieldAccumulator {

    private Map<String, Long> data;

    public void add(Map<String, Long> fields) {
        if (fields == null) {
            return;
        }
        if (data == null) {
            data = fields;
        } else {
            for (Map.Entry<String, Long> entry : fields.entrySet()) {
                Number old = data.get(entry.getKey());
                if (old == null) {
                    old = 0L;
                }
                data.put(entry.getKey(), old.longValue() + entry.getValue());
            }
        }
    }

    public void merge(CustomFieldAccumulator accumulator) {
        add(accumulator.data);
    }
}
