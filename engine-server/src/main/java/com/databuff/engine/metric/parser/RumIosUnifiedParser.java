package com.databuff.engine.metric.parser;

import com.databuff.common.utils.FieldValidator;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.engine.metric.model.RumIosErrorResourceFields;
import com.databuff.engine.metric.model.RumIosTraceResourceFields;
import com.databuff.entity.rum.*;
import com.databuff.entity.rum.moredb.RumIosRequest;
import com.databuff.entity.rum.starrocks.*;
import com.databuff.entity.rum.web.IRumIosData;
import com.databuff.entity.rum.web.LaunchType;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
public class RumIosUnifiedParser {

    public static List<IRumIosData> parseSpan(ResourceSpan resourceSpan) {
        List<IRumIosData> results = new ArrayList<>();

        try {
            RumIosTraceResourceFields commonFields = null;
            try {
                Resource resource = resourceSpan.getResource();
                log.debug("Processing ResourceSpan in RumIosUnifiedParser: {}", resourceSpan);
                commonFields = RumIosTraceResourceFields.fromResource(resource);
            } catch (Exception e) {
                log.error("RumIosUnifiedParser fromResource error, resource:{}", resourceSpan.getResource(), e);
                OtelMetricUtil.logException("rum: RumIosUnifiedParser fromResource error", e);
                return results; // Exit early if we can't get required attributes
            }

            for (ScopeSpan scopeSpan : resourceSpan.getScopeSpans()) {
                String scopeName = scopeSpan.getScope().getName();
                log.debug("Processing scopeSpan: {}", scopeSpan);

                for (Span span : scopeSpan.getSpans()) {
                    switch (scopeName) {
                        case "databuffRUM-appStart":
                            RumIosLaunch launch = parseLaunchSpan(span, commonFields);
                            if (validate(launch)) {
                                results.add(launch);
                            }
                            break;

                        case "databuffRUM-actions":
                            RumIosAction action = parseActionSpan(span, commonFields);
                            if (validate(action)) {
                                results.add(action);
                            }
                            break;

                        case "databuffRUM-viewcontroller":
                            log.debug("RumIosUnifiedParser: viewcontroller,span:{}", span);
                            // 处理 Page
                            RumIosPage page = parsePageSpan(span, commonFields);
                            log.debug("RumIosUnifiedParser: RumIosPage:{}", page);
                            if (validate(page)) {
                                results.add(page);
                            }

                            // 处理 LifecycleMethod
                            if (span.getEvents() != null) {
                                log.debug("span.getEvents() != null");
                                List<RumIosLifecycleMethod> methods = parseLifecycleMethod(span, commonFields);
                                log.debug("methods: {}", methods);
                                if (!methods.isEmpty()) results.addAll(methods);
                            }
                            break;

                        case "databuffRUM-http":
                            List<BaseRumIosSpan> httpSpans = parseHttpSpan(span, commonFields);
                            if (!httpSpans.isEmpty()) {
                                results.addAll(httpSpans);
                            }

                            RumIosRequest httpRequest = parseHttpSpanToRequestModel(span, commonFields);
                            if (httpRequest != null) {
                                results.add(httpRequest);
                            }

                            break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("Failed to parse ResourceSpan: {}", resourceSpan, e);
            OtelMetricUtil.logException("rum: RumIosUnifiedParser parseSpan", e);
        }

        return results;
    }

    public static List<IRumIosData> parseLog(ResourceLog resourceLog) {
        List<IRumIosData> results = new ArrayList<>();

        try {
            RumIosErrorResourceFields commonFields = null;
            try {
                Resource resource = resourceLog.getResource();
                commonFields = RumIosErrorResourceFields.fromResource(resource);
            } catch (Exception e) {
                log.error("RumIosUnifiedParser fromResource error, resource:{}", resourceLog.getResource(), e);
                OtelMetricUtil.logException("rum: RumIosUnifiedParser fromResource error", e);
                return results; // Exit early if we can't get required attributes
            }

            for (ScopeLog scopeLog : resourceLog.getScopeLogs()) {
                String scopeName = scopeLog.getScope().getName();

                for (LogRecord logRecord : scopeLog.getLogRecords()) {
                    switch (scopeName) {
                        case "databuffRUM-crash":
                            RumIosCrash crash = parseCrash(logRecord, commonFields);
                            if (validate(crash)) results.add(crash);
                            break;

                        case "databuffRUM-anr":
                            RumIosAnr anr = parseAnr(logRecord, commonFields);
                            if (validate(anr)) results.add(anr);
                            break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("Failed to parse ResourceLog: {}", resourceLog, e);
            OtelMetricUtil.logException("rum: RumIosUnifiedParser parseLog", e);
        }

        return results;
    }


    private static RumIosLaunch parseLaunchSpan(Span span, RumIosTraceResourceFields commonFields) {
        RumIosLaunch launch = new RumIosLaunch();
        // 设置公共字段
        commonFields.populateTarget(launch);
        // 设置时间相关字段
        setTimeFields(launch, span);

        launch.setLaunchId(Long.parseLong(span.getSpanId()));
        launch.setLaunchType(LaunchType.getChineseNameByEnglish(span.getName()));
        return launch;
    }

    private static RumIosAction parseActionSpan(Span span, RumIosTraceResourceFields commonFields) {
        RumIosAction action = new RumIosAction();
        commonFields.populateTarget(action);
        setTimeFields(action, span);

        action.setActionId(Long.parseLong(span.getSpanId()));
        action.setActionName(span.getName());

        span.getAttributes().stream()
                .filter(attr -> "viewName".equals(attr.getKey()))
                .findFirst()
                .ifPresent(attr -> action.setPageName(attr.getStringValue()));

        return action;
    }

    private static RumIosPage parsePageSpan(Span span, RumIosTraceResourceFields commonFields) {
        RumIosPage page = new RumIosPage();
        commonFields.populateTarget(page);
        setTimeFields(page, span);

        page.setPageId(Long.parseLong(span.getSpanId()));
        page.setPageName(span.getName());

        // Process span attributes
        for (Attribute attr : span.getAttributes()) {
            if ("type".equals(attr.getKey())) {
                String type = attr.getStringValue();
                // Valid types for page processing: 4 (page), 5 (launch+page), 6 (action+page), 7 (launch+action+page)
                if (!type.equals("4") && !type.equals("5") && !type.equals("6") && !type.equals("7")) {
                    continue;
                }
            }
            if ("pageId".equals(attr.getKey())) {
                try {
                    page.setPageId(Long.parseLong(attr.getStringValue()));
                } catch (NumberFormatException e) {
                    log.error("Invalid pageId format: {}", attr.getStringValue());
                    return null;
                }
            }
        }

        return page;
    }

    private static List<RumIosLifecycleMethod> parseLifecycleMethod(Span span, RumIosTraceResourceFields commonFields) {
        List<RumIosLifecycleMethod> methods = new ArrayList<>();

        Integer type = getSpanType(span);
        if (type == null) return methods;

        log.debug("RumIosUnifiedParser: type:{}", type);
        Integer appId = commonFields.getAppId();
        Integer dfApiKeyId = commonFields.getDfApiKeyId();

        Long spanId = Long.parseLong(span.getSpanId());
        String pageName = span.getName();

        //过期 = 0
        //启动 = 1
        //操作 = 2
        //页面 = 4
        //启动+操作 = 3
        //启动+页面 = 5
        //操作+页面 = 6
        //启动+操作+页面 = 7
        for (Event event : span.getEvents()) {
            if (type == 3 || type == 5 || type == 6 || type == 7) {
                if (type == 3 || type == 5 || type == 7) {
                    RumIosLifecycleMethod launchMethod = createLifecycleMethod(span, event, 1, appId, dfApiKeyId, spanId, pageName);
                    log.debug("RumIosUnifiedParser: createLifecycleMethod launchMethod: {}", launchMethod);
                    collectLifecycleMethod(launchMethod, methods);
                }

                if (type == 3 || type == 6 || type == 7) {
                    RumIosLifecycleMethod actionMethod = createLifecycleMethod(span, event, 2, appId, dfApiKeyId, spanId, pageName);
                    log.debug("RumIosUnifiedParser: createLifecycleMethod actionMethod : {}", actionMethod);
                    collectLifecycleMethod(actionMethod, methods);
                }

                if (type == 5 || type == 6 || type == 7) {
                    RumIosLifecycleMethod pageMethod = createLifecycleMethod(span, event, 4, appId, dfApiKeyId, spanId, pageName);
                    log.debug("RumIosUnifiedParser: createLifecycleMethod pageMethod : {}", pageMethod);
                    collectLifecycleMethod(pageMethod, methods);
                }
            } else {
                RumIosLifecycleMethod method = createLifecycleMethod(span, event, type, appId, dfApiKeyId, spanId, pageName);
                collectLifecycleMethod(method, methods);
            }
        }

        return methods;
    }

    private static Integer getSpanType(Span span) {
        return span.getAttributes().stream()
                .filter(attr -> "type".equals(attr.getKey()))
                .map(attr -> Integer.parseInt(attr.getStringValue()))
                .findFirst()
                .orElse(null);
    }

    private static RumIosLifecycleMethod createLifecycleMethod(Span span, Event event, int type, Integer appId, Integer dfApiKeyId, Long spanId, String pageName) {
        RumIosLifecycleMethod method = new RumIosLifecycleMethod();
        method.setStartTime(new Date(Long.parseLong(event.getTimeUnixNano()) / 1_000_000));
        method.setAppId(appId);
        method.setDfApiKeyId(dfApiKeyId);
        method.setSpanId(spanId);
        method.setType(type);
        method.setAssociatedId(getAssociatedId(span, type));
        method.setMethodName(event.getName());
        method.setPageName(pageName);

        Long start = Long.parseLong(event.getTimeUnixNano());
        Long end = getEventEndTime(event);
        method.setStart(start);
        method.setEnd(end);
        method.setDuration(end - start);

        return method;
    }

    private static Long getAssociatedId(Span span, Integer type) {
        if (type == null) return null;

        String key = null;
        switch (type) {
            case 1:
                key = "launchId";
                break;
            case 2:
                key = "actionId";
                break;
            case 4:
                key = "pageId";
                break;
            default:
                return null;
        }

        String finalKey = key;
        return span.getAttributes().stream()
                .filter(attr -> finalKey.equals(attr.getKey()))
                .map(attr -> Long.parseLong(attr.getStringValue()))
                .findFirst()
                .orElse(null);
    }

    private static Long getEventEndTime(Event event) {
        return event.getAttributes().stream()
                .filter(attr -> "endTime".equals(attr.getKey()))
                .map(Attribute::getStringValue)
                .map(Long::parseLong)
                .findFirst()
                .orElse(Long.parseLong(event.getTimeUnixNano()));
    }

    private static void collectLifecycleMethod(RumIosLifecycleMethod method, List<RumIosLifecycleMethod> out) {
        boolean hasErrors = FieldValidator.createRumValidator("RumIosUnifiedParser", "collectLifecycleMethod")
                .validate("startTime", method.getStartTime())
                .validate("appId", method.getAppId())
                .validate("dfApiKeyId", method.getDfApiKeyId())
                .validate("spanId", method.getSpanId())
                .validate("type", method.getType())
                .validate("associatedId", method.getAssociatedId())
                .validate("methodName", method.getMethodName())
                .validate("start", method.getStart())
                .validate("end", method.getEnd())
                .validate("duration", method.getDuration())
                .hasErrors();

        if (!hasErrors) {
            out.add(method);
        }
    }

    private static List<BaseRumIosSpan> parseHttpSpan(Span span, RumIosTraceResourceFields commonFields) {
        Integer type = getSpanType(span);
        if (type == null) {
            log.warn("BaseRumIosSpan parseHttpSpan type is null: {}", span);
            return Collections.emptyList();
        }

        if (type == 0) {
            log.debug("Skipping expired span with type 0");
            return Collections.emptyList();
        }

        Integer appId = commonFields.getAppId();
        Integer dfApiKeyId = commonFields.getDfApiKeyId();
        String dfApiKey = commonFields.getDfApiKey();
        Long deviceId = commonFields.getDeviceId();
        String appName = commonFields.getAppName();

        List<BaseRumIosSpan> spans = new ArrayList<>();

        //过期 = 0
        //启动 = 1
        //操作 = 2
        //页面 = 4
        //启动+操作 = 3
        //启动+页面 = 5
        //操作+页面 = 6
        //启动+操作+页面 = 7

        // Handle launch spans (types 1, 3, 5, 7)
        if (type == 1 || type == 3 || type == 5 || type == 7) {
            log.debug("RumIosUnifiedParser: processLaunchSpan: span : {}", span);
            RumIosLaunchSpan launchSpan = processLaunchSpan(span, appId, dfApiKeyId, dfApiKey, deviceId, appName);
            if (validate(launchSpan)) {
                spans.add(launchSpan);
            }
        }

        // Handle action spans (types 2, 3, 6, 7)
        if (type == 2 || type == 3 || type == 6 || type == 7) {
            log.debug("RumIosUnifiedParser: processActionSpan: span : {}", span);
            RumIosActionSpan actionSpan = processActionSpan(span, appId, dfApiKeyId, dfApiKey, deviceId, appName);
            if (validate(actionSpan)) {
                spans.add(actionSpan);
            }
        }

        // Handle page spans (types 4, 5, 6, 7)
        if (type == 4 || type == 5 || type == 6 || type == 7) {
            log.debug("RumIosUnifiedParser: processPageSpan: span : {}", span);
            RumIosPageSpan pageSpan = processPageSpan(span, appId, dfApiKeyId, dfApiKey, deviceId, appName);
            if (validate(pageSpan)) {
                spans.add(pageSpan);
            }
        }

        return spans;
    }


    private static RumIosRequest parseHttpSpanToRequestModel(Span span, RumIosTraceResourceFields commonFields) {
        if (span == null) {
            return null;
        }

        RumIosRequest requestModel = new RumIosRequest();

        requestModel.setApiKey(commonFields.getDfApiKey());
        requestModel.setAppId(String.valueOf(commonFields.getAppId()));
        requestModel.setAppName(commonFields.getAppName());

        // 设置 startTime
        try {
            long startTimeNanos = Long.parseLong(span.getStartTimeUnixNano());
            long endTimeNanos = Long.parseLong(span.getEndTimeUnixNano());
            requestModel.setStartTime(TimeUnit.NANOSECONDS.toMillis(startTimeNanos));
            requestModel.setDuration(endTimeNanos - startTimeNanos);
        } catch (NumberFormatException e) {
            log.warn("Invalid startTimeUnixNano format for iOS HTTP span: {}. SpanId: {}", span.getStartTimeUnixNano(), span.getSpanId());
            return null; // startTime 是必须的
        }

        // 设置 requestCount
        requestModel.setRequestCount(1L);


        if (span.getAttributes() != null) {
            for (Attribute attr : span.getAttributes()) {
                if ("x-databuff-server-service".equals(attr.getKey())) {
                    requestModel.setService(attr.getStringValue());
                }
            }
        }

        // 可以在这里添加对必要字段的校验，如 appId, service (如果service不能为空)
        if (requestModel.getAppId() == null) {
            log.warn("AppId is null for iOS HTTP request. SpanId: {}", span.getSpanId());
            return null;
        }

        return requestModel;
    }


    private static RumIosLaunchSpan processLaunchSpan(Span span, Integer appId, Integer dfApiKeyId, String dfApiKey, Long deviceId, String appName) {
        RumIosLaunchSpan launchSpan = new RumIosLaunchSpan();
        setCommonSpanFields(launchSpan, span, appId, dfApiKeyId, dfApiKey, deviceId, appName);
        return launchSpan;
    }

    private static RumIosActionSpan processActionSpan(Span span, Integer appId, Integer dfApiKeyId, String dfApiKey, Long deviceId, String appName) {
        RumIosActionSpan actionSpan = new RumIosActionSpan();
        setCommonSpanFields(actionSpan, span, appId, dfApiKeyId, dfApiKey, deviceId, appName);
        return actionSpan;
    }

    private static RumIosPageSpan processPageSpan(Span span, Integer appId, Integer dfApiKeyId, String dfApiKey, Long deviceId, String appName) {
        RumIosPageSpan pageSpan = new RumIosPageSpan();
        setCommonSpanFields(pageSpan, span, appId, dfApiKeyId, dfApiKey, deviceId, appName);
        return pageSpan;
    }


    private static void setCommonSpanFields(BaseRumIosSpan target, Span span, Integer appId, Integer dfApiKeyId, String dfApiKey, Long deviceId, String appName) {
        target.setStartTime(new Date(Long.parseLong(span.getStartTimeUnixNano()) / 1_000_000));
        target.setAppId(appId);
        target.setDfApiKeyId(dfApiKeyId);
        target.setDfApiKey(dfApiKey);
        target.setAppName(appName);
        target.setDeviceId(deviceId);
        target.setTraceId(Long.parseLong(span.getTraceId()));
        target.setSpanId(Long.parseLong(span.getSpanId()));
        target.setParentId(Long.parseLong(span.getParentSpanId()));

        long start = Long.parseLong(span.getStartTimeUnixNano());
        long end = Long.parseLong(span.getEndTimeUnixNano());
        target.setStart(start);
        target.setEnd(end);
        target.setDuration(end - start);

        for (Attribute attr : span.getAttributes()) {
            switch (attr.getKey()) {
                case "x-databuff-server-service":
                    target.setService(attr.getStringValue());
                    break;
                case "http.url":
                    target.setHttpUrl(attr.getStringValue());
                    break;
                case "http.status_code":
                    target.setStatusCode(attr.getIntValue().intValue());
                    break;
                case "launchId":
                    if (target instanceof RumIosLaunchSpan) {
                        ((RumIosLaunchSpan) target).setLaunchId(Long.parseLong(attr.getStringValue()));
                    }
                    break;
                case "actionId":
                    if (target instanceof RumIosActionSpan) {
                        ((RumIosActionSpan) target).setActionId(Long.parseLong(attr.getStringValue()));
                    }
                    break;
                case "pageId":
                    if (target instanceof RumIosPageSpan) {
                        ((RumIosPageSpan) target).setPageId(Long.parseLong(attr.getStringValue()));
                    }
                    break;
            }
        }
    }

    private static RumIosCrash parseCrash(LogRecord logRecord, RumIosErrorResourceFields commonFields) {
        RumIosCrash crash = new RumIosCrash();
        commonFields.populateTarget(crash);

        // Set start_time from timeUnixNano
        crash.setStartTime(new Date(Long.parseLong(logRecord.getTimeUnixNano()) / 1_000_000));

        for (Attribute attr : logRecord.getAttributes()) {
            switch (attr.getKey()) {
                case "crashId":
                    crash.setCrashId(Long.parseLong(attr.getStringValue()));
                    break;
                case "crashType":
                    crash.setCrashName(attr.getStringValue());
                    crash.setCrashType(attr.getStringValue());
                    break;
                case "threadTrace":
                    crash.setStackInfo(attr.getStringValue());
                    break;
                case "binaryImage":
                    crash.setBinaryImageUuid(attr.getStringValue());
                    break;
                case "memory":
                    crash.setDeviceMemory(attr.getIntValue());
                    break;
                case "mem":
                    crash.setAvailableMemory(attr.getIntValue());
                    break;
                case "totalDiskSpace":
                    crash.setDeviceStorage(attr.getIntValue());
                    break;
                case "freeDiskSpace":
                    crash.setAvailableStorage(attr.getIntValue());
                    break;
                case "appStartTime":
                    crash.setAppLaunchTime(new Date(attr.getIntValue()));
                    break;
            }
        }
        return crash;
    }

    private static RumIosAnr parseAnr(LogRecord logRecord, RumIosErrorResourceFields commonFields) {
        RumIosAnr anr = new RumIosAnr();
        commonFields.populateTarget(anr);

        // Set start_time from timeUnixNano
        anr.setStartTime(new Date(Long.parseLong(logRecord.getTimeUnixNano()) / 1_000_000));
        anr.setAnrName("主线程卡顿"); // Fixed value as per documentation

        for (Attribute attr : logRecord.getAttributes()) {
            switch (attr.getKey()) {
                case "anrId":
                    anr.setAnrId(Long.parseLong(attr.getStringValue()));
                    break;
                case "threadTrace":
                    anr.setStackInfo(attr.getStringValue());
                    break;
                case "binaryImage":
                    anr.setBinaryImageUuid(attr.getStringValue());
                    break;
                case "memory":
                    anr.setDeviceMemory(attr.getIntValue());
                    break;
                case "mem":
                    anr.setAvailableMemory(attr.getIntValue());
                    break;
                case "totalDiskSpace":
                    anr.setDeviceStorage(attr.getIntValue());
                    break;
                case "freeDiskSpace":
                    anr.setAvailableStorage(attr.getIntValue());
                    break;
                case "appStartTime":
                    anr.setAppLaunchTime(new Date(attr.getIntValue()));
                    break;
            }
        }
        return anr;
    }

    // 辅助方法
    private static void setTimeFields(BaseRumIosDetail target, Span span) {
        target.setStartTime(new Date(Long.parseLong(span.getStartTimeUnixNano()) / 1_000_000));
        long start = Long.parseLong(span.getStartTimeUnixNano());
        long end = Long.parseLong(span.getEndTimeUnixNano());
        target.setStart(start);
        target.setEnd(end);
        target.setDuration(end - start);
    }

    private static boolean validate(IRumIosData data) {
        if (data == null) return false;


        if (data instanceof RumIosLaunch) {
            RumIosLaunch launch = (RumIosLaunch) data;
            boolean hasErrors = validateCommonFields(
                    FieldValidator.createRumValidator("RumIosUnifiedParser", "validate")
                            .validate("launchId", launch.getLaunchId())
                            .validate("launchType", launch.getLaunchType()),
                    launch
            ).hasErrors();
            return !hasErrors;
        }

        if (data instanceof RumIosAction) {
            RumIosAction action = (RumIosAction) data;
            boolean hasErrors = validateCommonFields(
                    FieldValidator.createRumValidator("RumIosUnifiedParser", "validate")
                            .validate("actionId", action.getActionId())
                            .validate("actionName", action.getActionName())
                            .validate("pageName", action.getPageName()),
                    action
            ).hasErrors();
            return !hasErrors;
        }

        if (data instanceof RumIosPage) {
            RumIosPage page = (RumIosPage) data;
            boolean hasErrors = validateCommonFields(
                    FieldValidator.createRumValidator("RumIosUnifiedParser", "validate")
                            .validate("pageId", page.getPageId())
                            .validate("pageName", page.getPageName()),
                    page
            ).hasErrors();
            return !hasErrors;
        }

        if (data instanceof BaseRumIosSpan) {
            BaseRumIosSpan span = (BaseRumIosSpan) data;
            boolean hasErrors = FieldValidator.createRumValidator("RumIosUnifiedParser", "validate")
                    .validate("startTime", span.getStartTime())
                    .validate("appId", span.getAppId())
                    .validate("dfApiKeyId", span.getDfApiKeyId())
                    .validate("start", span.getStart())
                    .validate("end", span.getEnd())
                    .validate("duration", span.getDuration())
                    .hasErrors();
            return !hasErrors;
        }

        if (data instanceof RumIosCrash) {
            RumIosCrash crash = (RumIosCrash) data;
            boolean hasErrors = validateErrorFields(
                    FieldValidator.createRumValidator("IosResourceLogProcessor", "collectCrash")
                            .validate("crashId", crash.getCrashId())
                            .validate("crashName", crash.getCrashName())
                            .validate("crashType", crash.getCrashType()),
                    crash
            ).hasErrors();

            return !hasErrors;
        }

        if (data instanceof RumIosAnr) {
            RumIosAnr anr = (RumIosAnr) data;
            boolean hasErrors = validateErrorFields(
                    FieldValidator.createRumValidator("IosResourceLogProcessor", "collectAnr")
                            .validate("anrId", anr.getAnrId())
                            .validate("anrName", anr.getAnrName()),
                    anr
            ).hasErrors();
            return !hasErrors;
        }

        return false;
    }


    private static FieldValidator validateCommonFields(FieldValidator validator, BaseRumIosDetail detail) {
        return validator
                .validate("startTime", detail.getStartTime())
                .validate("appId", detail.getAppId())
                .validate("userId", detail.getUserId())
                .validate("deviceId", detail.getDeviceId())
                .validate("sessionId", detail.getSessionId())
                .validate("isJailbroken", detail.getIsJailbroken())
                .validate("appVersion", detail.getAppVersion())
                .validate("probeVersion", detail.getProbeVersion())
                .validate("ip", detail.getIp())
                .validate("region", detail.getRegion())
                .validate("isp", detail.getIsp())
                .validate("os", detail.getOs())
                .validate("deviceIdentifier", detail.getDeviceIdentifier())
                .validate("networkType", detail.getNetworkType())
                .validate("deviceArch", detail.getDeviceArch())
                .validate("uiOrientation", detail.getUiOrientation())
                .validate("dfApiKeyId", detail.getDfApiKeyId())
                .validate("start", detail.getStart())
                .validate("end", detail.getEnd())
                .validate("duration", detail.getDuration());
    }

    private static FieldValidator validateErrorFields(FieldValidator validator, BaseRumIosError error) {
        return validator
                .validate("startTime", error.getStartTime())
                .validate("appId", error.getAppId())
                .validate("userId", error.getUserId())
                .validate("deviceId", error.getDeviceId())
                .validate("sessionId", error.getSessionId())
                .validate("appVersion", error.getAppVersion())
                .validate("probeVersion", error.getProbeVersion())
                .validate("ip", error.getIp())
                .validate("region", error.getRegion())
                .validate("isp", error.getIsp())
                .validate("os", error.getOs())
                .validate("deviceIdentifier", error.getDeviceIdentifier())
                .validate("networkType", error.getNetworkType())
                .validate("deviceArch", error.getDeviceArch())
                .validate("uiOrientation", error.getUiOrientation())
                .validate("dfApiKeyId", error.getDfApiKeyId())
                .validate("appLaunchTime", error.getAppLaunchTime());
    }
}
