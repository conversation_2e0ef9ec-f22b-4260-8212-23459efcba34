package com.databuff.engine.metric;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.databuff.common.constants.Constant;
import com.databuff.engine.metric.fun.MetricRowToDeviceRowStreamFun;
import com.databuff.engine.util.FlinkEnv;
import com.databuff.engine.util.KafkaConsumerFactory;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamStatementSet;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

import static com.databuff.common.constants.Constant.SNMP_MEASUREMENTS;
import static com.databuff.common.constants.KafkaTopicConstant.METRIC_TOPIC;


/**
 * Snmp指标分析生成网络设备数据，接口数据入库mysql
 *
 * @author:TianMing
 * @date: 2022/2/10
 * @time: 10:44
 */
public class SnmpFromMetric {

    private static final Logger LOGGER = LoggerFactory.getLogger(SnmpFromMetric.class);

    public static void main(String[] args) {

        final ParameterTool params = ParameterTool.fromArgs(args);
        String jobName = params.get("job.name", "SnmpMetricAnalyse");

        /**
         * 获取Flink的运行环境
         */
        StreamExecutionEnvironment executionEnvironment = FlinkEnv.getFlinkStreamEnv(params);
        int parallelism = params.getInt("parallelism", 3);
        executionEnvironment.setParallelism(parallelism);
        StreamTableEnvironment tableEnvironment = FlinkEnv.getFlinkTableEnv(executionEnvironment, jobName);

        tableEnvironment.getConfig().getConfiguration().setString("pipeline.name", jobName);
        tableEnvironment.getConfig().getConfiguration().setString("table.exec.sink.not-null-enforcer", "drop");
        /**
         * 指定 Kafka Source
         * 指定topic名称 kafka源，并行度1
         */
        String metricTopic = params.get("kafka.metric.topic", METRIC_TOPIC);
        String mysqlHost = params.get("mysql.host", "mysql:3306");

        String groupId = Constant.Kafka.METRIC_SNMP_GROUP_ID;

        FlinkKafkaConsumer<String> metricConsumer = KafkaConsumerFactory.consumerFactory(
                params, metricTopic, groupId);
        //Flink从topic中指定的group上次消费的位置开始消费，所以必须配置group.id参数
        metricConsumer.setStartFromGroupOffsets();

        //网络设备列表输出表
        tableEnvironment.executeSql("CREATE TABLE dc_snmp_device (\n" +
                "    id STRING,\n" +
                "    apiKey STRING,\n" +
                "    hostId STRING,\n" +
                "    hostName STRING,\n" +
                "    deviceName STRING,\n" +
                "    deviceIp STRING,\n" +
                "    deviceType STRING,\n" +
                "    manufacturer STRING,\n" +
                "    modelNumber STRING,\n" +
                "    productName STRING,\n" +
                "    snNumber STRING,\n" +
                "    snmpProfile STRING,\n" +
                "    sysLocation STRING,\n" +
                "    sysObjectid STRING,\n" +
                "    sysServices STRING,\n" +
                "    updateTime STRING,\n" +
                "    upTime BIGINT,\n" +
                "    PRIMARY KEY (id) NOT ENFORCED\n" +
                ") WITH (\n" +
                "    'connector' = 'jdbc',\n" +
                "    'url' = 'jdbc:mysql://" + mysqlHost + "/dc_databuff?useSSL=false&useUnicode=true&characterEncoding=UTF-8&allowMultiQueries=true&serverTimezone=Asia/Shanghai',\n" +
                "    'table-name' = 'dc_snmp_device',\n" +
                "    'driver' = 'com.mysql.jdbc.Driver',\n" +
                "    'username' = 'root',\n" +
                "    'password' = '234*(sdlj12'\n" +
                ")");
        //网络设备接口列表输出表
        tableEnvironment.executeSql("CREATE TABLE dc_snmp_device_interface (\n" +
                "    id STRING,\n" +
                "    deviceId STRING,\n" +
                "    interface STRING,\n" +
                "    interfaceAlias STRING,\n" +
                "    interfaceMac STRING,\n" +
                "    status INT,\n" +
                "    updateTime STRING,\n" +
                "    tags STRING,\n" +
                "    PRIMARY KEY (id) NOT ENFORCED\n" +
                ") WITH (\n" +
                "    'connector' = 'jdbc',\n" +
                "    'url' = 'jdbc:mysql://" + mysqlHost + "/dc_databuff?useSSL=false&useUnicode=true&characterEncoding=UTF-8&allowMultiQueries=true&serverTimezone=Asia/Shanghai',\n" +
                "    'table-name' = 'dc_snmp_device_interface',\n" +
                "    'driver' = 'com.mysql.jdbc.Driver',\n" +
                "    'username' = 'root',\n" +
                "    'password' = '234*(sdlj12'\n" +
                ")");
        DataStreamSource<String> metricStream = executionEnvironment.addSource(metricConsumer);
        //将string 转成 json 避免后面多次转换
        SingleOutputStreamOperator<JSONObject> metricJsonStream = metricStream.flatMap(new RichFlatMapFunction<String, JSONObject>() {
            @Override
            public void flatMap(String s, Collector<JSONObject> out) {
                JSONObject metric = JSON.parseObject(s);
                String measurement = metric.getString("databuff_measurement");
                if (!SNMP_MEASUREMENTS.contains(measurement)) {
                    return;
                }
                if (!metric.containsKey("tags")) {
                    JSONObject tags = metric.getJSONObject("tag");
                    JSONArray tagArray = new JSONArray();
                    for (Map.Entry<String, Object> entry : tags.entrySet()) {
                        tagArray.add(entry.getKey() + ":" + entry.getValue());
                    }
                    metric.put("tags", tagArray);
                }
                out.collect(metric);
            }
        });

        SingleOutputStreamOperator<Row> deviceStream = metricJsonStream.flatMap(new MetricRowToDeviceRowStreamFun()).returns(Types.ROW(
                Types.STRING, Types.STRING, Types.STRING, Types.DOUBLE,Types.LONG,
                Types.STRING, Types.STRING, Types.STRING, Types.STRING, Types.STRING, Types.STRING, Types.STRING, Types.STRING, Types.STRING, Types.STRING,
                Types.STRING, Types.STRING, Types.STRING, Types.STRING, Types.STRING, Types.STRING, Types.STRING)).name("deviceRowData");

        Table deviceMetricTable = tableEnvironment.fromDataStream(deviceStream,
                "apiKey,hostId,metric,metricsVal,timestamp," +
                        "deviceType,interface,interfaceAlias,manufacturer,modelNumber,productName,snNumber,snmpDevice,snmpHost,snmpProfile," +
                        "sysLocation,sysObjectid,sysServices,loader,hostName,interfaceMac,tags,ts.proctime");


        tableEnvironment.createTemporaryView("deviceMetricTable", deviceMetricTable);

        Table deviceTable = tableEnvironment.sqlQuery("select " +
                " apiKey,hostId,hostName,snmpDevice,snmpHost,deviceType,manufacturer, " +
                " modelNumber,productName,snNumber,snmpProfile,sysLocation,sysObjectid,sysServices,window_end as updateTime  " +
                "  FROM TABLE(\n" +
                "  TUMBLE(TABLE deviceMetricTable, DESCRIPTOR(ts), INTERVAL '1' MINUTES))\n" +
                "  where metric =  'snmp.interface.ifOperStatus' GROUP BY apiKey,hostId,hostName,snmpDevice,snmpHost,deviceType,manufacturer," +
                " modelNumber,productName,snNumber,snmpProfile,sysLocation,sysObjectid,sysServices,window_start, window_end"
        );
        tableEnvironment.createTemporaryView("deviceTable", deviceTable);

        Table deviceUpTimeTable = tableEnvironment.sqlQuery("select " +
                " apiKey,hostId,hostName,snmpDevice,snmpHost,Max(metricsVal) as metricsVal,window_end as updateTime  " +
                "  FROM TABLE(\n" +
                "  TUMBLE(TABLE deviceMetricTable, DESCRIPTOR(ts), INTERVAL '1' MINUTES))\n" +
                "  where metric = 'snmp.sysUpTimeInstance' GROUP BY apiKey,hostId,hostName,snmpDevice,snmpHost,window_start, window_end"
        );
        tableEnvironment.createTemporaryView("deviceUpTimeTable", deviceUpTimeTable);

        StreamStatementSet set = tableEnvironment.createStatementSet();

        //网络设备数据入库
        set.addInsertSql("INSERT INTO dc_snmp_device " +
                "  select " +
                " MD5(a.apiKey||a.snmpDevice||a.snmpHost) as id,a.apiKey,a.hostId,a.hostName,a.snmpHost,a.snmpDevice,a.deviceType,a.manufacturer, " +
                " a.modelNumber,a.productName,a.snNumber,a.snmpProfile,a.sysLocation,a.sysObjectid,a.sysServices,CONVERT_TZ(CAST( a.updateTime AS STRING), 'Asia/Shanghai', 'Asia/Shanghai') as updateTime,CAST(b.metricsVal AS BIGINT) as upTime  " +
                "  FROM deviceTable a left join deviceUpTimeTable b on a.apiKey=b.apiKey and a.hostId=b.hostId and a.snmpHost=b.snmpHost and a.snmpDevice=b.snmpDevice and a.updateTime=b.updateTime where b.metricsVal IS NOT NULL ");
        //网络设备接口数据入库
        set.addInsertSql("INSERT INTO dc_snmp_device_interface " +
                "  select " +
                " MD5(apiKey||snmpDevice||snmpHost||interface) as id,MD5(apiKey||snmpDevice||snmpHost) as deviceId,interface,interfaceAlias,interfaceMac,CAST(metricsVal AS INT) as status, " +
                " CONVERT_TZ(CAST( ts AS STRING), 'Asia/Shanghai', 'Asia/Shanghai') as updateTime,tags  " +
                "  FROM deviceMetricTable where metric =  'snmp.interface.ifOperStatus' ");
        set.execute();
    }

}
