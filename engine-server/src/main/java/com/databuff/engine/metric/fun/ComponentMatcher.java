package com.databuff.engine.metric.fun;

import com.databuff.common.model.DCSpan;
import com.databuff.engine.metric.model.ComponentSpan;

import java.util.Map;

public interface ComponentMatcher {
    boolean match(DCSpan span, Map<String, String> meta);

    boolean initTagAndFields(ComponentSpan componentSpan, DCSpan span, Map<String, String> meta);

    Map<String, String> getOtelMetricTags();

    String getMetricName();
}
