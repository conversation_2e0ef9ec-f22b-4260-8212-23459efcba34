package com.databuff.engine.metric.model;

import com.databuff.common.model.DCSpan;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

import static com.databuff.common.constants.Constant.Trace.*;

@Data
public class CountSpans {

    private long windowStartTime;

    private String httpStatusCode;

    private String srcService;
    private String srcServiceId;
    private String srcServiceInstance;
    private String resource;
    private int isOut;
    private int isIn;
    private int cntOnly;
    private String metricName;
    private Map<String, String> tags;
    private String status;
    private String hostName;
    private String httpMethod;
    private String errorType;
    private String apiKey;
    private String service; // Represents destination service in business context
    private String serviceId; // Represents destination serviceId in business context
    private String serviceInstance; // Represents destination serviceInstance in business context

    // <<< Added: Business names and related fields for business metric
    private String srcBusName;
    private String dstBusName; // <<< The required field
    private String srcServiceType;
    private String dstServiceType;
    private String src_biz_id;
    private String dst_biz_id;
    private String src_biz_pid;
    private String dst_biz_pid;
    private String biz_internal_call;
    private String sub_biz_internal_call;

    private long allCount = 0;
    private long errorCount = 0;
    private long slowCount = 0;
    private long maxDuration = Long.MIN_VALUE;
    private long minDuration = Long.MAX_VALUE;
    private long sumDuration = 0;
    private long sumCpuTime = 0;
    private long sumSlowCount = 0;
    private long sumVerySlowCount = 0;
    private long respBodyLength = 0;
    private long reqBodyLength = 0;

    private PercentileAccumulator percentile = new PercentileAccumulator();
    private CustomFieldAccumulator accumulator = new CustomFieldAccumulator();

    public Map<Integer, Long> getPercentileAgg() {
        Map<Integer, Long> result = new HashMap<>();
        for (Map.Entry<Integer, Long> entry : percentile.buckets.entrySet()) {
            int bucket = entry.getKey();
            long count = entry.getValue();
            result.put(bucket, count);
        }
        return result;
    }

    public Map<String, Long> getCustomFieldAgg() {
        Map<String, Long> result = new HashMap<>();
        if (accumulator != null) {
            accumulator.getData().forEach(
                    (key, value) -> result.put(key, value.longValue())
            );
        }
        return result;
    }

    public synchronized void add(ServiceTrace span) {
        errorCount += span.getError();
        sumDuration += span.getDuration();
        percentile.add(span.getDuration());
        allCount += 1;
        if (span.getDuration() > maxDuration) {
            maxDuration = span.getDuration();
        }
        if (span.getDuration() < minDuration) {
            minDuration = span.getDuration();
        }
    }


    public synchronized void add(DCSpan span) {
        Long cpuTime = span.getCpuTime();
        if (cpuTime == null) {
            cpuTime = 0L;
        }
        errorCount += span.getError();
        sumSlowCount += span.getIsSlow();
        sumVerySlowCount += span.getIsVerySlow();
        sumDuration += span.getDuration();
        sumCpuTime += cpuTime;
        percentile.add(span.getDuration());
        allCount += 1;
        if (span.getDuration() > maxDuration) {
            maxDuration = span.getDuration();
        }
        if (span.getDuration() < minDuration) {
            minDuration = span.getDuration();
        }
        Map<String, String> metrics = span.getMetrics();
        if (metrics != null) {
            if (TRACE_IN_NAMES_MQ.contains(span.getName())) {
                String mqBodyLength = metrics.get(TRACE_METRIC_MQ_BODY_LENGTH);
                reqBodyLength += StringUtils.isBlank(mqBodyLength) ? 0 : Long.parseLong(mqBodyLength);
            } else if (TRACE_OUT_NAMES_MQ.contains(span.getName())) {
                String mqBodyLength = metrics.get(TRACE_METRIC_MQ_BODY_LENGTH);
                respBodyLength += StringUtils.isBlank(mqBodyLength) ? 0 : Long.parseLong(mqBodyLength);
            } else {
                String resp = metrics.get(TRACE_METRIC_RESP_BODY_LENGTH);
                String req = metrics.get(TRACE_METRIC_REQ_BODY_LENGTH);
                respBodyLength += StringUtils.isBlank(resp) ? 0 : Long.parseLong(resp);
                reqBodyLength += StringUtils.isBlank(req) ? 0 : Long.parseLong(req);
            }
        }
    }

    public synchronized void add(ComponentSpan span) {
        errorCount += span.getError();
        slowCount += span.getSlow();
        sumDuration += span.getDuration();
        percentile.add(span.getDuration());
        accumulator.add(span.getFields());
        allCount += 1;
        if (span.getDuration() > maxDuration) {
            maxDuration = span.getDuration();
        }
        if (span.getDuration() < minDuration) {
            minDuration = span.getDuration();
        }
    }

    public synchronized void add(CountSpans countSpans) {
        errorCount += countSpans.getErrorCount();
        sumSlowCount += countSpans.getSumSlowCount();
        sumVerySlowCount += countSpans.getSumVerySlowCount();
        sumDuration += countSpans.getSumDuration();
        percentile.merge(countSpans.getPercentile());
        accumulator.merge(countSpans.getAccumulator());
        allCount += countSpans.getAllCount();
        if (countSpans.getMaxDuration() > maxDuration) {
            maxDuration = countSpans.getMaxDuration();
        }
        if (countSpans.getMinDuration() < minDuration) {
            minDuration = countSpans.getMinDuration();
        }
    }

    public void add(BusinessComponentSpan businessComponentSpan) {
        allCount += businessComponentSpan.getCnt();
        errorCount += businessComponentSpan.getError();
        long duration = businessComponentSpan.getDuration();
        if (duration > maxDuration) {
            maxDuration = businessComponentSpan.getDuration();
        }
        sumDuration += duration;
    }
}