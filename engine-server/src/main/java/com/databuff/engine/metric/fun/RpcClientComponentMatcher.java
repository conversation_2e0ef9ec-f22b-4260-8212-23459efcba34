package com.databuff.engine.metric.fun;

import com.databuff.common.model.DCSpan;
import com.databuff.engine.metric.model.ComponentSpan;
import com.databuff.engine.util.TraceUtil;

import java.util.Map;

import static com.databuff.common.constants.Constant.Trace.*;
import static com.databuff.common.constants.TSDBIndex.TSDB_METRIC_RPC_TABLE_NAME;
import static com.databuff.engine.constant.MetricName.TAGS_COMPONENT_RPC_CLIENT;

public class RpcClientComponentMatcher extends ComponentBaseMatcher {
    @Override
    public boolean match(DCSpan span, Map<String, String> meta) {
        return TRACE_OUT_NAMES_RPC.contains(span.getName());
    }

    @Override
    public boolean initTagAndFields(ComponentSpan componentSpan, DCSpan span, Map<String, String> meta) {
        //客户端span只需要添加自己当前服务信息，下游心
        TraceUtil.initOutServiceAndInstance(componentSpan, span);
        Map<String, String> tags = componentSpan.getTags();
        //缓存远程调用
        fillRemotelySvcTag(tags, meta,span.getName());
        if (componentSpan.getService() == null) {
            return false;
        }
        componentSpan.setIsOut(1);
        String name = span.getName();
        String type = "";
        String statusCode = "";
        if (TRACE_NAME_OUT_GRPC_CLIENT.equals(name) || TRACE_NAME_OUT_GRPC_REQUEST.equals(name)) {
            //grpc
            type = TRACE_NAME_GRPC;
            statusCode = meta.getOrDefault("status.code", "");
        } else if (TRACE_NAME_OUT_DUBBO_CALL.equals(name)) {
            //dubbo
            type = TRACE_NAME_DUBBO;
        } else if (TRACE_NAME_OUT_SOFARPC_CALL.equals(name)) {
            //sofa
            type = TRACE_NAME_SOFARPC;
        }
        tags.put("type", type);
        tags.put("statusCode", statusCode);
        //慢调用次数 （这里的慢+非常慢） 非常慢调用次数 （非常慢）
        fillSlowCntMetric(componentSpan, span);
        fillBodyLengthMetric(componentSpan, span.getMetrics());
        return true;
    }

    @Override
    public Map<String, String> getOtelMetricTags() {
        return TAGS_COMPONENT_RPC_CLIENT;
    }

    @Override
    public String getMetricName() {
        return TSDB_METRIC_RPC_TABLE_NAME;
    }

}
