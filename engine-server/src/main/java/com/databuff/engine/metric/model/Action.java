package com.databuff.engine.metric.model;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

@Data
public class Action {

    private String type;
    private String applicationId;
    private String actionId;
    private String actionType;
    private String actionName;
    private String actionLoadingTime;
    private String actionLongTaskCount;
    private String actionResourceCount;
    private String actionErrorCount;
    private String sessionId;
    private String sessionType;
    private String viewUrl;
    private String viewReferrer;
    private String viewId;
    private String viewInForeground;
    private String usrId;
    private String apiKey;
    private Long startTime;

    public Action toAction(JSONObject jsonObject) {
        this.type = jsonObject.getString("type");
        this.applicationId = jsonObject.getString("application.id");
        this.actionId = jsonObject.getString("action.id");
        this.actionType = jsonObject.getString("action.type");
        this.actionName = jsonObject.getString("action.name");
        this.actionLoadingTime = jsonObject.getString("action.loading_time");
        this.actionLongTaskCount = jsonObject.getString("action.long_task.count");
        this.actionResourceCount = jsonObject.getString("action.resource.count");
        this.actionErrorCount = jsonObject.getString("action.error.count");
        this.sessionId = jsonObject.getString("session.id");
        this.sessionType = jsonObject.getString("session.type");
        this.viewUrl = jsonObject.getString("view.url");
        this.viewReferrer = jsonObject.getString("view.referrer");
        this.viewId = jsonObject.getString("view.id");
        this.viewInForeground = jsonObject.getString("view.in_foreground");
        this.usrId = jsonObject.getString("usr.id");
        this.apiKey = jsonObject.getString("apiKey");

        try {
            this.startTime = Long.valueOf(jsonObject.getString("date"));
        } catch (Exception e) {
            e.printStackTrace();
        }

        return this;
    }
}
