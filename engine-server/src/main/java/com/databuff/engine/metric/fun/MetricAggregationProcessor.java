package com.databuff.engine.metric.fun;

import com.databuff.common.metric.FlinkAggregationType;
import com.databuff.common.metric.StandardMetric;
import com.databuff.common.tsdb.model.TSDBPoint;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.moredb.proto.Common;
import lombok.Data;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Flink ProcessWindowFunction 核心实现 (高性能优化版 + 详尽中文注释)
 * <p>
 * <b>核心功能:</b>
 * 在 Flink 的时间窗口内，高效地聚合 {@link StandardMetric} 数据流。聚合行为基于指标内嵌的
 * {@link FlinkAggregationType} 聚合提示 (hint)。支持 SUM, FIRST, LAST, AVG, MIN, MAX, COUNT 等多种聚合。
 * <p>
 * <b>关键设计与性能优化策略:</b>
 * <ul>
 * <li><b>极致优化热点路径:</b> 处理每个指标字段的核心逻辑 (聚合累加、类型判断) 经过特别优化，
 * 严格控制甚至消除此路径上的对象分配，以应对高 TPS (目标 10w+) 场景。</li>
 * <li><b>静态方法分发 (Static Dispatch):</b> 使用 `switch` 语句和静态辅助方法 (`XxxHelper`)
 * 来处理不同的聚合类型，避免 Java 接口/虚方法调用的开销，有利于 JIT 编译器内联优化。</li>
 * <li><b>原生类型优先 (Primitive First):</b> 聚合计算优先使用 `long` 和 `double` 原生类型，
 * 减少自动装箱/拆箱开销和内存占用。</li>
 * <li><b>首值定类型 (First Value Determines Type):</b> 对于需要数值计算的聚合 (SUM, AVG, MIN, MAX)，
 * 该字段在窗口内的计算类型 (`long` 或 `double`) 由遇到的第一个有效数值决定 (优先判定为 `long`)。</li>
 * <li><b>严格类型一致性 (Strict Type Consistency):</b> 一旦字段的数值类型被确定，后续到达的、与该类型不兼容的数值
 * (例如，期望 `long` 但收到 `double`) 将被忽略，不参与当前窗口的聚合计算，并可能在调试级别记录（生产模式下热点路径无日志）。</li>
 * <li><b>手动溢出处理 (Manual Overflow Handling):</b> 对 `long` 类型的 SUM 操作，使用手动检查（位运算或比较）
 * 代替 `Math.addExact` 的异常机制来检测溢出，检测到溢出后采取饱和策略 (Saturate, 即设置为 `Long.MAX_VALUE` 或 `Long.MIN_VALUE`)，
 * 避免异常处理的性能开销。溢出事件仅在窗口结束时记录一次警告。</li>
 * <li><b>精简状态对象 (Lean State Object):</b> 内部状态类 {@link AggregationState} 设计紧凑，
 * 仅包含各种聚合所需的最小字段集，多为原生类型。</li>
 * <li><b>无状态容错 (Stateless Fault Tolerance):</b> 聚合状态仅在 `process` 方法调用期间存于内存 (`HashMap`)，
 * 符合用户明确的“无需重启恢复，失败时丢弃窗口中间状态”的要求。不使用 Flink 的托管状态。</li>
 * </ul>
 *
 * @see StandardMetric 输入的数据指标结构。
 * @see FlinkAggregationType 定义了聚合行为的枚举。
 * @see TSDBPoint 聚合结果输出的数据结构。
 * @see Common.FieldType TSDB 字段类型枚举 (用于结果输出)。
 */
public class MetricAggregationProcessor extends ProcessWindowFunction<StandardMetric, TSDBPoint, String, TimeWindow> {

    private static final Logger log = LoggerFactory.getLogger(MetricAggregationProcessor.class);
    /** 默认聚合类型，当 StandardMetric 中未指定 hint 时使用。*/
    private static final FlinkAggregationType DEFAULT_AGG_HINT = FlinkAggregationType.SUM;

    /**
     * 内部枚举，用于记录字段在其窗口生命周期内动态确定的数值计算类型。
     * UNKNOWN 表示尚未遇到有效的数值；LONG 表示按 long 类型计算；DOUBLE 表示按 double 类型计算。
     */
    private enum DeterminedNumericType { UNKNOWN, LONG, DOUBLE }

    /**
     * 内部状态类，用于封装单个字段在一个窗口内的完整聚合状态。
     * 此类的设计以性能为导向，字段多为原生类型，避免不必要的对象包装。
     * 通过 Lombok 的 @Data 注解自动生成 getter/setter 等样板代码。
     */
    @Data
    private static class AggregationState {
        // --- SUM/AVG/MIN/MAX 相关状态 ---
        /** 用于 numericType = LONG 的 SUM/AVG 累加值。*/
        private long   longSum = 0L;
        /** 用于 numericType = DOUBLE 的 SUM/AVG 累加值。*/
        private double doubleSum = 0.0;
        /** 有效数值计数 (用于 AVG 计算和 COUNT 聚合)。总是 long 类型。*/
        private long   count = 0L;
        /** 用于 numericType = LONG 的 MIN 聚合结果。*/
        private long   longMinValue = Long.MAX_VALUE;
        /** 用于 numericType = DOUBLE 的 MIN 聚合结果。*/
        private double doubleMinValue = Double.POSITIVE_INFINITY;
        /** 用于 numericType = LONG 的 MAX 聚合结果。*/
        private long   longMaxValue = Long.MIN_VALUE;
        /** 用于 numericType = DOUBLE 的 MAX 聚合结果。*/
        private double doubleMaxValue = Double.NEGATIVE_INFINITY;
        /** 标记 MIN/MAX 是否已被第一个有效数值初始化。防止使用初始边界值作为结果。*/
        private boolean minMaxInitialized = false;

        // --- 类型控制与状态标记 ---
        /** 记录由第一个有效数值决定的计算类型。*/
        private DeterminedNumericType numericType = DeterminedNumericType.UNKNOWN;
        /** 标记 longSum 在聚合过程中是否发生过溢出并被饱和处理。用于窗口结束时记录日志。*/
        private boolean overflowOccurred = false;

        // --- FIRST/LAST 相关状态 ---
        /** FIRST 聚合的结果值 (保持原始对象类型)。*/
        private Object firstValue = null;
        /** FIRST 聚合的值对应的时间戳 (ms)。使用 -1 表示尚未设置。*/
        private long firstTimestamp = -1L;
        /** LAST 聚合的结果值 (保持原始对象类型)。*/
        private Object lastValue = null;
        /** LAST 聚合的值对应的时间戳 (ms)。使用 -1 表示尚未设置。*/
        private long lastTimestamp = -1L;
    }

    // =========================================================================
    // Static Helper Classes for Aggregation Logic (静态辅助类处理聚合逻辑)
    // 使用静态方法避免虚调用开销，利于 JIT 内联。每个类处理一种或一类聚合。
    // =========================================================================

    /** 辅助处理 SUM 聚合，包含手动溢出检查和饱和逻辑。*/
    private static class SumHelper {
        /**
         * 累加 long 值到状态中。包含手动溢出检查。
         * @param state 当前聚合状态
         * @param value 要累加的 long 值
         */
        static void accumulate(AggregationState state, long value) {
            // 如果已经发生过溢出，则后续累加无意义 (值已被饱和)，直接返回。
            if (state.overflowOccurred) return;

            long currentSum = state.longSum;
            // 手动检查 Long 加法是否会溢出 (比 try-catch 更高效)
            if ((value > 0 && currentSum > Long.MAX_VALUE - value) || // 正向溢出检查
                    (value < 0 && currentSum < Long.MIN_VALUE - value))   // 负向溢出检查
            {
                // 发生溢出，进行饱和处理
                state.longSum = (value > 0) ? Long.MAX_VALUE : Long.MIN_VALUE;
                state.overflowOccurred = true; // 标记发生过溢出
                // 注意：日志记录被移到窗口结束时进行，避免在热点路径打印日志。
            } else {
                // 未溢出，正常累加
                state.longSum += value;
            }
        }

        /**
         * 累加 double 值到状态中。double 的溢出通常不需特殊处理 (会变成 Infinity)。
         * @param state 当前聚合状态
         * @param value 要累加的 double 值
         */
        static void accumulate(AggregationState state, double value) {
            state.doubleSum += value;
        }

        /**
         * 获取 SUM 聚合的最终结果。
         * @param state 最终聚合状态
         * @return Long 或 Double 类型的最终和值。
         */
        static Object getResult(AggregationState state) {
            // *** 如果从未有过有效数值，则不应有 SUM 结果 ***
            if (state.numericType == DeterminedNumericType.UNKNOWN) {
                return null; // <--- 修改点
            }
            if (state.numericType == DeterminedNumericType.LONG) {
                return state.longSum;
            } else {
                return state.doubleSum;
            }
        }
    }

    /** 辅助处理 AVG 聚合。其累加逻辑复用 SUM，这里只提供获取结果的方法。*/
    private static class AvgHelper {
        /**
         * 获取 AVG 聚合的最终结果。
         * @param state 最终聚合状态
         * @return Double 类型的平均值。如果计数为0，可能返回 null 或 0.0 (需定义)。
         */
        static Object getResult(AggregationState state) {
            if (state.count == 0) {
                // 定义窗口内无有效数值时 AVG 的行为，这里返回 null
                return null;
            }
            // AVG 结果总是返回 Double 类型，以保持一致性并处理可能的非整数结果。
            double sum = (state.numericType == DeterminedNumericType.LONG) ? (double) state.longSum : state.doubleSum;
            return sum / state.count;
        }
    }

    /** 辅助处理 MIN 和 MAX 聚合。*/
    private static class MinMaxHelper {
        /** 更新 long 类型的最小值和最大值。*/
        static void updateLongMinMax(AggregationState state, long value) {
            state.longMinValue = Math.min(state.longMinValue, value);
            state.longMaxValue = Math.max(state.longMaxValue, value);
        }
        /** 更新 double 类型的最小值和最大值。*/
        static void updateDoubleMinMax(AggregationState state, double value) {
            state.doubleMinValue = Math.min(state.doubleMinValue, value);
            state.doubleMaxValue = Math.max(state.doubleMaxValue, value);
        }
        /** 使用第一个有效 long 值初始化 MIN/MAX 状态。*/
        static void initializeMinMax(AggregationState state, long value) {
            state.longMinValue = value;
            state.longMaxValue = value;
            state.minMaxInitialized = true;
        }
        /** 使用第一个有效 double 值初始化 MIN/MAX 状态。*/
        static void initializeMinMax(AggregationState state, double value) {
            state.doubleMinValue = value;
            state.doubleMaxValue = value;
            state.minMaxInitialized = true;
        }
        /**
         * 获取 MIN 聚合的最终结果。
         * !! 确认点: !minMaxInitialized 时返回 null 的逻辑是正确的。
         */
        static Object getMinResult(AggregationState state) {
            if (!state.minMaxInitialized) { // 如果从未初始化，则无 MIN 结果
                return null; // <--- 确认此逻辑正确
            }
            return (state.numericType == DeterminedNumericType.LONG) ? state.longMinValue : state.doubleMinValue;
        }
        /**
         * 获取 MAX 聚合的最终结果。
         * !! 确认点: !minMaxInitialized 时返回 null 的逻辑是正确的。
         */
        static Object getMaxResult(AggregationState state) {
            if (!state.minMaxInitialized) { // 如果从未初始化，则无 MAX 结果
                return null; // <--- 确认此逻辑正确
            }
            return (state.numericType == DeterminedNumericType.LONG) ? state.longMaxValue : state.doubleMaxValue;
        }
    }

    /** 辅助处理 FIRST 和 LAST 聚合。*/
    private static class FirstLastHelper {
        /**
         * 更新 FIRST 值。如果新值的时间戳更早，则替换。
         * @param state 当前聚合状态
         * @param value 当前值 (保持原始对象)
         * @param timestamp 当前值的时间戳 (ms)
         */
        static void updateFirst(AggregationState state, Object value, long timestamp) {
            // 使用 -1 作为 firstTimestamp 的未设置标记
            if (state.firstTimestamp == -1L || timestamp < state.firstTimestamp) {
                state.firstValue = value;
                state.firstTimestamp = timestamp;
            }
        }
        /**
         * 更新 LAST 值。如果新值的时间戳更晚或相等，则替换 (相等时取后者)。
         * @param state 当前聚合状态
         * @param value 当前值 (保持原始对象)
         * @param timestamp 当前值的时间戳 (ms)
         */
        static void updateLast(AggregationState state, Object value, long timestamp) {
            // 使用 >= 保证时间戳相同时，后面到达的数据覆盖前面的
            if (state.lastTimestamp == -1L || timestamp >= state.lastTimestamp) {
                state.lastValue = value;
                state.lastTimestamp = timestamp;
            }
        }
        /** 获取 FIRST 聚合的最终结果。*/
        static Object getFirstResult(AggregationState state) { return state.firstValue; }
        /** 获取 LAST 聚合的最终结果。*/
        static Object getLastResult(AggregationState state) { return state.lastValue; }
    }

    /** 辅助处理 COUNT 聚合。*/
    private static class CountHelper {
        /**
         * 为有效的数值类型增加计数 (用于 AVG)。
         * 注意：此方法在 `dispatchAggregationUpdate` 中当值被确认为有效数值后调用。
         */
        static void accumulate(AggregationState state) {
            state.count++;
        }
        /**
         * 为 COUNT hint 增加计数。此方法在 `dispatchAggregationUpdate` 中被调用，
         * 只要字段值非 null 就会计数，不关心其是否能解析为数值。
         */
        static void accumulateAny(AggregationState state) {
            state.count++;
        }
        /** 获取 COUNT 聚合的最终结果。*/
        static Object getResult(AggregationState state) {
            // COUNT 结果总是返回 Long 类型
            return state.count;
        }
    }

    /** 辅助处理 RANGE 聚合 (MAX - MIN)。*/
    private static class RangeHelper {
        /**
         * 获取 RANGE 聚合的最终结果。
         * @param state 最终聚合状态
         * @return Long 或 Double 类型的差值。如果未初始化或只有一个点，可能返回 0 或 null。
         */
        static Object getResult(AggregationState state) {
            if (!state.minMaxInitialized) {
                return null; // 没有有效值，无法计算差值
            }

            // 如果只有一个有效数值点，最大值等于最小值，差值为0
            // AggregationState.count 记录的是有效数值的个数
            // if (state.count < 2) { // 或者只看 minMaxInitialized，如果只有一个点，max和min会相等
            //     return (state.numericType == DeterminedNumericType.LONG) ? 0L : 0.0;
            // }
            // 即使只有一个点，max 和 min 也是相等的，所以 max - min 结果会是0，这是正确的。

            if (state.numericType == DeterminedNumericType.LONG) {
                // 检查 longMaxValue 和 longMinValue 是否被有效更新过
                // minMaxInitialized 已经保证了它们至少被第一个值更新了
                return state.longMaxValue - state.longMinValue;
            } else if (state.numericType == DeterminedNumericType.DOUBLE) {
                return state.doubleMaxValue - state.doubleMinValue;
            } else {
                // UNKNOWN numericType, 但 minMaxInitialized 为 true 的情况理论上不应发生
                // 如果发生了，意味着有非数值类型被错误地标记为可计算 min/max
                return null;
            }
        }
    }

    // =========================================================================
    // ProcessWindowFunction Implementation (Flink 窗口函数实现)
    // =========================================================================

    /**
     * Flink ProcessWindowFunction 的主处理方法。在窗口触发时为每个 Key 调用一次。
     * 此方法协调整个聚合流程：初始化、遍历聚合、最终化结果、发送输出。
     *
     * @param key      Flink keyBy 产生的 Key (格式通常为 "database|measurement|sorted_tags")。
     * @param context  窗口上下文，提供窗口信息 (如结束时间) 等。未使用 Flink 托管状态。
     * @param elements 属于当前 Key 和当前窗口的所有 {@link StandardMetric} 元素迭代器 (只能迭代一次)。
     * @param out      结果收集器，用于向下游发送聚合后的 {@link TSDBPoint}。
     * @throws Exception Flink 允许 process 方法抛出异常。
     */
    @Override
    public void process(String key, Context context, Iterable<StandardMetric> elements, Collector<TSDBPoint> out) throws Exception {

        // --- 0. 初始化窗口处理上下文 ---
        Map<String, String> groupTags = null; // 分组标签
        String measurement = null;            // 指标名
        String database = null;               // 数据库名
        Map<String, FlinkAggregationType> aggregationHints = null; // 聚合提示
        boolean isInitialized = false;        // 标记上下文是否已从第一个有效 metric 初始化

        // 使用 HashMap 存储每个字段名到其聚合状态对象的映射。状态仅存于此方法作用域内。
        Map<String, AggregationState> fieldStates = new HashMap<>();

        // --- 1. 聚合窗口内所有元素 (热点路径) ---
        // 遍历窗口内的所有 StandardMetric 对象
        for (StandardMetric metric : elements) {
            // 基本校验：跳过 null 或没有字段的 metric
            if (metric == null || metric.getFields() == null || metric.getFields().isEmpty()) {
                continue; // 性能考虑：避免在热点路径记录日志
            }

            // 使用第一个有效的 metric 初始化窗口级别的上下文信息 (仅执行一次)
            if (!isInitialized) {
                // 必须包含 database 和 measurement 才能初始化
                if (metric.getMeasurement() == null || metric.getDatabase() == null) {
                    String errorMsg = String.format("MetricAggregationProcessor Missing required fields in StandardMetric." +
                                    " Database: '%s', Measurement: '%s' Timestamp: '%s'",
                            metric.getDatabase(), metric.getMeasurement() ,metric.getTimestamp());
                    log.warn(errorMsg);
                    // 使用logException记录错误埋点
                    OtelMetricUtil.logException(errorMsg, new NullPointerException(""));
                    continue; // 继续寻找下一个有效的 metric 来初始化
                }
                // 防御性拷贝 Map，避免共享引用可能导致的问题
                groupTags = metric.getTags() != null ? new HashMap<>(metric.getTags()) : new HashMap<>();
                measurement = metric.getMeasurement();
                database = metric.getDatabase();
                aggregationHints = metric.getFlinkAggregationHints() != null ? new HashMap<>(metric.getFlinkAggregationHints()) : Collections.emptyMap(); // 使用空 Map 避免 NullPointerException
                isInitialized = true; // 标记已初始化
            }

            // 处理当前 metric 中的所有字段，更新 fieldStates
            processSingleMetricFields(metric, fieldStates, aggregationHints, key);

        } // -- 结束遍历窗口元素 --

        // --- 2. 处理窗口结束逻辑 ---
        // 如果整个窗口都没有收到任何有效数据 (从未初始化)，则直接返回
        if (!isInitialized) {
            // 调试日志：log.debug("Key '{}' 的窗口关闭，但没有收到有效元素。", key);
            return;
        }

        // --- 3. 生成最终聚合结果 ---
        // 调用 finalize 方法计算每个字段的最终聚合值
        Map<String, Object> aggregatedFields = finalizeAggregatedFields(fieldStates, aggregationHints, key);

        // --- 4. 确定最终字段的 TSDB 类型 ---
        // 根据聚合提示和最终存在的字段名，确定每个字段写入 TSDB 时应标记的类型
        Map<String, Common.FieldType> finalFieldTypes = determineFinalFieldTypes(aggregationHints, aggregatedFields.keySet());

        // --- 5. 创建并发送 TSDBPoint ---
        // 仅当有实际聚合结果字段时才发送
        if (!aggregatedFields.isEmpty()) {
            // 可选：添加校验，确保聚合字段数与类型数匹配
            if (aggregatedFields.size() != finalFieldTypes.size()) {
                log.error("Key '{}' 的聚合字段数 ({}) 与最终类型数 ({}) 不匹配。字段: {}, 类型: {}",
                        key, aggregatedFields.size(), finalFieldTypes.size(), aggregatedFields.keySet(), finalFieldTypes.keySet());
                // 可能需要进一步调查或采取纠正措施，例如只发送类型匹配的字段
                OtelMetricUtil.logException("MetricAggregationProcessor 聚合字段数与最终类型数不匹配", new RuntimeException());
            }
            // 构建 TSDBPoint 对象并发送到下游
            buildAndCollectTsdbPoint(key, context.window(), database, measurement, groupTags, aggregatedFields, finalFieldTypes, out);
        } else {
            // 调试日志：log.debug("Key '{}' 的窗口关闭，但没有有效的聚合字段产生。", key);
        }
    }

    /**
     * 处理单个 StandardMetric 对象中的所有字段。
     * 对于每个字段，获取或创建其 {@link AggregationState}，并调用分发器更新状态。
     * (此方法是热点路径的一部分)
     *
     * @param metric       当前处理的 StandardMetric 对象 (保证非 null 且有字段)。
     * @param fieldStates  存储各字段聚合状态的 Map。
     * @param hints        聚合提示 Map (保证非 null)。
     * @param key          当前 Flink Key (用于可能的日志记录)。
     */
    private void processSingleMetricFields(StandardMetric metric, Map<String, AggregationState> fieldStates, Map<String, FlinkAggregationType> hints, String key) {
        // 获取一次时间戳，避免在循环内重复调用 getter
        long timestamp = metric.getTimestamp();

        // 遍历 metric 中的所有字段 (field)
        for (Map.Entry<String, Object> fieldEntry : metric.getFields().entrySet()) {
            String fieldName = fieldEntry.getKey();
            Object fieldValue = fieldEntry.getValue();

            // 跳过值为 null 的字段，这些值不参与任何聚合
            if (fieldValue == null) {
                continue;
            }

            // 获取该字段的聚合提示，如果缺失，则使用默认值 (SUM)
            FlinkAggregationType hint = hints.getOrDefault(fieldName, DEFAULT_AGG_HINT);

            // 获取或创建该字段的聚合状态对象
            // computeIfAbsent 保证了每个字段在窗口内只有一个 AggregationState 实例
            AggregationState state = fieldStates.computeIfAbsent(fieldName, k -> new AggregationState());

            // 调用聚合更新分发器，这是核心处理逻辑的入口
            dispatchAggregationUpdate(state, fieldName, fieldValue, timestamp, hint, key);
        }
    }

    /**
     * 聚合更新分发器：根据聚合提示 (hint) 将更新请求分发到具体的处理逻辑。
     * (这是性能最敏感的核心方法)
     * <p>
     * <b>优化重点:</b>
     * <ul>
     * <li><b>内联类型解析:</b> 直接在此方法内执行 `instanceof` 检查和数值转换，避免额外方法调用和对象创建。</li>
     * <li><b>早期处理非数值聚合:</b> FIRST, LAST, COUNT (Any) 在开始时处理并返回，简化后续流程。</li>
     * <li><b>首值定类型逻辑:</b> 当 `state.numericType` 为 UNKNOWN 时，根据当前值的解析结果确定窗口类型。</li>
     * <li><b>类型一致性强制:</b> 如果类型已确定，后续值必须兼容，否则忽略。</li>
     * <li><b>静态辅助方法调用:</b> 将具体的累加逻辑委托给 `XxxHelper` 的静态方法。</li>
     * </ul>
     *
     * @param state      该字段当前的聚合状态对象。
     * @param fieldName  当前处理的字段名。
     * @param fieldValue 当前字段的值 (保证非 null)。
     * @param timestamp  当前值的时间戳 (ms)。
     * @param hint       该字段的聚合提示。
     * @param key        当前 Flink Key。
     */
    private void dispatchAggregationUpdate(AggregationState state, String fieldName, Object fieldValue, long timestamp, FlinkAggregationType hint, String key) {

        // --- 1. 优先处理非数值型或特殊聚合类型 ---
        // 这些类型不依赖于数值解析结果
        switch (hint) {
            case FIRST:
                FirstLastHelper.updateFirst(state, fieldValue, timestamp);
                return;
            case LAST:
                FirstLastHelper.updateLast(state, fieldValue, timestamp);
                return;
            case COUNT:
                CountHelper.accumulateAny(state);
                return;
        }

        // --- 2. 处理数值型聚合 (SUM, AVG, MIN, MAX) ---
        // !! 修改: 类型解析逻辑调整，优先保留 Double/Float !!

        DeterminedNumericType currentNumType = DeterminedNumericType.UNKNOWN;
        long longVal = 0L;
        double doubleVal = 0.0;
        boolean isValidNumeric = false;

        // --- 修改后的内联类型解析逻辑 ---
        if (fieldValue instanceof Double || fieldValue instanceof Float) {
            // **关键修改**: 如果原始类型是 Double 或 Float，直接按 DOUBLE 处理
            doubleVal = ((Number) fieldValue).doubleValue();
            currentNumType = DeterminedNumericType.DOUBLE; // 强制为 DOUBLE
            isValidNumeric = true;
            // **不再** 尝试将其转为 long，即使值是 0.0 或 100.0
        } else if (fieldValue instanceof Long) {
            longVal = (Long) fieldValue;
            currentNumType = DeterminedNumericType.LONG;
            isValidNumeric = true;
        } else if (fieldValue instanceof Integer || fieldValue instanceof Short || fieldValue instanceof Byte) {
            longVal = ((Number) fieldValue).longValue();
            currentNumType = DeterminedNumericType.LONG;
            isValidNumeric = true;
        } else if (fieldValue instanceof BigDecimal) {
            BigDecimal bd = (BigDecimal) fieldValue;
            try {
                // 仍然尝试精确转 Long，因为 BigDecimal 可能代表精确的整数
                longVal = bd.longValueExact();
                currentNumType = DeterminedNumericType.LONG;
            } catch (ArithmeticException e) {
                // 无法精确转 Long，则按 Double 处理
                doubleVal = bd.doubleValue();
                currentNumType = DeterminedNumericType.DOUBLE;
            }
            isValidNumeric = true;
        } else if (fieldValue instanceof String) {
            // String 类型尝试解析为 Long 或 Double
            try {
                // 优先尝试 Long
                longVal = Long.parseLong((String) fieldValue);
                currentNumType = DeterminedNumericType.LONG;
                isValidNumeric = true;
            } catch (NumberFormatException e1) {
                try {
                    // 失败则尝试 Double
                    doubleVal = Double.parseDouble((String) fieldValue);
                    // **关键修改**: 如果字符串能解析为 double，类型就定为 DOUBLE
                    // 不再尝试将解析出的 double 转回 long
                    currentNumType = DeterminedNumericType.DOUBLE;
                    isValidNumeric = true;
                } catch (NumberFormatException e2) {
                    isValidNumeric = false; // 无法解析为任何数值
                }
            }
        }
        // --- 结束修改后的内联类型解析 ---

        // 如果当前值无法解析为有效的数值类型，则对于数值聚合操作直接忽略
        if (!isValidNumeric) {
            // 调试日志：log.debug("字段 '{}' 的值 '{}' (类型: {}) 无法解析为数值，对于 hint '{}' 将忽略。", fieldName, fieldValue, fieldValue.getClass().getSimpleName(), hint);
            return;
        }

        // --- 3. 根据解析结果更新聚合状态 ---

        // 情况 A: 这是该字段遇到的第一个有效数值 -> 确定窗口类型
        if (state.numericType == DeterminedNumericType.UNKNOWN) {
            state.numericType = currentNumType; // 由第一个值确定类型
            state.count = 1L;                   // 初始化计数器 (用于 AVG)

            // 根据确定的类型，初始化 SUM 和 MIN/MAX
            if (currentNumType == DeterminedNumericType.LONG) {
                SumHelper.accumulate(state, longVal);       // 初始化 Sum (含溢出检查)
                MinMaxHelper.initializeMinMax(state, longVal); // 初始化 Min/Max
            } else { // DOUBLE
                SumHelper.accumulate(state, doubleVal);
                MinMaxHelper.initializeMinMax(state, doubleVal);
            }

            // 情况 B: 类型已确定 -> 处理后续值，强制类型一致
        } else {
            // 增加有效数值计数 (用于 AVG)
            // 注意：这里调用 CountHelper.accumulate(state) 语义上更清晰，但直接 state.count++ 性能相同
            CountHelper.accumulate(state);

            // 检查当前值的类型是否与窗口已确定的类型兼容
            if (state.numericType == DeterminedNumericType.LONG) {
                // 期望 Long 类型
                if (currentNumType == DeterminedNumericType.LONG) {
                    // 类型匹配，分发到对应的 Long 聚合逻辑
                    switch (hint) {
                        case SUM: case AVG: // AVG 复用 SUM 的累加逻辑
                            SumHelper.accumulate(state, longVal);
                            break;
                        case MIN: // MIN/MAX 逻辑合并在一个方法里更新
                        case MAX:
                        case RANGE: // RANGE 依赖 MIN 和 MAX 的更新
                            MinMaxHelper.updateLongMinMax(state, longVal);
                            break;
                        // default: 其他 hint (FIRST, LAST, COUNT) 已在方法开始处处理
                    }
                } else {
                    // 类型不匹配：期望 Long，收到 Double -> 忽略此值
                    // 调试日志：log.warn("类型不匹配：字段 '{}' (Key: {}) 期望 Long，收到 Double 值 '{}'。忽略。", fieldName, key, fieldValue);
                    state.count--; // 值被忽略，计数需要减回
                    return; // 忽略不兼容的值
                }
            } else { // state.numericType == DeterminedNumericType.DOUBLE
                // 期望 Double 类型
                // 无论当前值解析为 Long 还是 Double，都提升为 Double 进行计算
                double valueAsDouble = (currentNumType == DeterminedNumericType.LONG) ? (double) longVal : doubleVal;

                // 分发到对应的 Double 聚合逻辑
                switch (hint) {
                    case SUM: case AVG:
                        SumHelper.accumulate(state, valueAsDouble);
                        break;
                    case MIN:
                    case MAX:
                    case RANGE: // RANGE 依赖 MIN 和 MAX 的更新
                        MinMaxHelper.updateDoubleMinMax(state, valueAsDouble);
                        break;
                    // default: 其他 hint 已处理
                }
            }
        }
    }


    /**
     * 在窗口结束时，根据所有字段的最终聚合状态 ({@link AggregationState}) 和聚合提示，
     * 构建包含最终聚合结果的 Map。
     *
     * @param fieldStates      存储各字段最终聚合状态的 Map。
     * @param aggregationHints 聚合提示 Map (保证非 null)。
     * @param key              当前 Flink Key (用于可能的日志记录)。
     * @return 一个 Map，键是字段名，值是该字段的最终聚合结果。
     */
    private Map<String, Object> finalizeAggregatedFields(Map<String, AggregationState> fieldStates, Map<String, FlinkAggregationType> aggregationHints, String key) {
        Map<String, Object> aggregatedFields = new HashMap<>();

        // 遍历所有在窗口内存活过的字段状态
        for (Map.Entry<String, AggregationState> stateEntry : fieldStates.entrySet()) {
            String fieldName = stateEntry.getKey();
            AggregationState state = stateEntry.getValue();
            // 获取该字段的聚合提示，不存在则使用默认值
            FlinkAggregationType hint = aggregationHints.getOrDefault(fieldName, DEFAULT_AGG_HINT);
            Object finalValue = null; // 用于存储计算出的最终值

            // ---- 日志记录：如果 Long SUM 发生过溢出，在此处记录一次 ----
            if (hint == FlinkAggregationType.SUM && state.numericType == DeterminedNumericType.LONG && state.overflowOccurred) {
                log.warn("Key: '{}', 字段: '{}' 的 Long SUM 计算发生溢出并被饱和处理。最终饱和值: {}", key, fieldName, state.longSum);
            }

            // ---- 使用静态辅助方法获取最终结果 ----
            switch (hint) {
                case SUM:   finalValue = SumHelper.getResult(state); break;
                case AVG:   finalValue = AvgHelper.getResult(state); break;
                case MIN:   finalValue = MinMaxHelper.getMinResult(state); break;
                case MAX:   finalValue = MinMaxHelper.getMaxResult(state); break;
                case FIRST: finalValue = FirstLastHelper.getFirstResult(state); break;
                case LAST:  finalValue = FirstLastHelper.getLastResult(state); break;
                case COUNT: finalValue = CountHelper.getResult(state); break;
                case RANGE: finalValue = RangeHelper.getResult(state); break;
                // default: 理论上所有 hint 都应被处理
            }

            // ---- 对最终值进行规范化并添加到结果 Map 中 ----
            // 仅当计算或获取到非 null 的最终值时才添加
            if (finalValue != null) {
                // normalizeFinalValue 负责确保结果类型符合预期 (如 COUNT 是 Long, AVG 是 Double)
                // 并处理 FIRST/LAST 可能得到的非数值类型或需要解析的数值字符串
                aggregatedFields.put(fieldName, normalizeFinalValue(finalValue, hint, state.numericType));
            }
        }
        return aggregatedFields; // 返回包含所有字段最终聚合结果的 Map
    }

    /**
     * 对最终的聚合结果值进行类型规范化。
     * 目的是确保输出类型符合聚合操作的预期语义（例如 COUNT 总是 Long，AVG 总是 Double），
     * 并尽量使 SUM/MIN/MAX 的结果类型与窗口期间确定的计算类型 (`determinedType`) 保持一致。
     * 同时，也处理 FIRST/LAST 可能捕获到的非 Number 类型值。
     * (此方法应尽量减少对象创建和复杂逻辑)
     *
     * @param value          计算得到的最终聚合值 (可能是 Number, String, Boolean, etc.)。
     * @param hint           该字段应用的聚合提示。
     * @param determinedType 该字段在窗口聚合期间确定的数值计算类型 (LONG 或 DOUBLE)。
     * @return 规范化后的最终值。
     */
    private Object normalizeFinalValue(Object value, FlinkAggregationType hint, DeterminedNumericType determinedType) {
        // 1. 根据 hint 确定固定类型的结果
        if (hint == FlinkAggregationType.COUNT) {
            // COUNT 结果必须是 Long 类型
            return (value instanceof Long) ? value : 0L; // 如果不是 Long (异常情况)，返回 0L
        }
        if (hint == FlinkAggregationType.AVG) {
            // AVG 结果必须是 Double 类型
            return (value instanceof Number) ? ((Number)value).doubleValue() : 0.0; // 如果不是 Number (异常情况)，返回 0.0
        }
        // 对于 RANGE，其结果类型 (Long 或 Double) 已经由 RangeHelper.getResult 根据 determinedType 确定。
        // 所以它会进入下面的 Number instanceof 逻辑。

        // 2. 处理 SUM, MIN, MAX 以及 FIRST/LAST 捕获到的 Number 类型
        if (value instanceof Number) {
            Number numValue = (Number) value;
            // 如果窗口期间确定按 LONG 计算
            if (determinedType == DeterminedNumericType.LONG) {
                // 检查最终值是否确实是需要 Double 精度来表示的 (例如，来自 FIRST/LAST 的浮点数)
                if (numValue instanceof Double || numValue instanceof Float || numValue instanceof BigDecimal) {
                    double dVal = numValue.doubleValue();
                    // 尝试转回 Long，如果可以无损表示
                    if (dVal == Math.floor(dVal) && !Double.isInfinite(dVal) && dVal >= Long.MIN_VALUE && dVal <= Long.MAX_VALUE) {
                        return (long) dVal;
                    } else {
                        // 无法无损转 Long，保持 Double
                        return dVal;
                    }
                }
                // 如果值本身是 Long 或其他整数类型，直接返回 longValue
                return numValue.longValue();
            } else { // 如果窗口期间确定按 DOUBLE 计算 (或未知，则默认 Double)
                // 返回 Double 类型
                return numValue.doubleValue();
            }
        }

        // 3. 处理 FIRST/LAST 捕获到的非 Number 类型
        // 例如 String, Boolean 等，直接返回原始对象。
        // 如果 FIRST/LAST 捕获到的是数值型字符串，当前逻辑不尝试再次解析，直接返回 String。
        // （如果需要解析，可以在这里添加 String 的数值解析逻辑，但会增加复杂度和开销）
        return value;
    }


    /**
     * 根据 Flink 的聚合提示 (hint) 和最终聚合出的字段集合，确定每个字段写入 TSDB 时
     * 应使用的 {@link Common.FieldType} (例如 SUM, GAUGE)。
     *
     * @param aggregationHints 原始的聚合提示 Map (保证非 null)。
     * @param finalFieldKeys   最终聚合结果中包含的所有字段名的 Set。
     * @return 一个 Map，键是字段名，值是对应的 TSDB 字段类型 (FieldType)。
     */
    private Map<String, Common.FieldType> determineFinalFieldTypes(Map<String, FlinkAggregationType> aggregationHints, Set<String> finalFieldKeys) {
        Map<String, Common.FieldType> finalTypes = new HashMap<>();
        // 遍历所有最终输出的字段
        for (String fieldName : finalFieldKeys) {
            // 获取该字段的聚合提示，没有则用默认值
            FlinkAggregationType hint = aggregationHints.getOrDefault(fieldName, DEFAULT_AGG_HINT);
            Common.FieldType type;
            // 根据 Flink 聚合类型映射到 TSDB 字段类型
            switch (hint) {
                case SUM:
                    // 对于 TSDB, SUM 类型通常表示累加器/计数器
                    type = Common.FieldType.SUM;
                    break;
                case COUNT:
                    // COUNT 本质上也是一种累加计数，映射到 TSDB SUM 类型
                    type = Common.FieldType.SUM;
                    break;
                case AVG:
                case MIN:
                case MAX:
                case FIRST:
                case LAST:
                case RANGE: // RANGE 结果也视为一个 GAUGE 值
                default:
                    // 其他聚合类型 (AVG, MIN, MAX, FIRST, LAST) 的结果代表窗口结束时的一个状态值/快照值，
                    // 映射到 TSDB GAUGE 类型
                    type = Common.FieldType.GAUGE;
                    break;
            }
            finalTypes.put(fieldName, type); // 存储字段名到 TSDB 类型的映射
        }
        return finalTypes; // 返回完整的类型映射 Map
    }

    /**
     * 创建 {@link TSDBPoint} 对象，并使用 Flink 的 Collector 将其发送到下游算子。
     *
     * @param key              当前 Flink Key。
     * @param window           当前触发计算的时间窗口对象。
     * @param database         数据库名。
     * @param measurement      指标名。
     * @param tags             分组标签 Map。
     * @param aggregatedFields 包含最终聚合结果的字段 Map。
     * @param finalFieldTypes  包含最终字段对应 TSDB 类型的 Map。
     * @param out              Flink 结果收集器。
     */
    private void buildAndCollectTsdbPoint(String key, TimeWindow window,
                                          String database, String measurement, Map<String, String> tags,
                                          Map<String, Object> aggregatedFields,
                                          Map<String, Common.FieldType> finalFieldTypes,
                                          Collector<TSDBPoint> out) {
        //  获取窗口【开始】时间戳作为 TSDBPoint 的时间戳
        long windowEndTimestamp = window.getStart();

        // 此处 database 和 measurement 在初始化时已校验过非空，理论上不需要再次检查
        // 但保留一个断言或日志可能有助于调试意外情况
        // assert database != null && !database.isEmpty();
        // assert measurement != null && !measurement.isEmpty();

        // 创建 TSDBPoint 实例
        TSDBPoint tsdbPoint = new TSDBPoint(
                database,           // 数据库名
                measurement,        // 指标名
                windowEndTimestamp, // 时间戳 (窗口结束时间)
                tags,               // 标签 Map
                aggregatedFields,   // 聚合字段 Map
                finalFieldTypes     // 字段类型 Map
        );

        // 明确设置时间单位为毫秒 (虽然构造函数可能默认，但显式设置更清晰)
        tsdbPoint.setTimeUnit(TimeUnit.MILLISECONDS);

        // 使用收集器发送到下游
        out.collect(tsdbPoint);

        // 性能考虑：避免在可能的 高 TPS 路径记录常规日志
        // log.debug("已收集 Key: '{}', 窗口结束时间: {} 的 TSDBPoint。", key, windowEndTimestamp);
    }
}