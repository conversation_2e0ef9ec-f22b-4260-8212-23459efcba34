package com.databuff.engine.metric.fun;

import com.databuff.common.model.DCSpan;
import com.databuff.engine.metric.model.ComponentSpan;
import com.databuff.engine.util.TraceUtil;

import java.util.Map;

import static com.databuff.common.constants.Constant.Trace.TRACE_OUT_NAMES_HTTP;
import static com.databuff.common.constants.TSDBIndex.TSDB_METRIC_HTTP_TABLE_NAME;
import static com.databuff.engine.constant.MetricName.TAGS_COMPONENT_HTTP_CLIENT;

public class HttpClientComponentMatcher extends ComponentBaseMatcher {
    @Override
    public boolean match(DCSpan span, Map<String, String> meta) {
        return TRACE_OUT_NAMES_HTTP.contains(span.getName());
    }

    @Override
    public boolean initTagAndFields(ComponentSpan componentSpan, DCSpan span, Map<String, String> meta) {
        TraceUtil.initOutServiceAndInstance(componentSpan, span);
        //缓存远程调用
        fillRemotelySvcTag(componentSpan.getTags(), meta,span.getName());
        if (componentSpan.getService() == null) {
            return false;
        }
        componentSpan.setIsOut(1);
        fillHttpUrlTag(componentSpan.getTags(), span, meta);
        //慢调用次数 （这里的慢+非常慢） 非常慢调用次数 （非常慢）
        fillSlowCntMetric(componentSpan, span);
        fillBodyLengthMetric(componentSpan, span.getMetrics());
        return true;
    }

    @Override
    public Map<String, String> getOtelMetricTags() {
        return TAGS_COMPONENT_HTTP_CLIENT;
    }

    @Override
    public String getMetricName() {
        return TSDB_METRIC_HTTP_TABLE_NAME;
    }

}
