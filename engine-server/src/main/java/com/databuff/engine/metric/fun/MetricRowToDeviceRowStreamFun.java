package com.databuff.engine.metric.fun;

import com.alibaba.fastjson.JSONObject;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

/**
 * @author:TianMing
 * @date: 2022/6/22
 * @time: 20:26
 */
public class MetricRowToDeviceRowStreamFun extends RichFlatMapFunction<JSONObject, Row> {
    @Override
    public void flatMap(JSONObject deviceMetric, Collector<Row> collector) throws Exception {

        JSONObject tag = deviceMetric.getJSONObject("tag");

        deviceMetric.remove("tag");

        deviceMetric.putAll(tag);

        String measurement = deviceMetric.getString("databuff_measurement");

        JSONObject fields = deviceMetric.getJSONObject("fields");
        for (String key : fields.keySet()) {
            String metric = measurement + "." + key;
            Double metricsVal = fields.getDouble(key);
            Row deviceMetricRow = new Row(22);
            deviceMetricRow.setField(0,deviceMetric.getString("apiKey"));
            deviceMetricRow.setField(1,deviceMetric.getString("host_id"));
            deviceMetricRow.setField(2,metric);
            deviceMetricRow.setField(3,metricsVal);
            deviceMetricRow.setField(4,deviceMetric.getLong("timestamp"));
            deviceMetricRow.setField(5,deviceMetric.getString("device_type"));
            deviceMetricRow.setField(6,deviceMetric.getString("interface"));
            deviceMetricRow.setField(7,deviceMetric.getString("interface_alias"));
            deviceMetricRow.setField(8,deviceMetric.getString("manufacturer"));
            deviceMetricRow.setField(9,deviceMetric.getString("model_number"));
            deviceMetricRow.setField(10,deviceMetric.getString("product_name"));
            deviceMetricRow.setField(11,deviceMetric.getString("sn_number"));
            deviceMetricRow.setField(12,deviceMetric.getString("snmp_device"));
            deviceMetricRow.setField(13,deviceMetric.getString("snmp_host"));
            deviceMetricRow.setField(14,deviceMetric.getString("snmp_profile"));
            deviceMetricRow.setField(15,deviceMetric.getString("sys_location"));
            deviceMetricRow.setField(16,deviceMetric.getString("sys_objectid"));
            deviceMetricRow.setField(17,deviceMetric.getString("sys_services"));
            deviceMetricRow.setField(18,deviceMetric.getString("loader"));
            deviceMetricRow.setField(19,deviceMetric.getString("host"));
            deviceMetricRow.setField(20,deviceMetric.getString("interface_mac"));
            deviceMetricRow.setField(21,deviceMetric.getString("tags"));

            collector.collect(deviceMetricRow);

        }

    }
}
