package com.databuff.engine.metric.model;

import com.databuff.entity.rum.web.IRumData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 解析span 给后续的页面 与操作使用
 * <AUTHOR>
 * @date 2024/07/24
 */
@Data
public class RumSpan implements IRumData {

    //避免双流 所以指标数据也用同个对象 但不是真的ajax span数据
    @ApiModelProperty(value = "是否为指标数据")
    private boolean isMetricData = false;


    @ApiModelProperty(value = "请求类型: AJAX, STATIC, or null for metric data")
    private RequestType requestType;

    public enum RequestType {
        AJAX,
        STATIC
    }

    @ApiModelProperty(value = "应用id")
    private Integer appId;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "用户ID", required = false)
    private String userId;

    @ApiModelProperty(value = "公网IP", required = false)
    private String ip;

    @ApiModelProperty(value = "运营商", required = false)
    private String isp;

    @ApiModelProperty(value = "浏览器", required = false)
    private String browser;

    @ApiModelProperty(value = "操作系统", required = false)
    private String operatingSystem;

    @ApiModelProperty(value = "地域", required = false)
    private String region;

    @ApiModelProperty(value = "探针版本", required = false)
    private String probeVersion;

    @ApiModelProperty(value = "User Agent", required = false)
    private String userAgent;

    @ApiModelProperty("用户 apikey")
    private String dfApiKey;

    @ApiModelProperty("租户 api key id")
    private Integer dfApiKeyId;

    //以下的不是resource attributes内的共用属性
    @ApiModelProperty(value = "traceId")
    private Long traceId;
    @ApiModelProperty(value = "操作id")
    private Long actionId;
    @ApiModelProperty(value = "spanId")
    private Long spanId;


}
