package com.databuff.engine.metric.fun;


import com.databuff.engine.metric.parser.RumWebDataSplitter;
import com.databuff.engine.metric.parser.RumWebUnifiedParser;
import com.databuff.entity.rum.ResourceLog;
import com.databuff.entity.rum.ResourceSpan;
import com.databuff.entity.rum.web.IRumData;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.functions.co.CoProcessFunction;
import org.apache.flink.util.Collector;

import java.util.List;

@Slf4j
public class UnifiedRumWebCoProcessFunction extends CoProcessFunction<ResourceSpan, ResourceLog, IRumData> {

    @Override
    public void processElement1(ResourceSpan value, Context ctx, Collector<IRumData> out) throws Exception {
        try {
            // 1) parse
            List<IRumData> resultList = RumWebUnifiedParser.parseSpan(value);

            // 2) 直接写 StarRocks + 侧输出
            for (IRumData data : resultList) {
                // (1) 发到主流 -> 下游 addSink
                out.collect(data);

                RumWebDataSplitter.splitAll(data, ctx);
            }
        } catch (Exception e) {
            log.error("Failed to process ResourceSpan: {}", value, e);
        }
    }

    @Override
    public void processElement2(ResourceLog value, Context ctx, Collector<IRumData> out) throws Exception {
        try {
            List<IRumData> resultList = RumWebUnifiedParser.parseLog(value);

            for (IRumData data : resultList) {
                out.collect(data);
                RumWebDataSplitter.splitAll(data, ctx);
            }
        } catch (Exception e) {
            log.error("Failed to process ResourceLog: {}", value, e);
        }
    }
}