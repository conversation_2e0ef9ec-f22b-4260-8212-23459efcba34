package com.databuff.engine.npm.model;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * @author:TianMing
 * @date: 2023/11/7
 * @time: 14:00
 */
@Data
public class NpmStats {
    private String uid;
    private JSONObject npmStats = new JSONObject();
    private Instant startTime;
    public NpmStats(){

    }
    public NpmStats(Instant startTime, String uid, JSONObject npmStats) {
        this.startTime = startTime;
        this.uid = uid;
        this.npmStats = npmStats;
    }
}
