package com.databuff.engine.npm.fun;

import lombok.Data;

import java.util.Map;

@Data
public class NpmFieldValueAccumulator {

    private Map<String, Double> data;

    public void add(Map<String, Double> fields) {
        if (fields == null) {
            return;
        }
        if (data == null) {
            data = fields;
        } else {
            for (Map.Entry<String, Double> entry : fields.entrySet()) {
                Double old = data.get(entry.getKey());
                if (old == null) {
                    old = 0.0;
                }
                data.put(entry.getKey(), old + entry.getValue());
            }
        }
    }

    public void merge(NpmFieldValueAccumulator accumulator) {
        add(accumulator.data);
    }
}
