package com.databuff.engine.resource.fun;

import com.databuff.common.ringbuffer.RingBufferMonitor;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.engine.util.FlinkOtelMetricUtil;
import com.databuff.sink.ringbuffer.Event;
import com.databuff.sink.ringbuffer.OlapWriterHandler;
import com.lmax.disruptor.LiteBlockingWaitStrategy;
import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.TimeoutException;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import com.lmax.disruptor.util.DaemonThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static com.databuff.engine.constant.MetricName.JOB_NAME;

@Slf4j
public class OlapRich2SinkFunction<T> extends RichSinkFunction<T> {

    private String fePorts;
    private String user;
    private String password;
    private String jobName;
    private String option;
    private Disruptor<Event> disruptor;
    private int sleepTimeNs;
    private int flushInterval;
    private long flushMaxSize;
    private long initBufferSize;
    private int maxPending;
    private int connectionRequestTimeout;
    private String maxFilterRatio;
    protected RingBuffer<Event> olapWriterEventRingBuffer;
    private RingBufferMonitor ringBufferMonitor;
    private Map<String, String> tags = new HashMap<>();

    private ScheduledExecutorService executorService;
    private OlapWriterHandler olapWriterHandler;

    public OlapRich2SinkFunction(String fePorts, String user, String password, String jobName, String option, String maxFilterRatio) {
        this.fePorts = fePorts;
        this.user = user;
        this.password = password;
        this.jobName = jobName;
        this.option = option;
        this.flushMaxSize = 200 * 1024 * 1024;
        this.initBufferSize = 5 * 1024 * 1024;
        this.maxPending = 20;
        this.connectionRequestTimeout = 30000;
        this.sleepTimeNs = 5000;
        this.flushInterval = 30 * 1000;
        this.tags.put(JOB_NAME, jobName);
        this.maxFilterRatio = maxFilterRatio;
    }

    public OlapRich2SinkFunction(int interval, int random, long size, int initSize, String fePorts, String user, String password, String jobName, String option, String maxFilterRatio, int maxPending, int connectionRequestTimeout) {
        this.fePorts = fePorts;
        this.user = user;
        this.password = password;
        this.jobName = jobName;
        this.option = option;
        this.flushMaxSize = size * 1024 * 1024;
        this.initBufferSize = initSize * 1024 * 1024;
        this.maxPending = maxPending;
        this.connectionRequestTimeout = connectionRequestTimeout;
        this.sleepTimeNs = 5000;
        this.flushInterval = (interval + new Random().nextInt(random)) * 1000;
        this.tags.put(JOB_NAME, jobName);
        this.maxFilterRatio = maxFilterRatio;
    }

    public OlapRich2SinkFunction(int interval, int random, long size, String fePorts, String user, String password, String jobName, String option, String maxFilterRatio) {
        this.fePorts = fePorts;
        this.user = user;
        this.password = password;
        this.jobName = jobName;
        this.option = option;
        this.flushMaxSize = size * 1024 * 1024;
        this.initBufferSize = 10 * 1024 * 1024;
        this.maxPending = 30;
        this.connectionRequestTimeout = 30000;
        this.sleepTimeNs = 5000;
        this.flushInterval = (interval + new Random().nextInt(random)) * 1000;
        this.tags.put(JOB_NAME, jobName);
        this.maxFilterRatio = maxFilterRatio;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        FlinkOtelMetricUtil.initOtelMetric(getRuntimeContext());
        Disruptor<Event> disruptor = new Disruptor<>(Event::new, 64 * 1024, DaemonThreadFactory.INSTANCE, ProducerType.MULTI, new LiteBlockingWaitStrategy());
        this.disruptor = disruptor;
        this.olapWriterHandler = new OlapWriterHandler(fePorts, user, password, jobName + "-" + getRuntimeContext().getIndexOfThisSubtask(),
                this.flushInterval, this.flushMaxSize, this.initBufferSize, this.maxFilterRatio, this.maxPending, this.connectionRequestTimeout);
        disruptor.handleEventsWith(this.olapWriterHandler);
        olapWriterEventRingBuffer = disruptor.start();

        ringBufferMonitor = new RingBufferMonitor(disruptor.getRingBuffer(), jobName + "-" + option + "-" + getRuntimeContext().getIndexOfThisSubtask() + "-olapWriter");
        ringBufferMonitor.startMonitoring(5000);

        executorService = Executors.newScheduledThreadPool(1);
        executorService.scheduleAtFixedRate(() -> {
            try {
                olapWriterEventRingBuffer.publishEvent((event, sequence) -> {
                    event.setClock(true);
                });
            } catch (Exception e) {
                // 处理异常
                log.error("publishEvent error", e);
                OtelMetricUtil.logException("publishEvent", e);
            }
        }, 0, 1, TimeUnit.SECONDS);
    }

    public Map<String, String> getTags() {
        return tags;
    }

    @Override
    public void close() throws Exception {
        long startTime = System.currentTimeMillis();
        log.info("Starting to close OlapRich2SinkFunction");

        try {
            // 1. 停止定时任务
            if (executorService != null) {
                executorService.shutdown();
                try {
                    if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                        executorService.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    executorService.shutdownNow();
                    Thread.currentThread().interrupt();
                }
                executorService = null;
            }

            // 2. 发送shutdown事件并等待处理完成
            if (olapWriterHandler != null && olapWriterEventRingBuffer != null) {
                try {
                    // 同步发送shutdown事件
                    olapWriterEventRingBuffer.publishEvent((event, sequence) -> {
                        event.setShutdown(true);
                    });

                    // 等待处理完成
                    boolean completed = olapWriterHandler.waitForShutdown(30000);
                    if (!completed) {
                        log.warn("RingBuffer processing did not complete within timeout period");
                    }

                    // 3. 关闭Disruptor
                    try {
                        // 先尝试优雅关闭
                        disruptor.shutdown(20, TimeUnit.SECONDS);
                    } catch (TimeoutException e) {
                        log.warn("Disruptor graceful shutdown timed out, forcing shutdown", e);
                        // 如果优雅关闭失败，强制关闭
                        disruptor.halt();
                    }
                } catch (Exception e) {
                    log.error("Error during shutdown sequence", e);
                    // 如果发生错误，确保Disruptor被关闭
                    if (disruptor != null) {
                        disruptor.halt();
                    }
                }
            }

            // 4. 关闭监控
            if (ringBufferMonitor != null) {
                try {
                    ringBufferMonitor.shutdown();
                } catch (Exception e) {
                    log.warn("Error shutting down ringBufferMonitor", e);
                }
                ringBufferMonitor = null;
            }

            // 5. 关闭OlapWriterHandler及其资源
            if (olapWriterHandler != null) {
                olapWriterHandler.shutdown();
                olapWriterHandler = null;
            }

            // 6. 清理其他资源
            olapWriterEventRingBuffer = null;
            disruptor = null;

            // 7. 清理Netty资源
            cleanupNettyResources();

            log.info("Finished closing OlapRich2SinkFunction, took {} ms",
                    System.currentTimeMillis() - startTime);

        } catch (Exception e) {
            log.error("Critical error during OlapRich2SinkFunction shutdown", e);
            throw e;
        } finally {
            try {
                super.close();
            } catch (Exception e) {
                log.error("Error in super.close()", e);
                throw e;
            }
        }
    }

    private void cleanupNettyResources() {
        try {
            // 不要禁用资源泄漏检测，反而可以提高级别来检查问题
            // io.netty.util.ResourceLeakDetector.setLevel(io.netty.util.ResourceLeakDetector.Level.DISABLED);

            // 如果希望严格检测资源泄漏，可以设置为PARANOID:
            io.netty.util.ResourceLeakDetector.setLevel(io.netty.util.ResourceLeakDetector.Level.PARANOID);

            // 移除FastThreadLocal中缓存的变量 (可选）
            io.netty.util.concurrent.FastThreadLocal.removeAll();

            // 如果有使用全局事件执行器（GlobalEventExecutor），可优雅关闭
            if (!io.netty.util.concurrent.GlobalEventExecutor.INSTANCE.isShuttingDown()) {
                io.netty.util.concurrent.GlobalEventExecutor.INSTANCE.shutdownGracefully(100, 200, TimeUnit.MILLISECONDS);
            }

            // 不要尝试通过反射销毁directArena
            // Netty的内存管理是自动完成的。
            // 只需确保引用计数的Buf都已释放，客户端或频道都已关闭。
        } catch (Exception e) {
            log.warn("Error during Netty cleanup", e);
        }
    }

}
