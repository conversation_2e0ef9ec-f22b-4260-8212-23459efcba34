package com.databuff.engine.constant;

import java.util.HashMap;
import java.util.Map;

public class MetricName {

    /**
     * flink to es
     */
    public static final String KAFKA_CONSUME = "kafka.consume";
    public static final String KAFKA_DELAY = "kafka.delay";
    public static final String DATA_DELAY = "data.delay";
    public static final String TSDB_SEND = "moredb.send";
    public static final String COMPONENT_DISCARD = "component.discard";
    public static final String TRACE_NO_ROOT = "trace.no.root";

    public static final String JOB_NAME = "jobName";

    public static final String JOB_NAME_TRACE_ANALYSE = "TraceAnalyse";
    public static final String JOB_NAME_TRACE_ANALYSE_FOR_FILL = "TraceAnalyseForFill";
    public static final String JOB_NAME_TRACE_COLLECTOR_ANALYSE = "TraceCollectorAnalyse";
    public static final String JOB_NAME_NPM_STATS_ANALYSE = "NpmStatsAnalyse";
    public static final String JOB_NAME_METRIC_ANALYSE = "MetricAnalyse";
    public static final String JOB_NAME_OTHER_DATA_ANALYSE = "OtherDataAnalyse";
    public static final String JOB_NAME_RUM_ANALYSE = "RumAnalyse";
    public static final String JOB_NAME_RUM_IOS_ANALYSE = "RumIosAnalyse";
    public static final String JOB_NAME_RUM_ANDROID_ANALYSE = "RumAndroidAnalyse";
    public static final String JOB_NAME_PROCESS_ANALYSE = "ProcessAnalyse";
    public static final String JOB_NAME_GENERIC_METRIC_ANALYSE = "GenericMetricAnalyse";

    public static final Map<String, String> TAGS_NPM_STATS_ANALYSE = initJobTags(JOB_NAME_NPM_STATS_ANALYSE);
    public static final Map<String, String> TAGS_TRACE_ANALYSE = initJobTags(JOB_NAME_TRACE_ANALYSE);
    public static final Map<String, String> TAGS_TRACE_ANALYSE_FOR_FILL = initJobTags(JOB_NAME_TRACE_ANALYSE_FOR_FILL);
    public static final Map<String, String> TAGS_OTHER_DATA_ANALYSE = initJobTags(JOB_NAME_OTHER_DATA_ANALYSE);
    public static final Map<String, String> TAGS_RUM_ANALYSE = initJobTags(JOB_NAME_RUM_ANALYSE);
    public static final Map<String, String> TAGS_RUM_IOS_ANALYSE = initJobTags(JOB_NAME_RUM_IOS_ANALYSE);
    public static final Map<String, String> TAGS_RUM_ANDROID_ANALYSE = initJobTags(JOB_NAME_RUM_ANDROID_ANALYSE);
    public static final Map<String, String> TAGS_PROCESS_ANALYSE = initJobTags(JOB_NAME_PROCESS_ANALYSE);
    public static final Map<String, String> TAGS_GENERIC_METRIC_ANALYSE = initJobTags(JOB_NAME_GENERIC_METRIC_ANALYSE);

    public static final String TAG_COMPONENT = "component";
    public static final String TAG_VALUE_COMPONENT_DB = "db";
    public static final String TAG_VALUE_COMPONENT_REDIS = "redis";
    public static final String TAG_VALUE_COMPONENT_CONFIG = "config";
    public static final String TAG_VALUE_COMPONENT_REMOTE = "remote";
    public static final String TAG_VALUE_COMPONENT_ELASTIC = "elastic";
    public static final String TAG_VALUE_COMPONENT_HTTP_SERVER = "httpServer";
    public static final String TAG_VALUE_COMPONENT_HTTP_CLIENT = "httpClient";
    public static final String TAG_VALUE_COMPONENT_RPC_SERVER = "rpcServer";
    public static final String TAG_VALUE_COMPONENT_RPC_CLIENT = "rpcClient";
    public static final String TAG_VALUE_COMPONENT_MQ_SERVER = "mqServer";
    public static final String TAG_VALUE_COMPONENT_MQ_PRODUCER = "mqProducer";
    public static final String TAG_VALUE_DB_POOL_GET = "dbPoolGet";
    public static final String TAG_VALUE_HTTP_POOL_GET = "httpPoolGet";
    public static final String TAG_VALUE_OBJ_POOL_GET = "objectPoolGet";

    public static final Map<String, String> TAGS_COMPONENT_REMOTE = initTags(TAG_COMPONENT, TAG_VALUE_COMPONENT_REMOTE);
    public static final Map<String, String> TAGS_COMPONENT_CONFIG = initTags(TAG_COMPONENT, TAG_VALUE_COMPONENT_CONFIG);
    public static final Map<String, String> TAGS_COMPONENT_ELASTIC = initTags(TAG_COMPONENT, TAG_VALUE_COMPONENT_ELASTIC);
    public static final Map<String, String> TAGS_COMPONENT_DB = initTags(TAG_COMPONENT, TAG_VALUE_COMPONENT_DB);
    public static final Map<String, String> TAGS_COMPONENT_REDIS = initTags(TAG_COMPONENT, TAG_VALUE_COMPONENT_REDIS);
    public static final Map<String, String> TAGS_COMPONENT_HTTP_SERVER = initTags(TAG_COMPONENT, TAG_VALUE_COMPONENT_HTTP_SERVER);
    public static final Map<String, String> TAGS_COMPONENT_HTTP_CLIENT = initTags(TAG_COMPONENT, TAG_VALUE_COMPONENT_HTTP_CLIENT);
    public static final Map<String, String> TAGS_COMPONENT_RPC_SERVER = initTags(TAG_COMPONENT, TAG_VALUE_COMPONENT_RPC_SERVER);
    public static final Map<String, String> TAGS_COMPONENT_RPC_CLIENT = initTags(TAG_COMPONENT, TAG_VALUE_COMPONENT_RPC_CLIENT);
    public static final Map<String, String> TAGS_COMPONENT_MQ_SERVER = initTags(TAG_COMPONENT, TAG_VALUE_COMPONENT_MQ_SERVER);
    public static final Map<String, String> TAGS_COMPONENT_MQ_PRODUCER = initTags(TAG_COMPONENT, TAG_VALUE_COMPONENT_MQ_PRODUCER);
    public static final Map<String, String> TAGS_DB_POOL_GET = initTags(TAG_COMPONENT, TAG_VALUE_DB_POOL_GET);
    public static final Map<String, String> TAGS_HTTP_POOL_GET = initTags(TAG_COMPONENT, TAG_VALUE_HTTP_POOL_GET);
    public static final Map<String, String> TAGS_OBJ_POOL_GET = initTags(TAG_COMPONENT, TAG_VALUE_OBJ_POOL_GET);

    public static Map<String, String> initJobTags(String jobName) {
        return initTags(JOB_NAME, jobName);
    }

    public static Map<String, String> initTags(String key, String value) {
        Map<String, String> tags = new HashMap<>();
        tags.put(key, value);
        return tags;
    }
}
