package com.databuff.engine.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.flink.types.Row;

import java.util.*;

/**
 * @author:TianMing
 * @date: 2022/7/22
 * @time: 14:08
 */
public class MetricDataUtil {


    public static JSONObject getRow2Json(Row row) {
        JSONObject metric = new JSONObject();
        Object metricObj = row.getField("metric");
        Object metricsValObj = row.getField("metricsVal");
        Object intervalObj = row.getField("interval");
        Object apikeyObj = row.getField("apiKey");
        Object typeObj = row.getField("type");
        Object collectorObj = row.getField("collector");
        Object hostIdObj = row.getField("host_id");
        Object collectorTypeObj = row.getField("collectorType");
        Object hostObj = row.getField("host");
        Object tagObj = row.getField("tag");
        Object tsObj = row.getField("timestamp");
        metric.put("metric", metricObj.toString());
        metric.put("metricsVal", Double.parseDouble(metricsValObj.toString()));
        long timestamp = Long.parseLong(tsObj.toString())*1000 ;
        metric.put("timestamp",timestamp);
        Map<String, String> tags = new HashMap<>(8);
        tags.put("apiKey", apikeyObj.toString());
        if (hostIdObj!=null){
            tags.put("host_id", hostIdObj.toString());
        }
        if (hostObj!=null){
            tags.put("host", hostObj.toString());
        }
        if (intervalObj!=null){
            metric.put("interval", intervalObj.toString());
        }else{
            metric.put("interval",0);
        }
        if (typeObj!=null){
            tags.put("metricType", typeObj.toString());
        }
        if (collectorObj!=null){
            tags.put("collector", collectorObj.toString());
        }
        if (collectorTypeObj!=null){
            tags.put("collectorType", collectorTypeObj.toString());
        }
        HashMap<String,Object> tag = (HashMap)tagObj;
        for (Map.Entry<String,Object> entry:tag.entrySet()){
            tags.put(entry.getKey(), entry.getValue().toString());
        }
        metric.put("tag", tags);

        return metric;
    }


    public static JSONObject getRow2EsJson(Row row) {
        JSONObject metric = new JSONObject();
        Set<String> fieldNames = row.getFieldNames(true);
        if (fieldNames==null){
            return metric;
        }
        for (String name : fieldNames){
            Object obj = row.getField(name);
            metric.put(name,obj);
        }
        Object tagObj = row.getField("tag");
        HashMap<String,Object> tag = (HashMap)tagObj;
        List<String> tags = new ArrayList<>(tag.size());
        for (Map.Entry<String,Object> entry:tag.entrySet()){
            tags.add(entry.getKey()+":"+ entry.getValue().toString());
        }
        metric.put("tags", tags);
        return metric;
    }
}
