package com.databuff.engine.util;

import org.apache.flink.runtime.state.KeyGroupRangeAssignment;

import java.util.HashMap;
import java.util.Map;

public class KeyBalanceUtil {

    private static volatile Integer[] balanceKeys;
    private static volatile int parallelism;

    public static void init(int currentParallelism) {
        if (parallelism == 0) {
            synchronized (KeyBalanceUtil.class) {
                if (parallelism == 0) {
                    balanceKeys = createBalanceKeys(currentParallelism);
                    parallelism = currentParallelism;
                }
            }
        }
    }

    public static Integer[] createBalanceKeys(int parallelism) {
        int maxParallelism = KeyGroupRangeAssignment.computeDefaultMaxParallelism(parallelism);
        int maxRandomKey = parallelism * 12;
        Map<Integer, Integer> key_subIndex_map = new HashMap<>();
        for (int randomKey = 0; randomKey < maxRandomKey; randomKey++) {
            int subtaskIndex = KeyGroupRangeAssignment.assignKeyToParallelOperator(randomKey, maxParallelism, parallelism);
            if (key_subIndex_map.keySet().contains(subtaskIndex))
                continue;
            key_subIndex_map.put(subtaskIndex, randomKey);
        }
        return key_subIndex_map.values().toArray(new Integer[key_subIndex_map.size()]);
    }

    public static Integer mappingKey(int key) {
        if (parallelism == 0) {
            return 0;
        }
        return balanceKeys[key % parallelism];
    }

}
