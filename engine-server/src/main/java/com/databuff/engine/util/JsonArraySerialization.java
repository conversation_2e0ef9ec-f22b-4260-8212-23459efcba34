package com.databuff.engine.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.flink.api.common.serialization.SerializationSchema;

/**
 * Author zlh
 * Date 2020/3/25
 */
public class JsonArraySerialization implements SerializationSchema<JSONArray> {
    @Override
    public byte[] serialize(JSONArray jsonArray) {
        return JSONArray.toJSONBytes(jsonArray, SerializerFeature.DisableCircularReferenceDetect);
    }
}
