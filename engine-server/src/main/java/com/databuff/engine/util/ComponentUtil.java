package com.databuff.engine.util;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.constants.OutConfig;
import com.databuff.common.model.DCSpan;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.common.utils.RedisKeyUtil;
import com.databuff.common.utils.ServiceUtil;
import com.databuff.engine.metric.fun.*;
import com.databuff.engine.metric.model.BusinessComponentSpan;
import com.databuff.engine.metric.model.ComponentSpan;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.util.OutputTag;

import java.nio.charset.StandardCharsets;
import java.util.*;

import static com.databuff.common.constants.Constant.Trace.*;
import static com.databuff.common.constants.TSDBIndex.*;
import static com.databuff.engine.constant.MetricName.COMPONENT_DISCARD;

@Slf4j
public class ComponentUtil {

    private static final List<ComponentMatcher> COMPONENT_MATCHERS = Arrays.asList(
            new RemoteCallComponentMatcher(), new ConfigComponentMatcher(),
            new DBComponentMatcher(), new RedisComponentMatcher(),
            new HttpClientComponentMatcher(), new HttpServerComponentMatcher(),
            new MqConsumerComponentMatcher(), new MqProducerComponentMatcher(),
            new RpcClientComponentMatcher(), new RpcServerComponentMatcher(),
            new ESComponentMatcher());

    private static final TreeMap<Long, String> DURATION_TAGS = initDurationTags();
    private static final List<ComponentMatcher> POOL_GET_MATCHERS = Arrays.asList(new DbPoolGetMatcher(), new ObjectPoolGetMatcher(), new HttpPoolGetMatcher());

    private static TreeMap<Long, String> initDurationTags() {
        TreeMap<Long, String> tmp = new TreeMap<>();
        tmp.put(0L, "0ms-10ms");
        tmp.put(10000000L, "10ms-100ms");
        tmp.put(100000000L, "100ms-500ms");
        tmp.put(500000000L, "500ms-1s");
        tmp.put(1000000000L, "1s-10s");
        tmp.put(10000000000L, "10s-n");
        return tmp;
    }

    public static List<ComponentSpan> parseDCSpan(DCSpan spanObj, List<BusinessComponentSpan> businessComponentSpans) {
        List<ComponentSpan> list = new ArrayList<>();
        try {
            Map<String, String> meta = spanObj.getMeta();
            if (meta == null) {
                return list;
            }
            String apiKey = spanObj.getDfApiKey();
            if (apiKey == null) {
                return list;
            }
            //异常指标统计
            logExceptionMetricIfNecessary(spanObj, meta, apiKey, list);
            if (poolGetMetricIfNecessary(spanObj, meta, apiKey, list)) {
                //如果生成获取池指标，不需要后续，直接跳过
                return list;
            }
            //正常已经支持的组件匹配生成指标
            boolean isMatch = componentMetric(spanObj, meta, apiKey, list, businessComponentSpans);
            Integer isIn = spanObj.getIsIn().intValue();
            Integer isOut = spanObj.getIsOut();

            if (!isMatch && (isIn == 1 || isOut == 1)) {
                //不在上面的组件中，归属到其它类指标
                otherMetric(spanObj, isIn, isOut, apiKey, list, businessComponentSpans);
            }
        } catch (Throwable e) {
            OtelMetricUtil.logException("ComponentJsonToSpanFunction", e);
        }
        return list;
    }

    /**
     * 连接池获取指标
     *
     * @param spanObj
     * @param meta
     * @param apiKey
     */
    private static boolean poolGetMetricIfNecessary(DCSpan spanObj, Map<String, String> meta, String apiKey, List<ComponentSpan> list) {
        try {
            for (ComponentMatcher matcher : POOL_GET_MATCHERS) {
                if (matcher.match(spanObj, meta)) {
                    try {
                        ComponentSpan componentSpan = new ComponentSpan();
                        componentSpan.setMetricName(matcher.getMetricName());
                        componentSpan.setStartTime(spanObj.getStartTime());
                        componentSpan.setLongStartTime(spanObj.getLongStartTime());
                        componentSpan.setApiKey(apiKey);
                        componentSpan.setCnt(1);
                        componentSpan.setSlow(null == spanObj.getSlow() ? 0 : spanObj.getSlow());
                        componentSpan.setError(spanObj.getError());
                        long duration = spanObj.getDuration();
                        componentSpan.setDuration(duration);
                        if (!matcher.initTagAndFields(componentSpan, spanObj, meta)) {
                            OtelMetricUtil.logCounter(COMPONENT_DISCARD, matcher.getOtelMetricTags(), 1);
                            break;
                        }
                        //根据上个span信息匹配出池的类型
                        String poolComponentType = "";
                        for (ComponentMatcher typeMatcher : COMPONENT_MATCHERS) {
                            DCSpan lastSpan = new DCSpan();
                            lastSpan.setServerService(spanObj.getService());
                            lastSpan.setName(meta.getOrDefault("lastSpanName", ""));
                            if (typeMatcher.match(lastSpan, meta)) {
                                poolComponentType = typeMatcher.getMetricName();
                                break;
                            }
                        }
                        Map<String, String> tags = componentSpan.getTags();
                        fillRootResourceTag(tags, meta);
                        tags.put("poolComponentType", poolComponentType);
                        list.add(componentSpan);
                        return true;
                    } catch (Throwable e) {
                        OtelMetricUtil.logException("PoolMatcher", e);
                    }
                }
            }
        } catch (Throwable e) {
            OtelMetricUtil.logException("logExceptionMetric", e);
        }
        return false;
    }


    private static void logExceptionMetricIfNecessary(DCSpan span, Map<String, String> meta, String apiKey, List<ComponentSpan> list) {
        try {
            String errorType = meta.get("error.type");
            if (errorType == null) {
                return;
            }
            String errorCode = meta.get("error.code");
            if (StringUtils.isEmpty(errorCode)) {
                errorCode = "";
            }
            String name = span.getName();
            ComponentSpan componentSpan = new ComponentSpan();
            componentSpan.setLongStartTime(span.getLongStartTime());
            componentSpan.setMetricName(TSDB_METRIC_EXCEPTION_TABLE_NAME);
            componentSpan.setCntOnly(1);
            componentSpan.setIsOut(span.getIsOut());
            componentSpan.setIsIn(span.getIsIn());
            componentSpan.setSlow(null == span.getSlow() ? 0 : span.getSlow());
            OutConfig outConfig = TRACE_OUT_MIDDLEWARE_CONFIGS.get(name);
            if (outConfig != null) {
                TraceUtil.initServiceAndInstanceFromClient(componentSpan, span);
            } else {
                TraceUtil.initServiceAndInstanceFromServer(componentSpan, span);
            }

            componentSpan.setStartTime(span.getStartTime());
            componentSpan.setLongStartTime(span.getLongStartTime());
            componentSpan.setApiKey(apiKey);
            componentSpan.setCnt(1);
            componentSpan.setError(1);
            Map<String, String> tags = componentSpan.getTags();
            fillExceptionRootResourceTag(tags, meta, span);
            tags.put("exceptionName", errorType);
            tags.put("exceptionCode", errorCode);
            //当前组件服务信息
            tags.put("componentService", span.getService());
            tags.put("componentServiceId", span.getServiceId());
            tags.put("componentServiceInstance", span.getServiceInstance());
            list.add(componentSpan);
        } catch (Throwable e) {
            OtelMetricUtil.logException("logExceptionMetric", e);
        }
    }

    private static void fillExceptionRootResourceTag(Map<String, String> tags, Map<String, String> meta, DCSpan dcSpan) {
        String rootResource = meta.get(ROOT_RESOURCE);
        String rootType = meta.get(ROOT_TYPE);
        String rootName = meta.get(ROOT_NAME);
        if (StringUtils.isNotEmpty(rootResource) && StringUtils.isNotEmpty(rootType)) {
            //有就填充rootResource
            tags.put("rootResource", rootResource);
            tags.put("rootComponentType", TRACE_TYPE_METRIC_NAME_MAP.getOrDefault(rootType, ""));
            return;
        } else if (StringUtils.isNotEmpty(rootResource) && StringUtils.isNotEmpty(rootName)) {
            //没有根据root name填充当前的
            tags.put("rootResource", rootResource);
            if (TRACE_IN_NAMES_HTTP.contains(rootName)) {
                tags.put("rootComponentType", TSDB_METRIC_HTTP_TABLE_NAME);
            } else if (TRACE_IN_NAMES_PRC.contains(rootName)) {
                tags.put("rootComponentType", TSDB_METRIC_RPC_TABLE_NAME);
            } else if (TRACE_IN_NAMES_MQ.contains(rootName)) {
                tags.put("rootComponentType", TSDB_METRIC_MQ_TABLE_NAME);
            } else {
                tags.put("rootComponentType", "");
            }
            return;
        }
        if (1 == dcSpan.getIsIn() && dcSpan.getType() != null) {
            tags.put("rootResource", dcSpan.getResource());
            tags.put("rootComponentType", TRACE_TYPE_METRIC_NAME_MAP.getOrDefault(dcSpan.getType(), ""));
            return;
        }
        tags.put("rootResource", "");
        tags.put("rootComponentType", "");
    }

    private static boolean componentMetric(DCSpan spanObj, Map<String, String> meta, String apiKey, List<ComponentSpan> list, List<BusinessComponentSpan> businessComponentSpans) {
        for (ComponentMatcher matcher : COMPONENT_MATCHERS) {
            if (matcher.match(spanObj, meta)) {
                try {
                    ComponentSpan componentSpan = new ComponentSpan();
                    componentSpan.setLongStartTime(spanObj.getLongStartTime());
                    componentSpan.setMetricName(matcher.getMetricName());
                    componentSpan.setLongStartTime(spanObj.getLongStartTime());
                    componentSpan.setStartTime(spanObj.getStartTime());
                    componentSpan.setApiKey(apiKey);
                    componentSpan.setCnt(1);
                    componentSpan.setSlow(null == spanObj.getSlow() ? 0 : spanObj.getSlow());
                    componentSpan.setError(spanObj.getError());

                    long duration = spanObj.getDuration();
                    componentSpan.setDuration(duration);
                    Map.Entry<Long, String> entry = DURATION_TAGS.floorEntry(duration);
                    componentSpan.getTags().put("durationRange", entry.getValue());

                    if (!matcher.initTagAndFields(componentSpan, spanObj, meta)) {
                        OtelMetricUtil.logCounter(COMPONENT_DISCARD, matcher.getOtelMetricTags(), 1);
                        break;
                    }
                    Map<String, String> tags = componentSpan.getTags();
                    fillRootResourceTag(tags, meta);
                    list.add(componentSpan);

                    collectBusinessComponent(spanObj, componentSpan, businessComponentSpans);
                    return true;
                } catch (Throwable e) {
                    OtelMetricUtil.logException("ComponentMatcher", e);
                }
            }
        }
        return false;
    }

    /**
     * 收集业务组件信息，将DCSpan和ComponentSpan转换为BusinessComponentSpan
     *
     * @param spanObj                原始的DCSpan对象，包含服务调用的基本信息
     * @param componentSpan          组件Span对象，包含组件级别的调用信息
     * @param businessComponentSpans 用于存储生成的业务组件Span的列表
     */
    private static void collectBusinessComponent(DCSpan spanObj, ComponentSpan componentSpan,
                                                 List<BusinessComponentSpan> businessComponentSpans) {
        // 确定源系统和目标系统的业务ID组合字符串
        SystemPair systemPair = determineSystemPair(spanObj);

        // 如果源系统和目标系统都未知，创建一个基本的业务组件Span
        if (systemPair.isBothEmpty() && componentSpan != null) {
            businessComponentSpans.add(createEmptyBusinessComponentSpan(componentSpan, spanObj));
            return;
        }

        // 为每个源-目标对创建BusinessComponentSpan
        createBusinessComponentSpans(systemPair, componentSpan, spanObj, businessComponentSpans);
    }

    /**
     * 确定源系统和目标系统的业务ID组合字符串
     *
     * @param spanObj DCSpan对象，包含服务调用信息
     * @return 包含源系统和目标系统业务ID组合字符串的SystemPair对象
     */
    private static SystemPair determineSystemPair(DCSpan spanObj) {
        List<String> srcCombinedStrings = null;
        List<String> dstCombinedStrings = null;

        // 调用链路: client_service_id -> service_id -> server_service_id
        // 确定源系统(src)和目标系统(dst)的业务ID映射关系
        if (spanObj.getClient_service_id() != null && spanObj.getServiceId() != null) {
            // 场景1: 存在client_service_id和service_id时
            // client_service_id作为源系统(src), service_id作为目标系统(dst)
            srcCombinedStrings = spanObj.getClient_biz_pid_id();
            dstCombinedStrings = spanObj.getBiz_pid_id();
        } else if (spanObj.getServiceId() != null && spanObj.getServer_service_id() != null) {
            // 场景2: 存在service_id和server_service_id时
            // service_id作为源系统(src), server_service_id作为目标系统(dst)
            srcCombinedStrings = spanObj.getBiz_pid_id();
            dstCombinedStrings = spanObj.getServer_biz_pid_id();
        } else if (spanObj.getServiceId() != null) {
            // 场景3: 只存在service_id时,需要根据isIn和isOut判断service_id的角色
            Integer isIn = spanObj.getIsIn() != null ? spanObj.getIsIn() : 0;
            Integer isOut = spanObj.getIsOut();
            if (isIn == 1) {
                // 入口点(Entry point)
                dstCombinedStrings = spanObj.getBiz_pid_id();
            } else if (isOut == 1) {
                // 出口点(Exit point)
                srcCombinedStrings = spanObj.getBiz_pid_id();
            }
        }

        // 转换null为空列表
        List<String> finalSrcCombined = srcCombinedStrings != null ? srcCombinedStrings : Collections.emptyList();
        List<String> finalDstCombined = dstCombinedStrings != null ? dstCombinedStrings : Collections.emptyList();

        return new SystemPair(finalSrcCombined, finalDstCombined);
    }

    /**
     * 创建一个所有业务相关字段为null的基本BusinessComponentSpan
     *
     * @param componentSpan 组件Span对象
     * @param spanObj       DCSpan对象
     * @return 创建的BusinessComponentSpan对象
     */
    private static BusinessComponentSpan createEmptyBusinessComponentSpan(ComponentSpan componentSpan, DCSpan spanObj) {
        BusinessComponentSpan bcs = BusinessComponentSpan.fromComponentSpan(componentSpan);
        bcs.setBiz_internal_call(0);
        bcs.setSub_biz_internal_call(0);
        // 设置所有业务字段为null
        bcs.setSrc_biz_id(null);
        bcs.setDst_biz_id(null);
        bcs.setSrc_biz_pid(null);
        bcs.setDst_biz_pid(null);
        bcs.setDstBusName(null);
        return bcs;
    }

    /**
     * 为每个源-目标对创建BusinessComponentSpan
     *
     * @param systemPair             源系统和目标系统的业务ID组合字符串
     * @param componentSpan          组件Span对象
     * @param spanObj                DCSpan对象
     * @param businessComponentSpans 用于存储生成的业务组件Span的列表
     */
    private static void createBusinessComponentSpans(SystemPair systemPair, ComponentSpan componentSpan,
                                                     DCSpan spanObj, List<BusinessComponentSpan> businessComponentSpans) {
        List<String> finalSrcCombined = systemPair.getSrcCombined();
        List<String> finalDstCombined = systemPair.getDstCombined();
        boolean srcIsEmpty = finalSrcCombined.isEmpty();
        boolean dstIsEmpty = finalDstCombined.isEmpty();

        int srcIterations = srcIsEmpty ? 1 : finalSrcCombined.size();
        int dstIterations = dstIsEmpty ? 1 : finalDstCombined.size();

        for (int i = 0; i < srcIterations; i++) {
            String srcCombined = srcIsEmpty ? null : finalSrcCombined.get(i);
            String[] srcParsed = parsePidIdNameString(srcCombined);

            for (int j = 0; j < dstIterations; j++) {
                String dstCombined = dstIsEmpty ? null : finalDstCombined.get(j);
                String[] dstParsed = parsePidIdNameString(dstCombined);

                // 创建并配置BusinessComponentSpan
                BusinessComponentSpan bcs = createBusinessComponentSpan(
                        componentSpan, spanObj, srcParsed, dstParsed);

                businessComponentSpans.add(bcs);
            }
        }
    }

    /**
     * 创建并配置单个BusinessComponentSpan对象
     *
     * @param componentSpan 组件Span对象
     * @param spanObj       DCSpan对象
     * @param srcParsed     解析后的源系统业务ID信息 [父业务系统id, 业务系统id, 业务系统名称]
     * @param dstParsed     解析后的目标系统业务ID信息 [父业务系统id,, 业务系统id, 业务系统名称]
     * @return 配置好的BusinessComponentSpan对象
     */
    private static BusinessComponentSpan createBusinessComponentSpan(ComponentSpan componentSpan, DCSpan spanObj,
                                                                     String[] srcParsed, String[] dstParsed) {
        String srcBizParentId = srcParsed[0];
        String srcBizId = srcParsed[1];

        String dstBizParentId = dstParsed[0];
        String dstBizId = dstParsed[1];
        String currentDstBusName = dstParsed[2];

        BusinessComponentSpan bcs = BusinessComponentSpan.fromComponentSpan(componentSpan);

        // 设置业务ID和名称
        bcs.setSrc_biz_id(srcBizId);
        bcs.setDst_biz_id(dstBizId);
        bcs.setSrc_biz_pid(srcBizParentId);
        bcs.setDst_biz_pid(dstBizParentId);
        bcs.setDstBusName(currentDstBusName);

        //在查询时，根据需要使用这两个字段来过滤数据。例如：
        //
        //查询业务系统级别的外部调用：WHERE biz_internal_call = 0
        //查询子系统级别的外部调用：WHERE sub_biz_internal_call = 0
        //查询服务级别的所有调用：不需要使用这两个字段进行过滤

        // 设置业务系统和子系统内部调用标志
        bcs.setBiz_internal_call(isSameBizSystem(srcBizParentId, srcBizId, dstBizParentId, dstBizId) ? 1 : 0);
        bcs.setSub_biz_internal_call(isSameSubBizSystem(srcBizId, dstBizId) ? 1 : 0);


        return bcs;
    }

    /**
     * 源系统和目标系统的业务ID组合字符串对
     */
    @Getter
    private static class SystemPair {
        private final List<String> srcCombined;
        private final List<String> dstCombined;

        public SystemPair(List<String> srcCombined, List<String> dstCombined) {
            this.srcCombined = srcCombined;
            this.dstCombined = dstCombined;
        }

        public boolean isBothEmpty() {
            return srcCombined.isEmpty() && dstCombined.isEmpty();
        }
    }

    /**
     * 解析格式为"父业务系统id-业务系统id-编码后的业务系统名称"的字符串。
     *
     * @param combinedString 格式为"pid-id-encodedName"的组合字符串
     * @return 返回一个包含[pid, id, name]的字符串数组
     */
    private static String[] parsePidIdNameString(String combinedString) {
        if (StringUtils.isBlank(combinedString)) {
            return new String[]{null, null, null};
        }

        String[] parts = combinedString.split("-", 3);
        String pid = null;
        String id = null;
        String name = null;

        if (parts.length >= 2) {
            pid = parts[0];
            id = parts[1];

            if (parts.length == 3) {
                String encodedName = parts[2];
                if (!"null".equalsIgnoreCase(encodedName)) {
                    try {
                        // 解码Base64编码的业务名称
                        name = new String(Base64.getDecoder().decode(encodedName), StandardCharsets.UTF_8);
                    } catch (IllegalArgumentException e) {
                        log.warn("解码业务系统名称时出错: {}", e.getMessage());
                        name = encodedName; // 解码失败时使用原始编码字符串
                    }
                }
            }
        } else {
            log.warn("无法将组合字符串'{}'解析为至少包含pid-id的格式。", combinedString);
        }

        return new String[]{pid, id, name};
    }


    private static boolean isSameBizSystem(String srcBizParentId, String srcBizId, String dstBizParentId, String dstBizId) {
        // 处理 null 值
        if (srcBizParentId == null || srcBizId == null || dstBizParentId == null || dstBizId == null) {
            return false;
        }
        //目前有 0-9 9-10 9-11  这些都算是同一个顶层业务系统 ,都要算内部调用
        //如果两个父系统id相同 则算是内部调用
        String checkInternalSrcBizParentId = "0".equals(srcBizParentId) ? srcBizId : srcBizParentId;
        String checkInternalDstBizParentId = "0".equals(dstBizParentId) ? dstBizId : dstBizParentId;

        // 如果两个检查用的父系统id相同，则算是内部调用
        return checkInternalSrcBizParentId.equals(checkInternalDstBizParentId);
    }

    private static boolean isSameSubBizSystem(String srcBizId, String dstBizId) {
        return srcBizId != null && srcBizId.equals(dstBizId);
    }

    /**
     * 不在已知支持组件中，归属到其它
     *
     * @param spanObj
     * @param isIn
     * @param isOut
     * @param apiKey
     */
    private static void otherMetric(DCSpan spanObj, Integer isIn, Integer isOut, String apiKey, List<ComponentSpan> list, List<BusinessComponentSpan> businessComponentSpans) {
        //不在上面的组件中，归属到其它
        try {
            ComponentSpan componentSpan = new ComponentSpan();
            componentSpan.setLongStartTime(spanObj.getLongStartTime());
            componentSpan.setMetricName(TSDB_METRIC_OTHER_TABLE_NAME);

            componentSpan.setStartTime(spanObj.getStartTime());
            componentSpan.setLongStartTime(spanObj.getLongStartTime());
            componentSpan.setApiKey(apiKey);
            componentSpan.setCnt(1);
            componentSpan.setError(spanObj.getError());

            long duration = spanObj.getDuration();
            componentSpan.setDuration(duration);
            Map.Entry<Long, String> entry = DURATION_TAGS.floorEntry(duration);
            componentSpan.getTags().put("durationRange", entry.getValue());

            // 如果调用过initOutServiceAndInstance失败,返回false ,导致写入other表的 ,需要维持原本的逻辑,避免来源跟目标service反了
            if (TRACE_OUT_NAMES_RPC.contains(spanObj.getName()) || TRACE_OUT_NAMES_HTTP.contains(spanObj.getName())) {
                TraceUtil.initOutServiceAndInstance(componentSpan, spanObj);
            } else {
                TraceUtil.initServiceAndInstance(componentSpan, spanObj);
            }

            componentSpan.setIsIn(isIn);
            componentSpan.setIsOut(isOut);
            Map<String, String> tags = componentSpan.getTags();
            fillRootResourceTag(tags, spanObj.getMeta());
            list.add(componentSpan);

            collectBusinessComponent(spanObj, componentSpan, businessComponentSpans);
        } catch (Throwable e) {
            OtelMetricUtil.logException("ComponentMatcher", e);
        }
    }

    /**
     * 填充入口根接口信息
     *
     * @param tags
     * @param meta
     */
    private static void fillRootResourceTag(Map<String, String> tags, Map<String, String> meta) {
        String rootResource = meta.get(ROOT_RESOURCE);
        String rootType = meta.get(ROOT_TYPE);
        String rootName = meta.get(ROOT_NAME);
        if (StringUtils.isNotEmpty(rootResource) && StringUtils.isNotEmpty(rootType)) {
            //有就填充rootResource
            tags.put("rootResource", rootResource);
            tags.put("rootComponentType", TRACE_TYPE_METRIC_NAME_MAP.getOrDefault(rootType, ""));
        } else if (StringUtils.isNotEmpty(rootResource) && StringUtils.isNotEmpty(rootName)) {
            //没有根据root name填充当前的
            tags.put("rootResource", rootResource);
            if (TRACE_IN_NAMES_HTTP.contains(rootName)) {
                tags.put("rootComponentType", TSDB_METRIC_HTTP_TABLE_NAME);
            } else if (TRACE_IN_NAMES_PRC.contains(rootName)) {
                tags.put("rootComponentType", TSDB_METRIC_RPC_TABLE_NAME);
            } else if (TRACE_IN_NAMES_MQ.contains(rootName)) {
                tags.put("rootComponentType", TSDB_METRIC_MQ_TABLE_NAME);
            } else {
                tags.put("rootComponentType", "");
            }
        } else {
            tags.put("rootResource", "");
            tags.put("rootComponentType", "");
        }
    }

}
