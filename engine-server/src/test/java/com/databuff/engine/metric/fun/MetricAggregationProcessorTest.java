package com.databuff.engine.metric.fun;

import com.databuff.common.metric.FlinkAggregationType;
import com.databuff.common.metric.StandardMetric;
import com.databuff.common.tsdb.model.TSDBPoint;
import com.databuff.moredb.proto.Common;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 针对 MetricAggregationProcessor 的单元测试类 (Java 8 兼容版)。
 * 使用 JUnit 5 和 Mockito 进行测试。
 * 重点验证窗口聚合逻辑、类型处理（首值定类型）、边界条件和输出 TSDBPoint 的正确性。
 * (包含用户原有测试和补充的测试用例)
 */
class MetricAggregationProcessorTest {

    private MetricAggregationProcessor processor;
    private ProcessWindowFunction<StandardMetric, TSDBPoint, String, TimeWindow>.Context mockContext;
    private TimeWindow testWindow;
    private Collector<TSDBPoint> mockCollector;
    private ArgumentCaptor<TSDBPoint> pointCaptor;

    private final long windowStart = 1700000000000L;
    private final long windowEnd = windowStart + 60000L;
    private final String TEST_DB = "test_db";
    private final String TEST_MEASUREMENT_EVENT = "biz_event";
    private final String TEST_MEASUREMENT_KPI = "biz_event_kpi"; // 虽然定义了，但在已有测试中未见使用
    private final String TEST_KEY = TEST_DB + "|" + TEST_MEASUREMENT_EVENT + "|host=A&dc=B";

    @BeforeEach
    void setUp() {
        processor = new MetricAggregationProcessor();
        // 使用 Mockito.mock(...) 替代 mock() 以提高可读性
        mockContext = Mockito.mock(ProcessWindowFunction.Context.class);
        mockCollector = Mockito.mock(Collector.class);
        pointCaptor = ArgumentCaptor.forClass(TSDBPoint.class);
        // TimeWindow 没有公共构造函数，但可以直接 new 创建实例用于测试
        testWindow = new TimeWindow(windowStart, windowEnd);
        when(mockContext.window()).thenReturn(testWindow);
    }

    // 添加一个更完整的版本
    private StandardMetric createMetric(String db, String measurement, long timestamp, Map<String, String> tags, Map<String, Object> fields, Map<String, FlinkAggregationType> hints) {
        StandardMetric metric = new StandardMetric(); // 使用无参构造
        metric.setDatabase(db); // 设置传入的 DB
        metric.setMeasurement(measurement);
        metric.setTimestamp(timestamp);
        metric.setTags(tags != null ? new HashMap<>(tags) : new HashMap<>());
        metric.setFields(fields != null ? new HashMap<>(fields) : new HashMap<>());
        metric.setFlinkAggregationHints(hints != null ? new HashMap<>(hints) : new HashMap<>());
        return metric;
    }

    // 可以保留一个使用默认 TEST_DB 的旧版本（如果其他测试依赖它）
    private StandardMetric createMetric(String measurement, long timestamp, Map<String, String> tags, Map<String, Object> fields, Map<String, FlinkAggregationType> hints) {
        return createMetric(TEST_DB, measurement, timestamp, tags, fields, hints); // 调用新方法
    }

    // --- Helper Assertion Method ---

    /**
     * 辅助断言方法，检查 TSDBPoint 的基本属性。
     */
    private void assertResultPoint(TSDBPoint point, String expectedDb, String expectedMeasurement, long expectedTimestamp, Map<String, String> expectedTags) {
        assertNotNull(point, "输出的 TSDBPoint 不应为 null");
        assertEquals(expectedDb, point.getDatabase(), "数据库名称不匹配");
        assertEquals(expectedMeasurement, point.getMeasurement(), "Measurement 名称不匹配");
        assertEquals(expectedTimestamp, point.getTimestamp(), "时间戳应为窗口结束时间");
        assertEquals(TimeUnit.MILLISECONDS, point.getTimeUnit(), "时间单位应为毫秒");
        assertEquals(expectedTags, point.getTags(), "Tags 不匹配");
        assertNotNull(point.getFields(), "Fields Map 不应为 null");
        assertNotNull(point.getFieldTypes(), "FieldTypes Map 不应为 null");
        if (point.getFields() != null && point.getFieldTypes() != null) { // 增加 null 检查避免 NPE
            assertEquals(point.getFields().size(), point.getFieldTypes().size(), "Fields 和 FieldTypes Map 大小应一致");
        }
    }

    // Helper to assert field value with tolerance for doubles
    private void assertFieldValue(TSDBPoint point, String fieldName, Object expectedValue, double delta) {
        assertNotNull(point.getFields(), "Fields map is null"); // 先检查 map 是否为 null
        assertTrue(point.getFields().containsKey(fieldName), "Field " + fieldName + " not found in output fields: " + point.getFields().keySet());
        Object actualValue = point.getFields().get(fieldName);

        if (expectedValue == null) {
            assertNull(actualValue, "Field " + fieldName + " should be null");
            return;
        }
        assertNotNull(actualValue, "Field " + fieldName + " has null value, expected: " + expectedValue);

        if (expectedValue instanceof Double && actualValue instanceof Double) {
            assertEquals((Double) expectedValue, (Double) actualValue, delta, "Field " + fieldName + " double value mismatch");
        } else if (expectedValue instanceof Float && actualValue instanceof Float) {
            assertEquals((Float) expectedValue, (Float) actualValue, (float) delta, "Field " + fieldName + " float value mismatch");
        } else {
            assertEquals(expectedValue, actualValue, "Field " + fieldName + " value mismatch (Expected: " + expectedValue.getClass().getSimpleName() + ", Actual: " + actualValue.getClass().getSimpleName() + ")");
        }
    }

    private void assertFieldValue(TSDBPoint point, String fieldName, Object expectedValue) {
        assertFieldValue(point, fieldName, expectedValue, 0.0001); // Default delta
    }

    // Helper to assert TSDB Type
    private void assertFieldType(TSDBPoint point, String fieldName, Common.FieldType expectedType) {
        assertNotNull(point.getFieldTypes(), "FieldTypes map is null"); // 先检查 map 是否为 null
        assertTrue(point.getFieldTypes().containsKey(fieldName), "Field type for " + fieldName + " not found in output types: " + point.getFieldTypes().keySet());
        assertEquals(expectedType, point.getFieldTypes().get(fieldName), "Field type mismatch for " + fieldName);
    }

    // ================================================
    //             测试用例 (包含原有和补充)
    // ================================================

    @Test
    @DisplayName("测试空窗口：不应输出任何 TSDBPoint")
    void testEmptyWindow() throws Exception {
        List<StandardMetric> input = Collections.emptyList();
        processor.process(TEST_KEY, mockContext, input, mockCollector);
        verify(mockCollector, never()).collect(any(TSDBPoint.class));
    }

    // ================== 输入与上下文测试 (补充) ==================
    @Nested
    @DisplayName("输入与上下文处理测试")
    class InputContextTests {

        @Test
        @DisplayName("包含 Null 指标的窗口应能正确处理")
        void testWindowWithNullMetrics() throws Exception {
            Map<String, Object> fields = new HashMap<>();
            fields.put("value", 10L);
            Map<String, FlinkAggregationType> hints = Collections.singletonMap("value", FlinkAggregationType.SUM);
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "null_metric");
            StandardMetric validMetric = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields, hints);

            List<StandardMetric> metrics = Arrays.asList(null, validMetric, null); // 混入 null
            processor.process("key_null_metrics", mockContext, metrics, mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();
            assertFieldValue(point, "value", 10L);
            assertResultPoint(point, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
        }

        @Test
        @DisplayName("补充：首个指标无效但后续指标有效时应能正确初始化")
        void testDelayedContextInitialization() throws Exception {
            Map<String, Object> fields1 = new HashMap<>(); fields1.put("val", 5L);
            Map<String, String> tags1 = new HashMap<>(); tags1.put("t","invalid");
            // 创建时明确指定 DB 或 null
            StandardMetric nullDbMetric = createMetric(null, TEST_MEASUREMENT_EVENT, windowStart + 500, tags1, fields1, null); // DB is null
            StandardMetric nullMeasureMetric = createMetric(TEST_DB, null, windowStart + 600, tags1, fields1, null); // Measurement is null

            Map<String, Object> fields2 = new HashMap<>(); fields2.put("val", 10L);
            Map<String, FlinkAggregationType> hints = Collections.singletonMap("val", FlinkAggregationType.SUM);
            Map<String, String> tags2 = Collections.singletonMap("tagX", "vX");
            // *** 修改点: 使用能设置 DB 的 createMetric 方法 ***
            StandardMetric validMetric = createMetric("db2", "measure2", windowStart + 1000, tags2, fields2, hints);

            List<StandardMetric> metrics = Arrays.asList(nullDbMetric, nullMeasureMetric, validMetric);
            processor.process("key_delayed_init", mockContext, metrics, mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();

            // --- 修改并添加断言 ---
            assertEquals("db2", point.getDatabase()); // 验证 DB
            assertEquals("measure2", point.getMeasurement()); // 验证 Measurement
            assertEquals(tags2, point.getTags()); // 验证 Tags
            assertFieldValue(point, "val", 10L);
            assertFieldType(point, "val", Common.FieldType.SUM);
        }
    }


    // ================== SUM 聚合测试 ==================
    @Nested
    @DisplayName("SUM 聚合测试")
    class SumAggregationTests {

        // --- 原有测试 ---
        @Test
        @DisplayName("测试单个元素 SUM 聚合")
        void testSingleElementSum() throws Exception {
            Map<String, Object> fields = new HashMap<>();
            fields.put("count", 1L);
            fields.put("value", 10.5);
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("count", FlinkAggregationType.SUM);
            hints.put("value", FlinkAggregationType.SUM);
            Map<String, String> tags = new HashMap<>();
            tags.put("host", "A");
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields, hints);
            processor.process(TEST_KEY, mockContext, Collections.singletonList(m1), mockCollector);
            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();
            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("count", 1L);
            expectedFields.put("value", 10.5);
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("count", Common.FieldType.SUM);
            expectedTypes.put("value", Common.FieldType.SUM);
            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertEquals(expectedFields, result.getFields());
            assertEquals(expectedTypes, result.getFieldTypes());
        }

        @Test
        @DisplayName("测试多个 Long 值 SUM 聚合")
        void testMultipleElementSumLong() throws Exception { /* ... 原有实现 ... */
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "1");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("cnt", FlinkAggregationType.SUM);
            hints.put("duration", FlinkAggregationType.SUM);

            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("cnt", 1L);
            fields1.put("duration", 100L);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);

            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("cnt", 1L);
            fields2.put("duration", 150L);
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);

            Map<String, Object> fields3 = new HashMap<>();
            fields3.put("cnt", 1L);
            fields3.put("duration", 200L);
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, fields3, hints);

            processor.process("key1", mockContext, Arrays.asList(m1, m2, m3), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();

            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("cnt", 3L);
            expectedFields.put("duration", 450L);
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("cnt", Common.FieldType.SUM);
            expectedTypes.put("duration", Common.FieldType.SUM);

            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertEquals(expectedFields, result.getFields());
            assertEquals(expectedTypes, result.getFieldTypes());
        }

        @Test
        @DisplayName("测试混合 Long 和 Double 值 SUM 聚合 (首值 Long, 后续 Double 忽略)")
        void testMultipleElementSumDoubleIgnored() throws Exception { /* ... 原有实现 ... */
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "1");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("value", FlinkAggregationType.SUM);

            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("value", 10L); // First is Long
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);

            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("value", 5.5);  // Second is Double (ignored)
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);

            Map<String, Object> fields3 = new HashMap<>();
            fields3.put("value", 20L); // Third is Long (processed)
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, fields3, hints);

            processor.process("key1", mockContext, Arrays.asList(m1, m2, m3), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();

            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("value", 30L); // 10 + 20 = 30
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("value", Common.FieldType.SUM);

            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertEquals(expectedFields, result.getFields());
            assertEquals(expectedTypes, result.getFieldTypes());
        }

        @Test
        @DisplayName("测试混合 Long 和 Double 值 SUM 聚合 (首值 Double)")
        void testMultipleElementSumDoubleFirst() throws Exception { /* ... 原有实现 ... */
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "1");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("value", FlinkAggregationType.SUM);

            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("value", 5.5); // First is Double
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);

            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("value", 10L); // Second is Long (processed as double)
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);

            processor.process("key1", mockContext, Arrays.asList(m1, m2), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();

            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("value", 15.5); // 5.5 + 10.0 = 15.5
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("value", Common.FieldType.SUM);

            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertFieldValue(result, "value", 15.5); // 使用辅助方法
            assertEquals(expectedTypes, result.getFieldTypes());
        }

        // --- 补充测试 ---
        @Test
        @DisplayName("补充：测试多个 Double 值 SUM 聚合")
        void testMultipleElementSumDouble() throws Exception {
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "sum_double");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("load", FlinkAggregationType.SUM);

            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("load", 0.75);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("load", 0.25);
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);
            Map<String, Object> fields3 = new HashMap<>();
            fields3.put("load", 1.5);
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, fields3, hints);

            processor.process("key_sum_double", mockContext, Arrays.asList(m1, m2, m3), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();

            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("load", 2.5); // 0.75 + 0.25 + 1.5 = 2.5
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("load", Common.FieldType.SUM);

            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertFieldValue(result, "load", 2.5);
            assertEquals(expectedTypes, result.getFieldTypes());
        }

        @Test
        @DisplayName("补充：测试字符串输入 SUM (可解析)")
        void testSumWithParsableStrings() throws Exception {
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "sum_str");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("val", FlinkAggregationType.SUM);

            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("val", "10"); // String -> Long (determines type)
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("val", "-5"); // String -> Long
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);
            Map<String, Object> fields3 = new HashMap<>();
            fields3.put("val", "100.0"); // String -> Long (parsable as exact long)
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, fields3, hints);
            Map<String, Object> fields4 = new HashMap<>();
            fields4.put("val", "20.5"); // String -> Double (ignored due to Long window type)
            StandardMetric m4 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 4000, tags, fields4, hints);

            processor.process("key_sum_str", mockContext, Arrays.asList(m1, m2, m3, m4), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();

            // Expected: 10 + (-5)  = 5. Type is Long. 100.0 "20.5" is ignored.
            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("val", 5L);
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("val", Common.FieldType.SUM);

            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertEquals(expectedFields, result.getFields());
            assertEquals(expectedTypes, result.getFieldTypes());
        }

        @Test
        @DisplayName("补充：测试字符串输入 SUM (不可解析)")
        void testSumWithNonParsableString() throws Exception {
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "sum_bad_str");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("val", FlinkAggregationType.SUM);

            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("val", 5L);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("val", "not a number"); // Ignored
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);
            Map<String, Object> fields3 = new HashMap<>();
            fields3.put("val", 10L);
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, fields3, hints);

            processor.process("key_sum_bad_str", mockContext, Arrays.asList(m1, m2, m3), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();

            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("val", 15L); // 5 + 10
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("val", Common.FieldType.SUM);

            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertEquals(expectedFields, result.getFields());
            assertEquals(expectedTypes, result.getFieldTypes());
        }

        @Test
        @DisplayName("补充：测试 Long SUM 正向溢出饱和")
        void testLongSumPositiveOverflowSaturation() throws Exception {
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "overflow_pos");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap("large_val", FlinkAggregationType.SUM);
            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("large_val", Long.MAX_VALUE - 10L);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("large_val", 20L); // Causes overflow
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);
            Map<String, Object> fields3 = new HashMap<>();
            fields3.put("large_val", 5L); // Should be ignored after saturation
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, fields3, hints);

            processor.process("key_overflow_pos", mockContext, Arrays.asList(m1, m2, m3), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();
            assertFieldValue(point, "large_val", Long.MAX_VALUE); // Should saturate to MAX_VALUE
            assertFieldType(point, "large_val", Common.FieldType.SUM);
        }

        @Test
        @DisplayName("补充：测试 Long SUM 负向溢出饱和")
        void testLongSumNegativeOverflowSaturation() throws Exception {
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "overflow_neg");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap("large_val", FlinkAggregationType.SUM);
            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("large_val", Long.MIN_VALUE + 10L);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("large_val", -20L); // Causes overflow
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);
            Map<String, Object> fields3 = new HashMap<>();
            fields3.put("large_val", -5L); // Should be ignored after saturation
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, fields3, hints);

            processor.process("key_overflow_neg", mockContext, Arrays.asList(m1, m2, m3), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();
            assertFieldValue(point, "large_val", Long.MIN_VALUE); // Should saturate to MIN_VALUE
            assertFieldType(point, "large_val", Common.FieldType.SUM);
        }
    }


    // ================== AVG 聚合测试 ==================
    @Nested
    @DisplayName("AVG 聚合测试")
    class AvgAggregationTests {

        // --- 原有测试 ---
        @Test
        @DisplayName("测试 AVG 聚合 (Long 输入)")
        void testAvgLong() throws Exception { /* ... 原有实现 ... */
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "avg");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("latency", FlinkAggregationType.AVG);

            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("latency", 10L);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("latency", 20L);
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);
            Map<String, Object> fields3 = new HashMap<>();
            fields3.put("latency", 30L);
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, fields3, hints);

            processor.process("key_avg", mockContext, Arrays.asList(m1, m2, m3), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();

            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("latency", Common.FieldType.GAUGE);

            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertEquals(1, result.getFields().size());
            assertFieldValue(result, "latency", 20.0);
            assertEquals(expectedTypes, result.getFieldTypes());
        }

        @Test
        @DisplayName("测试 AVG 聚合 (混合输入，首值 Long, 后续 Double 忽略)")
        void testAvgMixedLongFirst() throws Exception { /* ... 原有实现 ... */
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "avg");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("latency", FlinkAggregationType.AVG);

            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("latency", 10L); // First Long
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("latency", 20.5); // Second Double (ignored)
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);

            processor.process("key_avg_mix", mockContext, Arrays.asList(m1, m2), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();

            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("latency", 10.0); // Only m1 contributes, avg = 10.0 / 1 = 10.0
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("latency", Common.FieldType.GAUGE);

            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertEquals(expectedFields, result.getFields());
            assertEquals(expectedTypes, result.getFieldTypes());
        }

        // --- 补充测试 ---
        @Test
        @DisplayName("补充：测试 AVG 聚合 (Double 输入)")
        void testAvgDouble() throws Exception {
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "avg_double");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("score", FlinkAggregationType.AVG);

            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("score", 90.5);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("score", 80.0);
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);
            Map<String, Object> fields3 = new HashMap<>();
            fields3.put("score", 85.5);
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, fields3, hints);

            processor.process("key_avg_double", mockContext, Arrays.asList(m1, m2, m3), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();

            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("score", 85.3333); // (90.5 + 80.0 + 85.5) / 3
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("score", Common.FieldType.GAUGE);

            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertFieldValue(result, "score", 85.3333, 0.0001);
            assertEquals(expectedTypes, result.getFieldTypes());
        }

        @Test
        @DisplayName("补充：测试 AVG 聚合 (Long 输入产生小数结果)")
        void testAvgLongFractionalResult() throws Exception {
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "avg_frac");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("items", FlinkAggregationType.AVG);

            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("items", 10L);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("items", 15L);
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);

            processor.process("key_avg_frac", mockContext, Arrays.asList(m1, m2), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();

            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("items", 12.5); // (10 + 15) / 2 = 12.5
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("items", Common.FieldType.GAUGE);

            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertFieldValue(result, "items", 12.5);
            assertEquals(expectedTypes, result.getFieldTypes());
        }

        @Test
        @DisplayName("补充：测试 AVG 聚合 (无有效数值输入)")
        void testAvgWithNoValidNumerics() throws Exception {
            Map<String, String> tags = new HashMap<>(); tags.put("t","avg_invalid");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("value", FlinkAggregationType.AVG); // Hint for value

            Map<String, Object> fields1 = new HashMap<>(); fields1.put("value", "abc");
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = new HashMap<>(); fields2.put("value", null);
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);
            Map<String, Object> fields3 = new HashMap<>(); fields3.put("other", 10L); // Other field exists
            // **注意:** 这里的 m3 创建时传入了包含 value hint 的 hints map
            // 如果 StandardMetric 的 hints 是全局的，那么 other 会被默认 SUM 处理
            // 如果 StandardMetric 的 addField 才关联 hint，那么 other 不会被处理
            // 假设会被默认 SUM 处理：
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, fields3, hints);

            processor.process("key_avg_invalid", mockContext, Arrays.asList(m1, m2, m3), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();

            // --- 修改断言 ---
            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            // 预期 "value" 字段不存在
            assertFalse(result.getFields().containsKey("value"), "Field 'value' for AVG should be absent");
            assertFalse(result.getFieldTypes().containsKey("value"), "FieldType for 'value' should be absent");
            // 预期 "other" 字段存在，并应用了默认 SUM
            assertTrue(result.getFields().containsKey("other"), "Field 'other' should be present due to default SUM");
            assertFieldValue(result, "other", 10L);
            assertFieldType(result, "other", Common.FieldType.SUM);
        }
    }


    // ================== MIN/MAX 聚合测试 ==================
    @Nested
    @DisplayName("MIN/MAX 聚合测试")
    class MinMaxAggregationTests {
        // --- 原有测试 ---
        @Test
        @DisplayName("测试 MIN/MAX 聚合 (Long)")
        void testMinMaxLong() throws Exception { /* ... 原有实现 ... */
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "minmax");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("value_min", FlinkAggregationType.MIN);
            hints.put("value_max", FlinkAggregationType.MAX);

            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("value_min", 15L);
            fields1.put("value_max", 15L);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("value_min", 5L);
            fields2.put("value_max", 5L);
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);
            Map<String, Object> fields3 = new HashMap<>();
            fields3.put("value_min", 25L);
            fields3.put("value_max", 25L);
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, fields3, hints);

            processor.process("key_minmax", mockContext, Arrays.asList(m1, m2, m3), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();

            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("value_min", 5L);
            expectedFields.put("value_max", 25L);
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("value_min", Common.FieldType.GAUGE);
            expectedTypes.put("value_max", Common.FieldType.GAUGE);

            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertEquals(expectedFields, result.getFields());
            assertEquals(expectedTypes, result.getFieldTypes());
        }

        // --- 补充测试 ---
        @Test
        @DisplayName("补充：测试 MIN/MAX 聚合 (Double)")
        void testMinMaxDouble() throws Exception {
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "minmax_double");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("temp_min", FlinkAggregationType.MIN);
            hints.put("temp_max", FlinkAggregationType.MAX);

            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("temp_min", 20.5);
            fields1.put("temp_max", 20.5);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("temp_min", 18.2);
            fields2.put("temp_max", 18.2);
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);
            Map<String, Object> fields3 = new HashMap<>();
            fields3.put("temp_min", 22.8);
            fields3.put("temp_max", 22.8);
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, fields3, hints);

            processor.process("key_minmax_double", mockContext, Arrays.asList(m1, m2, m3), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();

            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("temp_min", 18.2);
            expectedFields.put("temp_max", 22.8);
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("temp_min", Common.FieldType.GAUGE);
            expectedTypes.put("temp_max", Common.FieldType.GAUGE);

            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertFieldValue(result, "temp_min", 18.2);
            assertFieldValue(result, "temp_max", 22.8);
            assertEquals(expectedTypes, result.getFieldTypes());
        }

        @Test
        @DisplayName("补充：测试 MIN/MAX 聚合 (混合类型, 首值 Double)")
        void testMinMaxMixedDoubleFirst() throws Exception {
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "minmax_mix");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("val_min", FlinkAggregationType.MIN);
            hints.put("val_max", FlinkAggregationType.MAX);

            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("val_min", 20.5);
            fields1.put("val_max", 20.5); // First Double
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("val_min", 10L);
            fields2.put("val_max", 10L); // Second Long (processed as double 10.0)
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);
            Map<String, Object> fields3 = new HashMap<>();
            fields3.put("val_min", 30L);
            fields3.put("val_max", 30L); // Third Long (processed as double 30.0)
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, fields3, hints);

            processor.process("key_minmax_mix", mockContext, Arrays.asList(m1, m2, m3), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();

            // Min is 10.0, Max is 30.0. Result type is Double.
            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("val_min", 10.0);
            expectedFields.put("val_max", 30.0);
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("val_min", Common.FieldType.GAUGE);
            expectedTypes.put("val_max", Common.FieldType.GAUGE);

            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertFieldValue(result, "val_min", 10.0);
            assertFieldValue(result, "val_max", 30.0);
            assertEquals(expectedTypes, result.getFieldTypes());
        }

        @Test
        @DisplayName("补充：测试 MIN/MAX 聚合 (单元素)")
        void testMinMaxSingleElement() throws Exception {
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "minmax_single");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("value", FlinkAggregationType.MIN); // Test MIN, MAX behaves similarly
            hints.put("value_max", FlinkAggregationType.MAX);

            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("value", 42L);
            fields1.put("value_max", 42L);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);

            processor.process("key_minmax_single", mockContext, Collections.singletonList(m1), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();

            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("value", 42L);
            expectedFields.put("value_max", 42L);
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("value", Common.FieldType.GAUGE);
            expectedTypes.put("value_max", Common.FieldType.GAUGE);

            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertEquals(expectedFields, result.getFields());
            assertEquals(expectedTypes, result.getFieldTypes());
        }
    }

    // ================== FIRST/LAST 聚合测试 ==================
    @Nested
    @DisplayName("FIRST/LAST 聚合测试")
    class FirstLastAggregationTests {
        // --- 原有测试 ---
        @Test
        @DisplayName("测试 FIRST 聚合")
        void testFirst() throws Exception { /* ... 原有实现 ... */
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "first");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("status", FlinkAggregationType.FIRST);
            Map<String, Object> fieldsM1 = new HashMap<>();
            fieldsM1.put("status", "Middle");
            StandardMetric m1_java8 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fieldsM1, hints);
            Map<String, Object> fieldsM2 = new HashMap<>();
            fieldsM2.put("status", "Start");
            StandardMetric m2_java8 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fieldsM2, hints); // Earliest
            Map<String, Object> fieldsM3 = new HashMap<>();
            fieldsM3.put("status", "End");
            StandardMetric m3_java8 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, fieldsM3, hints);
            processor.process("key_first", mockContext, Arrays.asList(m1_java8, m2_java8, m3_java8), mockCollector);
            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();
            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("status", "Start");
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("status", Common.FieldType.GAUGE);
            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertEquals(expectedFields, result.getFields());
            assertEquals(expectedTypes, result.getFieldTypes());
        }

        @Test
        @DisplayName("测试 LAST 聚合")
        void testLast() throws Exception { /* ... 原有实现 ... */
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "last");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("status", FlinkAggregationType.LAST);
            hints.put("val", FlinkAggregationType.LAST);
            Map<String, Object> fieldsM1 = new HashMap<>();
            fieldsM1.put("status", "Middle");
            fieldsM1.put("val", 10.0);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fieldsM1, hints);
            Map<String, Object> fieldsM2 = new HashMap<>();
            fieldsM2.put("status", "End");
            fieldsM2.put("val", 5.0); // Latest
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, fieldsM2, hints);
            Map<String, Object> fieldsM3 = new HashMap<>();
            fieldsM3.put("status", "Start");
            fieldsM3.put("val", 15.0);
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fieldsM3, hints);
            processor.process("key_last", mockContext, Arrays.asList(m1, m2, m3), mockCollector);
            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();
            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("status", "End");
            expectedFields.put("val", 5.0);
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("status", Common.FieldType.GAUGE);
            expectedTypes.put("val", Common.FieldType.GAUGE);
            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertEquals(expectedFields, result.getFields());
            assertEquals(expectedTypes, result.getFieldTypes());
        }

        // --- 补充测试 ---
        @Test
        @DisplayName("补充：测试 LAST 聚合 (相同时间戳)")
        void testLastWithIdenticalTimestamps() throws Exception {
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "last_tie");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("value", FlinkAggregationType.LAST);

            long identicalTimestamp = windowStart + 2000;
            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("value", "A");
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, identicalTimestamp, tags, fields1, hints);
            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("value", "B"); // Arrives later in list
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, identicalTimestamp, tags, fields2, hints);
            Map<String, Object> fields3 = new HashMap<>();
            fields3.put("value", "C"); // Earlier timestamp
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields3, hints);

            // The order in this list determines the "arrival" order for identical timestamps
            processor.process("key_last_tie", mockContext, Arrays.asList(m3, m1, m2), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();

            // For identical timestamps, the LAST logic (>=) should select the one processed later.
            // In the list [m3, m1, m2], m2 is processed last among those with timestamp 2000.
            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("value", "B");
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("value", Common.FieldType.GAUGE);

            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertEquals(expectedFields, result.getFields());
            assertEquals(expectedTypes, result.getFieldTypes());
        }
    }

    // ================== COUNT 聚合测试 ==================
    @Nested
    @DisplayName("COUNT 聚合测试")
    class CountAggregationTests {
        // --- 原有测试 ---
        @Test
        @DisplayName("测试 COUNT 聚合")
        void testCount() throws Exception { /* ... 原有实现 ... */
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "count");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("any_field", FlinkAggregationType.COUNT);
            hints.put("other_field", FlinkAggregationType.COUNT);
            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("any_field", "a");
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("any_field", "b");
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);
            Map<String, Object> fields3 = new HashMap<>();
            fields3.put("other_field", 1);
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, fields3, hints);
            Map<String, Object> fields4 = new HashMap<>();
            fields4.put("any_field", null); // Null field value
            StandardMetric m4 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 4000, tags, fields4, hints);
            processor.process("key_count", mockContext, Arrays.asList(m1, m2, m3, m4), mockCollector);
            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();
            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("any_field", 2L);
            expectedFields.put("other_field", 1L);
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("any_field", Common.FieldType.SUM);
            expectedTypes.put("other_field", Common.FieldType.SUM);
            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertEquals(expectedFields, result.getFields());
            assertEquals(expectedTypes, result.getFieldTypes());
        }

        @Test
        @DisplayName("无该字段的指标不参与 COUNT")
            // 已由 testCount 覆盖，但保留显式测试名称
        void testCountWithMissingField() throws Exception {
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "count_missing");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap("counter", FlinkAggregationType.COUNT);
            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("counter", 1);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("other_field", 99); // Does not have 'counter' field
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);
            Map<String, Object> fields3 = new HashMap<>();
            fields3.put("counter", "increment");
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, fields3, hints);
            processor.process("key_count_missing", mockContext, Arrays.asList(m1, m2, m3), mockCollector);
            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();
            assertFieldValue(result, "counter", 2L); // Only m1 and m3 contributed
            assertFieldType(result, "counter", Common.FieldType.SUM);
        }
    }

    // ================== 类型处理测试 (补充) ==================
    @Nested
    @DisplayName("类型处理与确定测试")
    class TypeHandlingTests {

        @Test
        @DisplayName("补充：测试多种数字类型输入 (Integer, Float, BigDecimal)")
        void testVariousNumericTypesInput() throws Exception {
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "types");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("val_sum", FlinkAggregationType.SUM);
            hints.put("bd_sum", FlinkAggregationType.SUM);
            hints.put("float_sum", FlinkAggregationType.SUM);

            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("val_sum", 10); // Integer -> Long (determines Long type)
            fields1.put("bd_sum", new BigDecimal("5.5")); // BigDecimal -> Double (determines Double type)
            fields1.put("float_sum", 2.5f); // Float -> Double (determines Double type)
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);

            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("val_sum", 5L);          // Long (compatible with Long)
            fields2.put("bd_sum", new BigDecimal("10")); // BigDecimal -> Long (compatible with Double) -> processed as 10.0
            fields2.put("float_sum", 10L);      // Long (compatible with Double) -> processed as 10.0
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);

            Map<String, Object> fields3 = new HashMap<>();
            fields3.put("val_sum", 1.5f); // Float -> Double (ignored, expected Long)
            // bd_sum and float_sum not present in m3

            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, fields3, hints);

            processor.process("key_types", mockContext, Arrays.asList(m1, m2, m3), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();

            // val_sum: 10(int) + 5(long) = 15L (1.5f ignored)
            // bd_sum: 5.5(bd double) + 10(bd long->double 10.0) = 15.5
            // float_sum: 2.5f(double) + 10L(double 10.0) = 12.5
            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("val_sum", 15L);
            expectedFields.put("bd_sum", 15.5);
            expectedFields.put("float_sum", 12.5);
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("val_sum", Common.FieldType.SUM);
            expectedTypes.put("bd_sum", Common.FieldType.SUM);
            expectedTypes.put("float_sum", Common.FieldType.SUM);

            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertEquals(expectedFields, result.getFields());
            assertEquals(expectedTypes, result.getFieldTypes());
        }
    }


    // ================== 边界与边缘情况测试 ==================
    @Nested
    @DisplayName("边界与边缘情况测试")
    class EdgeCaseTests {

        // --- 原有测试 ---
        @Test
        @DisplayName("测试混合聚合提示")
        void testMixedHints() throws Exception { /* ... 原有实现 ... */
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "mix");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("total_requests", FlinkAggregationType.SUM);
            hints.put("last_status", FlinkAggregationType.LAST);
            hints.put("first_user", FlinkAggregationType.FIRST);
            hints.put("p99_latency", FlinkAggregationType.MAX);
            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("total_requests", 1L);
            fields1.put("last_status", "OK");
            fields1.put("first_user", "UserA");
            fields1.put("p99_latency", 120.5);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("total_requests", 1L);
            fields2.put("last_status", "FAIL");
            fields2.put("first_user", "UserC");
            fields2.put("p99_latency", 150.0);
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, fields2, hints);
            Map<String, Object> fields3 = new HashMap<>();
            fields3.put("total_requests", 1L);
            fields3.put("last_status", "WARN");
            fields3.put("first_user", "UserB");
            fields3.put("p99_latency", 110.0);
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields3, hints);
            processor.process("key_mix", mockContext, Arrays.asList(m1, m2, m3), mockCollector);
            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();
            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("total_requests", 3L);
            expectedFields.put("last_status", "FAIL");
            expectedFields.put("first_user", "UserA");
            expectedFields.put("p99_latency", 150.0);
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("total_requests", Common.FieldType.SUM);
            expectedTypes.put("last_status", Common.FieldType.GAUGE);
            expectedTypes.put("first_user", Common.FieldType.GAUGE);
            expectedTypes.put("p99_latency", Common.FieldType.GAUGE);
            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertEquals(expectedFields, result.getFields());
            assertEquals(expectedTypes, result.getFieldTypes());
        }

        @Test
        @DisplayName("测试字段值为 null 时被忽略 (SUM/LAST)")
        void testNullFieldValue() throws Exception { /* ... 原有实现 ... */
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "null");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("value_sum", FlinkAggregationType.SUM);
            hints.put("value_last", FlinkAggregationType.LAST);
            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("value_sum", 10L);
            fields1.put("value_last", "A");
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("value_sum", null);
            fields2.put("value_last", null); // Null values
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);
            Map<String, Object> fields3 = new HashMap<>();
            fields3.put("value_sum", 5L);
            fields3.put("value_last", "B");
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, fields3, hints);
            processor.process("key_null", mockContext, Arrays.asList(m1, m2, m3), mockCollector);
            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();
            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("value_sum", 15L);
            expectedFields.put("value_last", "B");
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("value_sum", Common.FieldType.SUM);
            expectedTypes.put("value_last", Common.FieldType.GAUGE);
            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertEquals(expectedFields, result.getFields());
            assertEquals(expectedTypes, result.getFieldTypes());
        }

        @Test
        @DisplayName("测试非数字值用于数值聚合提示时被忽略")
        void testNonNumericFieldValueForNumericHints() throws Exception { /* ... 原有实现 ... */
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "non_numeric");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("value_sum", FlinkAggregationType.SUM);
            hints.put("value_min", FlinkAggregationType.MIN);
            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("value_sum", 10L);
            fields1.put("value_min", 5L);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("value_sum", "Not A Number");
            fields2.put("value_min", "Ignore Me"); // Non-numeric
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);
            processor.process("key_non_numeric", mockContext, Arrays.asList(m1, m2), mockCollector);
            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();
            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("value_sum", 10L);
            expectedFields.put("value_min", 5L);
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("value_sum", Common.FieldType.SUM);
            expectedTypes.put("value_min", Common.FieldType.GAUGE);
            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertEquals(expectedFields, result.getFields());
            assertEquals(expectedTypes, result.getFieldTypes());
        }

        @Test
        @DisplayName("测试无聚合提示时默认使用 SUM")
        void testMissingHintDefaultsToSum() throws Exception { /* ... 原有实现 ... */
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "default");
            Map<String, FlinkAggregationType> hints = new HashMap<>(); // Empty hints map
            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("value", 10L);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("value", 20L);
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);
            processor.process("key_default", mockContext, Arrays.asList(m1, m2), mockCollector);
            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();
            Map<String, Object> expectedFields = new HashMap<>();
            expectedFields.put("value", 30L); // Defaulted to SUM
            Map<String, Common.FieldType> expectedTypes = new HashMap<>();
            expectedTypes.put("value", Common.FieldType.SUM); // Default maps to SUM type
            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertEquals(expectedFields, result.getFields());
            assertEquals(expectedTypes, result.getFieldTypes());
        }

        @Test
        @DisplayName("补充：测试聚合字段在所有指标中均缺失 (且无其他字段)")
            // 修改测试描述以更清晰
        void testFieldMissingInAllMetrics() throws Exception {
            Map<String, String> tags = new HashMap<>();
            tags.put("t", "missing");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap("missing_field", FlinkAggregationType.SUM);

            // 输入指标没有字段
            StandardMetric m1 = createMetric(TEST_DB, TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, Collections.emptyMap(), hints);
            StandardMetric m2 = createMetric(TEST_DB, TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, Collections.emptyMap(), hints);

            processor.process("key_missing_only_hint", mockContext, Arrays.asList(m1, m2), mockCollector);

            // --- 修改验证 ---
            // 预期：由于没有有效字段初始化或聚合，不应有任何输出
            verify(mockCollector, never()).collect(any(TSDBPoint.class));
        }

        @Test
        @DisplayName("补充：测试聚合字段存在但所有值无效 (数值聚合)")
        void testFieldPresentButAllValuesInvalidForNumericHint() throws Exception {
            Map<String, String> tags = new HashMap<>(); tags.put("t","all_invalid");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("numeric_val", FlinkAggregationType.SUM);
            hints.put("some_other", FlinkAggregationType.LAST); // Keep this to ensure a point is generated

            Map<String, Object> fields1 = new HashMap<>(); fields1.put("numeric_val", "abc"); fields1.put("some_other", "X");
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = new HashMap<>(); fields2.put("numeric_val", null); fields2.put("some_other", "Y");
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);
            Map<String, Object> fields3 = new HashMap<>(); fields3.put("numeric_val", true); fields3.put("some_other", "Z");
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, fields3, hints);

            processor.process("key_all_invalid", mockContext, Arrays.asList(m1, m2, m3), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint result = pointCaptor.getValue();

            // *** 断言保持不变，依赖 Processor 代码修复 ***
            // 预期 "numeric_val" 不存在， "some_other" 存在且为 "Z"
            Map<String, Object> expectedFields = new HashMap<>(); expectedFields.put("some_other", "Z");
            Map<String, Common.FieldType> expectedTypes = new HashMap<>(); expectedTypes.put("some_other", Common.FieldType.GAUGE);

            assertResultPoint(result, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertEquals(expectedFields, result.getFields(), "Fields map mismatch");
            assertEquals(expectedTypes, result.getFieldTypes(), "FieldTypes map mismatch");
            assertFalse(result.getFields().containsKey("numeric_val"), "Field 'numeric_val' should be absent");
            assertFalse(result.getFieldTypes().containsKey("numeric_val"), "FieldType for 'numeric_val' should be absent");
        }
    }

    // ================== 0.0 (Double Zero) 处理测试 (新增) ==================
    @Nested
    @DisplayName("0.0 (Double Zero) 处理与类型保持测试")
    class ZeroDotZeroHandlingTests {

        private final String FIELD_NAME = "kpiAttributeValue"; // Use the relevant field name

        @Test
        @DisplayName("测试：单个 0.0 输入 (SUM hint)")
        void testSingleZeroDotZeroSum() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "single_0.0");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap(FIELD_NAME, FlinkAggregationType.SUM);
            Map<String, Object> fields = Collections.singletonMap(FIELD_NAME, 0.0); // Input is Double 0.0
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_KPI, windowStart + 1000, tags, fields, hints);

            processor.process("key_single_0.0", mockContext, Collections.singletonList(m1), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();

            assertResultPoint(point, TEST_DB, TEST_MEASUREMENT_KPI, windowEnd, tags);
            // **当前代码预期**: 0.0 会被识别为可转 Long，窗口类型设为 LONG，输出 0L
            // **用户期望**: 输出 0.0 (Double)
            assertFieldValue(point, FIELD_NAME, 0.0); // <-- 断言当前代码的行为 (输出 Long 0)
            assertEquals(Double.class, point.getFields().get(FIELD_NAME).getClass(), "字段类型应为 Double (当前代码行为)");
            // 如果要断言期望行为，应该是：
            // assertFieldValue(point, FIELD_NAME, 0.0);
            // assertEquals(Double.class, point.getFields().get(FIELD_NAME).getClass(), "字段类型应为 Double (期望行为)");
            assertFieldType(point, FIELD_NAME, Common.FieldType.SUM);
        }

        @Test
        @DisplayName("测试：0.0 输入后接整数 (SUM hint)")
        void testZeroDotZeroFollowedByLongSum() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "0.0_then_long");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap(FIELD_NAME, FlinkAggregationType.SUM);

            Map<String, Object> fields1 = Collections.singletonMap(FIELD_NAME, 0.0); // Input Double 0.0
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_KPI, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = Collections.singletonMap(FIELD_NAME, 10L); // Input Long 10
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_KPI, windowStart + 2000, tags, fields2, hints);
            Map<String, Object> fields3 = Collections.singletonMap(FIELD_NAME, 5L);  // Input Long 5
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_KPI, windowStart + 3000, tags, fields3, hints);

            processor.process("key_0.0_then_long", mockContext, Arrays.asList(m1, m2, m3), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();

            assertResultPoint(point, TEST_DB, TEST_MEASUREMENT_KPI, windowEnd, tags);
            // **当前代码预期**: 0.0 设置窗口类型为 LONG。后续 10L, 5L 兼容。Sum = 0 + 10 + 5 = 15L.
            // **用户期望**: 0.0 设置窗口类型为 DOUBLE。后续 10L, 5L 提升为 double。Sum = 0.0 + 10.0 + 5.0 = 15.0.
            assertFieldValue(point, FIELD_NAME, 15.0); // <-- 断言当前代码的行为
            assertEquals(Double.class, point.getFields().get(FIELD_NAME).getClass(), "字段类型应为 Double (当前代码行为)");
            // 如果要断言期望行为，应该是：
            // assertFieldValue(point, FIELD_NAME, 15.0);
            // assertEquals(Double.class, point.getFields().get(FIELD_NAME).getClass(), "字段类型应为 Double (期望行为)");
            assertFieldType(point, FIELD_NAME, Common.FieldType.SUM);
        }

        @Test
        @DisplayName("测试：整数输入后接 0.0 (SUM hint)")
        void testLongFollowedByZeroDotZeroSum() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "long_then_0.0");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap(FIELD_NAME, FlinkAggregationType.SUM);

            Map<String, Object> fields1 = Collections.singletonMap(FIELD_NAME, 10L); // Input Long 10 (设置类型为 LONG)
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_KPI, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = Collections.singletonMap(FIELD_NAME, 0.0);  // Input Double 0.0 (解析为 LONG 0, 但与窗口类型 LONG 匹配?) -> 不对，是解析为 DOUBLE，类型不匹配！
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_KPI, windowStart + 2000, tags, fields2, hints);
            Map<String, Object> fields3 = Collections.singletonMap(FIELD_NAME, 5L);  // Input Long 5
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_KPI, windowStart + 3000, tags, fields3, hints);

            processor.process("key_long_then_0.0", mockContext, Arrays.asList(m1, m2, m3), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();

            assertResultPoint(point, TEST_DB, TEST_MEASUREMENT_KPI, windowEnd, tags);
            // **当前代码预期**: 10L 设置窗口类型为 LONG。 0.0 解析为 DOUBLE，类型不匹配，被忽略。 5L 兼容。Sum = 10 + 5 = 15L.
            // **用户期望**: (如果期望是始终 Double) 10L->10.0, 0.0->0.0, 5L->5.0. Sum = 15.0.
            // **用户期望**: (如果期望是0.0能被兼容?) 10L + 0L + 5L = 15L. -> 但目前代码逻辑是忽略
            assertFieldValue(point, FIELD_NAME, 15L); // <-- 断言当前代码的行为 (忽略 0.0，输出 Long 15)
            assertEquals(Long.class, point.getFields().get(FIELD_NAME).getClass(), "字段类型应为 Long (当前代码行为)");
            assertFieldType(point, FIELD_NAME, Common.FieldType.SUM);
        }

        @Test
        @DisplayName("测试：0.0 与其他 Doubles 混合 (SUM hint)")
        void testZeroDotZeroMixedWithDoublesSum() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "0.0_mixed_double");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap(FIELD_NAME, FlinkAggregationType.SUM);

            Map<String, Object> fields1 = Collections.singletonMap(FIELD_NAME, 0.0);  // Input Double 0.0
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_KPI, windowStart + 1000, tags, fields1, hints);
            Map<String, Object> fields2 = Collections.singletonMap(FIELD_NAME, 1.5);  // Input Double 1.5
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_KPI, windowStart + 2000, tags, fields2, hints);
            Map<String, Object> fields3 = Collections.singletonMap(FIELD_NAME, 2.0);  // Input Double 2.0
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_KPI, windowStart + 3000, tags, fields3, hints);

            processor.process("key_0.0_mixed_double", mockContext, Arrays.asList(m1, m2, m3), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();

            assertResultPoint(point, TEST_DB, TEST_MEASUREMENT_KPI, windowEnd, tags);
            // **当前代码预期**: 0.0 解析为 LONG。1.5 解析为 DOUBLE。类型不匹配，1.5 被忽略。2.0 解析为 DOUBLE，类型不匹配，被忽略。Sum = 0L.
            // **更正预期 (根据上次修改逻辑)**: 0.0 (Double) -> 强制类型为 DOUBLE。1.5 (Double) 兼容。2.0 (Double) 兼容。Sum = 0.0 + 1.5 + 2.0 = 3.5.
            // !! 我们需要验证的是上次修改后的代码 !!
            assertFieldValue(point, FIELD_NAME, 3.5); // <-- 断言修改后代码的行为 (输出 Double 3.5)
            assertEquals(Double.class, point.getFields().get(FIELD_NAME).getClass(), "字段类型应为 Double (修改后代码行为)");
            assertFieldType(point, FIELD_NAME, Common.FieldType.SUM);
        }

        @Test
        @DisplayName("测试：字符串 '0.0' 输入 (SUM hint)")
        void testStringZeroDotZeroSum() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "string_0.0");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap(FIELD_NAME, FlinkAggregationType.SUM);
            Map<String, Object> fields = Collections.singletonMap(FIELD_NAME, "0.0"); // Input String "0.0"
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_KPI, windowStart + 1000, tags, fields, hints);

            processor.process("key_string_0.0", mockContext, Collections.singletonList(m1), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();

            assertResultPoint(point, TEST_DB, TEST_MEASUREMENT_KPI, windowEnd, tags);
            // **当前代码预期 (上次修改后)**: "0.0" 解析为 Double 0.0，窗口类型设为 DOUBLE，输出 0.0
            assertFieldValue(point, FIELD_NAME, 0.0); // <-- 断言修改后代码的行为
            assertEquals(Double.class, point.getFields().get(FIELD_NAME).getClass(), "字段类型应为 Double (修改后代码行为)");
            assertFieldType(point, FIELD_NAME, Common.FieldType.SUM);
        }

        @Test
        @DisplayName("测试：字符串 '0' 输入 (SUM hint)")
        void testStringZeroSum() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "string_0");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap(FIELD_NAME, FlinkAggregationType.SUM);
            Map<String, Object> fields = Collections.singletonMap(FIELD_NAME, "0"); // Input String "0"
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_KPI, windowStart + 1000, tags, fields, hints);

            processor.process("key_string_0", mockContext, Collections.singletonList(m1), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();

            assertResultPoint(point, TEST_DB, TEST_MEASUREMENT_KPI, windowEnd, tags);
            // **当前代码预期**: "0" 解析为 Long 0L，窗口类型设为 LONG，输出 0L
            assertFieldValue(point, FIELD_NAME, 0L); // <-- 断言当前代码的行为
            assertEquals(Long.class, point.getFields().get(FIELD_NAME).getClass(), "字段类型应为 Long (当前代码行为)");
            assertFieldType(point, FIELD_NAME, Common.FieldType.SUM);
        }

        // 可以为 MIN/MAX/AVG/FIRST/LAST 添加类似的 0.0 测试用例
        @Test
        @DisplayName("测试：单个 0.0 输入 (LAST hint)")
        void testSingleZeroDotZeroLast() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "single_0.0_last");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap(FIELD_NAME, FlinkAggregationType.LAST);
            Map<String, Object> fields = Collections.singletonMap(FIELD_NAME, 0.0); // Input is Double 0.0
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_KPI, windowStart + 1000, tags, fields, hints);

            processor.process("key_single_0.0_last", mockContext, Collections.singletonList(m1), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();

            assertResultPoint(point, TEST_DB, TEST_MEASUREMENT_KPI, windowEnd, tags);
            // **对于 LAST，normalizeFinalValue 会返回原始对象类型**
            // **当前代码预期**: 输入是 Double 0.0，LAST 直接捕获 Double 0.0
            // **用户期望**: 输出 0.0 (Double)
            assertFieldValue(point, FIELD_NAME, 0.0); // <-- LAST 应该保持 Double
            assertEquals(Double.class, point.getFields().get(FIELD_NAME).getClass(), "字段类型应为 Double (LAST hint)");
            assertFieldType(point, FIELD_NAME, Common.FieldType.GAUGE); // LAST -> GAUGE
        }

    }

    // ================== RANGE 聚合测试 (MAX - MIN) (新增) ==================
    @Nested
    @DisplayName("RANGE 聚合测试 (MAX - MIN)")
    class RangeAggregationTests {

        private final String FIELD_NAME_RANGE = "value_range";

        @Test
        @DisplayName("测试 RANGE 聚合 (多个 Long 输入)")
        void testRangeMultipleLongs() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "range_longs");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap(FIELD_NAME_RANGE, FlinkAggregationType.RANGE);

            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 10L), hints);
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 5L), hints); // min
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 20L), hints); // max
            StandardMetric m4 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 4000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 15L), hints);

            processor.process(TEST_KEY, mockContext, Arrays.asList(m1, m2, m3, m4), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();

            assertResultPoint(point, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertFieldValue(point, FIELD_NAME_RANGE, 15L); // 20L - 5L
            assertEquals(Long.class, point.getFields().get(FIELD_NAME_RANGE).getClass());
            assertFieldType(point, FIELD_NAME_RANGE, Common.FieldType.GAUGE);
        }

        @Test
        @DisplayName("测试 RANGE 聚合 (多个 Double 输入)")
        void testRangeMultipleDoubles() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "range_doubles");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap(FIELD_NAME_RANGE, FlinkAggregationType.RANGE);

            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 10.5), hints);
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 5.25), hints); // min
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 20.75), hints); // max
            StandardMetric m4 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 4000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 15.0), hints);

            processor.process(TEST_KEY, mockContext, Arrays.asList(m1, m2, m3, m4), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();

            assertResultPoint(point, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertFieldValue(point, FIELD_NAME_RANGE, 15.5); // 20.75 - 5.25
            assertEquals(Double.class, point.getFields().get(FIELD_NAME_RANGE).getClass());
            assertFieldType(point, FIELD_NAME_RANGE, Common.FieldType.GAUGE);
        }

        @Test
        @DisplayName("测试 RANGE 聚合 (单个 Long元素, 范围为0)")
        void testRangeSingleLongElement() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "range_single_long");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap(FIELD_NAME_RANGE, FlinkAggregationType.RANGE);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 42L), hints);

            processor.process(TEST_KEY, mockContext, Collections.singletonList(m1), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();
            assertResultPoint(point, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertFieldValue(point, FIELD_NAME_RANGE, 0L); // max(42) - min(42) = 0
            assertEquals(Long.class, point.getFields().get(FIELD_NAME_RANGE).getClass());
            assertFieldType(point, FIELD_NAME_RANGE, Common.FieldType.GAUGE);
        }

        @Test
        @DisplayName("测试 RANGE 聚合 (单个 Double元素, 范围为0.0)")
        void testRangeSingleDoubleElement() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "range_single_double");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap(FIELD_NAME_RANGE, FlinkAggregationType.RANGE);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 42.5), hints);

            processor.process(TEST_KEY, mockContext, Collections.singletonList(m1), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();
            assertResultPoint(point, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertFieldValue(point, FIELD_NAME_RANGE, 0.0); // max(42.5) - min(42.5) = 0.0
            assertEquals(Double.class, point.getFields().get(FIELD_NAME_RANGE).getClass());
            assertFieldType(point, FIELD_NAME_RANGE, Common.FieldType.GAUGE);
        }

        @Test
        @DisplayName("测试 RANGE 聚合 (所有 Long 值相同, 范围为0)")
        void testRangeAllLongsIdentical() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "range_longs_identical");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap(FIELD_NAME_RANGE, FlinkAggregationType.RANGE);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 7L), hints);
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 7L), hints);
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 7L), hints);

            processor.process(TEST_KEY, mockContext, Arrays.asList(m1, m2, m3), mockCollector);
            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();
            assertFieldValue(point, FIELD_NAME_RANGE, 0L);
            assertEquals(Long.class, point.getFields().get(FIELD_NAME_RANGE).getClass());
            assertFieldType(point, FIELD_NAME_RANGE, Common.FieldType.GAUGE);
        }

        @Test
        @DisplayName("测试 RANGE 聚合 (所有 Double 值相同, 范围为0.0)")
        void testRangeAllDoublesIdentical() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "range_doubles_identical");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap(FIELD_NAME_RANGE, FlinkAggregationType.RANGE);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 7.7), hints);
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 7.7), hints);

            processor.process(TEST_KEY, mockContext, Arrays.asList(m1, m2), mockCollector);
            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();
            assertFieldValue(point, FIELD_NAME_RANGE, 0.0);
            assertEquals(Double.class, point.getFields().get(FIELD_NAME_RANGE).getClass());
            assertFieldType(point, FIELD_NAME_RANGE, Common.FieldType.GAUGE);
        }


        @Test
        @DisplayName("测试 RANGE 聚合 (混合 Long 和 Double, 首值 Long, Double被忽略)")
        void testRangeMixedLongFirstDoubleIgnored() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "range_long_double_ignore");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap(FIELD_NAME_RANGE, FlinkAggregationType.RANGE);

            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 10L), hints); // Determines type LONG
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 25.5), hints); // Double, ignored
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 5L), hints);   // Long
            StandardMetric m4 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 4000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 30L), hints);  // Long

            processor.process(TEST_KEY, mockContext, Arrays.asList(m1, m2, m3, m4), mockCollector);
            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();
            // Min among Longs: 5L, Max among Longs: 30L. Range = 30 - 5 = 25L
            assertFieldValue(point, FIELD_NAME_RANGE, 25L);
            assertEquals(Long.class, point.getFields().get(FIELD_NAME_RANGE).getClass());
            assertFieldType(point, FIELD_NAME_RANGE, Common.FieldType.GAUGE);
        }

        @Test
        @DisplayName("测试 RANGE 聚合 (混合 Long 和 Double, 首值 Double, Long转Double)")
        void testRangeMixedDoubleFirstLongConverted() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "range_double_long_convert");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap(FIELD_NAME_RANGE, FlinkAggregationType.RANGE);

            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 10.5), hints); // Determines type DOUBLE
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 5L), hints);    // Long, converted to 5.0
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 20.0), hints);  // Double

            processor.process(TEST_KEY, mockContext, Arrays.asList(m1, m2, m3), mockCollector);
            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();
            // Values: 10.5, 5.0, 20.0. Min: 5.0, Max: 20.0. Range = 20.0 - 5.0 = 15.0
            assertFieldValue(point, FIELD_NAME_RANGE, 15.0);
            assertEquals(Double.class, point.getFields().get(FIELD_NAME_RANGE).getClass());
            assertFieldType(point, FIELD_NAME_RANGE, Common.FieldType.GAUGE);
        }

        @Test
        @DisplayName("测试 RANGE 聚合 (可解析的字符串 Long 输入)")
        void testRangeWithParsableStringLongs() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "range_str_long");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap(FIELD_NAME_RANGE, FlinkAggregationType.RANGE);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, Collections.singletonMap(FIELD_NAME_RANGE, "100"), hints); // Determines Long
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, Collections.singletonMap(FIELD_NAME_RANGE, "50"), hints);
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, Collections.singletonMap(FIELD_NAME_RANGE, "120"), hints);

            processor.process(TEST_KEY, mockContext, Arrays.asList(m1, m2, m3), mockCollector);
            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();
            assertFieldValue(point, FIELD_NAME_RANGE, 70L); // 120 - 50
            assertEquals(Long.class, point.getFields().get(FIELD_NAME_RANGE).getClass());
            assertFieldType(point, FIELD_NAME_RANGE, Common.FieldType.GAUGE);
        }

        @Test
        @DisplayName("测试 RANGE 聚合 (可解析的字符串 Double 输入)")
        void testRangeWithParsableStringDoubles() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "range_str_double");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap(FIELD_NAME_RANGE, FlinkAggregationType.RANGE);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, Collections.singletonMap(FIELD_NAME_RANGE, "100.5"), hints); // Determines Double
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, Collections.singletonMap(FIELD_NAME_RANGE, "50.25"), hints);
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, Collections.singletonMap(FIELD_NAME_RANGE, "120.0"), hints);

            processor.process(TEST_KEY, mockContext, Arrays.asList(m1, m2, m3), mockCollector);
            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();
            assertFieldValue(point, FIELD_NAME_RANGE, 69.75); // 120.0 - 50.25
            assertEquals(Double.class, point.getFields().get(FIELD_NAME_RANGE).getClass());
            assertFieldType(point, FIELD_NAME_RANGE, Common.FieldType.GAUGE);
        }

        @Test
        @DisplayName("测试 RANGE 聚合 (不可解析的字符串被忽略)")
        void testRangeWithNonParsableStringIgnored() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "range_bad_str");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap(FIELD_NAME_RANGE, FlinkAggregationType.RANGE);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 10L), hints);
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, Collections.singletonMap(FIELD_NAME_RANGE, "not-a-number"), hints);
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 30L), hints);

            processor.process(TEST_KEY, mockContext, Arrays.asList(m1, m2, m3), mockCollector);
            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();
            assertFieldValue(point, FIELD_NAME_RANGE, 20L); // 30L - 10L
            assertEquals(Long.class, point.getFields().get(FIELD_NAME_RANGE).getClass());
            assertFieldType(point, FIELD_NAME_RANGE, Common.FieldType.GAUGE);
        }

        @Test
        @DisplayName("测试 RANGE 聚合 (无有效数值输入, 字段不输出)")
        void testRangeWithNoValidNumericsFieldAbsent() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "range_no_numerics");
            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put(FIELD_NAME_RANGE, FlinkAggregationType.RANGE);
            hints.put("other_field", FlinkAggregationType.LAST); // To ensure a point is emitted

            Map<String, Object> fields1 = new HashMap<>();
            fields1.put(FIELD_NAME_RANGE, "abc");
            fields1.put("other_field", "val1");
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, fields1, hints);

            Map<String, Object> fields2 = new HashMap<>();
            fields2.put(FIELD_NAME_RANGE, true);
            fields2.put("other_field", "val2");
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, fields2, hints);

            processor.process(TEST_KEY, mockContext, Arrays.asList(m1, m2), mockCollector);
            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();

            assertResultPoint(point, TEST_DB, TEST_MEASUREMENT_EVENT, windowEnd, tags);
            assertFalse(point.getFields().containsKey(FIELD_NAME_RANGE), "RANGE field should be absent if no valid numerics");
            assertFalse(point.getFieldTypes().containsKey(FIELD_NAME_RANGE));
            assertTrue(point.getFields().containsKey("other_field"));
            assertFieldValue(point, "other_field", "val2");
            assertFieldType(point, "other_field", Common.FieldType.GAUGE);
        }

        @Test
        @DisplayName("测试 RANGE 聚合 (单个 0.0 Double 输入, 范围0.0)")
        void testRangeSingleZeroDotZeroDouble() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "range_single_0.0_double");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap(FIELD_NAME_RANGE, FlinkAggregationType.RANGE);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 0.0), hints);

            processor.process(TEST_KEY, mockContext, Collections.singletonList(m1), mockCollector);
            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();
            assertFieldValue(point, FIELD_NAME_RANGE, 0.0);
            assertEquals(Double.class, point.getFields().get(FIELD_NAME_RANGE).getClass());
            assertFieldType(point, FIELD_NAME_RANGE, Common.FieldType.GAUGE);
        }

        @Test
        @DisplayName("测试 RANGE 聚合 (单个 0L Long 输入, 范围0L)")
        void testRangeSingleZeroLong() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "range_single_0_long");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap(FIELD_NAME_RANGE, FlinkAggregationType.RANGE);
            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 0L), hints);

            processor.process(TEST_KEY, mockContext, Collections.singletonList(m1), mockCollector);
            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();
            assertFieldValue(point, FIELD_NAME_RANGE, 0L);
            assertEquals(Long.class, point.getFields().get(FIELD_NAME_RANGE).getClass());
            assertFieldType(point, FIELD_NAME_RANGE, Common.FieldType.GAUGE);
        }

        @Test
        @DisplayName("测试 RANGE 聚合 (0.0 Double 和其他 Doubles)")
        void testRangeZeroDotZeroWithOtherDoubles() throws Exception {
            Map<String, String> tags = Collections.singletonMap("case", "range_0.0_other_doubles");
            Map<String, FlinkAggregationType> hints = Collections.singletonMap(FIELD_NAME_RANGE, FlinkAggregationType.RANGE);

            StandardMetric m1 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 1000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 0.0), hints);   // Determines Double
            StandardMetric m2 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 2000, tags, Collections.singletonMap(FIELD_NAME_RANGE, 10.5), hints);
            StandardMetric m3 = createMetric(TEST_MEASUREMENT_EVENT, windowStart + 3000, tags, Collections.singletonMap(FIELD_NAME_RANGE, -2.5), hints);

            processor.process(TEST_KEY, mockContext, Arrays.asList(m1, m2, m3), mockCollector);
            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();
            // Values: 0.0, 10.5, -2.5. Min: -2.5, Max: 10.5. Range = 10.5 - (-2.5) = 13.0
            assertFieldValue(point, FIELD_NAME_RANGE, 13.0);
            assertEquals(Double.class, point.getFields().get(FIELD_NAME_RANGE).getClass());
            assertFieldType(point, FIELD_NAME_RANGE, Common.FieldType.GAUGE);
        }

        private Map<String, String> createGCTags() {
            Map<String, String> tags = new HashMap<>();
            tags.put("k8s.container.name", "service-g");
            tags.put("telemetry.distro.version", "2.15.0");
            tags.put("k8s.namespace.name", "test");
            tags.put("col_scope_name", "otlp/12/opentelemetry_metrics");
            tags.put("process.runtime.version", "1.8.0_412-8u412-ga-1build1-b08");
            tags.put("os.type", "linux");
            tags.put("process.pid", "8");
            tags.put("container.id", "646c84e6cf88761736d9361b9c9a206bd1e1db203b14dceaef8c427c574ee9f9");
            tags.put("telemetry.sdk.name", "opentelemetry");
            tags.put("telemetry.sdk.language", "java");
            tags.put("process.runtime.name", "OpenJDK Runtime Environment");
            tags.put("k8s.pod.name", "opentelemetry-service-g-795747c786-z5jfp");
            tags.put("service.instance.id", "test.opentelemetry-service-g-795747c786-z5jfp.service-g");
            tags.put("os.description", "Linux 5.10.0-***********.oe2203sp4.x86_64");
            tags.put("host.arch", "amd64");
            tags.put("k8s.deployment.name", "opentelemetry-service-g");
            tags.put("host.name", "opentelemetry-service-g-795747c786-z5jfp");
            tags.put("telemetry.sdk.version", "1.49.0");
            tags.put("serviceInstance", "test.opentelemetry-service-g-795747c786-z5jfp.service-g");
            tags.put("service.name", "opentelemetry-service-g");
            tags.put("telemetry.distro.name", "opentelemetry-java-instrumentation");
            tags.put("k8s.replicaset.name", "opentelemetry-service-g-795747c786");
            tags.put("k8s.node.name", "host73");
            tags.put("service.version", "20250217");
            tags.put("process.executable.path", "/usr/lib/jvm/java-8-openjdk-amd64/jre/bin/java");
            tags.put("service", "opentelemetry-service-g");
            tags.put("process.command_line", "/usr/lib/jvm/java-8-openjdk-amd64/jre/bin/java -javaagent");
            tags.put("process.runtime.description", "Private Build OpenJDK 64-Bit Server VM 25.412-b08");
            return tags;
        }

        @Test
        @DisplayName("测试 RANGE 聚合 (基于提供的GC数据 - 单个指标)")
        void testRangeWithProvidedGCData_SingleMetric() throws Exception {
            String db = "dddd_apm_metric";
            String measurement = "jvm.gc";
            Map<String, String> gcTags = createGCTags();
            String gcTestKey = db + "|" + measurement + "|" + mapToSortedString(gcTags); // Helper from GenericMetricWriterJob

            Map<String, Object> fields = new HashMap<>();
            fields.put("minor_collection_time", 286L); // JSON has 286, which is treated as Long

            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("minor_collection_time", FlinkAggregationType.RANGE);

            // Adjust timestamp to fit within the test window
            StandardMetric metric = createMetric(db, measurement, windowStart + 1000L, gcTags, fields, hints);

            processor.process(gcTestKey, mockContext, Collections.singletonList(metric), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();

            assertResultPoint(point, db, measurement, windowEnd, gcTags);
            assertFieldValue(point, "minor_collection_time", 0L); // Range of a single point is 0
            assertEquals(Long.class, point.getFields().get("minor_collection_time").getClass());
            assertFieldType(point, "minor_collection_time", Common.FieldType.GAUGE);
        }

        @Test
        @DisplayName("测试 RANGE 聚合 (基于提供的GC数据 - 多个指标)")
        void testRangeWithProvidedGCData_MultipleMetrics() throws Exception {
            String db = "dddd_apm_metric";
            String measurement = "jvm.gc";
            Map<String, String> gcTags = createGCTags();
            String gcTestKey = db + "|" + measurement + "|" + mapToSortedString(gcTags);

            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("minor_collection_time", FlinkAggregationType.RANGE);

            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("minor_collection_time", 286L);
            StandardMetric metric1 = createMetric(db, measurement, windowStart + 1000L, gcTags, fields1, hints);

            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("minor_collection_time", 150L); // New minimum
            StandardMetric metric2 = createMetric(db, measurement, windowStart + 2000L, gcTags, fields2, hints);

            Map<String, Object> fields3 = new HashMap<>();
            fields3.put("minor_collection_time", 300L); // New maximum
            StandardMetric metric3 = createMetric(db, measurement, windowStart + 3000L, gcTags, fields3, hints);

            Map<String, Object> fields4 = new HashMap<>();
            fields4.put("minor_collection_time", 200L); // Another value within range
            StandardMetric metric4 = createMetric(db, measurement, windowStart + 4000L, gcTags, fields4, hints);


            processor.process(gcTestKey, mockContext, Arrays.asList(metric1, metric2, metric3, metric4), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();

            assertResultPoint(point, db, measurement, windowEnd, gcTags);
            // Expected range: max(300L) - min(150L) = 150L
            assertFieldValue(point, "minor_collection_time", 150L);
            assertEquals(Long.class, point.getFields().get("minor_collection_time").getClass());
            assertFieldType(point, "minor_collection_time", Common.FieldType.GAUGE);
        }

        @Test
        @DisplayName("测试 RANGE 聚合 (基于提供的GC数据 - 多个指标都相同值)")
        void testRangeWithProvidedGCData_MultipleMetrics_SameValue() throws Exception {
            String db = "dddd_apm_metric";
            String measurement = "jvm.gc";
            Map<String, String> gcTags = createGCTags();
            String gcTestKey = db + "|" + measurement + "|" + mapToSortedString(gcTags);

            Map<String, FlinkAggregationType> hints = new HashMap<>();
            hints.put("minor_collection_time", FlinkAggregationType.RANGE);

            Map<String, Object> fields1 = new HashMap<>();
            fields1.put("minor_collection_time", 286L);
            StandardMetric metric1 = createMetric(db, measurement, windowStart + 1000L, gcTags, fields1, hints);

            Map<String, Object> fields2 = new HashMap<>();
            fields2.put("minor_collection_time", 286L); // New minimum
            StandardMetric metric2 = createMetric(db, measurement, windowStart + 2000L, gcTags, fields2, hints);

            Map<String, Object> fields3 = new HashMap<>();
            fields3.put("minor_collection_time", 286L); // New maximum
            StandardMetric metric3 = createMetric(db, measurement, windowStart + 3000L, gcTags, fields3, hints);

            Map<String, Object> fields4 = new HashMap<>();
            fields4.put("minor_collection_time", 286L); // Another value within range
            StandardMetric metric4 = createMetric(db, measurement, windowStart + 4000L, gcTags, fields4, hints);


            processor.process(gcTestKey, mockContext, Arrays.asList(metric1, metric2, metric3, metric4), mockCollector);

            verify(mockCollector, times(1)).collect(pointCaptor.capture());
            TSDBPoint point = pointCaptor.getValue();

            assertResultPoint(point, db, measurement, windowEnd, gcTags);
            assertFieldValue(point, "minor_collection_time", 0L);
            assertEquals(Long.class, point.getFields().get("minor_collection_time").getClass());
            assertFieldType(point, "minor_collection_time", Common.FieldType.GAUGE);
        }

    }

    private static String mapToSortedString(Map<String, String> map) {
        if (map == null || map.isEmpty()) {
            return ""; // 空 Map 返回空字符串
        }
        // 按 Key 字母顺序排序，然后拼接
        return map.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> entry.getKey() + "=" + entry.getValue()) // 格式化为 key=value
                .collect(Collectors.joining("&")); // 使用 '&' 作为分隔符 (或其他合适的字符)
    }
    // 其他可能的测试:
    // - 更复杂的 Tag 组合
    // - 不同 Measurement 但 Key 其他部分相同的场景 (取决于 Flink keyBy 逻辑，这里假设 key 包含 measurement)
    // - 涉及 TimeUnit 转换的场景 (如果 StandardMetric 或 TSDBPoint 支持)
}