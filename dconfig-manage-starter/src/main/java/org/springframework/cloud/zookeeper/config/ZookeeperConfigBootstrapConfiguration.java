package org.springframework.cloud.zookeeper.config;

import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Objects;

@Configuration
public class ZookeeperConfigBootstrapConfiguration {

    @Value("${spring.cloud.zookeeper.connect-string:}")
    private String connectString;

    @Value("${spring.cloud.zookeeper.password:}")
    private String password;

    @Value("${spring.cloud.zookeeper.loginconfig:}")
    private String authLoginConfig;

    @Bean
    public CuratorFramework curator() throws Exception {
//        System.setProperty(ZKClientConfig.ZK_SASL_CLIENT_USERNAME, "databuff");
//        System.setProperty(ZKClientConfig.ENABLE_CLIENT_SASL_KEY, "true");
//        System.setProperty("java.security.auth.login.config", authLoginConfig);
        CuratorFramework curator = CuratorFrameworkFactory.builder()
                .connectString(connectString)
                .retryPolicy((i, l, retrySleeper) -> false)
//                .authorization("digest", password.getBytes())
                .build();
        curator.start();
        return curator;
    }

    @Bean
    public ZookeeperPropertySourceLocator zookeeperPropertySourceLocator(CuratorFramework curator, DCZookeeperConfigProperties properties) {
        return new CustomZookeeperPropertySourceLocator(curator, properties);
    }

    @Bean
    public DCZookeeperConfigProperties zookeeperConfigProperties() {
        return new DCZookeeperConfigProperties();
    }
}