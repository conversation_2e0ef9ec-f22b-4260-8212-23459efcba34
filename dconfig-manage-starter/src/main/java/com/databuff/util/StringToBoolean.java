package com.databuff.util;

import com.alibaba.fastjson.JSON;
import dto.DConf;
import org.springframework.core.convert.converter.Converter;

public class StringToBoolean implements Converter<String, Boolean> {
    @Override
    public Boolean convert(String source) {
        if (!JSON.isValidObject(source)) {
            return Boolean.valueOf(source);
        }
        final DConf<Boolean> dConf = JSON.parseObject(source, DConf.class);
        return dConf.getValue();
    }
}