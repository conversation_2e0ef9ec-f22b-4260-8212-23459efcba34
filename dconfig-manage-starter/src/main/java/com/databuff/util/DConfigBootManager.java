package com.databuff.util;

import dto.DConf;
import io.swagger.annotations.Api;
import org.apache.curator.framework.CuratorFramework;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 分布式配置管理器
 */
@Api(value = "DConfigBootManager", tags = "分布式配置管理器")
@Component
public class DConfigBootManager extends DConfigManager implements IDConfigManager<DConf> {

    @Value("${spring.cloud.zookeeper.config.root:databuff}")
    private String root;


    @Value("${spring.application.name:default}")
    private String appName;

    @Autowired
    public DConfigBootManager(CuratorFramework curatorFramework) {
        super(curatorFramework);
    }

    @Override
    public void saveNode(String path, DConf dConf, Boolean overWrite) throws Exception {
        super.saveNode(getPath(path), dConf, overWrite);
    }

    @Override
    public void saveNodeByKey(String key, DConf dConf, Boolean overWrite) throws Exception {
        String path = String.format("/%s/%s/%s", root, appName, key.replace('.', '/'));
        super.saveNode(path, dConf, overWrite);
    }

    @Override
    public void createNode(String path, DConf data) throws Exception {
        super.createNode(getPath(path), data);
    }

    @Override
    public void deleteNode(String path) throws Exception {
        super.deleteNode(getPath(path));
    }

    @Override
    public void updateNode(String path, DConf data) throws Exception {
        super.updateNode(getPath(path), data);
    }

    @Override
    public DConf getData(String path) throws Exception {
        return super.getData(getPath(path));
    }

    @Override
    public List<DConf> getConfs(String path) throws Exception {
        return super.getConfs(getPath(path));
    }

    @Override
    public List<String> getChildrenPaths(String path) throws Exception {
        return super.getChildrenPaths(getPath(path));
    }

    private String getPath(String path) {
        if (path == null) {
            return "/" + root;
        }
        return path.startsWith("/" + root) ? path : "/" + root + path;
    }
}
