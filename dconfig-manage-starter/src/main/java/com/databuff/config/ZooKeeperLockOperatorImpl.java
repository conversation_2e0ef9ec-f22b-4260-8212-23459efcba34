package com.databuff.config;

import com.databuff.common.annotation.LogExecutionTime;
import com.databuff.lock.DConfigLockOperator;
import com.databuff.util.DConfigLockFactory;
import com.sun.istack.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.recipes.locks.InterProcessLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class ZooKeeperLockOperatorImpl implements DConfigLockOperator {

    @Autowired
    private DConfigLockFactory dConfigLockFactory;

    /**
     * 获取锁并执行回调方法
     *
     * @param lockKey      锁的键
     * @param attempts     尝试次数
     * @param timeout      超时时间
     * @param unit         时间单位
     * @param customMethod 自定义方法
     * @return 如果成功获取锁并执行回调方法则返回true，否则返回false
     */
    @Override
    public boolean acquireLockAndExecuteCallback(String lockKey,
                                                 int attempts,
                                                 long timeout,
                                                 TimeUnit unit,
                                                 Runnable customMethod) {
        // 获取锁实例
        final InterProcessLock lockInstance = dConfigLockFactory.getLockInstance(lockKey);
        try {
            // 如果尝试次数小于等于0，直接返回false
            if (attempts <= 0) {
                log.warn("尝试次数小于等于0，无法获取锁: {}", lockKey);
                return false;
            }

            // 尝试在指定时间内获取锁
            boolean isLock = lockInstance.acquire(timeout, unit);
            if (isLock) {
                log.debug("成功获取锁: {}", lockKey);
                try {
                    // 成功获取锁后执行自定义方法
                    customMethod.run();
                    return true;
                } finally {
                    // 释放锁
                    lockInstance.release();
                    log.debug("释放锁: {}", lockKey);
                }
            } else {
                // 如果未能获取锁，递归调用自身，减少尝试次数
                log.warn("未能获取锁，剩余尝试次数: {}，锁: {}", attempts - 1, lockKey);
                return acquireLockAndExecuteCallback(lockKey, attempts - 1, timeout, unit, customMethod);
            }
        } catch (Exception e) {
            log.error("获取锁时发生异常: {}", lockKey, e);
            return false;
        }
    }

    /**
     * 获取锁并执行回调方法，使用默认超时时间30秒
     *
     * @param lockKey      锁的键
     * @param attempts     尝试次数
     * @param customMethod 自定义方法
     * @return 如果成功获取锁并执行回调方法则返回true，否则返回false
     */
    @Override
    public boolean acquireLockAndExecuteCallback(String lockKey, int attempts, Runnable customMethod) {
        return acquireLockAndExecuteCallback(lockKey, attempts, 30, TimeUnit.SECONDS, customMethod);
    }

    /**
     * 获取锁并执行回调方法，使用默认尝试次数5次和默认超时时间30秒
     *
     * @param lockKey      锁的键
     * @param customMethod 自定义方法
     * @return 如果成功获取锁并执行回调方法则返回true，否则返回false
     */
    @Override
    public boolean acquireLockAndExecuteCallback(String lockKey, Runnable customMethod) {
        return acquireLockAndExecuteCallback(lockKey, 5, 30, TimeUnit.SECONDS, customMethod);
    }

    /**
     * 获取锁并执行回调方法
     *
     * @param lockKey      锁的键
     * @param attempts     尝试次数
     * @param timeout      超时时间
     * @param unit         时间单位
     * @param customMethod 成功回调方法
     * @param failMethod   失败回调方法
     * @return void
     */
    @Override
    public <T> T acquireLockAndExecuteCallback(String lockKey, int attempts, long timeout, TimeUnit unit, @NotNull Callable<T> customMethod, Callable<T> failMethod) {
        final InterProcessLock lockInstance = dConfigLockFactory.getLockInstance(lockKey);
        try {
            if (attempts <= 0) {
                log.warn("尝试次数小于等于0，无法获取锁: {}", lockKey);
                if (failMethod != null) {
                    return failMethod.call();
                }
                return null;
            }

            boolean isLock = lockInstance.acquire(timeout, unit);
            if (isLock) {
                log.debug("成功获取锁: {}", lockKey);
                T result;
                try {
                    result = customMethod.call();
                } finally {
                    lockInstance.release();
                    log.debug("释放锁: {}", lockKey);
                }
                return result;
            } else {
                log.warn("未能获取锁，剩余尝试次数: {}，锁: {}", attempts - 1, lockKey);
                return acquireLockAndExecuteCallback(lockKey, attempts - 1, timeout, unit, customMethod, failMethod);
            }
        } catch (Exception e) {
            log.error("获取锁时发生异常: {}", lockKey, e);
            if (failMethod != null) {
                try {
                    return failMethod.call();
                } catch (Exception ex) {
                    log.error("执行失败回调方法时发生异常: {}", lockKey, ex);
                }
            }
            return null;
        }
    }
    /**
     * 获取锁并执行回调方法
     *
     * @param lockKey      锁的键
     * @param attempts     尝试次数
     * @param customMethod 成功回调方法
     * @param failMethod   失败回调方法
     * @return void
     */
    @Override
    @LogExecutionTime(tag = {"lockKey"})
    public <T> T acquireLockAndExecuteCallback(String lockKey, int attempts, @NotNull Callable<T> customMethod, Callable<T> failMethod) {
        return acquireLockAndExecuteCallback(lockKey, attempts, 30, TimeUnit.SECONDS, customMethod, failMethod);
    }
}