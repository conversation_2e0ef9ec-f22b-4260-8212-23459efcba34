# DConfigBootManager使用说明

## 1. DConfigBootManager接口调用示例

DConfigBootManager(单例)是IDConfigManager接口的一个封装类，引入了springboot，可以使用注入的方式使用DConfigBootManager。下面详细举个3个接口调用的例子：

### 1.1 保存节点（path一样会更新）

```java
@Autowired
private DConfigBootManager dConfigBootManager;

public void saveNodeExample() {
  try {
    DConf dConf = DConf.builder().desc("描述").key("键").value("任何类型值").path("路径").builtIn(true).build();
    dConfigBootManager.saveNode(dConf);
  } catch (Exception e) {
    e.printStackTrace();
  }
}
```

### 1.2 更新节点

```java
@Autowired
private DConfigBootManager dConfigBootManager;

public void updateNodeExample() {
    try {
        dConfigBootManager.updateNode("/path/to/node", "new node data");
    } catch (Exception e) {
        e.printStackTrace();
    }
}
```

### 1.3 获取子路径及其数据

```java
@Autowired
private DConfigBootManager dConfigBootManager;

public void getConfsExample() {
    try {
        List<DConf> confs = dConfigBootManager.getConfs("/path/to/node");
        for (DConf dConf : confs) {
            System.out.println("Path: " + dConf.getPath() + ", Data: " + dConf.getValue());
        }
    } catch (Exception e) {
        e.printStackTrace();
    }
}
```

## 2. 订阅配置变化

### 2.1 Spring Boot工程

在Spring Boot工程中，引入`<artifactId>dconfig-manage-starter</artifactId>`依赖，

```xml
<dependency>
    <groupId>com.databff</groupId>
    <artifactId>dconfig-manage-starter</artifactId>
    <version>${databuff.version}</version>
</dependency>
```
在`bootstrap.yml`文件中进行一些配置(由于优先级关系不能配置在application.yml中)。
以下是一个配置示例：
```yaml
spring:
  application:
    name: task-executor  # 应用名称
  cloud:
    zookeeper:
      config:
        enabled: true  # 是否启用ZooKeeper配置
        root: databuff  # ZooKeeper的根路径
        profileSeparator: ','  # 配置文件分隔符
      discovery:
        enabled: true  # 是否启用ZooKeeper服务发现
      connect-string: zookeeper:2181  # ZooKeeper服务器地址
      connection-timeout: 10000  # 连接超时时间，单位为毫秒
      session-timeout: 60000  # 会话超时时间，单位为毫秒
      base-sleep-time-ms: 1000  # 重试策略的基础睡眠时间，单位为毫秒
      max-retries: 3  # 重试策略的最大重试次数
      max-sleep-ms: 3000  # 重试策略的最大睡眠时间，单位为毫秒
```


之后就可以使用`@Value("${变量名称:默认值}")`来读取Zookeeper中的动态配置。

如果需要监听（准实时）配置变化，需要满足以下条件：

1. 配置所在的class必须是一个Spring Bean（@Component、@Service、@Repository或@Controller等）
2. 需要在Spring Bean上配置`@RefreshScope`注解来监听配置变化，更新到内存配置中。

`@Value`读取配置是有优先级的，这些数据源的优先级从上到下依次降低，也就是说，如果同一个属性在多个数据源中都有定义，那么`@Value`会使用优先级最高的那个数据源中的值。

### 2.2 非Spring Boot工程

在非Spring Boot工程中，引入`<artifactId>dconfig-manage</artifactId>`依赖，可以使用`DConfigManagerFactory`创建`DConfigManager`。

使用`DConfigManager` bean的`subscribeNodeChange`订阅节点变化，`unsubscribeNodeChange`取消订阅节点变化。

以下是一个`subscribeNodeChange`和`unsubscribeNodeChange`的示例代码：

```java
public class DConfigManagerExample {

    private DConfigManager dConfigManager;

    public DConfigManagerExample() {
        dConfigManager = DConfigManagerFactory.create("zookeeper.connect.string");
    }

    public void subscribeNodeChangeExample() {
        dConfigManager.subscribeNodeChange("/path/to/node", CuratorCacheListener.builder()
            .forCreates(node -> System.out.println("Node created: " + node))
            .forChanges((oldNode, node) -> System.out.println("Node changed: " + node))
            .forDeletes(oldNode -> System.out.println("Node deleted: " + oldNode)));
    }

    public void unsubscribeNodeChangeExample() {
        dConfigManager.unsubscribeNodeChange("/path/to/node");
    }
}
```

## 3. 配置格式说明

`DConf<T>`规范了配置项的格式，包括描述，配置项键，配置项值(任意类型)，配置项路径，是否内置标记。建议所有内置配置项定义时都需要包含以上字段，使用`DConf`获取对应的配置项。

DConf数据结构：
```java
@ApiModel(value = "DConf", description = "配置信息")
public class DConf<T> implements Serializable {
    @ApiModelProperty(value = "描述", example = "这是一个示例描述")
    private String desc;
    @ApiModelProperty(value = "键", example = "exampleKey")
    private String key;
    @ApiModelProperty(value = "值", example = "exampleValue")
    private T value;
    @ApiModelProperty(value = "路径", example = "/example/path")
    private String path;
    @ApiModelProperty(value = "是否内置", example = "true")
    private boolean builtIn;
}
```
使用样例：
```java
@Value("${alarm.enabled}")
private DConf<Boolean> dConf;
```

## 4. 注意事项

- 在使用`@RefreshScope`时，需要注意这可能会导致定时任务`@Scheduled()`失效（当配置刷新后）。因此，建议避免在同一个类中同时使用这两个注解。

- `@Value`读取配置是有优先级的，这些数据源的优先级从上到下依次降低，也就是说，如果同一个属性在多个数据源中都有定义，那么`@Value`会使用优先级最高的那个数据源中的值。数据源的优先级如下：

    1. 命令行参数
    2. `SPRING_APPLICATION_JSON`中的属性。`SPRING_APPLICATION_JSON`是一个包含JSON格式的环境变量，用于提供一组应用程序属性。
    3. `ServletConfig`初始化参数
    4. `ServletContext`初始化参数
    5. `java:comp/env`中的JNDI属性
    6. `System.getProperties()`返回的系统属性
    7. 操作系统环境变量
    8. `RandomValuePropertySource`配置的随机值
    9. 应用程序以外的`application.properties`或`application.yml`配置文件
    10. 应用程序的`application.properties`或`application.yml`配置文件
    11. `@PropertySource`注解指定的配置文件
    12. 默认属性（使用`SpringApplication.setDefaultProperties`指定）

- Zookeeper配置的加载和合并过程主要由`ZookeeperPropertySourceLocator`类和`ZookeeperPropertySource`类完成。这些路径的优先级从上到下依次降低，也就是说，如果同一个属性在多个路径中都有定义，那么`ZookeeperPropertySourceLocator`会使用优先级最高的那个路径中的值。例如，一个root是databuff，名为"testApp"且激活了"dev"配置文件的应用，会有以下的上下文路径：

    1. `databuff/testApp,dev`
    2. `databuff/testApp`
    3. `databuff/application,dev`
    4. `databuff/application`