package com.databuff.dts.util;

import com.databuff.entity.rum.web.UrlAggregationSettings;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

class UrlProcessorTest {

    private List<UrlAggregationSettings.UriAggregationRule> rules;
    private List<String> errors = new ArrayList<>();

    @BeforeEach
    void setUp() {
        // 初始化聚合规则列表
        rules = Arrays.asList(
                new UrlAggregationSettings.UriAggregationRule(1, "/app/*.x"),
                new UrlAggregationSettings.UriAggregationRule(2, "/app/p?name"),
                new UrlAggregationSettings.UriAggregationRule(3, "/**/example"),
                new UrlAggregationSettings.UriAggregationRule(4, "/app/**/dir/file.*"),
                new UrlAggregationSettings.UriAggregationRule(5, "/**/*.jsp"),
                new UrlAggregationSettings.UriAggregationRule(6, "/api/v?/users/*"),
                new UrlAggregationSettings.UriAggregationRule(7, "/products/**/reviews"),
                new UrlAggregationSettings.UriAggregationRule(8, "/images/**/*.png"),
                new UrlAggregationSettings.UriAggregationRule(9, "/search/**"),
                // New rules for additional test cases
                new UrlAggregationSettings.UriAggregationRule(10, "/**/*.txt"),
                new UrlAggregationSettings.UriAggregationRule(11, "/**/api/v?/**"),
                new UrlAggregationSettings.UriAggregationRule(12, "/**/*file*.*")
        );

    }

    @Test
    void testNormalizeUrlWithVariousInputs() throws URISyntaxException {
        String url = "https://www.google.com/abc123abc/82737764223/OrderDispatchService?abc=123&def=456";
        String url1 = "https://192.168.50.113/abc123abc/82737764223/OrderDispatchService?abc=123&def=456";
        //锚点（anchor）。它通常用于指定网页中的一个具体位置，浏览器会将页面滚动到该位置。
        String url2 = "http://192.168.50.113:8084/#/mq/kafka?abc=123&def=456";
        String url3 = "https://example.com/app/test.x?param=value#fragment";
        String url4 = "https://example.com#fragment";
        String url5 = "https://example.com/app/#/test.x?param=value";
        String url6 = "https://**************/fonts/service-type-iconfont.b9784791.woff2";
        String url7 = "https://**************/css/element-dark.css";
        String url8 = "https://**************/browser.js?v1.0.0";
        String url9 = "https://**************/css/vendors~app.6db6ff74.css";
        String url10 = "https://**************/databuff/appMonitor/relationMap?__ps=m&durationRange=14400000";

        assertUrlNormalization(url, "/www.google.com/abc123abc/82737764223/OrderDispatchService", "/www.google.com/abc123abc/?/OrderDispatchService", "/www.google.com/?/?/OrderDispatchService");
        assertUrlNormalization(url1, "/192.168.50.113/abc123abc/82737764223/OrderDispatchService", "/192.168.50.113/abc123abc/?/OrderDispatchService", "/192.168.50.113/?/?/OrderDispatchService");
        assertUrlNormalization(url2, "/192.168.50.113:8084/#/mq/kafka", "/192.168.50.113:8084/#/mq/kafka", "/192.168.50.113:8084/#/mq/kafka");
        assertUrlNormalization(url3, "/example.com/app/test.x", "/example.com/app/test.x", "/example.com/app/test.x");
        assertUrlNormalization(url4, "/example.com", "/example.com", "/example.com");
        assertUrlNormalization(url5, "/example.com/app/#/test.x", "/example.com/app/#/test.x", "/example.com/app/#/test.x");




//        原始URL: https://www.google.com/abc123abc/82737764223/OrderDispatchService?abc=123&def=456
//        不做标准化展示: /www.google.com/abc123abc/82737764223/OrderDispatchService
//        纯数字的段替换为?后展示: /www.google.com/abc123abc/?/OrderDispatchService
//        含数字的段替换为?后展示: /www.google.com/?/?/OrderDispatchService
//
//
//        原始URL: https://192.168.50.113/abc123abc/82737764223/OrderDispatchService?abc=123&def=456
//        不做标准化展示: /192.168.50.113/abc123abc/82737764223/OrderDispatchService
//        纯数字的段替换为?后展示: /192.168.50.113/abc123abc/?/OrderDispatchService
//        含数字的段替换为?后展示: /192.168.50.113/?/?/OrderDispatchService
//
//        原始URL: http://192.168.50.113:8084/#/mq/kafka?abc=123&def=456
//        不做标准化展示: /192.168.50.113:8084/#/mq/kafka
//        纯数字的段替换为?后展示:/192.168.50.113:8084/#/mq/kafka
//        含数字的段替换为?后展示: /192.168.50.113:8084/#/mq/kafka

//        原始URL: https://example.com/app/test.x?param=value#fragment
//        不做标准化展示: /example.com/app/test.x
//        纯数字的段替换为?后展示: /example.com/app/test.x
//        含数字的段替换为?后展示: /example.com/app/test.x


//        原始URL: https://example.com#fragment
//        不做标准化展示: /example.com
//        纯数字的段替换为?后展示: /example.com
//        含数字的段替换为?后展示: /example.com


//        原始URL: https://example.com/app/#/test.x?param=value
//        不做标准化展示:/example.com/app/#/test.x
//         纯数字的段替换为?后展示: /example.com/app/#/test.x
//        含数字的段替换为?后展示:/example.com/app/#/test.x


//        原始URL: https://**************/css/vendors~app.6db6ff74.css
//        不做标准化展示: /**************/css/vendors~app.6db6ff74.css
//        纯数字的段替换为?后展示: /**************/css/vendors~app.6db6ff74.css
//        含数字的段替换为?后展示: /**************/css/?

        URI uri = new URI(url2);
        String path = uri.getPath();
        System.out.println(path);
        printUrlNormalization(url);
        printUrlNormalization(url1);
        printUrlNormalization(url2);
        printUrlNormalization(url3);
        printUrlNormalization(url4);
        printUrlNormalization(url5);
        printUrlNormalization(url6);
        printUrlNormalization(url7);
        printUrlNormalization(url8);
        printUrlNormalization(url9);
        printUrlNormalization(url10);
    }

    private void assertUrlNormalization(String input, String expectedNoNormalization, String expectedPureNumeric, String expectedAlphanumeric) {
        assertEquals(expectedNoNormalization, UrlProcessor.normalizeUrl(input, -1), "No normalization for: " + input);
        assertEquals(expectedPureNumeric, UrlProcessor.normalizeUrl(input, 0), "Pure numeric normalization for: " + input);
        assertEquals(expectedAlphanumeric, UrlProcessor.normalizeUrl(input, 1), "Alphanumeric normalization for: " + input);
    }

    private void printUrlNormalization(String input) {
        System.out.println("原始URL: " + input);
        System.out.println("不做标准化展示: " + UrlProcessor.normalizeUrl(input, -1));
        System.out.println("纯数字的段替换为?后展示: " + UrlProcessor.normalizeUrl(input, 0));
        System.out.println("含数字的段替换为?后展示: " + UrlProcessor.normalizeUrl(input, 1));
    }


    @Test
    void runAllTests() {
        // 运行所有测试方法
        testQueryParametersAndFragments();
        testNewComplexAggregateUri();
        testComplexAggregateUri();
        testOther();
        testEdgeCases();
        testAdditionalCases();

        // 如果有错误，抛出断言错误并显示所有错误信息
        if (!errors.isEmpty()) {
            StringBuilder errorMessage = new StringBuilder("测试遇到以下错误:\n");
            for (String error : errors) {
                errorMessage.append(error).append("\n");
            }
            throw new AssertionError(errorMessage.toString());
        }
    }

    /**
     * 断言URL聚合结果是否符合预期。
     *
     * @param input    输入的URL。
     * @param expected 预期的聚合结果。
     */
    private void assertUrlAggregation(String input, String expected) {
        String actual = UrlProcessor.aggregateUri(input, rules);
        if (!expected.equals(actual)) {
            String error = String.format("输入: %s\n预期: %s\n实际: %s", input, expected, actual);
            errors.add(error);
        }
    }

    /**
     * 测试包含查询参数和片段的URL。
     */
    void testQueryParametersAndFragments() {
        assertUrlAggregation("https://example.com/app/test.x?param=value#fragment", "/example.com/app/*.x");
        assertUrlAggregation("https://example.com/app/#/test.x?param=value", "/example.com/app/#/test.x");
        assertUrlAggregation("https://example.com/search/products?category=electronics&sort=price#results", "/example.com/search/**");
        assertUrlAggregation("https://example.com/search/#/products?category=electronics&sort=price", "/example.com/search/**");
    }

    /**
     * 测试新的复杂聚合URI规则。
     */
    void testNewComplexAggregateUri() {
        assertUrlAggregation("https://example.com/api/v2/users/12345", "/example.com/api/v?/users/*");
        assertUrlAggregation("https://example.com/api/v3/users/john.doe", "/example.com/api/v?/users/*");
        assertUrlAggregation("https://example.com/products/electronics/phones/iphone/reviews", "/example.com/products/**/reviews");
        assertUrlAggregation("https://example.com/products/reviews", "/example.com/products/**/reviews");
        assertUrlAggregation("https://example.com/images/logo.png", "/example.com/images/**/*.png");
        assertUrlAggregation("https://example.com/images/user/profile/avatar.png", "/example.com/images/**/*.png");
        assertUrlAggregation("https://example.com/search?q=test&page=2", "/example.com/search/**");
        assertUrlAggregation("https://example.com/search/advanced/filters", "/example.com/search/**");
    }

    /**
     * 测试复杂的聚合URI规则。
     */
    void testComplexAggregateUri() {
        assertUrlAggregation("https://example.com/app/test.x", "/example.com/app/*.x");
        assertUrlAggregation("https://example.com/app/document.x", "/example.com/app/*.x");
        assertUrlAggregation("https://example.com/app/pxname", "/example.com/app/p?name");
        assertUrlAggregation("https://example.com/app/pyname", "/example.com/app/p?name");
        assertUrlAggregation("https://example.com/foo/bar/example", "/example.com/**/example");
        assertUrlAggregation("https://example.com/example", "/example.com/**/example");
        assertUrlAggregation("https://example.com/app/foo/bar/dir/file.pdf", "/example.com/app/**/dir/file.*");
        assertUrlAggregation("https://example.com/app/dir/file.txt", "/example.com/app/**/dir/file.*");
        assertUrlAggregation("https://example.com/app/test.jsp", "/example.com/**/*.jsp");
        assertUrlAggregation("https://example.com/deep/nested/page.jsp", "/example.com/**/*.jsp");
    }

    /**
     * 测试其他情况，包括大小写敏感、包含特殊字符、不同端口和用户信息的URL。
     */
    void testOther() {
        assertUrlAggregation("https://example.com/APP/test.x", "/example.com/APP/test.x"); // 大小写敏感
        assertUrlAggregation("https://example.com/app/p%20name", "/example.com/app/p?name"); // URL编码的字符
        assertUrlAggregation("https://example.com:8080/app/test.x", "/example.com:8080/app/*.x"); // 包含端口
        assertUrlAggregation("https://user:<EMAIL>/app/test.x", "/user:<EMAIL>/app/*.x"); // 包含用户信息
        assertUrlAggregation("https://example.com/no/match", "/example.com/no/match"); // 不匹配任何规则
        assertUrlAggregation("https://example.com", "/example.com"); // 仅包含域名
    }

    /**
     * 测试边缘情况，包括空URL、无效URL以及不符合规则的URL。
     */
    void testEdgeCases() {
        assertNull(UrlProcessor.aggregateUri(null, rules)); // 输入URL为空
        assertUrlAggregation("https://example.com/test", "/example.com/test"); // 无特殊规则匹配
        assertUrlAggregation("not a url", "not a url"); // 无效URL格式
        assertUrlAggregation("https://example.com?param=value", "/example.com"); // 仅包含查询参数
        assertUrlAggregation("https://example.com#fragment", "/example.com"); // 仅包含片段
        assertUrlAggregation("http://**************:8080/console/#/search", "/**************:8080/console/#/search"); // 包含IP地址和端口
    }


    void testAdditionalCases() {
        // Test URLs with IP addresses and ports
        assertUrlAggregation("http://***********:8080/app/test.txt", "/***********:8080/**/*.txt");
        assertUrlAggregation("https://********/api/v1/users/123", "/********/api/v?/users/*");

        // Test URLs with subdomains
        assertUrlAggregation("https://subdomain.example.com/app/test.txt", "/subdomain.example.com/**/*.txt");
        assertUrlAggregation("https://api.example.com/api/v2/users/456", "/api.example.com/api/v?/users/*");

        // Test URLs with special characters in path
        assertUrlAggregation("https://example.com/app/test%20file.t", "/example.com/**/*file*.*");
        assertUrlAggregation("https://example.com/search/query+with+spaces", "/example.com/search/**");

        // Test URLs with multiple query parameters and fragments
        assertUrlAggregation("https://example.com/app/test.txt?param1=value1&param2=value2#section1", "/example.com/**/*.txt");
        assertUrlAggregation("https://example.com/search?q=test&page=2&sort=asc#results", "/example.com/search/**");

        // Test URLs with non-ASCII characters
        assertUrlAggregation("https://example.com/app/测试.x", "/example.com/app/*.x");
        assertUrlAggregation("https://example.com/search/中文查询", "/example.com/search/**");

        // Test URLs with different protocols
        assertUrlAggregation("ftp://example.com/app/test.x", "/example.com/app/*.x");
        assertUrlAggregation("ws://example.com/api/v1/socket", "/example.com/**/api/v?/**");

        // Test URLs with repeated path segments
        assertUrlAggregation("https://example.com/app/app/test.txt", "/example.com/**/*.txt");
        assertUrlAggregation("https://example.com/products/category/subcategory/item/reviews", "/example.com/products/**/reviews");
    }


    @Test
    void testExtractDomain() {
        assertDomainExtraction("https://www.example.com/path/to/page", "www.example.com");
        assertDomainExtraction("http://subdomain.example.co.uk:8080/path?query=value", "subdomain.example.co.uk");
        assertDomainExtraction("https://***********/admin", "***********");
        assertDomainExtraction("ftp://ftp.example.org:21/", "ftp.example.org");
        assertDomainExtraction("https://localhost/test", "localhost");
        assertDomainExtraction("invalid-url", null);
        assertDomainExtraction("", null);
        assertDomainExtraction(null, null);
    }

    private void assertDomainExtraction(String input, String expected) {
        String actual = UrlProcessor.extractDomain(input);
        if (!Objects.equals(expected, actual)) {
            String error = String.format("Input: %s\nExpected: %s\nActual: %s", input, expected, actual);
            errors.add(error);
        }
    }

    @Test
    void testExtractDomainFromProcessedUrl() {
        assertExtractDomainFromProcessedUrlExtraction("/www.example.com/path/to/page", "www.example.com");
        assertExtractDomainFromProcessedUrlExtraction("/subdomain.example.co.uk/path", "subdomain.example.co.uk");
        assertExtractDomainFromProcessedUrlExtraction("/***********/admin", "***********");
        assertExtractDomainFromProcessedUrlExtraction("/localhost/test", "localhost");
        assertExtractDomainFromProcessedUrlExtraction("/example.com", "example.com");
        assertExtractDomainFromProcessedUrlExtraction("/", "");
        assertExtractDomainFromProcessedUrlExtraction(null, null);
    }

    private void assertExtractDomainFromProcessedUrlExtraction(String input, String expected) {
        String actual = UrlProcessor.extractDomainFromProcessedUrl(input);
        if (!Objects.equals(expected, actual)) {
            String error = String.format("Input: %s\nExpected: %s\nActual: %s", input, expected, actual);
            errors.add(error);
        }
    }

    @Test
    void testExtractPathFromProcessedUrl() {
        assertPathExtraction("/www.example.com/path/to/page", "/path/to/page");
        assertPathExtraction("/subdomain.example.co.uk/api/v1/users", "/api/v1/users");
        assertPathExtraction("/***********/admin", "/admin");
        assertPathExtraction("/localhost/test", "/test");
        assertPathExtraction("/example.com", "");
        assertPathExtraction("/", "");
        assertPathExtraction(null, null);
    }

    private void assertPathExtraction(String input, String expected) {
        String actual = UrlProcessor.extractPathFromProcessedUrl(input);
        if (!Objects.equals(expected, actual)) {
            String error = String.format("Input: %s\nExpected: %s\nActual: %s", input, expected, actual);
            errors.add(error);
        }
    }



}
