package com.databuff.dts.util.com.virjar.geolib;

import com.databuff.dts.util.com.virjar.geolib.bean.GeoAdmin;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class GeoTransTest {

    @Test
    void testResolveGeoAdmin() {
        // Test Hangzhou coordinates
        double lng = 119.992008;
        double lat = 30.278615;

        //开源的不准
        GeoAdmin result = GeoTrans.resolveGeoAdmin(lng, lat);
        assertEquals("浙江省", result.getProvince());
        assertEquals("杭州市", result.getCity());
        assertEquals("西湖区", result.getDistrict());

        // Test Beijing coordinates
        lng = 116.456141;
        lat = 39.926800;

        result = GeoTrans.resolveGeoAdmin(lng, lat);
        assertEquals("北京市", result.getProvince());
        assertEquals("北京城区", result.getCity());
        assertEquals("东城区", result.getDistrict());
    }

    @Test
    void testPerformance() {
        double lng = 119.992008;
        double lat = 30.278615;

        long start = System.currentTimeMillis();
        int iterations = 100000;

        for(int i = 0; i < iterations; i++) {
            GeoAdmin geoAdmin = GeoTrans.resolveGeoAdmin(lng, lat);
            if (geoAdmin == null) {
            }

            String a=String.format("%s/%s/%s",
                    geoAdmin.getProvince(),
                    geoAdmin.getCity(),
                    geoAdmin.getDistrict());
        }

        long duration = System.currentTimeMillis() - start;
        System.out.println(iterations + " queries took: " + duration + "ms");
        System.out.println("Average query time: " + (duration/(double)iterations) + "ms");

        assertTrue(duration < 10000, "Performance test took too long");
        //100000 queries took: 4311ms
        //Average query time: 0.04311ms
    }


}
