package com.databuff.dts.receive.biz.processor.model;

import com.databuff.entity.BizEventReqs;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 对 {@link CompiledBizEventReqMatcher} 的单元测试 (V2 - 仅基于 Service ID 和 Resource 匹配)。
 */
@DisplayName("CompiledBizEventReqMatcher 单元测试 (仅 Service ID 匹配)")
class CompiledBizEventReqMatcherTest {

    private BizEventReqs createReq(String svcId, String svcName, List<String> resources) {
        BizEventReqs req = new BizEventReqs();
        req.setSvcId(svcId);
        req.setSvcName(svcName); // svcName 仅供参考，不参与匹配逻辑
        req.setResources(resources);
        return req;
    }

    @Test
    @DisplayName("当匹配规则列表为 null 时，matches 应返回 false")
    void matches_ReturnsFalse_WhenMatcherListIsNull() {
        CompiledBizEventReqMatcher matcher = new CompiledBizEventReqMatcher(null);
        // serviceName 参数会被忽略
        assertThat(matcher.matches("id1",  "res1")).isFalse();
    }

    @Test
    @DisplayName("当匹配规则列表为空时，matches 应返回 false")
    void matches_ReturnsFalse_WhenMatcherListIsEmpty() {
        CompiledBizEventReqMatcher matcher = new CompiledBizEventReqMatcher(Collections.emptyList());
        assertThat(matcher.matches("id1",  "res1")).isFalse();
    }

    @Nested
    @DisplayName("基于 Service ID 和 Resource 的匹配测试")
    class ServiceIdAndResourceMatchingTests {

        @Test
        @DisplayName("当 Service ID 匹配且规则资源列表为 null 时，应返回 true")
        void matches_ReturnsTrue_WhenServiceIdMatchesAndResourcesNull() {
            BizEventReqs rule = createReq("svc-123", "ServiceOne", null);
            CompiledBizEventReqMatcher matcher = new CompiledBizEventReqMatcher(Collections.singletonList(rule));
            assertThat(matcher.matches("svc-123", "anyResource")).isTrue();
        }

        @Test
        @DisplayName("当 Service ID 匹配且规则资源列表为空时，应返回 true")
        void matches_ReturnsTrue_WhenServiceIdMatchesAndResourcesEmpty() {
            BizEventReqs rule = createReq("svc-123", "ServiceOne", Collections.emptyList());
            CompiledBizEventReqMatcher matcher = new CompiledBizEventReqMatcher(Collections.singletonList(rule));
            assertThat(matcher.matches("svc-123",  "anyResource")).isTrue();
        }

        @Test
        @DisplayName("当 Service ID 和资源都匹配时，应返回 true")
        void matches_ReturnsTrue_WhenServiceIdAndResourceMatch() {
            BizEventReqs rule = createReq("svc-123", "ServiceOne", Arrays.asList("resourceA", "resourceB"));
            CompiledBizEventReqMatcher matcher = new CompiledBizEventReqMatcher(Collections.singletonList(rule));
            assertThat(matcher.matches("svc-123",  "resourceA")).isTrue();
        }

        @Test
        @DisplayName("当 Service ID 匹配但资源不匹配时，应返回 false")
        void matches_ReturnsFalse_WhenServiceIdMatchesButResourceDoesNot() {
            BizEventReqs rule = createReq("svc-123", "ServiceOne", Arrays.asList("resourceA", "resourceB"));
            CompiledBizEventReqMatcher matcher = new CompiledBizEventReqMatcher(Collections.singletonList(rule));
            assertThat(matcher.matches("svc-123",  "resourceC")).isFalse();
        }

        @Test
        @DisplayName("当 Service ID 不匹配时，即使 Service Name 匹配也不应返回 true")
        void matches_ReturnsFalse_WhenServiceIdDoesNotMatch() {
            // 规则需要 svc-123，输入是 svc-456，但 Service Name 匹配
            BizEventReqs rule = createReq("svc-123", "MatchingName", null);
            CompiledBizEventReqMatcher matcher = new CompiledBizEventReqMatcher(Collections.singletonList(rule));
            // serviceName 参数 "MatchingName" 被忽略，因为 ID 不匹配
            assertThat(matcher.matches("svc-456",  "anyResource")).isFalse(); // 断言修正为 isFalse
        }

        @Test
        @DisplayName("当规则中 Service ID 为空或 null 时，不应匹配任何传入的 Service ID")
        void matches_ReturnsFalse_WhenRuleSvcIdIsNullOrEmpty() {
            BizEventReqs ruleNullId = createReq(null, "SomeName", null);
            CompiledBizEventReqMatcher matcherNullId = new CompiledBizEventReqMatcher(Collections.singletonList(ruleNullId));
            assertThat(matcherNullId.matches("svc-123",  "anyResource")).isFalse();

            BizEventReqs ruleEmptyId = createReq("", "SomeName", null);
            CompiledBizEventReqMatcher matcherEmptyId = new CompiledBizEventReqMatcher(Collections.singletonList(ruleEmptyId));
            assertThat(matcherEmptyId.matches("svc-123",  "anyResource")).isFalse();
        }


        @Test
        @DisplayName("当有多个规则且其中一个通过 Service ID 和 Resource 匹配时，应返回 true")
        void matches_ReturnsTrue_WhenMultipleMatchersAndOneMatchesByIdAndResource() {
            BizEventReqs rule1 = createReq("svc-nomatch", "NoName", null); // ID 不匹配
            BizEventReqs rule2 = createReq("svc-123", "ServiceOne", Arrays.asList("resourceA")); // ID 和资源都匹配
            BizEventReqs rule3 = createReq("svc-123", "ServiceOne", Arrays.asList("resourceB")); // ID 匹配但资源不匹配
            CompiledBizEventReqMatcher matcher = new CompiledBizEventReqMatcher(Arrays.asList(rule1, rule2, rule3));
            assertThat(matcher.matches("svc-123",  "resourceA")).isTrue();
        }
    }

    // ServiceNameMatchingTests 被移除，因为不再基于 Service Name 匹配

    @Nested
    @DisplayName("输入参数变化的测试")
    class InputVariationsTests {

        @Test
        @DisplayName("当输入的 Service ID 为 null 时，规则中需要 Service ID 的项不应匹配")
        void matches_HandlesNullInputServiceId() {
            BizEventReqs ruleRequiresId = createReq("svc-123", "SomeName", null); // 规则需要 ID
            CompiledBizEventReqMatcher matcher = new CompiledBizEventReqMatcher(Collections.singletonList(ruleRequiresId));
            // 输入的 serviceId 为 null，无法匹配需要 "svc-123" 的规则
            assertThat(matcher.matches(null,  "anyResource")).isFalse();
        }

        @Test
        @DisplayName("当输入的 Service Name 为 null 时，不影响基于 Service ID 的匹配")
        void matches_HandlesNullInputServiceName() {
            BizEventReqs rule = createReq("svc-123", "SomeName", null); // 规则关心 ID
            CompiledBizEventReqMatcher matcher = new CompiledBizEventReqMatcher(Collections.singletonList(rule));
            // 输入的 serviceName 为 null，但 serviceId 匹配，应成功
            assertThat(matcher.matches("svc-123",  "anyResource")).isTrue();
            // 输入的 serviceName 为 null，serviceId 不匹配，应失败
            assertThat(matcher.matches("svc-456",  "anyResource")).isFalse();
        }

        @Test
        @DisplayName("当输入的 Resource 为 null 时")
        void matches_HandlesNullInputResource() {
            // 规则需要特定资源
            BizEventReqs ruleWithResource = createReq("svc-123", "ServiceOne", Arrays.asList("resourceA"));
            CompiledBizEventReqMatcher matcherWithResource = new CompiledBizEventReqMatcher(Collections.singletonList(ruleWithResource));
            // 输入 resource 为 null，规则需要 "resourceA"，不匹配
            assertThat(matcherWithResource.matches("svc-123",  null)).isFalse();

            // 规则不限制资源 (null)
            BizEventReqs ruleNoResourceNull = createReq("svc-123", "ServiceOne", null);
            CompiledBizEventReqMatcher matcherNoResourceNull = new CompiledBizEventReqMatcher(Collections.singletonList(ruleNoResourceNull));
            // 输入 resource 为 null，规则不限资源，匹配
            assertThat(matcherNoResourceNull.matches("svc-123",  null)).isTrue();

            // 规则不限制资源 (empty list)
            BizEventReqs ruleNoResourceEmpty = createReq("svc-123", "ServiceOne", Collections.emptyList());
            CompiledBizEventReqMatcher matcherNoResourceEmpty = new CompiledBizEventReqMatcher(Collections.singletonList(ruleNoResourceEmpty));
            // 输入 resource 为 null，规则不限资源，匹配
            assertThat(matcherNoResourceEmpty.matches("svc-123",  null)).isTrue();
        }

        @Test
        @DisplayName("当规则列表包含 null 条目时，应能跳过并正确处理")
        void matches_HandlesNullRuleInList() {
            BizEventReqs rule1 = createReq("svc-123", "ServiceOne", Arrays.asList("resourceA"));
            CompiledBizEventReqMatcher matcher = new CompiledBizEventReqMatcher(Arrays.asList(null, rule1));
            // 应该能匹配到 rule1
            assertThat(matcher.matches("svc-123",  "resourceA")).isTrue();
            // 应该不能匹配不符合 rule1 的情况
            assertThat(matcher.matches("svc-456", "resourceB")).isFalse();
        }
    }
}