package com.databuff.dts.receive.processor;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

public class DynamicMetaProcessorTest {
    /**
     * 主方法：解析示例 meta 数据，并调用 processRecord() 处理后输出结果。
     * 实际中，处理后的数据将被立即发送到 Kafka。
     */
    @Test
    public void testBasicFunctionality() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        List<String> jsonRecords = Arrays.asList(
                "{\"process_id\":\"7\",\"memory.alloc.byte\":\"304\",\"request.body.length\":\"5\",\"cpu.time.cost.ns\":\"63506\",\"_df.measured\":\"1\",\"_sampling_priority_v1\":\"1\",\"thread.id\":\"33\",\"_df.agent_psr\":\"1.0\",\"_df.top_level\":\"1\"}",
                "{\"process_id\":\"7\",\"memory.alloc.byte\":\"304\",\"request.body.length\":\"5\",\"cpu.time.cost.ns\":\"77881\",\"_df.measured\":\"1\",\"_sampling_priority_v1\":\"1\",\"thread.id\":\"34\",\"_df.agent_psr\":\"1.0\",\"_df.top_level\":\"1\"}",
                "{\"process_id\":\"7\",\"memory.alloc.byte\":\"304\",\"request.body.length\":\"5\",\"cpu.time.cost.ns\":\"67452\",\"_df.measured\":\"1\",\"_sampling_priority_v1\":\"1\",\"thread.id\":\"38\",\"_df.agent_psr\":\"1.0\",\"_df.top_level\":\"1\"}",
                "{\"db.type\":\"mysql\",\"agent.source\":\"databuffJavaAgent\",\"db.operation\":\"select\",\"language\":\"jvm\",\"thread.name\":\"ForkJoinPool.commonPool-worker-13\",\"db.instance\":\"dc_databuff\",\"normalized.resource\":\"select host_id as hostId ,host_name as hostName,ipaddress as hostIp, api_key as apikey from dc_res_host\\n         WHERE api_key = ? and\\n            \\n        \\n                \\n                    ( host_name IN\\n                                    (\\n                                        ?\\n                                    ,\\n                                        ?\\n                                    ,\\n                                        ?\\n                                    ,\\n                                        ?\\n                                    ,\\n                                        ?\\n                                    ,\\n                                        ?\\n                                    ) )\",\"component\":\"java-jdbc-prepared_statement\",\"span.kind\":\"client\",\"db.port\":\"3306\",\"generate.ip\":\"*************\",\"peer.hostname\":\"mysql\",\"generate.service\":\"df::webapp193\"}",
                "{\"db.type\":\"mysql\",\"agent.source\":\"databuffJavaAgent\",\"db.operation\":\"select\",\"language\":\"jvm\",\"thread.name\":\"ForkJoinPool.commonPool-worker-8\",\"db.instance\":\"dc_databuff\",\"normalized.resource\":\"select host_id as hostId ,host_name as hostName,ipaddress as hostIp, api_key as apikey from dc_res_host\\n         WHERE api_key = ? and\\n            \\n        \\n                \\n                    ( host_name IN\\n                                    (\\n                                        ?\\n                                    ,\\n                                        ?\\n                                    ,\\n                                        ?\\n                                    ,\\n                                        ?\\n                                    ,\\n                                        ?\\n                                    ,\\n                                        ?\\n                                    ) )\",\"component\":\"java-jdbc-prepared_statement\",\"span.kind\":\"client\",\"db.port\":\"3306\",\"generate.ip\":\"*************\",\"peer.hostname\":\"mysql\",\"generate.service\":\"df::webapp193\"}",
                "{\"db.type\":\"mysql\",\"agent.source\":\"databuffJavaAgent\",\"db.operation\":\"select\",\"language\":\"jvm\",\"thread.name\":\"ForkJoinPool.commonPool-worker-2\",\"db.instance\":\"dc_databuff\",\"normalized.resource\":\"select host_id as hostId ,host_name as hostName,ipaddress as hostIp, api_key as apikey from dc_res_host\\n         WHERE api_key = ? and\\n            \\n        \\n                \\n                    ( host_name IN\\n                                    (\\n                                        ?\\n                                    ,\\n                                        ?\\n                                    ,\\n                                        ?\\n                                    ,\\n                                        ?\\n                                    ,\\n                                        ?\\n                                    ,\\n                                        ?\\n                                    ) )\",\"component\":\"java-jdbc-prepared_statement\",\"span.kind\":\"client\",\"db.port\":\"3306\",\"generate.ip\":\"*************\",\"peer.hostname\":\"mysql\",\"generate.service\":\"df::webapp193\"}",
                "{\"root.resource\":\"POST /biz/bizScenarios\",\"component\":\"spring-web-controller\",\"span.kind\":\"server\",\"root.type\":\"web\",\"agent.source\":\"databuffJavaAgent\",\"generate.ip\":\"*************\",\"language\":\"jvm\",\"thread.name\":\"http-nio-18080-exec-3\",\"generate.service\":\"df::webapp193\"}",
                "{\"root.resource\":\"POST /biz/bizScenarios\",\"component\":\"spring-web-controller\",\"span.kind\":\"server\",\"root.type\":\"web\",\"agent.source\":\"databuffJavaAgent\",\"generate.ip\":\"*************\",\"language\":\"jvm\",\"thread.name\":\"http-nio-18080-exec-4\",\"generate.service\":\"df::webapp193\"}",
                "{\"root.resource\":\"POST /biz/bizScenarios\",\"component\":\"spring-web-controller\",\"span.kind\":\"server\",\"root.type\":\"web\",\"agent.source\":\"databuffJavaAgent\",\"generate.ip\":\"*************\",\"language\":\"jvm\",\"thread.name\":\"http-nio-18080-exec-9\",\"generate.service\":\"df::webapp193\"}"
        );

        List<Map<String, String>> records = new ArrayList<>();
        for (String json : jsonRecords) {
            Map<String, String> rec = objectMapper.readValue(json, new TypeReference<Map<String, String>>() {});
            records.add(rec);
        }

        DynamicMetaProcessor dynamicMetaProcessor = new DynamicMetaProcessor();
        List<DynamicMetaProcessor.ProcessedMeta> processedList = new ArrayList<>();
        for (Map<String, String> record : records) {
            DynamicMetaProcessor.ProcessedMeta pm = dynamicMetaProcessor.processRecord(record);
            processedList.add(pm);
        }

        for (DynamicMetaProcessor.ProcessedMeta pm : processedList) {
            System.out.println(pm);
            System.out.println("-----------------------------------------------------");
        }
    }

    /**
     * 生成模拟数据：基于基础模板，随机变化指定字段
     */
    private List<Map<String, String>> generateTestData(Map<String, String> baseTemplate,
                                                       Set<String> varyingFields,
                                                       int count) {
        List<Map<String, String>> result = new ArrayList<>(count);
        Random random = new Random();

        for (int i = 0; i < count; i++) {
            Map<String, String> record = new HashMap<>(baseTemplate);

            // 对指定字段进行随机变化
            for (String field : varyingFields) {
                if (record.containsKey(field)) {
                    record.put(field, String.valueOf(random.nextInt(100000)));
                }
            }

            result.add(record);
        }

        return result;
    }

    /**
     * 测试高并发下的锁性能和正确性
     */
    @Test
    public void testConcurrentProcessing() throws Exception {
        // 创建基础模板
        Map<String, String> processTemplate = new HashMap<>();
        processTemplate.put("process_id", "7");
        processTemplate.put("memory.alloc.byte", "304");
        processTemplate.put("request.body.length", "5");
        processTemplate.put("cpu.time.cost.ns", "0");  // 变化字段
        processTemplate.put("_df.measured", "1");
        processTemplate.put("_sampling_priority_v1", "1");
        processTemplate.put("thread.id", "0");  // 变化字段
        processTemplate.put("_df.agent_psr", "1.0");
        processTemplate.put("_df.top_level", "1");

        Map<String, String> dbTemplate = new HashMap<>();
        dbTemplate.put("db.type", "mysql");
        dbTemplate.put("agent.source", "databuffJavaAgent");
        dbTemplate.put("db.operation", "select");
        dbTemplate.put("language", "jvm");
        dbTemplate.put("thread.name", "");  // 变化字段
        dbTemplate.put("db.instance", "dc_databuff");
        dbTemplate.put("component", "java-jdbc-prepared_statement");
        dbTemplate.put("span.kind", "client");
        dbTemplate.put("db.port", "3306");
        dbTemplate.put("generate.ip", "*************");
        dbTemplate.put("peer.hostname", "mysql");
        dbTemplate.put("generate.service", "df::webapp193");

        Map<String, String> webTemplate = new HashMap<>();
        webTemplate.put("root.resource", "POST /biz/bizScenarios");
        webTemplate.put("component", "spring-web-controller");
        webTemplate.put("span.kind", "server");
        webTemplate.put("root.type", "web");
        webTemplate.put("agent.source", "databuffJavaAgent");
        webTemplate.put("generate.ip", "*************");
        webTemplate.put("language", "jvm");
        webTemplate.put("thread.name", "");  // 变化字段
        webTemplate.put("generate.service", "df::webapp193");

        // 生成大量测试数据
        List<Map<String, String>> processRecords = generateTestData(
                processTemplate,
                new HashSet<>(Arrays.asList("cpu.time.cost.ns", "thread.id")),
                1000);

        List<Map<String, String>> dbRecords = generateTestData(
                dbTemplate,
                new HashSet<>(Collections.singletonList("thread.name")),
                1000);

        List<Map<String, String>> webRecords = generateTestData(
                webTemplate,
                new HashSet<>(Collections.singletonList("thread.name")),
                1000);

        // 合并所有记录
        List<Map<String, String>> allRecords = new ArrayList<>();
        allRecords.addAll(processRecords);
        allRecords.addAll(dbRecords);
        allRecords.addAll(webRecords);

        // 随机打乱记录顺序，模拟真实场景
        Collections.shuffle(allRecords);

        // 创建处理器，使用自定义配置
        DynamicMetaProcessor.Config config = DynamicMetaProcessor.Config.builder()
                .cacheInitialCapacity(100)
                .cacheMaximumSize(1000)
                .templateClusterCount(20)  // 增加聚类数量以减少锁竞争
                .build();

        DynamicMetaProcessor processor = new DynamicMetaProcessor(config);

        // 并发处理记录
        int threadCount = 10;  // 使用10个线程并发处理
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        ConcurrentLinkedQueue<Exception> exceptions = new ConcurrentLinkedQueue<>();
        AtomicInteger processedCount = new AtomicInteger(0);

        // 分割数据给每个线程
        int recordsPerThread = allRecords.size() / threadCount;

        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    int startIndex = threadIndex * recordsPerThread;
                    int endIndex = (threadIndex == threadCount - 1)
                            ? allRecords.size()
                            : (threadIndex + 1) * recordsPerThread;

                    List<Map<String, String>> threadRecords =
                            allRecords.subList(startIndex, endIndex);

                    for (Map<String, String> record : threadRecords) {
                        processor.processRecord(record);
                        processedCount.incrementAndGet();
                    }
                } catch (Exception e) {
                    exceptions.add(e);
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        boolean completed = latch.await(30, TimeUnit.SECONDS);
        executor.shutdown();

        // 验证结果
        Assertions.assertTrue(completed, "处理超时");
        Assertions.assertTrue(exceptions.isEmpty(),
                "处理过程中发生异常: " + exceptions.stream()
                        .map(Throwable::getMessage)
                        .collect(Collectors.joining(", ")));

        Assertions.assertEquals(allRecords.size(), processedCount.get(), "处理记录数不匹配");

        // 验证模板数量合理（应该只有3个主要模板）
        long templateCount = processor.getTemplateCount();
        System.out.println("生成的模板数量: " + templateCount);
        Assertions.assertTrue(templateCount <= 10, "模板数量过多，应该只有少量模板");

        // 输出处理结果统计
        System.out.println("成功处理记录数: " + processedCount.get());
        System.out.println("生成的模板数量: " + templateCount);

        // 测试模板重用率
        testTemplateReuseRate(processor, processRecords);
    }

    /**
     * 测试模板重用率 - 验证相似记录是否能够复用模板
     */
    private void testTemplateReuseRate(DynamicMetaProcessor processor, List<Map<String, String>> similarRecords) {
        // 清除之前的模板
        processor.clearTemplates();

        // 先处理一批记录，建立模板
        int initialBatchSize = 100;
        for (int i = 0; i < initialBatchSize; i++) {
            processor.processRecord(similarRecords.get(i));
        }

        // 记录初始模板数
        long initialTemplateCount = processor.getTemplateCount();
        System.out.println("初始模板数: " + initialTemplateCount);

        // 处理剩余记录，检查模板增长率
        for (int i = initialBatchSize; i < similarRecords.size(); i++) {
            processor.processRecord(similarRecords.get(i));
        }

        long finalTemplateCount = processor.getTemplateCount();
        System.out.println("最终模板数: " + finalTemplateCount);

        // 计算模板重用率
        double reuseRate = 1.0 - ((double)(finalTemplateCount - initialTemplateCount) / (similarRecords.size() - initialBatchSize));
        System.out.println("模板重用率: " + (reuseRate * 100) + "%");

        // 验证重用率高于阈值
        Assertions.assertTrue(reuseRate > 0.9, "模板重用率应该高于90%");
    }

    /**
     * 测试高负载下的性能和内存使用
     */
    @Test
    public void testHighLoadPerformance() throws Exception {
        // 创建大量不同类型的模板基础
        List<Map<String, String>> templateBases = new ArrayList<>();

        // 添加10种不同类型的基础模板
        for (int i = 0; i < 10; i++) {
            Map<String, String> template = new HashMap<>();
            template.put("template_type", "type_" + i);
            template.put("common_field_1", "value1");
            template.put("common_field_2", "value2");
            template.put("varying_field_1", "0");  // 将变化
            template.put("varying_field_2", "0");  // 将变化

            // 添加一些随机字段，使模板更加多样化
            for (int j = 0; j < 5; j++) {
                template.put("extra_field_" + i + "_" + j, "value_" + j);
            }

            templateBases.add(template);
        }

        // 为每种模板生成大量变体
        List<Map<String, String>> allRecords = new ArrayList<>();
        Set<String> varyingFields = new HashSet<>(Arrays.asList("varying_field_1", "varying_field_2"));

        for (Map<String, String> base : templateBases) {
            allRecords.addAll(generateTestData(base, varyingFields, 5000));  // 每种模板5000条记录
        }

        // 随机打乱所有记录
        Collections.shuffle(allRecords);

        // 创建处理器，使用较大的缓存容量
        DynamicMetaProcessor.Config config = DynamicMetaProcessor.Config.builder()
                .cacheInitialCapacity(1000)
                .cacheMaximumSize(10000)
                .templateClusterCount(50)  // 更多的聚类以减少锁竞争
                .build();

        DynamicMetaProcessor processor = new DynamicMetaProcessor(config);

        // 测量处理时间和内存使用
        Runtime runtime = Runtime.getRuntime();
        runtime.gc();  // 尝试在测试前进行垃圾回收

        long startMemory = runtime.totalMemory() - runtime.freeMemory();
        long startTime = System.currentTimeMillis();

        // 使用多线程并发处理
        int threadCount = 20;  // 使用20个线程
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);

        int recordsPerThread = allRecords.size() / threadCount;
        AtomicInteger processedCount = new AtomicInteger(0);

        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    int startIndex = threadIndex * recordsPerThread;
                    int endIndex = (threadIndex == threadCount - 1)
                            ? allRecords.size()
                            : (threadIndex + 1) * recordsPerThread;

                    List<Map<String, String>> threadRecords =
                            allRecords.subList(startIndex, endIndex);

                    for (Map<String, String> record : threadRecords) {
                        processor.processRecord(record);
                        processedCount.incrementAndGet();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        boolean completed = latch.await(60, TimeUnit.SECONDS);
        executor.shutdown();

        long endTime = System.currentTimeMillis();
        runtime.gc();  // 尝试在测试后进行垃圾回收
        long endMemory = runtime.totalMemory() - runtime.freeMemory();

        // 计算性能指标
        long processingTime = endTime - startTime;
        long memoryUsed = endMemory - startMemory;
        double recordsPerSecond = (double) processedCount.get() * 1000 / processingTime;

        // 输出性能指标
        System.out.println("处理记录总数: " + processedCount.get());
        System.out.println("处理时间: " + processingTime + " ms");
        System.out.println("每秒处理记录数: " + recordsPerSecond);
        System.out.println("内存使用: " + (memoryUsed / (1024 * 1024)) + " MB");
        System.out.println("生成的模板数量: " + processor.getTemplateCount());

        // 验证结果
        Assertions.assertTrue(completed, "处理超时");
        Assertions.assertEquals(allRecords.size(), processedCount.get(), "处理记录数不匹配");
        Assertions.assertTrue(recordsPerSecond > 10000, "处理速度应该超过每秒10000条记录");
        Assertions.assertTrue(processor.getTemplateCount() <= 50, "模板数量应该不超过50个");
    }

    /**
     * 测试极端情况：所有记录都不同，没有共性
     */
    @Test
    public void testExtremeCaseAllDifferent() {
        // 创建基础模板
        Map<String, String> baseTemplate = new HashMap<>();
        baseTemplate.put("base_field", "base_value");

        // 生成1000条完全不同的记录
        List<Map<String, String>> records = new ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            Map<String, String> record = new HashMap<>(baseTemplate);
            // 添加10个唯一字段，确保每条记录都不同
            for (int j = 0; j < 10; j++) {
                record.put("unique_field_" + j, "unique_value_" + i + "_" + j);
            }
            records.add(record);
        }

        // 使用较小的缓存容量，测试LRU淘汰机制
        DynamicMetaProcessor.Config config = DynamicMetaProcessor.Config.builder()
                .cacheInitialCapacity(100)
                .cacheMaximumSize(200)  // 只允许200个模板
                .similarityThreshold(0.9)  // 提高相似度阈值
                .build();

        DynamicMetaProcessor processor = new DynamicMetaProcessor(config);

        // 处理所有记录
        for (Map<String, String> record : records) {
            processor.processRecord(record);
        }

        // 验证模板数量不超过最大缓存容量
        long templateCount = processor.getTemplateCount();
        System.out.println("极端情况下的模板数量: " + templateCount);
        Assertions.assertTrue(templateCount <= 200, "模板数量不应超过最大缓存容量");
    }

    /**
     * 测试模板演化：记录随时间逐渐变化
     */
    @Test
    public void testTemplateEvolution() {
        // 创建初始模板
        Map<String, String> initialTemplate = new HashMap<>();
        initialTemplate.put("field1", "value1");
        initialTemplate.put("field2", "value2");
        initialTemplate.put("field3", "value3");
        initialTemplate.put("field4", "value4");
        initialTemplate.put("field5", "value5");

        DynamicMetaProcessor processor = new DynamicMetaProcessor();

        // 处理初始模板记录
        processor.processRecord(new HashMap<>(initialTemplate));

        // 逐步演化记录，每次改变一个字段
        Map<String, String> evolvingRecord = new HashMap<>(initialTemplate);

        // 第一阶段：改变field5
        evolvingRecord.put("field5", "newValue5");
        processor.processRecord(new HashMap<>(evolvingRecord));

        // 第二阶段：改变field4
        evolvingRecord.put("field4", "newValue4");
        processor.processRecord(new HashMap<>(evolvingRecord));

        // 第三阶段：改变field3
        evolvingRecord.put("field3", "newValue3");
        processor.processRecord(new HashMap<>(evolvingRecord));

        // 第四阶段：改变field2
        evolvingRecord.put("field2", "newValue2");
        processor.processRecord(new HashMap<>(evolvingRecord));

        // 第五阶段：改变field1
        evolvingRecord.put("field1", "newValue1");
        processor.processRecord(new HashMap<>(evolvingRecord));

        // 检查最终模板数量
        long templateCount = processor.getTemplateCount();
        System.out.println("演化后的模板数量: " + templateCount);

        // 验证模板数量合理（应该有多个模板，但不会为每次变化都创建新模板）
        Assertions.assertTrue(templateCount > 1 && templateCount < 6,
                "模板数量应该在1到6之间，实际为: " + templateCount);
    }
}