package com.databuff.dts.receive.rum.processor.enhancers;

import java.util.Random;

/**
 * 测试lua脚本的多项式滚动哈希
 *
 *  这种哈希方式常被用于字符串哈希或 Rabin-Karp 算法里，对常见的随机输入有不错的分布特性。
 *  2147483647 是一个大质数 。质数作为模数有助于减少哈希冲突。
 * <AUTHOR>
 * @date 2025/01/19
 */
public class StableHash {
    private static final int MOD = 2147483647; // Equivalent to 2^31-1
    private static final int BUCKET_COUNT = 100; // Modulo for bucket distribution

    /**
     * Computes the hash of a string and returns the result modulo 100.
     *
     * @param input The input string to hash (e.g., "entityId:monthStr").
     * @return An integer in the range 0..99 representing the bucket index.
     */
    public static int stableHashMod100(String input) {
        int seed = 131; // Seed value for polynomial rolling hash
        long h = 0; // Use long to avoid overflow in intermediate computations

        for (int i = 0; i < input.length(); i++) {
            h = (h * seed + input.charAt(i)) % MOD; // Simulate (h * seed + char) & 0x7fffffff
        }

        return (int) (h % BUCKET_COUNT); // Final modulo for bucket distribution
    }

    /**
     * Example usage: Simulates random input and tests the distribution of buckets.
     */
    public static void main(String[] args) {
        int testSize = 1_000_000; // Number of samples
        int[] buckets = new int[BUCKET_COUNT]; // Array to count occurrences in each bucket
        Random random = new Random();
        String monthStr = "202502";

        for (int i = 0; i < testSize; i++) {
            // Generate random entityId and construct the input string
            String entityId = String.valueOf(random.nextInt(1_000_000_000));
            String input = entityId + ":" + monthStr;

            // Compute the bucket index and increment its count
            int bucket = stableHashMod100(input);
            buckets[bucket]++;
        }

        // Print results
        System.out.println("Bucket distribution:");
        int total = 0;
        for (int i = 0; i < BUCKET_COUNT; i++) {
            System.out.printf("Bucket %d: %d%n", i, buckets[i]);
            total += buckets[i];
        }
        System.out.printf("Total: %d%n", total);
        //100个桶 最多最少 大约差几百
    }
}

