package com.databuff.dts.receive.biz.processor.config.compiler;

import com.databuff.common.tsdb.model.WhereOp;
import com.databuff.dts.receive.biz.processor.condition.AndCondition;
import com.databuff.dts.receive.biz.processor.condition.ExecutableCondition;
import com.databuff.dts.receive.biz.processor.condition.OrCondition;
import com.databuff.dts.receive.biz.processor.condition.SimpleCondition;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.params.provider.Arguments.arguments;

/**
 * 对 {@link ConditionCompilerImpl} 的单元测试。
 * <p>
 * 验证条件编译器能否正确地:
 * <ul>
 * <li>处理 null 或空的输入条件。</li>
 * <li>解析和编译各种有效的简单条件 (不同操作符、类型)。</li>
 * <li>解析和编译 List 结构为隐式的 AND 条件。</li>
 * <li>解析和编译 Map 结构为显式的 AND/OR 复合条件。</li>
 * <li>处理嵌套的复合条件。</li>
 * <li>根据字段已知类型、值类型、值格式正确推断比较类型。</li>
 * <li>正确解析和转换目标值。</li>
 * <li>在条件结构或内容无效时返回 null 或适当的默认行为。</li>
 * <li>正确收集条件中使用的字段到 requiredFieldsCollector。</li>
 * </ul>
 * </p>
 */
@DisplayName("ConditionCompilerImpl 单元测试 (V3.13)")
class ConditionCompilerImplTest {

    private ConditionCompilerImpl conditionCompiler;
    private Set<String> requiredFieldsCollector;
    private ObjectMapper objectMapper; // 用于测试 SET 解析

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        conditionCompiler = new ConditionCompilerImpl(objectMapper);
        requiredFieldsCollector = new HashSet<>();
    }

    // --- 静态辅助方法 (保持 static) ---
    private static Map<String, Object> createSimpleConditionMap(String field, String operatorSymbol, Object value) {
        return createSimpleConditionMap(field, operatorSymbol, value, null, false);
    }

    private static Map<String, Object> createSimpleConditionMap(String field, String operatorSymbol, Object value, String fieldType, boolean ignoreCase) {
        Map<String, Object> condition = new HashMap<>();
        condition.put("left", field);
        condition.put("operator", operatorSymbol);
        condition.put("right", value);
        if(fieldType != null) {
            condition.put("fieldType", fieldType);
        }
        condition.put("caseInsensitive", ignoreCase);
        return condition;
    }

    private static Map<String, Object> createCompositeConditionMap(String connector, Object left, Object right) {
        Map<String, Object> condition = new HashMap<>();
        condition.put("connector", connector.toUpperCase());
        condition.put("left", left);
        condition.put("right", right);
        return condition;
    }

    // --- 测试方法 ---

    @Nested
    @DisplayName("处理 Null 或空条件")
    class NullOrEmptyConditionTests {

        @Test
        @DisplayName("当输入 conditionsRaw 为 null 时，compileConditions 应返回 null")
        void compileConditions_returnsNull_whenInputIsNull() {
            ExecutableCondition result = conditionCompiler.compileConditions(null, requiredFieldsCollector);
            assertThat(result).isNull();
            assertThat(requiredFieldsCollector).isEmpty();
        }

        @Test
        @DisplayName("当输入 conditionsRaw 为空列表时，compileConditions 应返回 AlwaysTrueCondition")
        void compileConditions_returnsAlwaysTrue_whenInputIsEmptyList() {
            ExecutableCondition result = conditionCompiler.compileConditions(Collections.emptyList(), requiredFieldsCollector);
            assertThat(result).isNotNull();
            assertThat(result.evaluate(Collections.emptyMap())).isTrue();
            assertThat(requiredFieldsCollector).isEmpty();
        }
    }

    @Nested
    @DisplayName("简单条件编译测试")
    class SimpleConditionCompilationTests {

        @ParameterizedTest(name = "[{index}] 操作符符号 \"{1}\"")
        @MethodSource("com.databuff.dts.receive.biz.processor.config.compiler.ConditionCompilerImplTest#validSimpleConditions")
        @DisplayName("应能成功编译各种有效的简单条件")
        void compileConditions_parsesValidSimpleConditions(Map<String, Object> conditionMap, String expectedField, WhereOp expectedOp, Object expectedValue, String expectedCompType) {

            ExecutableCondition result = conditionCompiler.compileConditions(Collections.singletonList(conditionMap), requiredFieldsCollector);

            assertThat(result).isNotNull().isInstanceOf(SimpleCondition.class);
            SimpleCondition simpleCond = (SimpleCondition) result;

            assertThat(simpleCond.getField()).isEqualTo(expectedField);
            assertThat(simpleCond.getOperator()).isEqualTo(expectedOp);

            if (expectedValue instanceof BigDecimal) {
                assertThat(simpleCond.getValue()).isInstanceOf(BigDecimal.class);
                assertThat((BigDecimal) simpleCond.getValue()).isEqualByComparingTo((BigDecimal) expectedValue);
            } else if (expectedValue instanceof Set) {
                assertThat(simpleCond.getValue()).isInstanceOf(Set.class).isEqualTo(expectedValue);
            } else {
                assertThat(simpleCond.getValue()).isEqualTo(expectedValue);
            }
            assertThat(simpleCond.getComparisonType()).isEqualTo(expectedCompType);
            assertThat(requiredFieldsCollector).contains(expectedField);
        }

        @Test
        @DisplayName("当简单条件 Map 无效时 (缺少 operator)，应返回 null")
        void compileConditions_returnsNull_whenSimpleConditionMapIsInvalid() {
            Map<String, Object> invalidMap = new HashMap<>();
            invalidMap.put("left", "field");
            invalidMap.put("right", "value");

            ExecutableCondition result = conditionCompiler.compileConditions(Collections.singletonList(invalidMap), requiredFieldsCollector);
            assertThat(result).isNull();
        }

        @Test
        @DisplayName("当操作符无效时，应返回 null")
        void compileConditions_returnsNull_whenOperatorIsInvalid() {
            Map<String, Object> invalidMap = createSimpleConditionMap("field", "INVALID_OP_SYMBOL", "value");
            ExecutableCondition result = conditionCompiler.compileConditions(Collections.singletonList(invalidMap), requiredFieldsCollector);
            assertThat(result).isNull();
        }

        @Test
        @DisplayName("当数值解析失败时，应返回 null")
        void compileConditions_returnsNull_whenNumericParseFails() {
            Map<String, Object> invalidMap = createSimpleConditionMap("duration", ">", "abc", "NUMERIC", false);
            ExecutableCondition result = conditionCompiler.compileConditions(Collections.singletonList(invalidMap), requiredFieldsCollector);
            assertThat(result).isNull();
        }

        @Test
        @DisplayName("当 Set 解析失败时，应返回带有空 Set 的 SimpleCondition")
        void compileConditions_handlesSetParseFailure() {
            Map<String, Object> invalidMap = createSimpleConditionMap("tags", "INLIST", "[a,b", "SET_STRING", false); // 使用 "INLIST", JSON 语法错误
            ExecutableCondition result = conditionCompiler.compileConditions(Collections.singletonList(invalidMap), requiredFieldsCollector);

            assertThat(result).isNotNull().isInstanceOf(SimpleCondition.class);
            SimpleCondition simpleCond = (SimpleCondition) result;
            assertThat(simpleCond.getOperator()).isEqualTo(WhereOp.IN); // 操作符应为 IN
            assertThat(simpleCond.getValue()).isEqualTo(Collections.emptySet()); // 值是空 Set
            assertThat(simpleCond.getComparisonType()).isEqualTo("SET_STRING");
            assertThat(requiredFieldsCollector).contains("tags");
        }
    }

    @Nested
    @DisplayName("复合条件编译测试 (List = 隐式 AND)")
    class ImplicitAndCompilationTests {

        @Test
        @DisplayName("应将包含多个简单条件的 List 编译为 AndCondition")
        void compileConditions_parsesAndCondition_forListOfSimpleConditions() {
            Map<String, Object> cond1 = createSimpleConditionMap("field1", "=", "A");
            Map<String, Object> cond2 = createSimpleConditionMap("field2", ">", 10, "NUMERIC", false);
            List<Map<String, Object>> conditionList = Arrays.asList(cond1, cond2);
            ExecutableCondition result = conditionCompiler.compileConditions(conditionList, requiredFieldsCollector);

            assertThat(result).isInstanceOf(AndCondition.class);
            AndCondition andCond = (AndCondition) result;
            assertThat(andCond.getChildren()).hasSize(2);
            // 验证第一个子节点
            assertThat(andCond.getChildren().get(0))
                    .isInstanceOfSatisfying(SimpleCondition.class, sc -> {
                        assertThat(sc.getField()).isEqualTo("field1");
                        assertThat(sc.getOperator()).isEqualTo(WhereOp.EQ);
                        assertThat(sc.getValue()).isEqualTo("A");
                    });
            // 验证第二个子节点
            assertThat(andCond.getChildren().get(1))
                    .isInstanceOfSatisfying(SimpleCondition.class, sc -> {
                        assertThat(sc.getField()).isEqualTo("field2");
                        assertThat(sc.getOperator()).isEqualTo(WhereOp.GT);
                        assertThat(sc.getValue()).isInstanceOf(BigDecimal.class);
                        assertThat((BigDecimal)sc.getValue()).isEqualByComparingTo("10");
                        assertThat(sc.getComparisonType()).isEqualTo("NUMERIC");
                    });
            assertThat(requiredFieldsCollector).containsExactlyInAnyOrder("field1", "field2");
        }

        @Test
        @DisplayName("当 List 只包含一个条件时，应直接返回该条件 (优化)")
        void compileConditions_optimizesSingleChild_forListOfConditions() {
            Map<String, Object> cond1 = createSimpleConditionMap("field1", "=", "A");
            List<Map<String, Object>> conditionList = Collections.singletonList(cond1);
            ExecutableCondition result = conditionCompiler.compileConditions(conditionList, requiredFieldsCollector);

            assertThat(result).isInstanceOf(SimpleCondition.class);
            assertThat(((SimpleCondition) result).getField()).isEqualTo("field1");
            assertThat(((SimpleCondition) result).getOperator()).isEqualTo(WhereOp.EQ);
            assertThat(requiredFieldsCollector).containsExactly("field1");
        }

        @Test
        @DisplayName("当 List 中任何一个条件编译失败时，整个 List 编译结果应为 null")
        void compileConditions_returnsNull_whenAnyChildInListFails() {
            Map<String, Object> cond1 = createSimpleConditionMap("field1", "=", "A");
            Map<String, Object> invalidCond = new HashMap<>();
            invalidCond.put("left", "field2"); // 无效条件

            List<Map<String, Object>> conditionList = Arrays.asList(cond1, invalidCond);
            ExecutableCondition result = conditionCompiler.compileConditions(conditionList, requiredFieldsCollector);
            assertThat(result).isNull();
            // field1 可能在失败前被加入
            // assertThat(requiredFieldsCollector).doesNotContain("field2"); // 确保无效字段未加入
        }
    }

    @Nested
    @DisplayName("复合条件编译测试 (Map + connector)")
    class ExplicitCompositeCompilationTests {

        @Test
        @DisplayName("应能解析 connector=AND 的复合条件")
        void compileConditions_parsesExplicitAndCondition() {
            Map<String, Object> condLeft = createSimpleConditionMap("f_left", "=", "L");
            Map<String, Object> condRight = createSimpleConditionMap("f_right", "!=", "R"); // 使用 NEQ
            Map<String, Object> compositeMap = createCompositeConditionMap("AND", condLeft, condRight);
            ExecutableCondition result = conditionCompiler.compileConditions(compositeMap, requiredFieldsCollector);

            assertThat(result).isInstanceOf(AndCondition.class);
            AndCondition andCond = (AndCondition) result;
            assertThat(andCond.getChildren()).hasSize(2);
            // 验证左子节点
            assertThat(andCond.getChildren().get(0))
                    .isInstanceOfSatisfying(SimpleCondition.class, sc -> {
                        assertThat(sc.getField()).isEqualTo("f_left");
                        assertThat(sc.getOperator()).isEqualTo(WhereOp.EQ);
                        assertThat(sc.getValue()).isEqualTo("L");
                    });
            // 验证右子节点
            assertThat(andCond.getChildren().get(1))
                    .isInstanceOfSatisfying(SimpleCondition.class, sc -> {
                        assertThat(sc.getField()).isEqualTo("f_right");
                        assertThat(sc.getOperator()).isEqualTo(WhereOp.NEQ); // 验证 NEQ
                        assertThat(sc.getValue()).isEqualTo("R");
                    });
            assertThat(requiredFieldsCollector).containsExactlyInAnyOrder("f_left", "f_right");
        }

        @Test
        @DisplayName("应能解析 connector=OR 的复合条件")
        void compileConditions_parsesExplicitOrCondition() {
            Map<String, Object> condLeft = createSimpleConditionMap("f_left", "<", 10, "NUMERIC", false);
            Map<String, Object> condRight = createSimpleConditionMap("f_right", ">", 20, "NUMERIC", false);
            Map<String, Object> compositeMap = createCompositeConditionMap("OR", condLeft, condRight);
            ExecutableCondition result = conditionCompiler.compileConditions(compositeMap, requiredFieldsCollector);

            assertThat(result).isInstanceOf(OrCondition.class);
            OrCondition orCond = (OrCondition) result;
            assertThat(orCond.getChildren()).hasSize(2);
            // 验证左子节点
            assertThat(orCond.getChildren().get(0))
                    .isInstanceOfSatisfying(SimpleCondition.class, sc -> {
                        assertThat(sc.getField()).isEqualTo("f_left");
                        assertThat(sc.getOperator()).isEqualTo(WhereOp.LT);
                        assertThat((BigDecimal)sc.getValue()).isEqualByComparingTo("10");
                    });
            // 验证右子节点
            assertThat(orCond.getChildren().get(1))
                    .isInstanceOfSatisfying(SimpleCondition.class, sc -> {
                        assertThat(sc.getField()).isEqualTo("f_right");
                        assertThat(sc.getOperator()).isEqualTo(WhereOp.GT);
                        assertThat((BigDecimal)sc.getValue()).isEqualByComparingTo("20");
                    });
            assertThat(requiredFieldsCollector).containsExactlyInAnyOrder("f_left", "f_right");
        }

        @Test
        @DisplayName("应能解析嵌套的复合条件")
        void compileConditions_parsesNestedCompositeConditions() {
            // (A=1 AND B=2) OR C=3
            Map<String, Object> condA = createSimpleConditionMap("A", "=", 1, "NUMERIC", false);
            Map<String, Object> condB = createSimpleConditionMap("B", "=", 2, "NUMERIC", false);
            Map<String, Object> condC = createSimpleConditionMap("C", "=", 3, "NUMERIC", false);
            Map<String, Object> innerAnd = createCompositeConditionMap("AND", condA, condB);
            Map<String, Object> outerOr = createCompositeConditionMap("OR", innerAnd, condC);
            ExecutableCondition result = conditionCompiler.compileConditions(outerOr, requiredFieldsCollector);

            // 顶层是 OR
            assertThat(result).isInstanceOf(OrCondition.class);
            OrCondition orRoot = (OrCondition) result;
            assertThat(orRoot.getChildren()).hasSize(2);

            // 第一个子节点是 AND
            assertThat(orRoot.getChildren().get(0)).isInstanceOf(AndCondition.class);
            AndCondition innerAndActual = (AndCondition) orRoot.getChildren().get(0);
            assertThat(innerAndActual.getChildren()).hasSize(2);
            // AND 的子节点是 Simple A 和 Simple B
            assertThat(innerAndActual.getChildren().get(0))
                    .isInstanceOfSatisfying(SimpleCondition.class, sc -> assertThat(sc.getField()).isEqualTo("A"));
            assertThat(innerAndActual.getChildren().get(1))
                    .isInstanceOfSatisfying(SimpleCondition.class, sc -> assertThat(sc.getField()).isEqualTo("B"));

            // 第二个子节点是 Simple C
            assertThat(orRoot.getChildren().get(1))
                    .isInstanceOfSatisfying(SimpleCondition.class, sc -> assertThat(sc.getField()).isEqualTo("C"));

            assertThat(requiredFieldsCollector).containsExactlyInAnyOrder("A", "B", "C");
        }

        @Test
        @DisplayName("当复合条件只有一个有效左子节点且右子节点为null/empty(AlwaysTrue)时，不应自动优化为SimpleCondition")
        void compileConditions_doesNotOptimizeSingleChildAndTrue_forExplicitComposite() {
            Map<String, Object> condLeft = createSimpleConditionMap("f_left", "=", "L");
            // rightRaw is null, which compileMap interprets as rightCond = ALWAYS_TRUE_CONDITION
            Map<String, Object> compositeMap = createCompositeConditionMap("AND", condLeft, null);
            ExecutableCondition result = conditionCompiler.compileConditions(compositeMap, requiredFieldsCollector);

            // 修正点：结果仍然是 AndCondition
            assertThat(result).isNotNull().isInstanceOf(AndCondition.class);
            AndCondition andCond = (AndCondition) result;
            assertThat(andCond.getChildren()).hasSize(2);

            // 第一个子节点是 SimpleCondition
            assertThat(andCond.getChildren().get(0)).isInstanceOfSatisfying(SimpleCondition.class, sc -> {
                assertThat(sc.getField()).isEqualTo("f_left");
                assertThat(sc.getOperator()).isEqualTo(WhereOp.EQ);
                assertThat(sc.getValue()).isEqualTo("L");
            });

            // 第二个子节点是 ALWAYS_TRUE_CONDITION
            assertThat(andCond.getChildren().get(1).evaluate(Collections.emptyMap()))
                    .as("Right child of AND should be AlwaysTrue due to null rightRaw")
                    .isTrue();

            assertThat(requiredFieldsCollector).containsExactly("f_left");
        }

        @Test
        @DisplayName("当复合条件的子节点编译失败时应返回 null") // 保持描述
        void compileConditions_returnsNull_whenExplicitCompositeIsInvalid() {
            Map<String, Object> condLeft = createSimpleConditionMap("f_left", "=", "L");
            Map<String, Object> invalidRight = new HashMap<>(); // 无效 Map
            Map<String, Object> compositeMap = createCompositeConditionMap("AND", condLeft, invalidRight);

            ExecutableCondition result = conditionCompiler.compileConditions(compositeMap, requiredFieldsCollector);

            // *** 再次修正断言: 期望结果为 null ***
            assertThat(result).isNull();
            // 可以断言 requiredFieldsCollector 只包含了有效部分的字段（如果需要）
            assertThat(requiredFieldsCollector).containsExactly("f_left"); // 左侧字段应该被收集了
        }

        @Test
        @DisplayName("当 connector 无效时应返回 null")
        void compileConditions_returnsNull_whenConnectorIsInvalid() {
            Map<String, Object> condLeft = createSimpleConditionMap("f_left", "=", "L");
            Map<String, Object> compositeMap = createCompositeConditionMap("INVALID_CONN", condLeft, null);
            ExecutableCondition result = conditionCompiler.compileConditions(compositeMap, requiredFieldsCollector);
            assertThat(result).isNull();
        }
    }

    @Nested
    @DisplayName("类型推断与值解析集成测试")
    class TypeDeterminationIntegrationTests {
        // ... (这部分的测试用例应已通过，保持不变) ...
        @Test
        @DisplayName("对于已知 STRING 字段，即使值像数字，也应按 STRING 处理")
        void compileConditions_usesKnownTypeString_overValueFormat() {
            Map<String, Object> conditionMap = createSimpleConditionMap("service", "=", "123");
            ExecutableCondition result = conditionCompiler.compileConditions(Collections.singletonList(conditionMap), requiredFieldsCollector);
            assertThat(result).isInstanceOf(SimpleCondition.class);
            SimpleCondition simpleCond = (SimpleCondition) result;
            assertThat(simpleCond.getComparisonType()).isEqualTo("STRING");
            assertThat(simpleCond.getValue()).isEqualTo("123");
            assertThat(requiredFieldsCollector).contains("service");
        }
        @Test
        @DisplayName("对于已知 NUMERIC 字段，即使值是字符串，也应按 NUMERIC 处理")
        void compileConditions_usesKnownTypeNumeric_overValueFormat() {
            Map<String, Object> conditionMap = createSimpleConditionMap("duration", "=", "5000");
            ExecutableCondition result = conditionCompiler.compileConditions(Collections.singletonList(conditionMap), requiredFieldsCollector);
            assertThat(result).isInstanceOf(SimpleCondition.class);
            SimpleCondition simpleCond = (SimpleCondition) result;
            assertThat(simpleCond.getComparisonType()).isEqualTo("NUMERIC");
            assertThat(simpleCond.getValue()).isInstanceOf(BigDecimal.class);
            assertThat((BigDecimal)simpleCond.getValue()).isEqualByComparingTo("5000");
            assertThat(requiredFieldsCollector).contains("duration");
        }
        @Test
        @DisplayName("对于未知字段，根据值的格式推断类型 (布尔)")
        void compileConditions_usesValueFormatBoolean_forUnknownField() {
            Map<String, Object> conditionMap = createSimpleConditionMap("unknownField", "=", "true");
            ExecutableCondition result = conditionCompiler.compileConditions(Collections.singletonList(conditionMap), requiredFieldsCollector);
            assertThat(result).isInstanceOf(SimpleCondition.class);
            SimpleCondition simpleCond = (SimpleCondition) result;
            assertThat(simpleCond.getComparisonType()).isEqualTo("BOOLEAN");
            assertThat(simpleCond.getValue()).isEqualTo(Boolean.TRUE);
            assertThat(requiredFieldsCollector).contains("unknownField");
        }
        @Test
        @DisplayName("对于未知字段，根据值的格式推断类型 (数值)")
        void compileConditions_usesValueFormatNumeric_forUnknownField() {
            Map<String, Object> conditionMap = createSimpleConditionMap("unknownNumField", "=", "123.45");
            ExecutableCondition result = conditionCompiler.compileConditions(Collections.singletonList(conditionMap), requiredFieldsCollector);
            assertThat(result).isInstanceOf(SimpleCondition.class);
            SimpleCondition simpleCond = (SimpleCondition) result;
            assertThat(simpleCond.getComparisonType()).isEqualTo("NUMERIC");
            assertThat(simpleCond.getValue()).isInstanceOf(BigDecimal.class);
            assertThat((BigDecimal)simpleCond.getValue()).isEqualByComparingTo("123.45");
            assertThat(requiredFieldsCollector).contains("unknownNumField");
        }
        @Test
        @DisplayName("对于未知字段，如果值格式无法推断，默认为 STRING")
        void compileConditions_defaultsToString_forUnknownFieldAndFormat() {
            Map<String, Object> conditionMap = createSimpleConditionMap("unknownStrField", "=", "some text");
            ExecutableCondition result = conditionCompiler.compileConditions(Collections.singletonList(conditionMap), requiredFieldsCollector);
            assertThat(result).isInstanceOf(SimpleCondition.class);
            SimpleCondition simpleCond = (SimpleCondition) result;
            assertThat(simpleCond.getComparisonType()).isEqualTo("STRING");
            assertThat(simpleCond.getValue()).isEqualTo("some text");
            assertThat(requiredFieldsCollector).contains("unknownStrField");
        }
    }

    // --- 静态 @MethodSource 提供者 (使用 WhereOp.symbol 修正操作符字符串) ---
    static Stream<Arguments> validSimpleConditions() {
        // 提供 Map<String, Object>, expectedField, expectedOp, expectedValue, expectedCompType
        return Stream.of(
                // Numeric
                arguments(createSimpleConditionMap("duration", ">", 100L, "NUMERIC", false), "duration", WhereOp.GT, new BigDecimal("100"), "NUMERIC"),
                arguments(createSimpleConditionMap("level", "=", 5), "level", WhereOp.EQ, new BigDecimal("5"), "NUMERIC"),
                arguments(createSimpleConditionMap("unknown", "<=", "10.5"), "unknown", WhereOp.LTE, new BigDecimal("10.5"), "NUMERIC"),
                // String - *** 使用 WhereOp 定义的 Symbol ***
                arguments(createSimpleConditionMap("service", "LIKE", "Order%", "STRING", false), "service", WhereOp.LIKE, "Order%", "STRING"),
                arguments(createSimpleConditionMap("meta.url", "STARTWITH", "/api", null, true), "meta.url", WhereOp.START_WITH, "/api", "STRING"), // 使用 "STARTWITH"
                arguments(createSimpleConditionMap("unknown", "=", "text"), "unknown", WhereOp.EQ, "text", "STRING"),
                // Boolean
                arguments(createSimpleConditionMap("virtual", "=", true, "BOOLEAN", false), "virtual", WhereOp.EQ, Boolean.TRUE, "BOOLEAN"),
                arguments(createSimpleConditionMap("unknown", "=", "false"), "unknown", WhereOp.EQ, Boolean.FALSE, "BOOLEAN"),
                // Set String - *** 使用 WhereOp 定义的 Symbol ***
                arguments(createSimpleConditionMap("tags", "INLIST", Arrays.asList("A", "B"), "SET_STRING", false), "tags", WhereOp.IN, new HashSet<>(Arrays.asList("A", "B")), "SET_STRING"), // 使用 "INLIST"
                // Set Long - *** 使用 WhereOp 定义的 Symbol ***
                arguments(createSimpleConditionMap("ids", "NOTINLIST", "[100, 200]", "SET_LONG", false), "ids", WhereOp.NOT_IN, new HashSet<>(Arrays.asList(100L, 200L)), "SET_LONG"), // 使用 "NOTINLIST"
                // IS NULL / IS NOT NULL - *** 使用 WhereOp 定义的 Symbol ***
                arguments(createSimpleConditionMap("optionalField", "EMPTY", null), "optionalField", WhereOp.IS, null, "STRING"), // 使用 "EMPTY"
                arguments(createSimpleConditionMap("requiredField", "NOTEMPTY", null), "requiredField", WhereOp.IS_NOT, null, "STRING") // 使用 "NOTEMPTY"
        );
    }

    // 在 ConditionCompilerImplTest.java 中

    // 可以创建一个新的 Nested 测试类来组织这些特定的测试
    @Nested
    @DisplayName("字符串操作符对已知非字符串类型字段的类型推断测试 (无显式fieldType)")
    class StringOperatorOnNonStringFieldTypeInferenceTests {

        // 辅助方法，创建简单条件 Map 时不指定 fieldType
        private Map<String, Object> createSimpleConditionMapNoFieldType(String field, String operatorSymbol, Object value) {
            Map<String, Object> condition = new HashMap<>();
            condition.put("left", field);
            condition.put("operator", operatorSymbol);
            condition.put("right", value);
            // caseInsensitive 可以根据具体测试场景设定，这里默认为 false
            condition.put("caseInsensitive", false);
            return condition;
        }

        @Test
        @DisplayName("已知 LONG 字段 'duration' 与 LIKE 操作符，应推断为 STRING 比较")
        void compile_longFieldWithLike_shouldInferStringComparison() {
            // 前提：DCSpanFieldTypeResolver.getKnownType("duration") 返回 DataType.LONG
            Map<String, Object> conditionMap = createSimpleConditionMapNoFieldType("duration", "LIKE", "12");
            requiredFieldsCollector.clear(); // 清理收集器

            ExecutableCondition result = conditionCompiler.compileConditions(Collections.singletonList(conditionMap), requiredFieldsCollector);

            assertThat(result).isInstanceOf(SimpleCondition.class);
            SimpleCondition simpleCond = (SimpleCondition) result;
            assertThat(simpleCond.getField()).isEqualTo("duration");
            assertThat(simpleCond.getOperator()).isEqualTo(WhereOp.LIKE);
            assertThat(simpleCond.getComparisonType()).isEqualTo("STRING"); // 关键断言：期望比较类型是 STRING
            assertThat(simpleCond.getValue()).isEqualTo("12");         // 目标值应为字符串
            assertThat(requiredFieldsCollector).contains("duration");

            // 可选的实际评估验证
            Map<String, Object> data = new HashMap<>();
            data.put("duration", 123L); // 实际值是 Long
            assertThat(simpleCond.evaluate(data)).as("123L LIKE '12'").isTrue(); // "123" contains "12"

            data.put("duration", 987L);
            assertThat(simpleCond.evaluate(data)).as("987L LIKE '12'").isFalse(); // "987" does not contain "12"
        }

        @Test
        @DisplayName("已知 INTEGER 字段 'error' 与 STARTWITH 操作符，应推断为 STRING 比较")
        void compile_integerFieldWithStartWith_shouldInferStringComparison() {
            // 前提：DCSpanFieldTypeResolver.getKnownType("error") 返回 DataType.INTEGER
            Map<String, Object> conditionMap = createSimpleConditionMapNoFieldType("error", "STARTWITH", "40");
            requiredFieldsCollector.clear();

            ExecutableCondition result = conditionCompiler.compileConditions(Collections.singletonList(conditionMap), requiredFieldsCollector);

            assertThat(result).isInstanceOf(SimpleCondition.class);
            SimpleCondition simpleCond = (SimpleCondition) result;
            assertThat(simpleCond.getField()).isEqualTo("error");
            assertThat(simpleCond.getOperator()).isEqualTo(WhereOp.START_WITH);
            assertThat(simpleCond.getComparisonType()).isEqualTo("STRING"); // 关键断言
            assertThat(simpleCond.getValue()).isEqualTo("40");
            assertThat(requiredFieldsCollector).contains("error");

            Map<String, Object> data = new HashMap<>();
            data.put("error", 404); // 实际值是 Integer
            assertThat(simpleCond.evaluate(data)).as("404 STARTWITH '40'").isTrue(); // "404" starts with "40"

            data.put("error", 500);
            assertThat(simpleCond.evaluate(data)).as("500 STARTWITH '40'").isFalse(); // "500" does not start with "40"
        }

        @Test
        @DisplayName("已知 BOOLEAN 字段 'virtual' 与 LIKE 操作符，应推断为 STRING 比较")
        void compile_booleanFieldWithLike_shouldInferStringComparison() {
            // 前提：DCSpanFieldTypeResolver.getKnownType("virtual") 返回 DataType.BOOLEAN
            Map<String, Object> conditionMap = createSimpleConditionMapNoFieldType("virtual", "LIKE", "ru");
            requiredFieldsCollector.clear();

            ExecutableCondition result = conditionCompiler.compileConditions(Collections.singletonList(conditionMap), requiredFieldsCollector);

            assertThat(result).isInstanceOf(SimpleCondition.class);
            SimpleCondition simpleCond = (SimpleCondition) result;
            assertThat(simpleCond.getField()).isEqualTo("virtual");
            assertThat(simpleCond.getOperator()).isEqualTo(WhereOp.LIKE);
            assertThat(simpleCond.getComparisonType()).isEqualTo("STRING"); // 关键断言
            assertThat(simpleCond.getValue()).isEqualTo("ru");
            assertThat(requiredFieldsCollector).contains("virtual");

            Map<String, Object> data = new HashMap<>();
            data.put("virtual", true); // 实际值是 Boolean
            assertThat(simpleCond.evaluate(data)).as("true LIKE 'ru'").isTrue(); // "true" contains "ru"

            data.put("virtual", false);
            assertThat(simpleCond.evaluate(data)).as("false LIKE 'ru'").isFalse(); // "false" does not contain "ru"
        }

        @Test
        @DisplayName("控制用例：已知 BOOLEAN 字段 'virtual' 与 EQ 操作符和字符串目标值 'false'，应推断为 BOOLEAN 比较")
        void compile_booleanFieldWithEqAndStringValue_shouldStillInferBooleanComparison() {
            // 这个测试确保对 LIKE 等的修改没有破坏对布尔值常规推断（基于值的格式）
            // 前提：DCSpanFieldTypeResolver.getKnownType("virtual") 返回 DataType.BOOLEAN
            Map<String, Object> conditionMap = createSimpleConditionMapNoFieldType("virtual", "=", "false");
            requiredFieldsCollector.clear();

            ExecutableCondition result = conditionCompiler.compileConditions(Collections.singletonList(conditionMap), requiredFieldsCollector);

            assertThat(result).isInstanceOf(SimpleCondition.class);
            SimpleCondition simpleCond = (SimpleCondition) result;
            assertThat(simpleCond.getField()).isEqualTo("virtual");
            assertThat(simpleCond.getOperator()).isEqualTo(WhereOp.EQ);
            // 因为 "=" 不是强制字符串的操作符，determineComparisonType 会继续推断：
            // 1. explicitFieldType (无) -> 2. operator force (无) -> 3. knownType (BOOLEAN)
            // 或者如果字段未知，会根据 "false" 值的格式推断为 BOOLEAN
            assertThat(simpleCond.getComparisonType()).isEqualTo("BOOLEAN"); // 关键断言
            assertThat(simpleCond.getValue()).isEqualTo(Boolean.FALSE);  // 目标值被正确解析为 Boolean
            assertThat(requiredFieldsCollector).contains("virtual");

            Map<String, Object> data = new HashMap<>();
            data.put("virtual", false);
            assertThat(simpleCond.evaluate(data)).isTrue();
            data.put("virtual", true);
            assertThat(simpleCond.evaluate(data)).isFalse();
        }

        @Test
        @DisplayName("已知 INTEGER 字段 'error' 与 REGEX 操作符，应推断为 STRING 比较")
        void compile_integerFieldWithRegex_shouldInferStringComparison() {
            // 前提：DCSpanFieldTypeResolver.getKnownType("error") is DataType.INTEGER
            Map<String, Object> conditionMap = createSimpleConditionMapNoFieldType("error", "REGEX", "^4\\d{2}$"); // 匹配如 400, 404 等
            requiredFieldsCollector.clear();

            ExecutableCondition result = conditionCompiler.compileConditions(Collections.singletonList(conditionMap), requiredFieldsCollector);

            assertThat(result).isInstanceOf(SimpleCondition.class);
            SimpleCondition simpleCond = (SimpleCondition) result;
            assertThat(simpleCond.getField()).isEqualTo("error");
            assertThat(simpleCond.getOperator()).isEqualTo(WhereOp.REGEX);
            assertThat(simpleCond.getComparisonType()).isEqualTo("STRING"); // 关键断言
            assertThat(simpleCond.getValue()).isEqualTo("^4\\d{2}$");
            assertThat(requiredFieldsCollector).contains("error");

            Map<String, Object> data = new HashMap<>();
            data.put("error", 404); // 实际值是 Integer
            assertThat(simpleCond.evaluate(data)).as("404 REGEX '^4\\d{2}$'").isTrue();

            data.put("error", 500);
            assertThat(simpleCond.evaluate(data)).as("500 REGEX '^4\\d{2}$'").isFalse();

            data.put("error", 40); // 不匹配三位数
            assertThat(simpleCond.evaluate(data)).as("40 REGEX '^4\\d{2}$'").isFalse();
        }
    }

    // 请将这个新的 Nested 测试类添加到 ConditionCompilerImplTest.java 文件内部

    @Nested
    @DisplayName("用户提供的复杂JSON结构解析测试")
    class UserProvidedComplexJsonTests {

        @Test
        @DisplayName("测试范例1：列表内所有Map含 'connector:AND'，应解析为各Map简化后条件的AND组合")
        void compileConditions_userExample1_allMapsInListHaveConnectorAnd() {
            // queryStr: "根类型 等于 不区分大小写的1 AND 请求类型 等于 不区分大小写的1 AND Http响应码 等于 不区分大小写的1"
            List<Map<String, Object>> conditions = Arrays.asList(
                    new HashMap<String, Object>() {{
                        put("left", "meta.root.type");
                        put("type", "composite"); // 这类元数据字段应被SimpleCondition解析忽略
                        put("right", "1");
                        put("operator", "=");
                        put("connector", "AND"); // 用于列表连接逻辑
                        put("operatorEnum", "EQ");
                        put("caseInsensitive", true);
                    }},
                    new HashMap<String, Object>() {{
                        put("left", "meta.http.method");
                        put("type", "composite");
                        put("right", "1");
                        put("operator", "=");
                        put("connector", "AND");
                        put("operatorEnum", "EQ");
                        put("caseInsensitive", true);
                    }},
                    new HashMap<String, Object>() {{
                        put("left", "meta.http.status_code");
                        put("type", "composite");
                        put("right", "1");
                        put("operator", "=");
                        put("connector", "AND");
                        put("operatorEnum", "EQ");
                        put("caseInsensitive", true);
                    }}
            );

            ExecutableCondition result = conditionCompiler.compileConditions(conditions, requiredFieldsCollector);

            assertThat(result).isNotNull().isInstanceOf(AndCondition.class);
            AndCondition andCond = (AndCondition) result;
            assertThat(andCond.getChildren()).hasSize(3);

            // 验证子条件
            SimpleCondition cond1 = (SimpleCondition) andCond.getChildren().get(0);
            assertThat(cond1.getField()).isEqualTo("meta.root.type");
            assertThat(cond1.getOperator()).isEqualTo(WhereOp.EQ);
            assertThat(cond1.getValue()).isEqualTo("1"); // 默认按STRING处理，除非有fieldType或值格式特殊

            SimpleCondition cond2 = (SimpleCondition) andCond.getChildren().get(1);
            assertThat(cond2.getField()).isEqualTo("meta.http.method");
            assertThat(cond2.getOperator()).isEqualTo(WhereOp.EQ);
            assertThat(cond2.getValue()).isEqualTo("1");

            SimpleCondition cond3 = (SimpleCondition) andCond.getChildren().get(2);
            assertThat(cond3.getField()).isEqualTo("meta.http.status_code");
            assertThat(cond3.getOperator()).isEqualTo(WhereOp.EQ);
            assertThat(cond3.getValue()).isEqualTo("1");

            assertThat(requiredFieldsCollector).containsExactlyInAnyOrder("meta.root.type", "meta.http.method", "meta.http.status_code");
        }

        @Test
        @DisplayName("测试范例2：复杂嵌套，顶层AND，一个子元素是(OR AND AlwaysTrue)")
        void compileConditions_userExample2_complexNestingWithMixedConnectors() {
            // queryStr: "根类型 = 1 AND 请求类型 = 1 AND Http响应码 = 1 AND (根类型 = cc OR 运行唯一标识 != vv)"
            Map<String, Object> mapC1 = new HashMap<String, Object>() {{
                put("left", "meta.root.type"); put("type", "composite"); put("right", "1");
                put("operator", "="); put("connector", "AND"); put("operatorEnum", "EQ"); put("caseInsensitive", true);
            }};
            Map<String, Object> mapC2 = new HashMap<String, Object>() {{
                put("left", "meta.http.method"); put("type", "composite"); put("right", "1");
                put("operator", "="); put("connector", "AND"); put("operatorEnum", "EQ"); put("caseInsensitive", true);
            }};
            Map<String, Object> mapC3 = new HashMap<String, Object>() {{
                put("left", "meta.http.status_code"); put("type", "composite"); put("right", "1");
                put("operator", "="); put("connector", "AND"); put("operatorEnum", "EQ"); put("caseInsensitive", true);
            }};

            Map<String, Object> mapC4_1_connOR = new HashMap<String, Object>() {{
                put("left", "meta.root.type"); put("right", "cc"); put("operator", "=");
                put("connector", "OR"); put("caseInsensitive", true);
            }};
            Map<String, Object> mapC4_2_connOR = new HashMap<String, Object>() {{
                put("left", "meta.runtime-id"); put("right", "vv"); put("operator", "!=");
                put("connector", "OR"); put("caseInsensitive", true);
            }};

            Map<String, Object> mapC4_complex = new HashMap<String, Object>() {{
                put("left", Arrays.asList(mapC4_1_connOR, mapC4_2_connOR));
                put("type", "composite");
                put("right", Collections.emptyList()); // 将被视为 AlwaysTrue
                put("connector", "AND"); // MapC4自身的连接符
                put("operatorEnum", "EQ");
            }};

            List<Map<String, Object>> conditions = Arrays.asList(mapC1, mapC2, mapC3, mapC4_complex);
            ExecutableCondition result = conditionCompiler.compileConditions(conditions, requiredFieldsCollector);

            assertThat(result).isNotNull().isInstanceOf(AndCondition.class);
            AndCondition topAnd = (AndCondition) result;
            assertThat(topAnd.getChildren()).hasSize(4);

            // ... (对 C1, C2, C3 的断言保持不变)
            assertThat(topAnd.getChildren().get(0)).isInstanceOfSatisfying(SimpleCondition.class, sc -> {
                assertThat(sc.getField()).isEqualTo("meta.root.type"); assertThat(sc.getValue()).isEqualTo("1");
            });
            assertThat(topAnd.getChildren().get(1)).isInstanceOfSatisfying(SimpleCondition.class, sc -> {
                assertThat(sc.getField()).isEqualTo("meta.http.method"); assertThat(sc.getValue()).isEqualTo("1");
            });
            assertThat(topAnd.getChildren().get(2)).isInstanceOfSatisfying(SimpleCondition.class, sc -> {
                assertThat(sc.getField()).isEqualTo("meta.http.status_code"); assertThat(sc.getValue()).isEqualTo("1");
            });


            // 验证第四个子条件 (child4)，即 mapC4_complex 的解析结果
            // 预期：AndCondition( OrCondition(SimpleC4_1, SimpleC4_2), AlwaysTrueCondition )
            ExecutableCondition child4 = topAnd.getChildren().get(3);
            assertThat(child4).isNotNull().isInstanceOf(AndCondition.class); // 修正点：child4 本身是一个 AND
            AndCondition child4And = (AndCondition) child4;
            assertThat(child4And.getChildren()).hasSize(2);

            // AndCondition 的第一个子节点应该是 OrCondition
            assertThat(child4And.getChildren().get(0)).isInstanceOf(OrCondition.class);
            OrCondition orSubCondition = (OrCondition) child4And.getChildren().get(0);
            assertThat(orSubCondition.getChildren()).hasSize(2);

            SimpleCondition subCond1 = (SimpleCondition) orSubCondition.getChildren().get(0);
            assertThat(subCond1.getField()).isEqualTo("meta.root.type");
            assertThat(subCond1.getOperator()).isEqualTo(WhereOp.EQ);
            assertThat(subCond1.getValue()).isEqualTo("cc");

            SimpleCondition subCond2 = (SimpleCondition) orSubCondition.getChildren().get(1);
            assertThat(subCond2.getField()).isEqualTo("meta.runtime-id");
            assertThat(subCond2.getOperator()).isEqualTo(WhereOp.NEQ);
            assertThat(subCond2.getValue()).isEqualTo("vv");

            // AndCondition 的第二个子节点应该是 AlwaysTrueCondition (来自空的 right 列表)
            assertThat(child4And.getChildren().get(1).evaluate(Collections.emptyMap()))
                    .as("Child4's second part (from empty 'right' list) should be AlwaysTrue")
                    .isTrue();


            assertThat(requiredFieldsCollector).containsExactlyInAnyOrder(
                    "meta.root.type", "meta.http.method", "meta.http.status_code", "meta.runtime-id"
            );
        }

        // 在 ConditionCompilerImplTest.java 文件中的 UserProvidedComplexJsonTests Nested 类里添加：

        @Test
        @DisplayName("测试用户范例：业务异常 'error == 1 OR error != 1'，应始终为true")
        void compileConditions_userExample_businessRule_evaluatesAsAlwaysTrueForErrorField() {
            // queryStr: "error 等于 不区分大小写的1 OR error 不等于 不区分大小写的1"
            // (此逻辑等价于 error IS NOT NULL 且可比较，对于数字类型通常为真)
            List<Map<String, Object>> conditions = Arrays.asList(
                    new HashMap<String, Object>() {{
                        put("left", "error");
                        put("type", "composite");
                        put("right", "1");
                        put("operator", "=");
                        put("connector", "OR"); // 指示列表元素间使用 OR 连接
                        put("operatorEnum", "EQ");
                        put("caseInsensitive", true); // caseInsensitive 对数字比较通常无影响
                        // 推荐: put("fieldType", "NUMERIC");
                    }},
                    new HashMap<String, Object>() {{
                        put("left", "error");
                        put("type", "composite");
                        put("right", "1");
                        put("operator", "!=");
                        put("connector", "OR"); // 指示列表元素间使用 OR 连接
                        put("operatorEnum", "NEQ");
                        put("caseInsensitive", true);
                        // 推荐: put("fieldType", "NUMERIC");
                    }}
            );

            // 编译条件
            ExecutableCondition result = conditionCompiler.compileConditions(conditions, requiredFieldsCollector);

            // 1. 验证编译结构
            assertThat(result).isNotNull().isInstanceOf(OrCondition.class);
            OrCondition orCond = (OrCondition) result;
            assertThat(orCond.getChildren()).hasSize(2);

            SimpleCondition child1 = (SimpleCondition) orCond.getChildren().get(0);
            assertThat(child1.getField()).isEqualTo("error");
            assertThat(child1.getOperator()).isEqualTo(WhereOp.EQ);
            // DCSpanFieldTypeResolver 将 "error" 视为 INTEGER，determineComparisonType 可能推断为 NUMERIC
            // parseConditionValue 会将 "1" 转为 BigDecimal(1)
            assertThat(child1.getValue()).isInstanceOf(BigDecimal.class);
            assertThat((BigDecimal) child1.getValue()).isEqualByComparingTo("1");
            // comparisonType 会根据字段已知类型或值的格式来推断，这里可能是 NUMERIC
            // assertThat(child1.getComparisonType()).isEqualTo("NUMERIC");


            SimpleCondition child2 = (SimpleCondition) orCond.getChildren().get(1);
            assertThat(child2.getField()).isEqualTo("error");
            assertThat(child2.getOperator()).isEqualTo(WhereOp.NEQ);
            assertThat(child2.getValue()).isInstanceOf(BigDecimal.class);
            assertThat((BigDecimal) child2.getValue()).isEqualByComparingTo("1");
            // assertThat(child2.getComparisonType()).isEqualTo("NUMERIC");

            assertThat(requiredFieldsCollector).containsExactly("error");
            requiredFieldsCollector.clear(); // 为下一次评估清理

            // 2. 验证评估逻辑
            Map<String, Object> preparedDataSpan1 = new HashMap<>();
            // 场景1: error = 0 (或任何非1的数字，如BigDecimal)
            // DCSpanFieldTypeResolver 将 error 定义为 INTEGER, SimpleCondition 的 evaluate 会尝试转换
            preparedDataSpan1.put("error", BigDecimal.ZERO); // 或者 new Integer(0)

            assertThat(result.evaluate(preparedDataSpan1))
                    .as("当 error=0 时， (error == 1 OR error != 1) 应为 true")
                    .isTrue();

            Map<String, Object> preparedDataSpan2 = new HashMap<>();
            // 场景2: error = 1 (BigDecimal)
            preparedDataSpan2.put("error", BigDecimal.ONE); // 或者 new Integer(1)

            assertThat(result.evaluate(preparedDataSpan2))
                    .as("当 error=1 时， (error == 1 OR error != 1) 应为 true")
                    .isTrue();

            Map<String, Object> preparedDataSpan3 = new HashMap<>();
            // 场景3: error 字段不存在 (preparedData 中没有 "error" key)
            // SimpleCondition.evaluate 中 rawActualValue 会是 null
            // (error == 1) -> (null == 1) -> false
            // (error != 1) -> (null != 1) -> true (因为 this.value 是 BigDecimal(1) 而不是 null)
            // 所以 false OR true -> true
            assertThat(result.evaluate(preparedDataSpan3))
                    .as("当 error 字段不存在时， (error == 1 OR error != 1) 应为 true")
                    .isTrue();

            Map<String, Object> preparedDataSpan4 = new HashMap<>();
            // 场景4: error 字段值为 null (与字段不存在类似，rawActualValue 为 null)
            preparedDataSpan4.put("error", null);
            assertThat(result.evaluate(preparedDataSpan4))
                    .as("当 error 字段值为 null 时， (error == 1 OR error != 1) 应为 true")
                    .isTrue();
        }


        // 在 ConditionCompilerImplTest.java 文件中的 UserProvidedComplexJsonTests Nested 类里添加：

        @Test
        @DisplayName("测试用户范例 '复杂' 业务规则的完整解析和评估")
        void compileAndEvaluate_userExample_complexBusinessRule() {
            // queryStr: "根类型 = '1' AND (请求类型 = '1' OR meta.event0event != '1') AND 出口 = 1 AND (服务ID = '2')"
            // 构造 conditions JSON
            Map<String, Object> mapC1 = new HashMap<String, Object>() {{
                put("left", "meta.root.type"); put("type", "composite"); put("right", "1");
                put("operator", "="); put("connector", "AND"); put("operatorEnum", "EQ"); put("caseInsensitive", true);
            }};
            Map<String, Object> mapC2_1_connOR = new HashMap<String, Object>() {{
                put("left", "meta.http.method"); put("right", "1"); put("operator", "=");
                put("connector", "OR"); put("caseInsensitive", true);
            }};
            Map<String, Object> mapC2_2_connOR = new HashMap<String, Object>() {{
                put("left", "meta.event0event"); put("right", "1"); put("operator", "!=");
                put("connector", "OR"); put("caseInsensitive", true);
            }};
            Map<String, Object> mapC2_complexLeft = new HashMap<String, Object>() {{
                put("left", Arrays.asList(mapC2_1_connOR, mapC2_2_connOR));
                put("type", "composite");
                put("right", Collections.emptyList()); // 视为 AlwaysTrue
                put("connector", "AND"); // MapC2自身的连接符
                put("operatorEnum", "EQ");
            }};
            Map<String, Object> mapC3 = new HashMap<String, Object>() {{
                put("left", "isOut"); put("type", "composite"); put("right", "1");
                put("operator", "="); put("connector", "AND"); put("operatorEnum", "EQ"); put("caseInsensitive", true);
                // isOut 在 DCSpanFieldTypeResolver 中是 INTEGER，应进行数值比较
                // 为确保测试按数值比较，可以显式添加 "fieldType": "NUMERIC"
                // 但这里依赖编译器的类型推断
            }};
            Map<String, Object> mapC4_1_connAND = new HashMap<String, Object>() {{
                put("left", "serviceId"); put("right", "2"); put("operator", "=");
                put("connector", "AND"); put("caseInsensitive", true);
            }};
            Map<String, Object> mapC4_complexLeft = new HashMap<String, Object>() {{
                put("left", Collections.singletonList(mapC4_1_connAND));
                put("type", "composite");
                put("right", Collections.emptyList());
                put("connector", "AND");
                put("operatorEnum", "EQ");
            }};
            List<Map<String, Object>> conditions = Arrays.asList(mapC1, mapC2_complexLeft, mapC3, mapC4_complexLeft);

            requiredFieldsCollector.clear();
            ExecutableCondition compiledRule = conditionCompiler.compileConditions(conditions, requiredFieldsCollector);

            // 1. 验证编译后的结构
            assertThat(compiledRule).isNotNull().isInstanceOf(AndCondition.class);
            AndCondition topAnd = (AndCondition) compiledRule;
            assertThat(topAnd.getChildren()).hasSize(4);

            // Cond1: meta.root.type == "1"
            assertThat(topAnd.getChildren().get(0)).isInstanceOfSatisfying(SimpleCondition.class, sc -> {
                assertThat(sc.getField()).isEqualTo("meta.root.type");
                assertThat(sc.getOperator()).isEqualTo(WhereOp.EQ);
                // 值 "1" 会根据字段类型或值的格式被解析，meta.* 通常是STRING
                // 如果DCSpanFieldTypeResolver.getKnownType("meta.root.type") 是 STRING (默认)
                // 或者 determineComparisonType 基于 "1" 推断为 STRING (如果字段未知)
                // 它的 comparisonType 会是 STRING，parsedValue 是 "1"
                assertThat(sc.getValue()).isEqualTo("1"); // 假设按字符串比较
                assertThat(sc.getComparisonType()).isEqualTo("STRING");
            });

            // Cond2_eff: (meta.http.method == "1" OR meta.event0event != "1")
            // Map2_complexLeft (child 1 of topAnd) 本身是一个 AND (ItsLeft OR ItsRight_AlwaysTrue)
            // 所以它等效于 ItsLeft, 而 ItsLeft 是 OR
            assertThat(topAnd.getChildren().get(1)).isInstanceOf(AndCondition.class); // Map2_complexLeft 本身是 AND(OR, True)
            AndCondition cond2AndWrapper = (AndCondition) topAnd.getChildren().get(1);
            assertThat(cond2AndWrapper.getChildren().get(0)).isInstanceOf(OrCondition.class); // 有效部分
            OrCondition cond2Or = (OrCondition) cond2AndWrapper.getChildren().get(0);
            assertThat(cond2Or.getChildren()).hasSize(2);
            assertThat(cond2Or.getChildren().get(0)).isInstanceOfSatisfying(SimpleCondition.class, sc -> {
                assertThat(sc.getField()).isEqualTo("meta.http.method");
                assertThat(sc.getOperator()).isEqualTo(WhereOp.EQ);
                assertThat(sc.getValue()).isEqualTo("1");
                assertThat(sc.getComparisonType()).isEqualTo("STRING");
            });
            assertThat(cond2Or.getChildren().get(1)).isInstanceOfSatisfying(SimpleCondition.class, sc -> {
                assertThat(sc.getField()).isEqualTo("meta.event0event");
                assertThat(sc.getOperator()).isEqualTo(WhereOp.NEQ);
                assertThat(sc.getValue()).isEqualTo("1");
                assertThat(sc.getComparisonType()).isEqualTo("STRING");
            });
            assertThat(cond2AndWrapper.getChildren().get(1).evaluate(Collections.emptyMap())).isTrue(); // AlwaysTrue 部分

            // Cond3: isOut == 1 (numeric)
            assertThat(topAnd.getChildren().get(2)).isInstanceOfSatisfying(SimpleCondition.class, sc -> {
                assertThat(sc.getField()).isEqualTo("isOut");
                assertThat(sc.getOperator()).isEqualTo(WhereOp.EQ);
                // isOut 是 INTEGER, "1" 会被转为 BigDecimal(1) for NUMERIC comparison
                assertThat(sc.getValue()).isInstanceOf(BigDecimal.class);
                assertThat((BigDecimal) sc.getValue()).isEqualByComparingTo("1");
                assertThat(sc.getComparisonType()).isEqualTo("NUMERIC");
            });

            // Cond4_eff: serviceId == "2"
            // MapC4_complexLeft (child 3 of topAnd) 是 AND(Simple(serviceId=="2"), AlwaysTrue)
            assertThat(topAnd.getChildren().get(3)).isInstanceOf(AndCondition.class);
            AndCondition cond4AndWrapper = (AndCondition) topAnd.getChildren().get(3);
            assertThat(cond4AndWrapper.getChildren().get(0)).isInstanceOfSatisfying(SimpleCondition.class, sc -> {
                assertThat(sc.getField()).isEqualTo("serviceId");
                assertThat(sc.getOperator()).isEqualTo(WhereOp.EQ);
                assertThat(sc.getValue()).isEqualTo("2"); // serviceId 在DCSpanFieldTypeResolver中是STRING
                assertThat(sc.getComparisonType()).isEqualTo("STRING");
            });
            assertThat(cond4AndWrapper.getChildren().get(1).evaluate(Collections.emptyMap())).isTrue();


            assertThat(requiredFieldsCollector).containsExactlyInAnyOrder(
                    "meta.root.type", "meta.http.method", "meta.event0event", "isOut", "serviceId"
            );

            // 2. 验证评估逻辑 - 触发场景
            Map<String, Object> preparedDataTrigger = new HashMap<>();
            preparedDataTrigger.put("meta.root.type", "1");       // Cond1 true
            preparedDataTrigger.put("isOut", new BigDecimal("1")); // Cond3 true (使用BigDecimal以匹配解析后的类型)
            preparedDataTrigger.put("serviceId", "2");             // Cond4 true
            preparedDataTrigger.put("meta.http.method", "other");  // Cond2 OR的第一部分 false
            preparedDataTrigger.put("meta.event0event", "not1"); // Cond2 OR的第二部分 true -> Cond2 true

            assertThat(compiledRule.evaluate(preparedDataTrigger))
                    .as("复杂业务规则应被触发")
                    .isTrue();

            // 3. 验证评估逻辑 - 不触发场景 (让 Cond3 isOut != 1 失败)
            Map<String, Object> preparedDataNoTrigger = new HashMap<>();
            preparedDataNoTrigger.put("meta.root.type", "1");       // Cond1 true
            preparedDataNoTrigger.put("isOut", new BigDecimal("0")); // Cond3 false
            preparedDataNoTrigger.put("serviceId", "2");             // Cond4 true
            preparedDataNoTrigger.put("meta.http.method", "1");      // Cond2 true

            assertThat(compiledRule.evaluate(preparedDataNoTrigger))
                    .as("复杂业务规则不应被触发，因为isOut不匹配")
                    .isFalse();
        }

    }

}