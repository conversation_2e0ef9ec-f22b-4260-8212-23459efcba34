package com.databuff.dts.receive.biz.processor;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.constants.Constant;
import com.databuff.common.constants.TSDBIndex;
import com.databuff.common.metric.FlinkAggregationType;
import com.databuff.dts.receive.biz.processor.condition.ExecutableCondition;
import com.databuff.dts.receive.biz.processor.config.ObservabilityConfigLoader;
import com.databuff.dts.receive.biz.processor.model.CompiledBizEventReqMatcher;
import com.databuff.dts.receive.biz.processor.model.CompiledEventRule;
import com.databuff.dts.receive.biz.processor.model.CompiledExceptionRule;
import com.databuff.entity.BizKpiConfig;
import com.databuff.entity.enums.CalculationMethod;
import com.databuff.kafka.KafkaSender;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 对 {@link BusinessObservabilityProcessor} 的单元测试。
 * (V3 - 修正重复的内部类名)
 */
@DisplayName("BusinessObservabilityProcessor 单元测试 (V3)")
@ExtendWith(MockitoExtension.class)
class BusinessObservabilityProcessorTest {

    @Mock
    private ObservabilityConfigLoader configLoader;

    @Mock
    private KafkaSender kafkaSender;

    @InjectMocks
    private BusinessObservabilityProcessor processor;

    @Captor
    private ArgumentCaptor<List<JSONObject>> kafkaMetricsCaptor;

    // --- 辅助方法 (保持不变) ---
    private JSONObject createBaseSpanJson(String apiKey, String serviceId, String serviceName, String resource) {
        JSONObject span = new JSONObject();
        span.put("df-api-key", apiKey);
        span.put("serviceId", serviceId);
        span.put("service", serviceName);
        span.put("resource", resource);
        span.put("isIn", 1);
        span.put("trace_id", UUID.randomUUID().toString());
        span.put("span_id", UUID.randomUUID().toString().substring(0, 8));
        span.put("duration", 150L * 1_000_000);
        span.put("end", System.currentTimeMillis() * 1_000_000);
        span.put("error", 0);
        span.put("meta", new JSONObject());
        span.put("metrics", new JSONObject());
        return span;
    }

    /**
     * 创建一个模拟的 CompiledEventRule
     * (V2 修正: 适配 matches(serviceId, resource) 签名)
     */
    private CompiledEventRule createMockEventRule(int eventId, String eventName, String svcId, String resource) {
        CompiledBizEventReqMatcher matcher = mock(CompiledBizEventReqMatcher.class);

        // *** 修正: 使用两个参数匹配器，对应 matches(serviceId, resource) ***
        if (resource != null) {
            // 当 resource 非 null 时，精确匹配 serviceId 和 resource
            // 注意：由于 processor 内部调用时 resource 可能为 null，这里可能需要更灵活的匹配
            // 但为了测试简单，我们先假设 processor 会传入与这里匹配的 resource
            // 或者使用 ArgumentMatchers.nullable(String.class) / eq() 组合
            when(matcher.matches(eq(svcId), eq(resource))).thenReturn(true);
        } else {
            // 当 resource 为 null 时 (表示规则不限资源), 匹配 serviceId 和任何 resource (包括 null)
            // any() 会匹配 null
            when(matcher.matches(eq(svcId), any())).thenReturn(true);
        }

        // 为了更精确地模拟 findMatchedEventRules 中的调用：
        // processor 从 spanJson 获取 serviceId, serviceName, resource
        // 然后调用 rule.getReqMatcher().matches(serviceId, resource)
        // 所以 Mock 时 serviceName 不再需要
        // （之前的 createMockEventRule 里的 when 语句已根据上面的 if/else 修正）

        return new CompiledEventRule(eventId, eventName, matcher);
    }

    private CompiledExceptionRule createMockExceptionRule(String errorType, String errorName, int priority, boolean matchResult) {
        ExecutableCondition mockCondition = mock(ExecutableCondition.class);
        when(mockCondition.evaluate(anyMap())).thenReturn(matchResult);
        return new CompiledExceptionRule(errorType, errorName, priority, mockCondition);
    }

    private BizKpiConfig createMockKpiConfig(int sourceEventId, String sourceAttributeKey) {
        BizKpiConfig kpiConfig = new BizKpiConfig();
        kpiConfig.setKpiName("测试KPI");
        kpiConfig.setSourceEventId(String.valueOf(sourceEventId));
        kpiConfig.setSourceAttributeKey(sourceAttributeKey);
        kpiConfig.setCalculationMethod(CalculationMethod.SUM);
        kpiConfig.setKpiUnit("个");
        kpiConfig.setBusinessUnit("次");
        return kpiConfig;
    }

    // --- 测试方法 ---

    @Nested
    @DisplayName("输入校验测试")
    class InputValidationTests {

        @Test
        @DisplayName("当输入 Span JSON 为 null 时，应跳过处理")
        void processSpan_skips_whenInputIsNull() {
            processor.processSpan(null);
            verifyNoInteractions(configLoader, kafkaSender);
        }

        @Test
        @DisplayName("当 Span JSON 缺少 apiKey 时，应跳过处理")
        void processSpan_skips_whenApiKeyIsMissing() {
            JSONObject span = createBaseSpanJson(null, "svc1", "SvcName1", "/res1");
            span.remove("df-api-key");
            span.remove("apiKey");
            span.remove("dfApiKey");
            processor.processSpan(span);
            verifyNoInteractions(configLoader, kafkaSender);
        }

        @Test
        @DisplayName("当 Span 不是入口 Span (isIn != 1) 时，应跳过处理")
        void processSpan_skips_whenNotIn() {
            JSONObject span = createBaseSpanJson("key1", "svc1", "SvcName1", "/res1");
            span.put("isIn", 0);
            processor.processSpan(span);
            verifyNoInteractions(configLoader, kafkaSender);
            span.put("isIn", 2);
            processor.processSpan(span);
            verifyNoInteractions(configLoader, kafkaSender);
            span.remove("isIn");
            processor.processSpan(span);
            verifyNoInteractions(configLoader, kafkaSender);
        }

        @Test
        @DisplayName("当 Span 缺少 ServiceId 和 ServiceName 时，应跳过处理")
        void processSpan_skips_whenServiceIdAndNameMissing() {
            JSONObject span = createBaseSpanJson("key1", null, null, "/res1");
            span.remove("serviceId");
            span.remove("service");
            processor.processSpan(span);
            verifyNoInteractions(configLoader, kafkaSender);
        }
    }

    @Nested
    @DisplayName("事件匹配流程测试")
    class EventMatchingFlowTests {

        @Test
        @DisplayName("当没有匹配的事件规则时，不应继续处理或发送 Kafka")
        void processSpan_doesNothing_whenNoEventRulesMatch() {
            String apiKey = "api-no-match";
            String serviceId = "svc-no-match";
            String resource = "/res";
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcNoMatch", resource);

            when(configLoader.getEventRulesByServiceId(eq(apiKey), eq(serviceId))).thenReturn(Collections.emptyList());
            when(configLoader.getRequiredFields()).thenReturn(Collections.singleton("duration"));

            processor.processSpan(span);

            verify(configLoader, times(1)).getEventRulesByServiceId(apiKey, serviceId);
            verify(configLoader, never()).getExceptionRules(anyString(), anyInt());
            verify(kafkaSender, never()).data2Kafka(anyList(), anyString());
        }

        @Test
        @DisplayName("当事件规则匹配时，应继续进行异常和 KPI 处理")
        void processSpan_proceeds_whenEventRuleMatches() {
            String apiKey = "api-match";
            String serviceId = "svc-match";
            String resource = "/res-match";
            int eventId = 1;
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcMatch", resource);

            CompiledEventRule matchingRule = createMockEventRule(eventId, "匹配事件", serviceId, resource);
            when(configLoader.getEventRulesByServiceId(eq(apiKey), eq(serviceId))).thenReturn(Collections.singletonList(matchingRule));
            when(configLoader.getExceptionRules(eq(apiKey), eq(eventId))).thenReturn(Collections.emptyList());
            when(configLoader.getScenarioIdsForEvent(eq(apiKey), eq(eventId))).thenReturn(Collections.emptyList());
            when(configLoader.getRequiredFields()).thenReturn(new HashSet<>(Arrays.asList("duration", "error")));

            processor.processSpan(span);

            verify(configLoader, times(1)).getEventRulesByServiceId(apiKey, serviceId);
            verify(configLoader, times(1)).getExceptionRules(apiKey, eventId);
            verify(configLoader, times(1)).getScenarioIdsForEvent(apiKey, eventId);
            verify(kafkaSender, times(1)).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));

            List<JSONObject> sentMetrics = kafkaMetricsCaptor.getValue();
            assertThat(sentMetrics).hasSize(1);
            JSONObject metric = sentMetrics.get(0);
            assertThat(metric.getString("measurement")).isEqualTo(TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT);
            assertThat(metric.getJSONObject("tags").getString("bizEventId")).isEqualTo(String.valueOf(eventId));
            assertThat(metric.getJSONObject("fields").getLong("error")).isEqualTo(0L);
            assertThat(metric.getJSONObject("tags").getString("bizError")).isEmpty();
            assertThat(metric.getJSONObject("tags").getString("systemError")).isEmpty();
        }
    }

    @Nested
    @DisplayName("异常规则处理测试")
    class ExceptionRuleHandlingTests {

        @Test
        @DisplayName("事件匹配但无异常规则匹配时，指标中 error=0")
        void processSpan_noError_whenEventMatchesButNoExceptionRuleMatches() {
            String apiKey = "api-ok";
            String serviceId = "svc-ok";
            String resource = "/ok";
            int eventId = 2;
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcOk", resource);
            span.getJSONObject("meta").put("http.status_code", "200");

            CompiledEventRule matchingRule = createMockEventRule(eventId, "成功事件", serviceId, resource);
            CompiledExceptionRule nonMatchingRule = createMockExceptionRule("business", "SomeError", 10, false);

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Collections.singletonList(matchingRule));
            when(configLoader.getExceptionRules(apiKey, eventId)).thenReturn(Collections.singletonList(nonMatchingRule));
            when(configLoader.getRequiredFields()).thenReturn(Collections.singleton("meta.http.status_code"));

            processor.processSpan(span);

            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            JSONObject metric = kafkaMetricsCaptor.getValue().get(0);
            assertThat(metric.getJSONObject("fields").getLong("error")).isEqualTo(0L);
            assertThat(metric.getJSONObject("tags").getString("bizError")).isEmpty();
            assertThat(metric.getJSONObject("tags").getString("systemError")).isEmpty();
        }

        @Test
        @DisplayName("匹配业务异常规则时，指标中包含 bizError 且 error=1")
        void processSpan_bizError_whenBizExceptionRuleMatches() {
            String apiKey = "api-biz-err";
            String serviceId = "svc-biz-err";
            String resource = "/err";
            int eventId = 3;
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcBizErr", resource);
            span.getJSONObject("metrics").put("invalid_input", "1");

            CompiledEventRule matchingRule = createMockEventRule(eventId, "业务错误事件", serviceId, resource);
            CompiledExceptionRule bizErrorRule = createMockExceptionRule("business", "INVALID_PARAM", 5, true);
            CompiledExceptionRule sysErrorRule = createMockExceptionRule("system", "Timeout", 10, false);

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Collections.singletonList(matchingRule));
            when(configLoader.getExceptionRules(apiKey, eventId)).thenReturn(Arrays.asList(bizErrorRule, sysErrorRule));
            when(configLoader.getRequiredFields()).thenReturn(Collections.singleton("metrics.invalid_input"));

            processor.processSpan(span);

            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            JSONObject metric = kafkaMetricsCaptor.getValue().get(0);
            assertThat(metric.getJSONObject("fields").getLong("error")).isEqualTo(1L);
            assertThat(metric.getJSONObject("tags").getString("bizError")).isEqualTo("INVALID_PARAM");
            assertThat(metric.getJSONObject("tags").getString("systemError")).isEmpty();
        }

        @Test
        @DisplayName("匹配系统异常规则时，指标中包含 systemError 且 error=1")
        void processSpan_sysError_whenSysExceptionRuleMatches() {
            String apiKey = "api-sys-err";
            String serviceId = "svc-sys-err";
            String resource = "/timeout";
            int eventId = 4;
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcSysErr", resource);
            span.put("error", 1);

            CompiledEventRule matchingRule = createMockEventRule(eventId, "系统错误事件", serviceId, resource);
            CompiledExceptionRule bizErrorRule = createMockExceptionRule("business", "NotFound", 10, false);
            CompiledExceptionRule sysErrorRule = createMockExceptionRule("system", "DB_TIMEOUT", 5, true);

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Collections.singletonList(matchingRule));
            when(configLoader.getExceptionRules(apiKey, eventId)).thenReturn(Arrays.asList(bizErrorRule, sysErrorRule));
            when(configLoader.getRequiredFields()).thenReturn(Collections.singleton("error"));

            processor.processSpan(span);

            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            JSONObject metric = kafkaMetricsCaptor.getValue().get(0);
            assertThat(metric.getJSONObject("fields").getLong("error")).isEqualTo(1L);
            assertThat(metric.getJSONObject("tags").getString("bizError")).isEmpty();
            assertThat(metric.getJSONObject("tags").getString("systemError")).isEqualTo("DB_TIMEOUT");
        }

        @Test
        @DisplayName("同时匹配多个同类型异常时，应选择优先级最高的")
        void processSpan_selectsHighestPriorityError_whenMultipleMatch() {
            String apiKey = "api-prio";
            String serviceId = "svc-prio";
            String resource = "/multi";
            int eventId = 5;
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcPrio", resource);
            span.put("error", 1);
            span.getJSONObject("meta").put("code", "500");

            CompiledEventRule matchingRule = createMockEventRule(eventId, "优先级事件", serviceId, resource);
            CompiledExceptionRule sysError1 = createMockExceptionRule("system", "SERVER_ERROR", 10, true);
            CompiledExceptionRule sysError2 = createMockExceptionRule("system", "INFRA_FAILURE", 5, true); // 更高优先级
            CompiledExceptionRule bizError1 = createMockExceptionRule("business", "AUTH_FAILED", 20, true);
            CompiledExceptionRule bizError2 = createMockExceptionRule("business", "INVALID_TOKEN", 15, true); // 更高优先级

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Collections.singletonList(matchingRule));
            // 返回列表的顺序不影响结果，因为 processor 内部会排序或选择最低 priority 值
            when(configLoader.getExceptionRules(apiKey, eventId)).thenReturn(Arrays.asList(sysError1, bizError1, sysError2, bizError2));
            when(configLoader.getRequiredFields()).thenReturn(new HashSet<>(Arrays.asList("error", "meta.code")));

            processor.processSpan(span);

            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            JSONObject metric = kafkaMetricsCaptor.getValue().get(0);
            assertThat(metric.getJSONObject("fields").getLong("error")).isEqualTo(1L);
            assertThat(metric.getJSONObject("tags").getString("bizError")).isEqualTo("INVALID_TOKEN"); // Prio 15
            assertThat(metric.getJSONObject("tags").getString("systemError")).isEqualTo("INFRA_FAILURE"); // Prio 5
        }
    }

    @Nested
    @DisplayName("KPI 指标生成测试")
    class KpiMetricGenerationTests {
        private String apiKey = "api-kpi";
        private String serviceId = "svc-kpi";
        private String resource = "/order";
        private int eventId = 6;
        private int scenarioId = 60;
        private String kpiField = "metrics.orderValue";

        @Test
        @DisplayName("当KPI配置存在但sourceEventId为空字符串时，不应生成kpi指标")
        void processSpan_noKpiMetric_whenKpiConfigSourceEventIdIsEmptyString() {
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcKpiEmptySrcEvt", resource);
            span.getJSONObject("metrics").put(kpiField, "123.45");

            CompiledEventRule matchingRule = createMockEventRule(eventId, "事件ForEmptySrcEventId", serviceId, resource);
            BizKpiConfig kpiConfigWithEmptySourceId = createMockKpiConfig(eventId, kpiField); // 用辅助方法创建基础配置
            kpiConfigWithEmptySourceId.setSourceEventId(""); // 设置sourceEventId为空字符串

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Collections.singletonList(matchingRule));
            when(configLoader.getExceptionRules(apiKey, eventId)).thenReturn(Collections.emptyList());
            when(configLoader.getScenarioIdsForEvent(apiKey, eventId)).thenReturn(Collections.singletonList(scenarioId));
            when(configLoader.getKpiConfig(apiKey, scenarioId)).thenReturn(kpiConfigWithEmptySourceId); // 返回这个特殊配置
            when(configLoader.getRequiredFields()).thenReturn(Collections.singleton(kpiField));

            processor.processSpan(span);

            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> metrics = kafkaMetricsCaptor.getValue();
            assertThat(metrics).hasSize(1); // 应该只有 biz_event 指标
            assertThat(metrics.get(0).getString("measurement")).isEqualTo(TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT);
            assertThat(metrics.stream().anyMatch(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT_KPI.equals(m.getString("measurement"))))
                    .as("不应生成KPI指标，因为KPI配置的sourceEventId为空字符串")
                    .isFalse();
        }

        @Test
        @DisplayName("当KPI配置存在但sourceEventId为null时，不应生成kpi指标")
        void processSpan_noKpiMetric_whenKpiConfigSourceEventIdIsNull() {
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcKpiNullSrcEvt", resource);
            span.getJSONObject("metrics").put(kpiField, "456.78");

            CompiledEventRule matchingRule = createMockEventRule(eventId, "事件ForNullSrcEventId", serviceId, resource);
            BizKpiConfig kpiConfigWithNullSourceId = createMockKpiConfig(eventId, kpiField);
            kpiConfigWithNullSourceId.setSourceEventId(null); // 设置sourceEventId为null

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Collections.singletonList(matchingRule));
            when(configLoader.getExceptionRules(apiKey, eventId)).thenReturn(Collections.emptyList());
            when(configLoader.getScenarioIdsForEvent(apiKey, eventId)).thenReturn(Collections.singletonList(scenarioId));
            when(configLoader.getKpiConfig(apiKey, scenarioId)).thenReturn(kpiConfigWithNullSourceId);
            when(configLoader.getRequiredFields()).thenReturn(Collections.singleton(kpiField));

            processor.processSpan(span);

            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> metrics = kafkaMetricsCaptor.getValue();
            assertThat(metrics).hasSize(1);
            assertThat(metrics.get(0).getString("measurement")).isEqualTo(TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT);
            assertThat(metrics.stream().anyMatch(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT_KPI.equals(m.getString("measurement"))))
                    .as("不应生成KPI指标，因为KPI配置的sourceEventId为null")
                    .isFalse();
        }

        @Test
        @DisplayName("当KPI配置存在但sourceEventId不是有效整数时，不应生成kpi指标")
        void processSpan_noKpiMetric_whenKpiConfigSourceEventIdIsInvalidIntegerFormat() {
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcKpiInvalidIntSrcEvt", resource);
            span.getJSONObject("metrics").put(kpiField, "789.01");

            CompiledEventRule matchingRule = createMockEventRule(eventId, "事件ForInvalidIntSrcEventId", serviceId, resource);
            BizKpiConfig kpiConfigWithInvalidIntSourceId = createMockKpiConfig(eventId, kpiField);
            kpiConfigWithInvalidIntSourceId.setSourceEventId("abc"); // 非整数

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Collections.singletonList(matchingRule));
            when(configLoader.getExceptionRules(apiKey, eventId)).thenReturn(Collections.emptyList());
            when(configLoader.getScenarioIdsForEvent(apiKey, eventId)).thenReturn(Collections.singletonList(scenarioId));
            when(configLoader.getKpiConfig(apiKey, scenarioId)).thenReturn(kpiConfigWithInvalidIntSourceId);
            when(configLoader.getRequiredFields()).thenReturn(Collections.singleton(kpiField));

            processor.processSpan(span);

            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> metrics = kafkaMetricsCaptor.getValue();
            assertThat(metrics).hasSize(1);
            assertThat(metrics.get(0).getString("measurement")).isEqualTo(TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT);
            assertThat(metrics.stream().anyMatch(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT_KPI.equals(m.getString("measurement"))))
                    .as("不应生成KPI指标，因为KPI配置的sourceEventId格式无效")
                    .isFalse();
        }
        @Test
        @DisplayName("事件匹配多个场景，部分场景有有效KPI且匹配，应只为这些场景生成KPI")
        void processSpan_eventInMultipleScenarios_kpiGeneratedOnlyForValidAndMatchingConfigs() {
            String multiApiKey = "api-multi-scenario";
            String multiServiceId = "svc-multi-scenario";
            String multiResource = "/multi-res";
            int multiEventId = 801; // 一个事件

            // 定义完整的路径和基础键名
            String kpiFieldFullPath = "metrics.transactionValue";
            String kpiFieldBaseKey = "transactionValue"; // 这是实际存在于 metrics JSON 对象中的键

            int scenarioA_hasMatchingKpi = 810; // 此场景有匹配的KPI
            int scenarioB_noKpiConfig = 811;   // 此场景没有KPI配置
            int scenarioC_kpiWrongSourceEvent = 812; // 此场景KPI配置指向其他事件
            int scenarioD_kpiSourceEventNotMatchedBySpan = 913;
            int unrelatedEventId = 999;


            JSONObject span = createBaseSpanJson(multiApiKey, multiServiceId, "SvcMultiScenario", multiResource);
            // 使用基础键名将值放入 metrics 对象
            span.getJSONObject("metrics").put(kpiFieldBaseKey, 500.0);

            CompiledEventRule matchingRule = createMockEventRule(multiEventId, "多场景事件", multiServiceId, multiResource);

            // KPI 配置仍然使用完整路径
            BizKpiConfig kpiConfigForA = createMockKpiConfig(multiEventId, kpiFieldFullPath);
            BizKpiConfig kpiConfigForC = createMockKpiConfig(unrelatedEventId, kpiFieldFullPath);
            BizKpiConfig kpiConfigForD = createMockKpiConfig(unrelatedEventId + 1, kpiFieldFullPath);


            when(configLoader.getEventRulesByServiceId(multiApiKey, multiServiceId)).thenReturn(Collections.singletonList(matchingRule));
            when(configLoader.getExceptionRules(multiApiKey, multiEventId)).thenReturn(Collections.emptyList());

            when(configLoader.getScenarioIdsForEvent(multiApiKey, multiEventId))
                    .thenReturn(Arrays.asList(scenarioA_hasMatchingKpi, scenarioB_noKpiConfig, scenarioC_kpiWrongSourceEvent, scenarioD_kpiSourceEventNotMatchedBySpan));

            when(configLoader.getKpiConfig(multiApiKey, scenarioA_hasMatchingKpi)).thenReturn(kpiConfigForA);
            when(configLoader.getKpiConfig(multiApiKey, scenarioB_noKpiConfig)).thenReturn(null);
            when(configLoader.getKpiConfig(multiApiKey, scenarioC_kpiWrongSourceEvent)).thenReturn(kpiConfigForC);
            when(configLoader.getKpiConfig(multiApiKey, scenarioD_kpiSourceEventNotMatchedBySpan)).thenReturn(kpiConfigForD);


            // RequiredFields 仍然使用完整路径
            when(configLoader.getRequiredFields()).thenReturn(Collections.singleton(kpiFieldFullPath));

            processor.processSpan(span);

            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> sentMetrics = kafkaMetricsCaptor.getValue();

            assertThat(sentMetrics).hasSize(2);

            Optional<JSONObject> bizEventMetric = sentMetrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT.equals(m.getString("measurement")) &&
                            String.valueOf(multiEventId).equals(m.getJSONObject("tags").getString("bizEventId")))
                    .findFirst();
            assertThat(bizEventMetric).as("应包含事件的biz_event指标").isPresent();

            List<JSONObject> kpiMetrics = sentMetrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT_KPI.equals(m.getString("measurement")))
                    .collect(Collectors.toList());

            assertThat(kpiMetrics).as("应只为配置正确且sourceEventId匹配的场景A生成一条KPI指标").hasSize(1);
            JSONObject theKpiMetric = kpiMetrics.get(0);
            assertThat(theKpiMetric.getJSONObject("tags").getString("bizScenarioId")).isEqualTo(String.valueOf(scenarioA_hasMatchingKpi));
            assertThat(theKpiMetric.getJSONObject("tags").getString("bizEventId")).isEqualTo(String.valueOf(multiEventId));
            // 现在这里应该能正确获取到500.0
            assertThat(theKpiMetric.getJSONObject("fields").getDoubleValue("kpiAttributeValue")).isEqualTo(500.0);
        }


        // 为了测试不同的CalculationMethod，可以稍微修改createMockKpiConfig辅助方法
        // 或者在测试中直接创建和修改BizKpiConfig对象
        private BizKpiConfig createMockKpiConfigWithMethod(int sourceEventId, String sourceAttributeKey, CalculationMethod method) {
            BizKpiConfig kpiConfig = createMockKpiConfig(sourceEventId, sourceAttributeKey); // 调用现有的
            kpiConfig.setCalculationMethod(method);
            return kpiConfig;
        }

        @Test
        @DisplayName("当KPI计算方法为FIRST时，flinkAggregationHints应为FIRST")
        void processSpan_kpiCalculationMethodFirst_mapsToCorrectFlinkAggHint() {
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcKpiFirstMethod", resource);
            span.getJSONObject("metrics").put(kpiField, "10.0");

            CompiledEventRule matchingRule = createMockEventRule(eventId, "订单事件FIRST", serviceId, resource);
            // 使用新的辅助方法或直接设置
            BizKpiConfig kpiConfig = createMockKpiConfigWithMethod(eventId, kpiField, CalculationMethod.FIRST);

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Collections.singletonList(matchingRule));
            when(configLoader.getExceptionRules(apiKey, eventId)).thenReturn(Collections.emptyList());
            when(configLoader.getScenarioIdsForEvent(apiKey, eventId)).thenReturn(Collections.singletonList(scenarioId));
            when(configLoader.getKpiConfig(apiKey, scenarioId)).thenReturn(kpiConfig);
            when(configLoader.getRequiredFields()).thenReturn(Collections.singleton(kpiField));

            processor.processSpan(span);

            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> metrics = kafkaMetricsCaptor.getValue();
            Optional<JSONObject> kpiMetricOpt = metrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT_KPI.equals(m.getString("measurement")))
                    .findFirst();
            assertThat(kpiMetricOpt).isPresent();
            JSONObject kpiMetric = kpiMetricOpt.get();
            assertThat(kpiMetric.getJSONObject("flinkAggregationHints").getString("kpiAttributeValue"))
                    .isEqualTo(FlinkAggregationType.FIRST.name());
        }

        @Test
        @DisplayName("当KPI计算方法为LAST时，flinkAggregationHints应为LAST")
        void processSpan_kpiCalculationMethodLast_mapsToCorrectFlinkAggHint() {
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcKpiLastMethod", resource);
            span.getJSONObject("metrics").put(kpiField, "20.5");

            CompiledEventRule matchingRule = createMockEventRule(eventId, "订单事件LAST", serviceId, resource);
            BizKpiConfig kpiConfig = createMockKpiConfigWithMethod(eventId, kpiField, CalculationMethod.LAST);

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Collections.singletonList(matchingRule));
            when(configLoader.getExceptionRules(apiKey, eventId)).thenReturn(Collections.emptyList());
            when(configLoader.getScenarioIdsForEvent(apiKey, eventId)).thenReturn(Collections.singletonList(scenarioId));
            when(configLoader.getKpiConfig(apiKey, scenarioId)).thenReturn(kpiConfig);
            when(configLoader.getRequiredFields()).thenReturn(Collections.singleton(kpiField));

            processor.processSpan(span);

            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> metrics = kafkaMetricsCaptor.getValue();
            Optional<JSONObject> kpiMetricOpt = metrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT_KPI.equals(m.getString("measurement")))
                    .findFirst();
            assertThat(kpiMetricOpt).isPresent();
            JSONObject kpiMetric = kpiMetricOpt.get();
            assertThat(kpiMetric.getJSONObject("flinkAggregationHints").getString("kpiAttributeValue"))
                    .isEqualTo(FlinkAggregationType.LAST.name());
        }

        @Test
        @DisplayName("当KPI计算方法为null时，flinkAggregationHints应默认为SUM")
        void processSpan_kpiCalculationMethodNull_defaultsToSumFlinkAggHint() {
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcKpiNullMethod", resource);
            span.getJSONObject("metrics").put(kpiField, "30.0");

            CompiledEventRule matchingRule = createMockEventRule(eventId, "订单事件NullMethod", serviceId, resource);
            BizKpiConfig kpiConfig = createMockKpiConfigWithMethod(eventId, kpiField, null); // CalculationMethod is null

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Collections.singletonList(matchingRule));
            when(configLoader.getExceptionRules(apiKey, eventId)).thenReturn(Collections.emptyList());
            when(configLoader.getScenarioIdsForEvent(apiKey, eventId)).thenReturn(Collections.singletonList(scenarioId));
            when(configLoader.getKpiConfig(apiKey, scenarioId)).thenReturn(kpiConfig);
            when(configLoader.getRequiredFields()).thenReturn(Collections.singleton(kpiField));

            processor.processSpan(span);

            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> metrics = kafkaMetricsCaptor.getValue();
            Optional<JSONObject> kpiMetricOpt = metrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT_KPI.equals(m.getString("measurement")))
                    .findFirst();
            assertThat(kpiMetricOpt).isPresent();
            JSONObject kpiMetric = kpiMetricOpt.get();
            assertThat(kpiMetric.getJSONObject("flinkAggregationHints").getString("kpiAttributeValue"))
                    .isEqualTo(FlinkAggregationType.SUM.name()); // Default
        }

        @Test
        @DisplayName("当事件匹配且关联场景和有效 KPI 配置时，应生成 biz_event 和 biz_event_kpi 指标")
        void processSpan_generatesKpiMetric_whenScenarioAndKpiMatch() {
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcKpi", resource);
            span.getJSONObject("metrics").put("orderValue", "199.99");

            CompiledEventRule matchingRule = createMockEventRule(eventId, "订单事件", serviceId, resource);
            BizKpiConfig kpiConfig = createMockKpiConfig(eventId, kpiField);

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Collections.singletonList(matchingRule));
            when(configLoader.getExceptionRules(apiKey, eventId)).thenReturn(Collections.emptyList());
            when(configLoader.getScenarioIdsForEvent(apiKey, eventId)).thenReturn(Collections.singletonList(scenarioId));
            when(configLoader.getKpiConfig(apiKey, scenarioId)).thenReturn(kpiConfig);
            when(configLoader.getRequiredFields()).thenReturn(Collections.singleton(kpiField));

            processor.processSpan(span);

            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> metrics = kafkaMetricsCaptor.getValue();
            assertThat(metrics).hasSize(2);

            Optional<JSONObject> bizEventMetric = metrics.stream().filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT.equals(m.getString("measurement"))).findFirst();
            assertThat(bizEventMetric).isPresent();
            assertThat(bizEventMetric.get().getJSONObject("fields").getLongValue("error")).isEqualTo(0L);

            Optional<JSONObject> kpiMetric = metrics.stream().filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT_KPI.equals(m.getString("measurement"))).findFirst();
            assertThat(kpiMetric).isPresent();
            assertThat(kpiMetric.get().getJSONObject("tags").getString("bizScenarioId")).isEqualTo(String.valueOf(scenarioId));
            assertThat(kpiMetric.get().getJSONObject("tags").getString("bizEventId")).isEqualTo(String.valueOf(eventId));
            assertThat(kpiMetric.get().getJSONObject("fields").getDoubleValue("kpiAttributeValue")).isEqualTo(199.99);
            assertThat(kpiMetric.get().getJSONObject("flinkAggregationHints").getString("kpiAttributeValue")).isEqualTo(FlinkAggregationType.SUM.name());
            assertThat(kpiMetric.get().getJSONObject("fields").getLongValue("error")).isEqualTo(0L);
        }

        @Test
        @DisplayName("当发生错误时，kpiAttributeValue 应为 0")
        void processSpan_kpiValueIsZero_whenErrorOccurs() {
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcKpi", resource);
            span.getJSONObject("metrics").put("orderValue", "199.99");
            CompiledEventRule matchingRule = createMockEventRule(eventId, "订单事件", serviceId, resource);
            CompiledExceptionRule errorRule = createMockExceptionRule("system", "Timeout", 5, true);
            BizKpiConfig kpiConfig = createMockKpiConfig(eventId, kpiField);

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Collections.singletonList(matchingRule));
            when(configLoader.getExceptionRules(apiKey, eventId)).thenReturn(Collections.singletonList(errorRule));
            when(configLoader.getScenarioIdsForEvent(apiKey, eventId)).thenReturn(Collections.singletonList(scenarioId));
            when(configLoader.getKpiConfig(apiKey, scenarioId)).thenReturn(kpiConfig);
            when(configLoader.getRequiredFields()).thenReturn(new HashSet<>(Arrays.asList(kpiField, "some_error_field"))); // Assume error rule needs a field

            processor.processSpan(span);

            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> metrics = kafkaMetricsCaptor.getValue();
            Optional<JSONObject> kpiMetric = metrics.stream().filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT_KPI.equals(m.getString("measurement"))).findFirst();
            assertThat(kpiMetric).isPresent();
            assertThat(kpiMetric.get().getJSONObject("fields").getDoubleValue("kpiAttributeValue")).isEqualTo(0.0);
            assertThat(kpiMetric.get().getJSONObject("fields").getLongValue("error")).isEqualTo(1L);
        }

        @Test
        @DisplayName("当 KPI 源属性在 Span 中不存在时，kpiAttributeValue 应为 0")
        void processSpan_kpiValueIsZero_whenAttributeMissing() {
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcKpi", resource);
            CompiledEventRule matchingRule = createMockEventRule(eventId, "订单事件", serviceId, resource);
            BizKpiConfig kpiConfig = createMockKpiConfig(eventId, kpiField); // Needs metrics.orderValue

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Collections.singletonList(matchingRule));
            when(configLoader.getExceptionRules(apiKey, eventId)).thenReturn(Collections.emptyList());
            when(configLoader.getScenarioIdsForEvent(apiKey, eventId)).thenReturn(Collections.singletonList(scenarioId));
            when(configLoader.getKpiConfig(apiKey, scenarioId)).thenReturn(kpiConfig);
            when(configLoader.getRequiredFields()).thenReturn(Collections.singleton(kpiField));

            processor.processSpan(span);

            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> metrics = kafkaMetricsCaptor.getValue();
            Optional<JSONObject> kpiMetric = metrics.stream().filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT_KPI.equals(m.getString("measurement"))).findFirst();
            assertThat(kpiMetric).isPresent();
            assertThat(kpiMetric.get().getJSONObject("fields").getDoubleValue("kpiAttributeValue")).isEqualTo(0.0);
        }

        @Test
        @DisplayName("当 KPI 源属性值非数字时，kpiAttributeValue 应为 0")
        void processSpan_kpiValueIsZero_whenAttributeNotNumeric() {
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcKpi", resource);
            span.getJSONObject("metrics").put("orderValue", "ninety-nine"); // Non-numeric

            CompiledEventRule matchingRule = createMockEventRule(eventId, "订单事件", serviceId, resource);
            BizKpiConfig kpiConfig = createMockKpiConfig(eventId, kpiField);

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Collections.singletonList(matchingRule));
            when(configLoader.getExceptionRules(apiKey, eventId)).thenReturn(Collections.emptyList());
            when(configLoader.getScenarioIdsForEvent(apiKey, eventId)).thenReturn(Collections.singletonList(scenarioId));
            when(configLoader.getKpiConfig(apiKey, scenarioId)).thenReturn(kpiConfig);
            when(configLoader.getRequiredFields()).thenReturn(Collections.singleton(kpiField));

            processor.processSpan(span);

            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> metrics = kafkaMetricsCaptor.getValue();
            Optional<JSONObject> kpiMetric = metrics.stream().filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT_KPI.equals(m.getString("measurement"))).findFirst();
            assertThat(kpiMetric).isPresent();
            assertThat(kpiMetric.get().getJSONObject("fields").getDoubleValue("kpiAttributeValue")).isEqualTo(0.0);
        }

        @Test
        @DisplayName("当事件没有关联到场景时，不应生成 kpi 指标")
        void processSpan_noKpiMetric_whenNoScenarioMapping() {
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcKpi", resource);
            CompiledEventRule matchingRule = createMockEventRule(eventId, "订单事件", serviceId, resource);

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Collections.singletonList(matchingRule));
            when(configLoader.getExceptionRules(apiKey, eventId)).thenReturn(Collections.emptyList());
            when(configLoader.getScenarioIdsForEvent(apiKey, eventId)).thenReturn(Collections.emptyList()); // <<< 返回空
            when(configLoader.getRequiredFields()).thenReturn(Collections.emptySet());

            processor.processSpan(span);

            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> metrics = kafkaMetricsCaptor.getValue();
            assertThat(metrics).hasSize(1);
            assertThat(metrics.get(0).getString("measurement")).isEqualTo(TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT);
        }

        @Test
        @DisplayName("当场景没有配置 KPI 时，不应生成 kpi 指标")
        void processSpan_noKpiMetric_whenNoKpiConfig() {
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcKpi", resource);
            CompiledEventRule matchingRule = createMockEventRule(eventId, "订单事件", serviceId, resource);

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Collections.singletonList(matchingRule));
            when(configLoader.getExceptionRules(apiKey, eventId)).thenReturn(Collections.emptyList());
            when(configLoader.getScenarioIdsForEvent(apiKey, eventId)).thenReturn(Collections.singletonList(scenarioId));
            when(configLoader.getKpiConfig(apiKey, scenarioId)).thenReturn(null); // <<< 返回 null
            when(configLoader.getRequiredFields()).thenReturn(Collections.emptySet());

            processor.processSpan(span);

            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> metrics = kafkaMetricsCaptor.getValue();
            assertThat(metrics).hasSize(1);
            assertThat(metrics.get(0).getString("measurement")).isEqualTo(TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT);
        }
    }

    @Nested
    @DisplayName("运行时异常处理测试") // *** 重命名此类 ***
    class RuntimeExceptionHandlingTests { // *** 重命名此类 ***

        @Test
        @DisplayName("当条件评估抛出异常时，应记录错误并继续处理（不崩溃）")
        void processSpan_handlesExceptionDuringConditionEvaluation() {
            String apiKey = "api-eval-ex";
            String serviceId = "svc-eval-ex";
            String resource = "/ex";
            int eventId = 7;
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcEvalEx", resource);

            CompiledEventRule matchingRule = createMockEventRule(eventId, "评估异常事件", serviceId, resource);
            CompiledExceptionRule ruleWithError = mock(CompiledExceptionRule.class);
            ExecutableCondition failingCondition = mock(ExecutableCondition.class);

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Collections.singletonList(matchingRule));
            when(configLoader.getExceptionRules(apiKey, eventId)).thenReturn(Collections.singletonList(ruleWithError));
            when(ruleWithError.getRootCondition()).thenReturn(failingCondition);
            when(failingCondition.evaluate(anyMap())).thenThrow(new RuntimeException("Condition evaluation failed!"));
            when(configLoader.getRequiredFields()).thenReturn(Collections.emptySet());

            // 执行，期望不抛出异常到外部
            assertThatCode(() -> processor.processSpan(span)).doesNotThrowAnyException();

            // 验证 Kafka 仍然被调用（biz_event 指标应该生成）
            verify(kafkaSender, times(1)).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> metrics = kafkaMetricsCaptor.getValue();
            Optional<JSONObject> bizEventMetric = metrics.stream().filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT.equals(m.getString("measurement"))).findFirst();
            assertThat(bizEventMetric).isPresent();
            // 评估异常通常不计入业务/系统错误指标，error 应为 0
            assertThat(bizEventMetric.get().getJSONObject("fields").getLongValue("error")).isEqualTo(0L);
        }

        @Test
        @DisplayName("当 Kafka 发送失败时，应记录错误但不中断处理器")
        void processSpan_handlesExceptionDuringKafkaSend() {
            String apiKey = "api-kafka-ex";
            String serviceId = "svc-kafka-ex";
            String resource = "/send";
            int eventId = 8;
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcKafkaEx", resource);

            CompiledEventRule matchingRule = createMockEventRule(eventId, "发送异常事件", serviceId, resource);

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Collections.singletonList(matchingRule));
            when(configLoader.getExceptionRules(apiKey, eventId)).thenReturn(Collections.emptyList());
            when(configLoader.getScenarioIdsForEvent(apiKey, eventId)).thenReturn(Collections.emptyList());
            when(configLoader.getRequiredFields()).thenReturn(Collections.emptySet());
            doThrow(new RuntimeException("Kafka connection error")).when(kafkaSender).data2Kafka(anyList(), anyString());

            // 执行，期望不抛出异常
            assertThatCode(() -> processor.processSpan(span)).doesNotThrowAnyException();

            // 验证 kafkaSender.data2Kafka 被调用了
            verify(kafkaSender, times(1)).data2Kafka(anyList(), anyString());
        }
    }

    // Add this nested class inside BusinessObservabilityProcessorTest

    @Nested
    @DisplayName("多事件规则匹配场景测试")
    class MultiEventRuleScenariosTests {

        private String apiKey = "api-multi";
        private String serviceId = "svc-multi";
        private String resource = "/multi-endpoint";

        @Test
        @DisplayName("Span(error=0) 匹配事件A(无错)和事件B(业务错)，应为每个事件生成独立错误状态的指标")
        void processSpan_multiEvents_oneBizError_zeroSpanError() {
            // --- Arrange ---
            int eventIdA = 101; // 事件 A: 无异常规则
            int eventIdB = 102; // 事件 B: 匹配业务异常
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcMulti", resource);
            span.put("error", 0); // Span 本身无错误标记
            span.getJSONObject("meta").put("specific_for_b", "trigger_biz_error"); // 用于触发事件B的错误

            CompiledEventRule ruleA = createMockEventRule(eventIdA, "事件A-无错", serviceId, resource);
            CompiledEventRule ruleB = createMockEventRule(eventIdB, "事件B-业务错", serviceId, resource);

            CompiledExceptionRule bizErrorRuleB = createMockExceptionRule("business", "BIZ_ERROR_B", 10, true);

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Arrays.asList(ruleA, ruleB));
            // 事件 A 没有配置异常规则
            when(configLoader.getExceptionRules(apiKey, eventIdA)).thenReturn(Collections.emptyList());
            // 事件 B 配置了业务异常规则
            when(configLoader.getExceptionRules(apiKey, eventIdB)).thenReturn(Collections.singletonList(bizErrorRuleB));
            when(configLoader.getRequiredFields()).thenReturn(Collections.singleton("meta.specific_for_b"));
            // 假设事件都不关联KPI，简化测试
            when(configLoader.getScenarioIdsForEvent(anyString(), anyInt())).thenReturn(Collections.emptyList());

            // --- Act ---
            processor.processSpan(span);

            // --- Assert ---
            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> metrics = kafkaMetricsCaptor.getValue();
            assertThat(metrics).hasSize(2); // 应为两个事件生成两个 biz_event 指标

            // 验证事件 A 的指标
            Optional<JSONObject> metricAOpt = metrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT.equals(m.getString("measurement")))
                    .filter(m -> String.valueOf(eventIdA).equals(m.getJSONObject("tags").getString("bizEventId")))
                    .findFirst();
            assertThat(metricAOpt).isPresent();
            JSONObject metricA = metricAOpt.get();
            assertThat(metricA.getJSONObject("fields").getLongValue("error")).as("Metric A error count").isEqualTo(0L); // 事件A无错
            assertThat(metricA.getJSONObject("tags").getString("bizError")).as("Metric A bizError tag").isEmpty();
            assertThat(metricA.getJSONObject("tags").getString("systemError")).as("Metric A systemError tag").isEmpty();

            // 验证事件 B 的指标
            Optional<JSONObject> metricBOpt = metrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT.equals(m.getString("measurement")))
                    .filter(m -> String.valueOf(eventIdB).equals(m.getJSONObject("tags").getString("bizEventId")))
                    .findFirst();
            assertThat(metricBOpt).isPresent();
            JSONObject metricB = metricBOpt.get();
            assertThat(metricB.getJSONObject("fields").getLongValue("error")).as("Metric B error count").isEqualTo(1L); // 事件B有业务错
            assertThat(metricB.getJSONObject("tags").getString("bizError")).as("Metric B bizError tag").isEqualTo("BIZ_ERROR_B");
            assertThat(metricB.getJSONObject("tags").getString("systemError")).as("Metric B systemError tag").isEmpty();
        }

        @Test
        @DisplayName("Span(error=1) 匹配事件A(无规则->默认系统错)和事件B(匹配系统错)，应为每个事件生成独立错误状态的指标")
        void processSpan_multiEvents_oneDefaultSysError_oneCustomSysError_oneSpanError() {
            // --- Arrange ---
            int eventIdA = 201; // 事件 A: 无异常规则 -> 应匹配注入的默认系统错
            int eventIdB = 202; // 事件 B: 匹配自定义系统异常
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcMultiSysErr", resource);
            span.put("error", 1); // Span 本身有错误标记
            span.getJSONObject("meta").put("specific_for_b", "trigger_sys_error"); // 用于触发事件B的错误

            CompiledEventRule ruleA = createMockEventRule(eventIdA, "事件A-默认系统错", serviceId, resource);
            CompiledEventRule ruleB = createMockEventRule(eventIdB, "事件B-自定义系统错", serviceId, resource);

            // 模拟 ConfigProcessorImpl 注入了默认规则后的情况
            // !! 注意：测试本身不执行注入，而是模拟 getExceptionRules 返回注入后的结果 !!
            CompiledExceptionRule defaultSysErrorRule = createMockExceptionRule("system", "请求失败", Integer.MAX_VALUE, true); // 假设默认规则条件(error==1)匹配
            CompiledExceptionRule customSysErrorRuleB = createMockExceptionRule("system", "SYS_ERROR_B", 5, true); // 事件B的自定义系统错也匹配

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Arrays.asList(ruleA, ruleB));
            // 事件 A 只包含注入的默认规则
            when(configLoader.getExceptionRules(apiKey, eventIdA)).thenReturn(Collections.singletonList(defaultSysErrorRule));
            // 事件 B 包含其自定义规则 (假设用户没有配'请求失败',所以B的列表里没有默认规则)
            when(configLoader.getExceptionRules(apiKey, eventIdB)).thenReturn(Collections.singletonList(customSysErrorRuleB));
            when(configLoader.getRequiredFields()).thenReturn(new HashSet<>(Arrays.asList("error", "meta.specific_for_b")));
            when(configLoader.getScenarioIdsForEvent(anyString(), anyInt())).thenReturn(Collections.emptyList());

            // --- Act ---
            processor.processSpan(span);

            // --- Assert ---
            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> metrics = kafkaMetricsCaptor.getValue();
            assertThat(metrics).hasSize(2);

            // 验证事件 A 的指标
            Optional<JSONObject> metricAOpt = metrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT.equals(m.getString("measurement")))
                    .filter(m -> String.valueOf(eventIdA).equals(m.getJSONObject("tags").getString("bizEventId")))
                    .findFirst();
            assertThat(metricAOpt).isPresent();
            JSONObject metricA = metricAOpt.get();
            assertThat(metricA.getJSONObject("fields").getLongValue("error")).as("Metric A error count").isEqualTo(1L); // 匹配了默认系统错
            assertThat(metricA.getJSONObject("tags").getString("bizError")).as("Metric A bizError tag").isEmpty();
            assertThat(metricA.getJSONObject("tags").getString("systemError")).as("Metric A systemError tag").isEqualTo("请求失败"); // 默认系统错名称

            // 验证事件 B 的指标
            Optional<JSONObject> metricBOpt = metrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT.equals(m.getString("measurement")))
                    .filter(m -> String.valueOf(eventIdB).equals(m.getJSONObject("tags").getString("bizEventId")))
                    .findFirst();
            assertThat(metricBOpt).isPresent();
            JSONObject metricB = metricBOpt.get();
            assertThat(metricB.getJSONObject("fields").getLongValue("error")).as("Metric B error count").isEqualTo(1L); // 匹配了自定义系统错
            assertThat(metricB.getJSONObject("tags").getString("bizError")).as("Metric B bizError tag").isEmpty();
            assertThat(metricB.getJSONObject("tags").getString("systemError")).as("Metric B systemError tag").isEqualTo("SYS_ERROR_B"); // 自定义系统错名称
        }

        @Test
        @DisplayName("Span(error=1) 匹配事件A(业务错+默认系统错)和事件B(系统错)，应为每个事件生成独立错误状态的指标")
        void processSpan_multiEvents_mixErrors_oneSpanError() {
            // --- Arrange ---
            int eventIdA = 301; // 事件 A: 匹配业务错，也匹配注入的默认系统错
            int eventIdB = 302; // 事件 B: 匹配自定义系统错
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcMultiMix", resource);
            span.put("error", 1); // Span 本身有错误标记
            span.getJSONObject("meta").put("specific_for_a", "trigger_biz_error_a");
            span.getJSONObject("meta").put("specific_for_b", "trigger_sys_error_b");

            CompiledEventRule ruleA = createMockEventRule(eventIdA, "事件A-混合错", serviceId, resource);
            CompiledEventRule ruleB = createMockEventRule(eventIdB, "事件B-系统错", serviceId, resource);

            CompiledExceptionRule bizErrorRuleA = createMockExceptionRule("business", "BIZ_ERROR_A", 15, true); // 事件A业务错匹配
            // 模拟注入的默认系统错 (error==1 匹配)
            CompiledExceptionRule defaultSysErrorRule = createMockExceptionRule("system", "请求失败", Integer.MAX_VALUE, true);
            // 事件B的自定义系统错也匹配
            CompiledExceptionRule customSysErrorRuleB = createMockExceptionRule("system", "SYS_ERROR_B", 5, true);

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Arrays.asList(ruleA, ruleB));
            // 事件 A 包含业务错规则和注入的默认系统错规则
            when(configLoader.getExceptionRules(apiKey, eventIdA)).thenReturn(Arrays.asList(bizErrorRuleA, defaultSysErrorRule));
            // 事件 B 只包含自定义系统错规则
            when(configLoader.getExceptionRules(apiKey, eventIdB)).thenReturn(Collections.singletonList(customSysErrorRuleB));
            when(configLoader.getRequiredFields()).thenReturn(new HashSet<>(Arrays.asList("error", "meta.specific_for_a", "meta.specific_for_b")));
            when(configLoader.getScenarioIdsForEvent(anyString(), anyInt())).thenReturn(Collections.emptyList());

            // --- Act ---
            processor.processSpan(span);

            // --- Assert ---
            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> metrics = kafkaMetricsCaptor.getValue();
            assertThat(metrics).hasSize(2);

            // 验证事件 A 的指标
            Optional<JSONObject> metricAOpt = metrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT.equals(m.getString("measurement")))
                    .filter(m -> String.valueOf(eventIdA).equals(m.getJSONObject("tags").getString("bizEventId")))
                    .findFirst();
            assertThat(metricAOpt).isPresent();
            JSONObject metricA = metricAOpt.get();
            assertThat(metricA.getJSONObject("fields").getLongValue("error")).as("Metric A error count").isEqualTo(1L); // 有错误
            assertThat(metricA.getJSONObject("tags").getString("bizError")).as("Metric A bizError tag").isEqualTo("BIZ_ERROR_A"); // 业务错优先
            assertThat(metricA.getJSONObject("tags").getString("systemError")).as("Metric A systemError tag").isEqualTo("请求失败"); // 系统错是默认的

            // 验证事件 B 的指标
            Optional<JSONObject> metricBOpt = metrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT.equals(m.getString("measurement")))
                    .filter(m -> String.valueOf(eventIdB).equals(m.getJSONObject("tags").getString("bizEventId")))
                    .findFirst();
            assertThat(metricBOpt).isPresent();
            JSONObject metricB = metricBOpt.get();
            assertThat(metricB.getJSONObject("fields").getLongValue("error")).as("Metric B error count").isEqualTo(1L); // 有系统错
            assertThat(metricB.getJSONObject("tags").getString("bizError")).as("Metric B bizError tag").isEmpty();
            assertThat(metricB.getJSONObject("tags").getString("systemError")).as("Metric B systemError tag").isEqualTo("SYS_ERROR_B"); // 自定义系统错
        }


        @Test
        @DisplayName("当Span匹配多个事件，KPI的错误状态和值应仅基于其对应来源事件的错误状态")
        void processSpan_multiEvents_kpiErrorAndValueTiedToSpecificSourceEventError() {
            // --- Arrange ---
            int eventIdA = 401; // 事件 A: 自身无错误规则匹配
            int eventIdB = 402; // 事件 B: 自身匹配系统错误规则
            int scenarioIdA = 410; // 场景A，其KPI来源于事件A
            int scenarioIdB = 420; // 场景B，其KPI来源于事件B
            String kpiFieldA = "metrics.valueA";
            String kpiFieldB = "metrics.valueB";

            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcMultiKpiSpecificError", resource);
            span.put("error", 0); // Span顶层error为0
            span.getJSONObject("metrics").put("valueA", 100.0); // KPI A 的源数据
            span.getJSONObject("metrics").put("valueB", 200.0); // KPI B 的源数据
            span.getJSONObject("meta").put("triggerB_sys_error_field", "some_value"); // 用于触发事件B的系统错误

            CompiledEventRule ruleA = createMockEventRule(eventIdA, "事件A-无错", serviceId, resource);
            CompiledEventRule ruleB = createMockEventRule(eventIdB, "事件B-有错", serviceId, resource);

            // 事件A没有匹配的异常规则 (或者其规则评估为false)
            CompiledExceptionRule nonMatchingRuleForA = createMockExceptionRule("system", "DefaultError", Integer.MAX_VALUE, false);
            // 事件B匹配一个系统错误规则
            CompiledExceptionRule sysErrorRuleB = createMockExceptionRule("system", "SYS_ERROR_B", 5, true);

            BizKpiConfig kpiConfigA = createMockKpiConfig(eventIdA, kpiFieldA); // KPI A 指向 事件A
            BizKpiConfig kpiConfigB = createMockKpiConfig(eventIdB, kpiFieldB); // KPI B 指向 事件B

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Arrays.asList(ruleA, ruleB));
            when(configLoader.getExceptionRules(apiKey, eventIdA)).thenReturn(Collections.singletonList(nonMatchingRuleForA)); // 事件A无错
            when(configLoader.getExceptionRules(apiKey, eventIdB)).thenReturn(Collections.singletonList(sysErrorRuleB));       // 事件B有错

            when(configLoader.getScenarioIdsForEvent(apiKey, eventIdA)).thenReturn(Collections.singletonList(scenarioIdA));
            when(configLoader.getScenarioIdsForEvent(apiKey, eventIdB)).thenReturn(Collections.singletonList(scenarioIdB));
            when(configLoader.getKpiConfig(apiKey, scenarioIdA)).thenReturn(kpiConfigA);
            when(configLoader.getKpiConfig(apiKey, scenarioIdB)).thenReturn(kpiConfigB);
            when(configLoader.getRequiredFields()).thenReturn(new HashSet<>(Arrays.asList(kpiFieldA, kpiFieldB, "meta.triggerB_sys_error_field")));

            // --- Act ---
            processor.processSpan(span);

            // --- Assert ---
            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> metrics = kafkaMetricsCaptor.getValue();
            assertThat(metrics).hasSize(4); // 2 biz_event + 2 biz_event_kpi

            // 验证 biz_event 指标的错误状态 (这部分逻辑应该依然正确)
            Optional<JSONObject> bizEventA = metrics.stream().filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT.equals(m.getString("measurement")) && String.valueOf(eventIdA).equals(m.getJSONObject("tags").getString("bizEventId"))).findFirst();
            assertThat(bizEventA).isPresent();
            assertThat(bizEventA.get().getJSONObject("fields").getLongValue("error")).as("BizEvent A error count").isEqualTo(0L); // 事件A本身无错

            Optional<JSONObject> bizEventB = metrics.stream().filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT.equals(m.getString("measurement")) && String.valueOf(eventIdB).equals(m.getJSONObject("tags").getString("bizEventId"))).findFirst();
            assertThat(bizEventB).isPresent();
            assertThat(bizEventB.get().getJSONObject("fields").getLongValue("error")).as("BizEvent B error count").isEqualTo(1L); // 事件B有错

            // 验证 KPI 指标的 error 字段和 kpiAttributeValue
            // KPI A (来源于无错的事件A)
            Optional<JSONObject> kpiMetricA = metrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT_KPI.equals(m.getString("measurement")))
                    .filter(m -> String.valueOf(scenarioIdA).equals(m.getJSONObject("tags").getString("bizScenarioId")))
                    .findFirst();
            assertThat(kpiMetricA).isPresent();
            assertThat(kpiMetricA.get().getJSONObject("fields").getLongValue("error")).as("KPI Metric A error count (event A was not in error)")
                    .isEqualTo(0L); // 修正：因为事件A无错
            assertThat(kpiMetricA.get().getJSONObject("fields").getDoubleValue("kpiAttributeValue")).as("KPI Metric A value (event A was not in error)")
                    .isEqualTo(100.0); // 修正：因为事件A无错，KPI值应正常计算

            // KPI B (来源于有错的事件B)
            Optional<JSONObject> kpiMetricB = metrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT_KPI.equals(m.getString("measurement")))
                    .filter(m -> String.valueOf(scenarioIdB).equals(m.getJSONObject("tags").getString("bizScenarioId")))
                    .findFirst();
            assertThat(kpiMetricB).isPresent();
            assertThat(kpiMetricB.get().getJSONObject("fields").getLongValue("error")).as("KPI Metric B error count (event B was in error)")
                    .isEqualTo(1L); // 因为事件B有错
            assertThat(kpiMetricB.get().getJSONObject("fields").getDoubleValue("kpiAttributeValue")).as("KPI Metric B value (event B was in error)")
                    .isEqualTo(0.0); // 因为事件B有错，KPI值为0
        }
    }

    // ===================================================================================
    // == 新增的测试用例，用于专门验证您描述的 "KPI上报了所有关联的业务事件" 问题 ==
    // ===================================================================================
    @Nested
    @DisplayName("复杂KPI场景与多事件匹配对KPI唯一性影响的测试")
    class ComplexKpiAndMultiEventMatchingForKpiUniquenessTests {

        private String apiKey = "api-unique-kpi";
        private String serviceId = "svc-unique-kpi";
        private String resource = "/unique-kpi-endpoint";
        private int sharedScenarioId = 700; // 两个事件都属于这个场景

        // 事件ID定义
        private int eventIdX_KpiSource = 701; // KPI配置将指向此事件
        private int eventIdY_NonKpiSource = 702; // Span也会匹配此事件，但不应产生独立KPI
        private int eventIdZ_NotMatched = 703; // Span不会匹配此事件 (用于验证KPI配置的sourceEventId的精确性)

        // KPI源属性
        private String kpiFieldForEventX = "metrics.valueForX";

        @Test
        @DisplayName("当Span匹配多个事件(X,Y)同属场景S, 且S的KPI指向事件X时, [新逻辑]应只为事件X生成一条KPI指标, [旧逻辑]可能会为X和Y都生成")
        void processSpan_multiMatchedEventsInSameScenario_kpiForCorrectSourceEventOnly_uniquenessCheck() {
            // --- Arrange ---
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcUniqueKpi", resource);
            span.getJSONObject("metrics").put("valueForX", 123.45); // 事件X的KPI源数据
            span.getJSONObject("metrics").put("valueForY", 678.90); // 事件Y的数据，不应作为KPI值
            span.put("error", 0);

            CompiledEventRule ruleX = createMockEventRule(eventIdX_KpiSource, "事件X-KPI源", serviceId, resource);
            CompiledEventRule ruleY = createMockEventRule(eventIdY_NonKpiSource, "事件Y-同场景非KPI源", serviceId, resource);

            BizKpiConfig kpiConfigForSharedScenario = createMockKpiConfig(eventIdX_KpiSource, kpiFieldForEventX); // KPI指向事件X

            // Mock ConfigLoader行为
            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Arrays.asList(ruleX, ruleY)); // Span匹配事件X和Y

            when(configLoader.getExceptionRules(apiKey, eventIdX_KpiSource)).thenReturn(Collections.emptyList());
            when(configLoader.getExceptionRules(apiKey, eventIdY_NonKpiSource)).thenReturn(Collections.emptyList());

            // 两个事件都属于同一个场景 sharedScenarioId
            when(configLoader.getScenarioIdsForEvent(apiKey, eventIdX_KpiSource)).thenReturn(Collections.singletonList(sharedScenarioId));
            when(configLoader.getScenarioIdsForEvent(apiKey, eventIdY_NonKpiSource)).thenReturn(Collections.singletonList(sharedScenarioId));

            when(configLoader.getKpiConfig(apiKey, sharedScenarioId)).thenReturn(kpiConfigForSharedScenario); // 该场景的KPI配置

            when(configLoader.getRequiredFields()).thenReturn(new HashSet<>(Arrays.asList(kpiFieldForEventX, "metrics.valueForY")));

            // --- Act ---
            processor.processSpan(span);

            // --- Assert ---
            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> sentMetrics = kafkaMetricsCaptor.getValue();

            // 验证 biz_event 指标 (应有两条，分别为事件X和事件Y)
            long bizEventMetricsCount = sentMetrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT.equals(m.getString("measurement")))
                    .count();
            assertThat(bizEventMetricsCount).as("应为匹配的事件X和Y分别生成biz_event指标").isEqualTo(2);


            // 验证 biz_event_kpi 指标
            List<JSONObject> kpiMetrics = sentMetrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT_KPI.equals(m.getString("measurement")))
                    .collect(Collectors.toList());

            // --- 断言核心 ---
            // 使用新逻辑：应该只有一条 KPI 指标，且其 bizEventId 是 eventIdX_KpiSource
            // 使用旧逻辑：可能会有两条 KPI 指标，一条 bizEventId=eventIdX_KpiSource (value=123.45)，
            //              另一条 bizEventId=eventIdY_NonKpiSource (value=0.0)

            // 为了让测试在两种逻辑下都能提供信息，我们可以先检查数量，再检查内容
            // 如果是旧逻辑，这里会失败 (期望1，实际2)
            assertThat(kpiMetrics).as("期望只为KPI配置中指定的sourceEventId生成一条biz_event_kpi指标").hasSize(1);

            // 如果上面的断言通过了（说明行为符合新逻辑，或者旧逻辑恰好也只生成了一条），再细致检查内容
            if (!kpiMetrics.isEmpty()) {
                JSONObject theKpiMetric = kpiMetrics.get(0);
                assertThat(theKpiMetric.getJSONObject("tags").getString("bizScenarioId"))
                        .as("KPI指标的场景ID应正确")
                        .isEqualTo(String.valueOf(sharedScenarioId));
                assertThat(theKpiMetric.getJSONObject("tags").getString("bizEventId"))
                        .as("KPI指标的业务事件ID应为KPI配置中指定的sourceEventId (事件X)")
                        .isEqualTo(String.valueOf(eventIdX_KpiSource));
                assertThat(theKpiMetric.getJSONObject("fields").getDoubleValue("kpiAttributeValue"))
                        .as("KPI值应从事件X的源属性计算得到")
                        .isEqualTo(123.45);
                assertThat(theKpiMetric.getJSONObject("fields").getLongValue("error"))
                        .as("KPI指标的错误计数")
                        .isEqualTo(0L);
            }
        }

        @Test
        @DisplayName("当Span只匹配事件Y, 但场景S的KPI指向未匹配的事件X时, [新逻辑]不应生成KPI, [旧逻辑]可能会为事件Y生成value=0的KPI")
        void processSpan_matchedEventY_kpiSourceX_notMatched_uniquenessCheck() {
            // --- Arrange ---
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcUniqueKpiNoMatch", resource);
            span.getJSONObject("metrics").put("valueForY", 678.90); // 事件Y的数据
            span.put("error", 0);

            CompiledEventRule ruleY = createMockEventRule(eventIdY_NonKpiSource, "事件Y-无对应KPI", serviceId, resource);

            // 场景sharedScenarioId的KPI源是事件X (事件X此次不会被span匹配到)
            BizKpiConfig kpiConfigForSharedScenario = createMockKpiConfig(eventIdX_KpiSource, kpiFieldForEventX);

            // Mock ConfigLoader行为
            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Collections.singletonList(ruleY)); // Span只匹配事件Y

            when(configLoader.getExceptionRules(apiKey, eventIdY_NonKpiSource)).thenReturn(Collections.emptyList());

            when(configLoader.getScenarioIdsForEvent(apiKey, eventIdY_NonKpiSource)).thenReturn(Collections.singletonList(sharedScenarioId)); // 事件Y属于场景

            when(configLoader.getKpiConfig(apiKey, sharedScenarioId)).thenReturn(kpiConfigForSharedScenario); // 该场景的KPI配置指向事件X

            when(configLoader.getRequiredFields()).thenReturn(new HashSet<>(Arrays.asList("metrics.valueForY", kpiFieldForEventX)));

            // --- Act ---
            processor.processSpan(span);

            // --- Assert ---
            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> sentMetrics = kafkaMetricsCaptor.getValue();

            // 验证 biz_event 指标 (应只有事件Y的)
            long bizEventMetricsCount = sentMetrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT.equals(m.getString("measurement")))
                    .count();
            assertThat(bizEventMetricsCount).as("应只为匹配的事件Y生成biz_event指标").isEqualTo(1);
            assertThat(sentMetrics.get(0).getJSONObject("tags").getString("bizEventId")).isEqualTo(String.valueOf(eventIdY_NonKpiSource));


            // 验证 biz_event_kpi 指标
            long kpiMetricsCount = sentMetrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT_KPI.equals(m.getString("measurement")))
                    .count();

            // --- 断言核心 ---
            // 使用新逻辑：不应生成任何 KPI 指标
            // 使用旧逻辑：可能会为事件Y生成一条 KPI 指标，bizEventId=eventIdY_NonKpiSource, value=0.0
            // 如果是旧逻辑，这里会失败 (期望0，实际1)
            assertThat(kpiMetricsCount).as("不应生成任何biz_event_kpi指标，因为KPI配置的sourceEventId未被匹配").isZero();
        }
    }

    // 在 BusinessObservabilityProcessorTest.java 文件中，添加这个新的 Nested 测试类

    @Nested
    @DisplayName("高级KPI与错误处理场景测试")
    class AdvancedKpiAndErrorScenariosTests {

        private final String apiKey = "api-adv-kpi";
        private final String serviceId = "svc-adv-kpi";
        private final String resource = "/adv-endpoint";

        @Test
        @DisplayName("Span匹配事件A(无错,KPI1源)和事件B(有错,KPI2源)，应分别为KPI1和KPI2生成正确的指标和错误状态")
        void processSpan_twoEvents_twoKpis_oneEventInError_kpiErrorsAreSpecific() {
            // --- Arrange ---
            int eventIdA = 501; // 事件A，无错，KPI1的源
            int eventIdB = 502; // 事件B，有系统错误，KPI2的源

            int scenarioIdA = 510; // 场景A，包含KPI1 (源: eventIdA)
            int scenarioIdB = 520; // 场景B，包含KPI2 (源: eventIdB)

            String kpiFieldA = "metrics.valueA";
            String kpiFieldB = "metrics.valueB";

            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcAdvKpi", resource);
            span.put("error", 0); // Span顶层无错
            span.getJSONObject("metrics").put("valueA", 100.0);
            span.getJSONObject("metrics").put("valueB", 200.0);
            span.getJSONObject("meta").put("error_trigger_for_B", "fail"); // 用于触发事件B的错误

            CompiledEventRule ruleA = createMockEventRule(eventIdA, "事件A", serviceId, resource);
            CompiledEventRule ruleB = createMockEventRule(eventIdB, "事件B", serviceId, resource);

            // 事件A无异常规则匹配 (或返回的规则评估为false)
            CompiledExceptionRule noErrorRuleForA = createMockExceptionRule("system", "Default", Integer.MAX_VALUE, false);
            // 事件B匹配一个系统错误
            CompiledExceptionRule sysErrorRuleForB = createMockExceptionRule("system", "SYS_ERROR_B", 10, true);

            BizKpiConfig kpiConfigA = createMockKpiConfig(eventIdA, kpiFieldA);
            BizKpiConfig kpiConfigB = createMockKpiConfig(eventIdB, kpiFieldB);

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Arrays.asList(ruleA, ruleB));
            when(configLoader.getExceptionRules(apiKey, eventIdA)).thenReturn(Collections.singletonList(noErrorRuleForA));
            when(configLoader.getExceptionRules(apiKey, eventIdB)).thenReturn(Collections.singletonList(sysErrorRuleForB));

            when(configLoader.getScenarioIdsForEvent(apiKey, eventIdA)).thenReturn(Collections.singletonList(scenarioIdA));
            when(configLoader.getScenarioIdsForEvent(apiKey, eventIdB)).thenReturn(Collections.singletonList(scenarioIdB));
            when(configLoader.getKpiConfig(apiKey, scenarioIdA)).thenReturn(kpiConfigA);
            when(configLoader.getKpiConfig(apiKey, scenarioIdB)).thenReturn(kpiConfigB);
            when(configLoader.getRequiredFields()).thenReturn(new HashSet<>(Arrays.asList(kpiFieldA, kpiFieldB, "meta.error_trigger_for_B")));

            // --- Act ---
            processor.processSpan(span);

            // --- Assert ---
            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> metrics = kafkaMetricsCaptor.getValue();
            assertThat(metrics).hasSize(4); // 2 biz_event + 2 biz_event_kpi

            // 验证 BizEvent A
            Optional<JSONObject> bizEventA = metrics.stream().filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT.equals(m.getString("measurement")) && String.valueOf(eventIdA).equals(m.getJSONObject("tags").getString("bizEventId"))).findFirst();
            assertThat(bizEventA).isPresent();
            assertThat(bizEventA.get().getJSONObject("fields").getLongValue("error")).isEqualTo(0L);

            // 验证 BizEvent B
            Optional<JSONObject> bizEventB = metrics.stream().filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT.equals(m.getString("measurement")) && String.valueOf(eventIdB).equals(m.getJSONObject("tags").getString("bizEventId"))).findFirst();
            assertThat(bizEventB).isPresent();
            assertThat(bizEventB.get().getJSONObject("fields").getLongValue("error")).isEqualTo(1L);
            assertThat(bizEventB.get().getJSONObject("tags").getString("systemError")).isEqualTo("SYS_ERROR_B");

            // 验证 KPI A (源于无错的 EventA)
            Optional<JSONObject> kpiMetricA = metrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT_KPI.equals(m.getString("measurement")) && String.valueOf(scenarioIdA).equals(m.getJSONObject("tags").getString("bizScenarioId")))
                    .findFirst();
            assertThat(kpiMetricA).isPresent();
            assertThat(kpiMetricA.get().getJSONObject("fields").getLongValue("error")).isEqualTo(0L);
            assertThat(kpiMetricA.get().getJSONObject("fields").getDoubleValue("kpiAttributeValue")).isEqualTo(100.0);

            // 验证 KPI B (源于有错的 EventB)
            Optional<JSONObject> kpiMetricB = metrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT_KPI.equals(m.getString("measurement")) && String.valueOf(scenarioIdB).equals(m.getJSONObject("tags").getString("bizScenarioId")))
                    .findFirst();
            assertThat(kpiMetricB).isPresent();
            assertThat(kpiMetricB.get().getJSONObject("fields").getLongValue("error")).isEqualTo(1L);
            assertThat(kpiMetricB.get().getJSONObject("fields").getDoubleValue("kpiAttributeValue")).isEqualTo(0.0);
        }

        @Test
        @DisplayName("Span匹配事件A(KPI源, 无错)和事件B(有错, 非KPI源)，事件A的KPI不应受事件B错误影响")
        void processSpan_kpiEventNoError_otherEventError_kpiNotAffected() {
            // --- Arrange ---
            int eventIdA_kpiSource = 601; // 事件A, KPI的源, 本身无错
            int eventIdB_errorOnly = 602; // 事件B, 自身有错, 但不是任何KPI的源 (或其场景无KPI)

            int scenarioIdA = 610; // 场景A, 包含KPI (源: eventIdA_kpiSource)
            // 场景B (如果事件B属于某个场景) 在此测试中不配置KPI，或者事件B不属于任何带KPI的场景

            String kpiFieldA = "metrics.valueA";
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcKpiUnaffected", resource);
            span.getJSONObject("metrics").put("valueA", 50.0);
            span.getJSONObject("meta").put("trigger_event_b_error", "true"); // 触发事件B的错误

            CompiledEventRule ruleA = createMockEventRule(eventIdA_kpiSource, "事件A-KPI源", serviceId, resource);
            CompiledEventRule ruleB = createMockEventRule(eventIdB_errorOnly, "事件B-仅错误", serviceId, resource);

            CompiledExceptionRule noErrorRuleForA = createMockExceptionRule("system", "Default", Integer.MAX_VALUE, false);
            CompiledExceptionRule sysErrorRuleForB = createMockExceptionRule("system", "SYS_ERROR_FOR_B", 5, true);
            BizKpiConfig kpiConfigA = createMockKpiConfig(eventIdA_kpiSource, kpiFieldA);

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Arrays.asList(ruleA, ruleB));
            when(configLoader.getExceptionRules(apiKey, eventIdA_kpiSource)).thenReturn(Collections.singletonList(noErrorRuleForA));
            when(configLoader.getExceptionRules(apiKey, eventIdB_errorOnly)).thenReturn(Collections.singletonList(sysErrorRuleForB));

            when(configLoader.getScenarioIdsForEvent(apiKey, eventIdA_kpiSource)).thenReturn(Collections.singletonList(scenarioIdA));
            when(configLoader.getScenarioIdsForEvent(apiKey, eventIdB_errorOnly)).thenReturn(Collections.emptyList()); // 事件B不属于任何带KPI的场景
            when(configLoader.getKpiConfig(apiKey, scenarioIdA)).thenReturn(kpiConfigA);
            when(configLoader.getRequiredFields()).thenReturn(new HashSet<>(Arrays.asList(kpiFieldA, "meta.trigger_event_b_error")));

            // --- Act ---
            processor.processSpan(span);

            // --- Assert ---
            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> metrics = kafkaMetricsCaptor.getValue();
            // 预期：biz_event for A, biz_event for B, biz_event_kpi for A. 共3条
            assertThat(metrics).hasSize(3);

            // 验证 KPI A (源于无错的 EventA)
            Optional<JSONObject> kpiMetricA = metrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT_KPI.equals(m.getString("measurement")) &&
                            String.valueOf(scenarioIdA).equals(m.getJSONObject("tags").getString("bizScenarioId")))
                    .findFirst();
            assertThat(kpiMetricA).isPresent();
            assertThat(kpiMetricA.get().getJSONObject("fields").getLongValue("error"))
                    .as("KPI A 的错误计数应为0，因为它自己的源事件A无错")
                    .isEqualTo(0L);
            assertThat(kpiMetricA.get().getJSONObject("fields").getDoubleValue("kpiAttributeValue"))
                    .as("KPI A 的值应正常计算，因为它自己的源事件A无错")
                    .isEqualTo(50.0);
        }

        @Test
        @DisplayName("一个业务事件同时是多个场景的KPI来源，应为每个场景都生成KPI指标")
        void processSpan_singleEventSourceForMultipleKpisInDifferentScenarios() {
            // --- Arrange ---
            int sourceEventId = 701;
            int scenarioX = 710;
            int scenarioY = 720;
            String kpiField = "metrics.universalValue";

            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcMultiScnKpi", resource);
            span.getJSONObject("metrics").put("universalValue", 250.0);
            span.put("error", 0); // 假设此事件无错

            CompiledEventRule ruleSource = createMockEventRule(sourceEventId, "共享KPI源事件", serviceId, resource);
            CompiledExceptionRule noErrorRule = createMockExceptionRule("system", "Default", Integer.MAX_VALUE, false);

            BizKpiConfig kpiConfigX = createMockKpiConfig(sourceEventId, kpiField);
            kpiConfigX.setKpiName("KPI for Scenario X");
            BizKpiConfig kpiConfigY = createMockKpiConfig(sourceEventId, kpiField); // 假设使用相同的字段，但可以是不同的
            kpiConfigY.setKpiName("KPI for Scenario Y");


            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Collections.singletonList(ruleSource));
            when(configLoader.getExceptionRules(apiKey, sourceEventId)).thenReturn(Collections.singletonList(noErrorRule));

            // 共享KPI源事件同时属于场景X和场景Y
            when(configLoader.getScenarioIdsForEvent(apiKey, sourceEventId)).thenReturn(Arrays.asList(scenarioX, scenarioY));
            when(configLoader.getKpiConfig(apiKey, scenarioX)).thenReturn(kpiConfigX);
            when(configLoader.getKpiConfig(apiKey, scenarioY)).thenReturn(kpiConfigY);
            when(configLoader.getRequiredFields()).thenReturn(Collections.singleton(kpiField));

            // --- Act ---
            processor.processSpan(span);

            // --- Assert ---
            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> metrics = kafkaMetricsCaptor.getValue();
            // 预期: 1个 biz_event (for sourceEventId) + 2个 biz_event_kpi (for scenarioX and scenarioY) = 3条
            assertThat(metrics).hasSize(3);

            // 验证 KPI for Scenario X
            Optional<JSONObject> kpiMetricX = metrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT_KPI.equals(m.getString("measurement")) &&
                            String.valueOf(scenarioX).equals(m.getJSONObject("tags").getString("bizScenarioId")))
                    .findFirst();
            assertThat(kpiMetricX).isPresent();
            assertThat(kpiMetricX.get().getJSONObject("tags").getString("bizEventId")).isEqualTo(String.valueOf(sourceEventId));
            assertThat(kpiMetricX.get().getJSONObject("fields").getDoubleValue("kpiAttributeValue")).isEqualTo(250.0);
            assertThat(kpiMetricX.get().getJSONObject("fields").getLongValue("error")).isEqualTo(0L);

            // 验证 KPI for Scenario Y
            Optional<JSONObject> kpiMetricY = metrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT_KPI.equals(m.getString("measurement")) &&
                            String.valueOf(scenarioY).equals(m.getJSONObject("tags").getString("bizScenarioId")))
                    .findFirst();
            assertThat(kpiMetricY).isPresent();
            assertThat(kpiMetricY.get().getJSONObject("tags").getString("bizEventId")).isEqualTo(String.valueOf(sourceEventId));
            assertThat(kpiMetricY.get().getJSONObject("fields").getDoubleValue("kpiAttributeValue")).isEqualTo(250.0);
            assertThat(kpiMetricY.get().getJSONObject("fields").getLongValue("error")).isEqualTo(0L);
        }

        // 可以根据需要添加更多针对 CalculationMethod (FIRST, LAST) 的测试
        // 例如，如果测试FIRST，需要mock多个span来构成一个窗口，这超出了单个processSpan的测试范围
        // 对于单个processSpan，我们主要验证kpiAttributeValue是否正确提取，以及flinkAggregationHint是否正确设置
        @Test
        @DisplayName("KPI配置为FIRST方法，指标中应包含正确的聚合提示")
        void processSpan_kpiMethodFirst_setsCorrectFlinkHint() {
            int eventId = 701;
            int scenarioId = 710;
            String kpiField = "metrics.firstValue";
            JSONObject span = createBaseSpanJson(apiKey, serviceId, "SvcKpiFirst", resource);
            span.getJSONObject("metrics").put("firstValue", 55.0);

            CompiledEventRule rule = createMockEventRule(eventId, "事件FIRST", serviceId, resource);
            BizKpiConfig kpiConfig = createMockKpiConfig(eventId, kpiField);
            kpiConfig.setCalculationMethod(CalculationMethod.FIRST); // 设置为FIRST

            when(configLoader.getEventRulesByServiceId(apiKey, serviceId)).thenReturn(Collections.singletonList(rule));
            when(configLoader.getExceptionRules(apiKey, eventId)).thenReturn(Collections.emptyList());
            when(configLoader.getScenarioIdsForEvent(apiKey, eventId)).thenReturn(Collections.singletonList(scenarioId));
            when(configLoader.getKpiConfig(apiKey, scenarioId)).thenReturn(kpiConfig);
            when(configLoader.getRequiredFields()).thenReturn(Collections.singleton(kpiField));

            processor.processSpan(span);

            verify(kafkaSender).data2Kafka(kafkaMetricsCaptor.capture(), eq(Constant.Kafka.STANDARD_METRIC_TOPIC));
            List<JSONObject> metrics = kafkaMetricsCaptor.getValue();
            Optional<JSONObject> kpiMetric = metrics.stream()
                    .filter(m -> TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT_KPI.equals(m.getString("measurement")) &&
                            String.valueOf(scenarioId).equals(m.getJSONObject("tags").getString("bizScenarioId")))
                    .findFirst();

            assertThat(kpiMetric).isPresent();
            assertThat(kpiMetric.get().getJSONObject("fields").getDoubleValue("kpiAttributeValue")).isEqualTo(55.0);
            assertThat(kpiMetric.get().getJSONObject("flinkAggregationHints").getString("kpiAttributeValue"))
                    .isEqualTo(FlinkAggregationType.FIRST.name());
        }

    }
}