package com.databuff.dts.receive.biz.processor.config.processor;

import com.databuff.dts.receive.biz.processor.condition.ExecutableCondition;
import com.databuff.dts.receive.biz.processor.config.compiler.ConditionCompiler;
import com.databuff.dts.receive.biz.processor.config.dto.ConfigSourceData;
import com.databuff.dts.receive.biz.processor.config.dto.ProcessedConfigData;
import com.databuff.dts.receive.biz.processor.model.CompiledExceptionRule;
import com.databuff.entity.BizEvent;
import com.databuff.entity.BizEventReqs;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatcher;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@DisplayName("ConfigProcessorImpl 单元测试")
@ExtendWith(MockitoExtension.class)
class ConfigProcessorImplTest {

    @Mock
    private ConditionCompiler conditionCompiler;

    @Mock
    private ObjectMapper objectMapper; // Mock ObjectMapper for controlled JSON parsing

    @InjectMocks
    private ConfigProcessorImpl configProcessor;

    // --- Constants matching those in ConfigProcessorImpl for default rule ---
    private static final String DEFAULT_RULE_ERROR_NAME = "请求失败";
    private static final String DEFAULT_RULE_ERROR_TYPE = "system";
    private static final int DEFAULT_RULE_PRIORITY = Integer.MAX_VALUE;
    private static final String DEFAULT_RULE_CONDITION_FIELD = "error";
    private static final Object DEFAULT_RULE_CONDITION_VALUE = 1;
    private static final String DEFAULT_RULE_CONDITION_OPERATOR_SYMBOL = "=";
    private static final String DEFAULT_RULE_CONDITION_FIELD_TYPE = "NUMERIC";

    // --- Helper Methods ---

    private BizEvent createBizEvent(int id, String apiKey, String bizReqsJson, String exceptionRulesJson) {
        BizEvent event = new BizEvent();
        event.setId(id);
        event.setApiKey(apiKey);
        event.setBizName("Event " + id);
        event.setBizReqs(bizReqsJson); // Assume simple reqs for these tests
        event.setExceptionRules(exceptionRulesJson);
        return event;
    }

    // ArgumentMatcher to identify the call to compile the default condition (error == 1)
    private ArgumentMatcher<Object> isDefaultConditionRaw() {
        return conditionsRaw -> {
            if (!(conditionsRaw instanceof List)) return false;
            List<?> list = (List<?>) conditionsRaw;
            if (list.size() != 1 || !(list.get(0) instanceof Map)) return false;
            Map<?, ?> map = (Map<?, ?>) list.get(0);
            return DEFAULT_RULE_CONDITION_FIELD.equals(map.get("left")) &&
                    DEFAULT_RULE_CONDITION_OPERATOR_SYMBOL.equals(map.get("operator")) &&
                    DEFAULT_RULE_CONDITION_VALUE.equals(map.get("right")) &&
                    DEFAULT_RULE_CONDITION_FIELD_TYPE.equals(map.get("fieldType"));
        };
    }

    // ArgumentMatcher to identify calls for user-defined conditions (anything not the default)
    private ArgumentMatcher<Object> isUserConditionRaw() {
        // A simple check for non-list or list not matching default should suffice for these tests
        return conditionsRaw -> !(conditionsRaw instanceof List) || !isDefaultConditionRaw().matches(conditionsRaw);
    }

    // Helper to extract the FIRST field name from a simple user condition structure for testing
    // In a real scenario, ConditionCompiler would extract ALL fields.
    private String extractFirstFieldNameFromUserCondition(Object conditionsRaw) {
        try {
            if (conditionsRaw instanceof List && !((List<?>) conditionsRaw).isEmpty()) {
                Object firstCond = ((List<?>) conditionsRaw).get(0);
                if (firstCond instanceof Map) {
                    return (String) ((Map<?, ?>) firstCond).get("left");
                }
            }
        } catch (Exception e) { /* ignore casting issues */ }
        return null; // Return null if structure doesn't match expected simple form
    }


    // Mock setup for ObjectMapper readValue for exception rules
    @SuppressWarnings("unchecked")
    private void setupObjectMapperForExceptions(String json, List<Map<String, Object>> result) throws Exception {
        // Use unchecked cast for TypeReference, common practice in Mockito setups
        when(objectMapper.readValue(eq(json), any(TypeReference.class))).thenReturn(result);
    }

    // Mock setup for ObjectMapper readValue failure
    @SuppressWarnings("unchecked")
    private void setupObjectMapperForExceptionsFailure(String json) throws Exception {
        when(objectMapper.readValue(eq(json), any(TypeReference.class)))
                .thenThrow(new JsonProcessingException("Simulated JSON parse error"){});
    }

    // Mock setup for ObjectMapper readValue for BizEventReqs (simple mock)
    @SuppressWarnings("unchecked")
    private void setupObjectMapperForBizReqs(String json) throws Exception {
        // Assume a simple structure or empty list is sufficient for these tests
        List<BizEventReqs> mockReqs = Collections.singletonList(new BizEventReqs());
        mockReqs.get(0).setSvcId("mockSvcId"); // Need a service ID for indexing
        when(objectMapper.readValue(eq(json), any(TypeReference.class))).thenReturn(mockReqs);
    }


    @Nested
    @DisplayName("parseAndCompileExceptionRules 效果测试 (通过 processConfigs)")
    class ParseAndCompileExceptionRulesEffectTests {

        private final String apiKey = "test-key";
        private final int eventId = 1;
        private final String simpleBizReqsJson = "[{\"svcId\":\"mockSvcId\"}]"; // Minimal valid BizReqs

        @Mock ExecutableCondition mockUserCondition;
        @Mock ExecutableCondition mockDefaultCondition;


        @BeforeEach
        void setupBaseMocks() throws Exception {
            // Setup default behavior for BizReqs parsing if needed
            setupObjectMapperForBizReqs(simpleBizReqsJson);

            // --- MODIFIED: Use thenAnswer and lenient ---
            // Use lenient() to avoid UnnecessaryStubbingException in tests that don't use all mocks
            lenient().when(conditionCompiler.compileConditions(argThat(isUserConditionRaw()), any(Set.class)))
                    .thenAnswer(new Answer<ExecutableCondition>() {
                        @Override
                        public ExecutableCondition answer(InvocationOnMock invocation) throws Throwable {
                            Set<String> requiredFields = invocation.getArgument(1);
                            Object conditionsRaw = invocation.getArgument(0);
                            // Simulate adding the field to the collector
                            String fieldName = extractFirstFieldNameFromUserCondition(conditionsRaw);
                            if (fieldName != null) {
                                requiredFields.add(fieldName);
                            }
                            return mockUserCondition; // Return the mock condition
                        }
                    });

            lenient().when(conditionCompiler.compileConditions(argThat(isDefaultConditionRaw()), any(Set.class)))
                    .thenAnswer(new Answer<ExecutableCondition>() {
                        @Override
                        public ExecutableCondition answer(InvocationOnMock invocation) throws Throwable {
                            Set<String> requiredFields = invocation.getArgument(1);
                            // Simulate adding the default field to the collector
                            requiredFields.add(DEFAULT_RULE_CONDITION_FIELD);
                            return mockDefaultCondition; // Return the mock condition
                        }
                    });
            // --- END MODIFICATION ---
        }

        @Test
        @DisplayName("当 exceptionRules 为 null 时，应注入默认规则")
        void processConfigs_injectsDefaultRule_whenExceptionRulesNull() throws Exception {
            // Arrange
            BizEvent event = createBizEvent(eventId, apiKey, simpleBizReqsJson, null);
            ConfigSourceData sourceData = new ConfigSourceData(Collections.singletonList(event), Collections.emptyList());

            // Act
            ProcessedConfigData result = configProcessor.processConfigs(sourceData);

            // Assert
            verify(conditionCompiler, times(1)).compileConditions(argThat(isDefaultConditionRaw()), any(Set.class));
            verify(conditionCompiler, never()).compileConditions(argThat(isUserConditionRaw()), any(Set.class));

            Map<Integer, List<CompiledExceptionRule>> exceptionsForKey = result.getNewEventExceptionCache().get(apiKey);
            assertThat(exceptionsForKey).isNotNull();
            List<CompiledExceptionRule> rules = exceptionsForKey.get(eventId);
            assertThat(rules).hasSize(1);
            CompiledExceptionRule rule = rules.get(0);
            assertThat(rule.getErrorName()).isEqualTo(DEFAULT_RULE_ERROR_NAME);
            assertThat(rule.getErrorType()).isEqualTo(DEFAULT_RULE_ERROR_TYPE);
            assertThat(rule.getPriority()).isEqualTo(DEFAULT_RULE_PRIORITY);
            assertThat(rule.getRootCondition()).isSameAs(mockDefaultCondition);

            // --- MODIFIED Assertion ---
            assertThat(result.getRequiredFields()).containsExactly(DEFAULT_RULE_CONDITION_FIELD);
        }

        @Test
        @DisplayName("当 exceptionRules 为空JSON时，应注入默认规则")
        void processConfigs_injectsDefaultRule_whenExceptionRulesEmptyJson() throws Exception {
            // Arrange
            String emptyJson = "[]";
            BizEvent event = createBizEvent(eventId, apiKey, simpleBizReqsJson, emptyJson);
            ConfigSourceData sourceData = new ConfigSourceData(Collections.singletonList(event), Collections.emptyList());
            setupObjectMapperForExceptions(emptyJson, Collections.emptyList());

            // Act
            ProcessedConfigData result = configProcessor.processConfigs(sourceData);

            // Assert
            verify(conditionCompiler, times(1)).compileConditions(argThat(isDefaultConditionRaw()), any(Set.class));
            verify(conditionCompiler, never()).compileConditions(argThat(isUserConditionRaw()), any(Set.class));

            Map<Integer, List<CompiledExceptionRule>> exceptionsForKey = result.getNewEventExceptionCache().get(apiKey);
            assertThat(exceptionsForKey).isNotNull();
            List<CompiledExceptionRule> rules = exceptionsForKey.get(eventId);
            assertThat(rules).hasSize(1);
            CompiledExceptionRule rule = rules.get(0);
            assertThat(rule.getErrorName()).isEqualTo(DEFAULT_RULE_ERROR_NAME);
            assertThat(rule.getErrorType()).isEqualTo(DEFAULT_RULE_ERROR_TYPE);
            assertThat(rule.getPriority()).isEqualTo(DEFAULT_RULE_PRIORITY);
            assertThat(rule.getRootCondition()).isSameAs(mockDefaultCondition);

            // --- MODIFIED Assertion ---
            assertThat(result.getRequiredFields()).containsExactly(DEFAULT_RULE_CONDITION_FIELD);
        }


        @Test
        @DisplayName("当有用户规则但无默认签名规则时，应注入默认规则")
        void processConfigs_injectsDefaultRule_whenUserRulesExistButNotDefaultSignature() throws Exception {
            // Arrange
            String userFieldName = "fieldA";
            String userRulesJson = "[{\"errorType\":\"biz\",\"errorName\":\"USER_ERROR\",\"priority\":10,\"conditions\":[{\"left\":\"" + userFieldName + "\",\"operator\":\"=\",\"right\":\"valueA\"}]}]";
            List<Map<String, Object>> userRulesParsed = Collections.singletonList(
                    new HashMap<String, Object>() {{
                        put("errorType", "biz");
                        put("errorName", "USER_ERROR");
                        put("priority", 10);
                        put("conditions", Collections.singletonList(new HashMap<String, Object>() {{ put("left", userFieldName); put("operator", "="); put("right", "valueA"); }}));
                    }}
            );
            BizEvent event = createBizEvent(eventId, apiKey, simpleBizReqsJson, userRulesJson);
            ConfigSourceData sourceData = new ConfigSourceData(Collections.singletonList(event), Collections.emptyList());
            setupObjectMapperForExceptions(userRulesJson, userRulesParsed);

            // Act
            ProcessedConfigData result = configProcessor.processConfigs(sourceData);

            // Assert
            verify(conditionCompiler, times(1)).compileConditions(argThat(isUserConditionRaw()), any(Set.class));
            verify(conditionCompiler, times(1)).compileConditions(argThat(isDefaultConditionRaw()), any(Set.class));

            Map<Integer, List<CompiledExceptionRule>> exceptionsForKey = result.getNewEventExceptionCache().get(apiKey);
            assertThat(exceptionsForKey).isNotNull();
            List<CompiledExceptionRule> rules = exceptionsForKey.get(eventId);
            assertThat(rules).hasSize(2);

            Optional<CompiledExceptionRule> defaultRuleOpt = rules.stream().filter(r -> r.getPriority() == DEFAULT_RULE_PRIORITY).findFirst();
            assertThat(defaultRuleOpt).isPresent();
            CompiledExceptionRule defaultRule = defaultRuleOpt.get();
            assertThat(defaultRule.getErrorName()).isEqualTo(DEFAULT_RULE_ERROR_NAME);
            assertThat(defaultRule.getErrorType()).isEqualTo(DEFAULT_RULE_ERROR_TYPE);
            assertThat(defaultRule.getRootCondition()).isSameAs(mockDefaultCondition);

            Optional<CompiledExceptionRule> userRuleOpt = rules.stream().filter(r -> r.getPriority() == 10).findFirst();
            assertThat(userRuleOpt).isPresent();
            assertThat(userRuleOpt.get().getErrorName()).isEqualTo("USER_ERROR");
            assertThat(userRuleOpt.get().getRootCondition()).isSameAs(mockUserCondition);

            // --- MODIFIED Assertion ---
            assertThat(result.getRequiredFields()).containsExactlyInAnyOrder(DEFAULT_RULE_CONDITION_FIELD, userFieldName);
        }

        @Test
        @DisplayName("当用户规则包含默认签名规则时，不应注入默认规则")
        void processConfigs_doesNotInjectDefaultRule_whenUserRuleHasDefaultSignature() throws Exception {
            // Arrange
            String userFieldName = "fieldB";
            String userRulesJson = "[{\"errorType\":\"system\",\"errorName\":\"请求失败\",\"priority\":5,\"conditions\":[{\"left\":\"" + userFieldName + "\",\"operator\":\">\",\"right\":100}]}]";
            List<Map<String, Object>> userRulesParsed = Collections.singletonList(
                    new HashMap<String, Object>() {{
                        put("errorType", DEFAULT_RULE_ERROR_TYPE);
                        put("errorName", DEFAULT_RULE_ERROR_NAME);
                        put("priority", 5);
                        put("conditions", Collections.singletonList(new HashMap<String, Object>() {{ put("left", userFieldName); put("operator", ">"); put("right", 100); }}));
                    }}
            );
            BizEvent event = createBizEvent(eventId, apiKey, simpleBizReqsJson, userRulesJson);
            ConfigSourceData sourceData = new ConfigSourceData(Collections.singletonList(event), Collections.emptyList());
            setupObjectMapperForExceptions(userRulesJson, userRulesParsed);

            // Act
            ProcessedConfigData result = configProcessor.processConfigs(sourceData);

            // Assert
            verify(conditionCompiler, times(1)).compileConditions(argThat(isUserConditionRaw()), any(Set.class));
            verify(conditionCompiler, never()).compileConditions(argThat(isDefaultConditionRaw()), any(Set.class));

            Map<Integer, List<CompiledExceptionRule>> exceptionsForKey = result.getNewEventExceptionCache().get(apiKey);
            assertThat(exceptionsForKey).isNotNull();
            List<CompiledExceptionRule> rules = exceptionsForKey.get(eventId);
            assertThat(rules).hasSize(1);

            CompiledExceptionRule userRule = rules.get(0);
            assertThat(userRule.getErrorName()).isEqualTo(DEFAULT_RULE_ERROR_NAME);
            assertThat(userRule.getErrorType()).isEqualTo(DEFAULT_RULE_ERROR_TYPE);
            assertThat(userRule.getPriority()).isEqualTo(5);
            assertThat(userRule.getRootCondition()).isSameAs(mockUserCondition);

            // --- MODIFIED Assertion ---
            assertThat(result.getRequiredFields()).containsExactly(userFieldName);
        }

        @Test
        @DisplayName("当 exceptionRules JSON 解析失败时，仍应尝试注入默认规则")
        void processConfigs_injectsDefaultRule_whenJsonParsingFails() throws Exception {
            // Arrange
            String invalidJson = "[{\"errorType\":\"system\"";
            BizEvent event = createBizEvent(eventId, apiKey, simpleBizReqsJson, invalidJson);
            ConfigSourceData sourceData = new ConfigSourceData(Collections.singletonList(event), Collections.emptyList());
            setupObjectMapperForExceptionsFailure(invalidJson);

            // Act
            ProcessedConfigData result = configProcessor.processConfigs(sourceData);

            // Assert
            verify(conditionCompiler, times(1)).compileConditions(argThat(isDefaultConditionRaw()), any(Set.class));
            verify(conditionCompiler, never()).compileConditions(argThat(isUserConditionRaw()), any(Set.class));

            Map<Integer, List<CompiledExceptionRule>> exceptionsForKey = result.getNewEventExceptionCache().get(apiKey);
            assertThat(exceptionsForKey).isNotNull();
            List<CompiledExceptionRule> rules = exceptionsForKey.get(eventId);
            assertThat(rules).hasSize(1);
            CompiledExceptionRule rule = rules.get(0);
            assertThat(rule.getErrorName()).isEqualTo(DEFAULT_RULE_ERROR_NAME);
            assertThat(rule.getErrorType()).isEqualTo(DEFAULT_RULE_ERROR_TYPE);
            assertThat(rule.getPriority()).isEqualTo(DEFAULT_RULE_PRIORITY);
            assertThat(rule.getRootCondition()).isSameAs(mockDefaultCondition);

            // --- MODIFIED Assertion ---
            assertThat(result.getRequiredFields()).containsExactly(DEFAULT_RULE_CONDITION_FIELD);
        }

        @Test
        @DisplayName("当用户规则条件编译失败时，跳过该规则，但仍注入默认规则")
        void processConfigs_injectsDefaultRule_whenUserConditionCompileFails() throws Exception {
            // Arrange
            String userFieldName = "fieldC";
            String userRulesJson = "[{\"errorType\":\"biz\",\"errorName\":\"FAIL_COMPILE\",\"priority\":10,\"conditions\":[{\"left\":\"" + userFieldName + "\",\"operator\":\"?\",\"right\":\"fail\"}]}]";
            List<Map<String, Object>> userRulesParsed = Collections.singletonList(
                    new HashMap<String, Object>() {{
                        put("errorType", "biz");
                        put("errorName", "FAIL_COMPILE");
                        put("priority", 10);
                        put("conditions", Collections.singletonList(new HashMap<String, Object>() {{ put("left", userFieldName); put("operator", "?"); put("right", "fail"); }})); // User condition object
                    }}
            );
            BizEvent event = createBizEvent(eventId, apiKey, simpleBizReqsJson, userRulesJson);
            ConfigSourceData sourceData = new ConfigSourceData(Collections.singletonList(event), Collections.emptyList());
            setupObjectMapperForExceptions(userRulesJson, userRulesParsed);

            // --- MODIFIED Mock: User condition compile fails (returns null), Default compiles ok ---
            // Override the lenient mock from BeforeEach specifically for this test
            when(conditionCompiler.compileConditions(argThat(isUserConditionRaw()), any(Set.class))).thenReturn(null);
            // Default condition still compiles fine (using the lenient mock from BeforeEach)
            // We don't need to redefine the default condition mock here unless we want a different behavior for it

            // Act
            ProcessedConfigData result = configProcessor.processConfigs(sourceData);

            // Assert
            // Verify both compile attempts happened
            verify(conditionCompiler, times(1)).compileConditions(argThat(isUserConditionRaw()), any(Set.class));
            verify(conditionCompiler, times(1)).compileConditions(argThat(isDefaultConditionRaw()), any(Set.class));

            Map<Integer, List<CompiledExceptionRule>> exceptionsForKey = result.getNewEventExceptionCache().get(apiKey);
            assertThat(exceptionsForKey).isNotNull();
            List<CompiledExceptionRule> rules = exceptionsForKey.get(eventId);
            assertThat(rules).hasSize(1); // Only the default rule, user rule was skipped

            CompiledExceptionRule rule = rules.get(0);
            assertThat(rule.getErrorName()).isEqualTo(DEFAULT_RULE_ERROR_NAME);
            assertThat(rule.getErrorType()).isEqualTo(DEFAULT_RULE_ERROR_TYPE);
            assertThat(rule.getRootCondition()).isSameAs(mockDefaultCondition);

            // --- MODIFIED Assertion ---
            // The requiredFieldsCollector for the user rule was passed to compileConditions,
            // but since it returned null, the ConfigProcessorImpl likely didn't add any fields from it.
            // However, the default rule compilation *did* add "error".
            assertThat(result.getRequiredFields()).containsExactly(DEFAULT_RULE_CONDITION_FIELD);
        }

        @Test
        @DisplayName("当默认规则条件编译失败时，不应注入默认规则")
        void processConfigs_doesNotInjectDefaultRule_whenDefaultConditionCompileFails() throws Exception {
            // Arrange
            BizEvent event = createBizEvent(eventId, apiKey, simpleBizReqsJson, null); // No user rules
            ConfigSourceData sourceData = new ConfigSourceData(Collections.singletonList(event), Collections.emptyList());

            // --- MODIFIED Mock: Default condition compile fails ---
            // Override the lenient mock from BeforeEach
            when(conditionCompiler.compileConditions(argThat(isDefaultConditionRaw()), any(Set.class))).thenReturn(null);

            // Act
            ProcessedConfigData result = configProcessor.processConfigs(sourceData);

            // Assert
            verify(conditionCompiler, times(1)).compileConditions(argThat(isDefaultConditionRaw()), any(Set.class));
            verify(conditionCompiler, never()).compileConditions(argThat(isUserConditionRaw()), any(Set.class));

            Map<Integer, List<CompiledExceptionRule>> exceptionsForKey = result.getNewEventExceptionCache().get(apiKey);
            assertThat(exceptionsForKey).isNotNull();
            List<CompiledExceptionRule> rules = exceptionsForKey.get(eventId);
            assertThat(rules).isEmpty(); // No rules should be present

            // --- MODIFIED Assertion ---
            // Since default rule compile failed, "error" should not be added
            assertThat(result.getRequiredFields()).isEmpty();
        }
    }
}