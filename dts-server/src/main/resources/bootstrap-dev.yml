spring:
  application:
    name: DTS
  cloud:
    zookeeper:
      connection-timeout: 30000  # Zookeeper 客户端的连接超时时间，单位是毫秒。
      session-timeout: 120000  # Zookeeper 客户端的会话超时时间，单位是毫秒。
      config:
        enabled: true
        root: databuff
        profileSeparator: ','
        paths:
          - /javaagent
          - /dotnetagent
      discovery:
        enabled: true
      connect-string: 192.168.50.193:12181
      base-sleep-time-ms: 1000  # 重试策略的基础睡眠时间
      max-retries: 3  # 重试策略的最大重试次数
      max-sleep-ms: 3000

#javaagent 全局配置
databuff:
  DTS:
    global:
      #慢sql ms
      slow_sql: 500
  javaagent:
    global:
      print_logs: false
      service_sample_rate: 1
      sql_normalized_type: 0
      url_path_normalized_type: 1
      response_header_editable: true
      databuff.slf4j.simpleLogger.defaultLogLevel: info
      databuff:
        slf4j:
          simpleLogger:
            defaultLogLevel: info
  dotnetagent:
    # 是否启用 Trace，默认值为 true
    DF_TRACE_ENABLED: "true"
    # 是否启用日志注入
    DF_LOGS_INJECTION: "true"
    # 全局采样率，示例值为 100%
    DF_TRACE_SAMPLE_RATE: "1.0"
    # 可配置服务名基于正则表达式的采样规则，
    DF_TRACE_SAMPLING_RULES: "[{'sample_rate':1.0}]" # 采样率 100%
    # Header "User-Agent" 映射到 Tag "user.agent"
    DF_TRACE_HEADER_TAGS: "User-Agent:dotnet.agent"
    # 服务名称映射 #cite： 将 mysql 服务映射为 main-mysql-db
    DF_TRACE_SERVICE_MAPPING: "test:net-test,test1:net-test1"
    # 全局 Tags 配置
    DF_TAGS: "databuff:dotnet-trace" # 示例全局 Tag Key1:Value1