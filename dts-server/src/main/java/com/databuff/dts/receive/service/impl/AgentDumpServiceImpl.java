package com.databuff.dts.receive.service.impl;

import com.databuff.dao.mysql.AgentDumpMapper;
import com.databuff.dts.receive.service.AgentDumpService;
import com.databuff.dts.util.FileUtil;
import com.databuff.entity.dump.AgentDump;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Date;

import static com.databuff.dts.receive.service.impl.AgentServiceImpl.AGENT_DUMP_PATH;

@Slf4j
@Service
public class AgentDumpServiceImpl implements AgentDumpService {


    @Autowired
    private AgentDumpMapper agentDumpMapper;

    @Override
    public void dumpFileUpload(Long id, MultipartFile zipFile) {
        if (id == null) {
            log.error("id is null");
            return;
        }

        final AgentDump agentDump = agentDumpMapper.selectById(id);
        if (agentDump == null) {
            log.error("agentDump is null");
            return;
        }
        final String apiKey = agentDump.getApiKey();
        final String service = agentDump.getService();
        final String serviceInstance = agentDump.getServiceInstance();
        final String serviceAndInstance = service + File.separator + serviceInstance;
        final String path = AGENT_DUMP_PATH + apiKey + File.separator;

        File upgradePath = new File(path);
        if (!upgradePath.exists()) {
            upgradePath.mkdirs();
        }
        File dumpZipFile = null;
        try {
            log.info("-------------------{}上传agentDump压缩包", serviceAndInstance);
            dumpZipFile = FileUtil.multipartFileToFile(zipFile);
        } catch (IOException e) {
            if (dumpZipFile != null && !dumpZipFile.delete()) {
                log.warn(serviceAndInstance + "上传agentDump压缩包文件删除失败");
            }
            log.error("流转换文件 error:{}", e);
            throw new RuntimeException(serviceAndInstance + "上传agentDump包失败，流转换文件错误");
        }
        //解压安装包到版本文件下面
        //解压后文件的文件夹路径
//        String upPackPath = path + serviceAndInstance + File.separator;
        String targetFileName = String.format("%s_%s_%d.zip", service, serviceInstance, agentDump.getId()).replaceAll("[:\"*?<>|]", "");
        log.info("-------------------上传{}Dump包到目标文件夹", serviceAndInstance);
        File targetFile = new File(path + targetFileName);
        try {
            zipFile.transferTo(targetFile);
//            targetFileName = targetFile.getName();
        } catch (IOException e) {
            log.error("上传文件 error:{}", e);
            throw new RuntimeException(serviceAndInstance + "上传Dump包失败");
        }
        File file = new File(path + targetFileName);
        if (file.exists()) {
            final Date nowTime = new Date();
            agentDump.setFileName(targetFileName);
            agentDump.setFileSize(file.length());
            agentDump.setPath(path + targetFileName);
            agentDump.setUploadTime(nowTime);
            agentDumpMapper.updateByIdSelective(agentDump);
        }
    }
}