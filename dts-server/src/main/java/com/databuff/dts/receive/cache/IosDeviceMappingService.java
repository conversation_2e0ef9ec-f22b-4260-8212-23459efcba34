package com.databuff.dts.receive.cache;

import com.databuff.dao.mysql.IosDeviceMappingMapper;
import com.databuff.entity.rum.mysql.IosDeviceMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
@Slf4j
public class IosDeviceMappingService {
    private Map<String, String> deviceMapping = new ConcurrentHashMap<>();

    @Autowired
    private IosDeviceMappingMapper iosDeviceMappingMapper;

    @PostConstruct
    @Scheduled(fixedRate = 60 * 1000) // Every minute
    public void loadMappings() {
        try {
            List<IosDeviceMapping> mappings = iosDeviceMappingMapper.getAllDeviceMappings();
            Map<String, String> newMapping = mappings.stream()
                    .collect(Collectors.toMap(
                            IosDeviceMapping::getProbeValue,
                            IosDeviceMapping::getDeviceModel,
                            (v1, v2) -> v1,
                            ConcurrentHashMap::new
                    ));
            deviceMapping = newMapping;
            log.debug("Refreshed {} iOS device mappings", mappings.size());
        } catch (Exception e) {
            log.error("Failed to load iOS device mappings", e);
        }
    }

    public String mapDeviceIdentifier(String probeValue) {
        // 找不到映射 就直接使用原本的字段 ,ex:测试机是 arm64
        return deviceMapping.getOrDefault(probeValue, probeValue);
    }
}
