package com.databuff.dts.receive.processor.out;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.constants.Constant;
import com.databuff.common.utils.RedisKeyUtil;
import com.databuff.common.utils.ServiceUtil;
import com.databuff.dts.config.RefreshScopeConfig;
import com.databuff.dts.receive.model.SlowConfig;
import com.databuff.dts.receive.service.ComponentInstanceService;
import com.databuff.entity.TraceServiceEntity;
import com.databuff.service.JedisService;
import com.databuff.service.ServiceSyncService;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.apache.commons.lang3.StringUtils;

import static com.databuff.common.constants.Constant.AGENT_HOSTNAME2;
import static com.databuff.common.constants.Constant.Trace.*;
import static com.databuff.common.constants.Constant.VIRTUAL;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.concurrent.TimeUnit;

public class HttpRpcClientOutProcessor extends OutProcessor {


    /**
     * 用于数据源为Databuff的调用地址与服务端服务名的缓存。
     * 该缓存是使用 Guava 的 CacheBuilder 构建的，配置如下：
     * - 最大大小：缓存可以容纳的最大条目数为 20000。一旦达到此大小，缓存将开始根据其逐出策略逐出条目。
     * - 写入后过期：缓存中的每个条目将在 24 小时写后过期并被删除。
     */
    private final Cache<String, String> cacheAddrServerSvcs = CacheBuilder.newBuilder()
            .maximumSize(20000)
            .expireAfterWrite(24, TimeUnit.HOURS)
            .softValues()
            .recordStats()
            .build();

    /**
     * 用于调用地址与服务端服务名的缓存。
     * 该缓存是使用 Guava 的 CacheBuilder 构建的，配置如下：
     * - 最大大小：缓存可以容纳的最大条目数为 20000。一旦达到此大小，缓存将开始根据其逐出策略逐出条目。
     * - 写入后过期：缓存中的每个条目将在 24 小时写后过期并被删除。
     */
    private final Cache<String, String> cacheRemoteSvcs = CacheBuilder.newBuilder()
            .maximumSize(20000)
            .expireAfterWrite(24, TimeUnit.HOURS)
            .softValues()
            .recordStats()
            .build();

    private JedisService jedisService;
    public HttpRpcClientOutProcessor(ServiceSyncService serviceSyncService, ComponentInstanceService componentInstanceService, JedisService jedisService, RefreshScopeConfig refreshScopeConfig) {
        super(serviceSyncService, componentInstanceService, jedisService, refreshScopeConfig);
        this.jedisService = jedisService;
    }

    @Override
    public boolean match(String name) {
        return TRACE_OUT_NAMES_HTTP.contains(name) || TRACE_OUT_NAMES_RPC.contains(name);
    }

    @Override
    public boolean process(JSONObject s, String name, String k8sClusterId) {
        cacheFillServerSvc(s, name);
        JSONObject meta = s.getJSONObject(META);
        String serverService = meta.getString(TAG_SERVER_SERVICE);
        String serverIp = meta.getString(TAG_SERVER_IP);
        fillSlow(s);
        if (StringUtils.isNotEmpty(serverService)) {
            String apiKey = s.getString(Constant.API_KEY2);
            initServerService(s, apiKey, serverService, serverIp);
        }else{
            if (externalRemoteCalls(s,name)){
                return true ;
            }
        }
        return initGenerateService(s);
    }

    /**
     * databuff数据源为了避免服务链接中断，找到不到下游服务,这里做一层缓存，在中断时异常做个补充
     * @param s
     * @param name
     */
    private void cacheFillServerSvc(JSONObject s,String name) {
        String key = null;
        JSONObject meta = s.getJSONObject(META);
        String dataSource = meta.getString(DATA_SOURCE);
        String serverService = meta.getString(TAG_SERVER_SERVICE);
        if (TRACE_OUT_NAMES_HTTP.contains(name)){
            //http调用 使用peer.hostname+peer.port作为调用地址
            String peerHostname = meta.getString(PEER_HOSTNAME);
            if (StringUtils.isEmpty(peerHostname)||"127.0.0.1".equals(peerHostname)){
                return;
            }
            String peerPort = meta.getString(PEER_PORT);
            key = dataSource+":"+peerHostname + ":" + peerPort;

        }else if (TRACE_OUT_NAMES_RPC.contains(name)){
            //rpc调用 直接使用resource作为调用地址
            key = dataSource+":"+s.getString(RESOURCE);
        }
        if (key == null){
            return;
        }
        if (StringUtils.isNotEmpty(serverService)) {
            //服务端服务不为空 缓存最新信息
            cacheAddrServerSvcs.put(key, serverService);
            return;
        }
        //额外增加一个补充条件，只有当前span错误的情况需要填充
        if (s.getIntValue("error") == 0){
            return;
        }
        //服务端服务为空 从缓存中获取
        String serverSvc = cacheAddrServerSvcs.getIfPresent(key);
        if (StringUtils.isNotEmpty(serverSvc)){
            meta.put(TAG_SERVER_SERVICE,serverSvc);
            String peerHostname = meta.getString(PEER_HOSTNAME);
            if (TRACE_NAME_OUT_DUBBO_CALL.equals(name) && StringUtils.isNotEmpty(peerHostname)){
                //如果是dubbo调用 可以取peer.hostname填充serverIp
                meta.put(TAG_SERVER_IP,peerHostname);
            }
        }
    }

    /**
     * databuff的上面已经通过serverService判断下游服务是否已经监测，而第三方（otel，sky）需要通过缓存判断，由flink通过span关联与否缓存是否存在服务
     * 标记处理外部远程调用
     */
    private boolean externalRemoteCalls(JSONObject s,String name){
        JSONObject meta = s.getJSONObject(META);
        //这里需要
        String[] remoteServiceArr = ServiceUtil.getRemotelySvc(meta, name);
        if (remoteServiceArr==null){
            //如果peer.hostname和peer.port 为空
            return false;
        }
        String dataSource = meta.getString(DATA_SOURCE);

        String remoteService = remoteServiceArr[0] + "-" + remoteServiceArr[1] + ":" + remoteServiceArr[2];
        String type = remoteServiceArr[0].toLowerCase();
        if (StringUtils.isNotBlank(dataSource) && !dataSource.equals(TRACE_DATA_SOURCE_DATABUFF)){
            //如果数据源不是databuff，去缓存中拿一下
            String remoteAddress = dataSource+":"+remoteService;
            String cacheKey = RedisKeyUtil.generateRemotelySvc(s.getString(Constant.API_KEY2), remoteAddress);
            //先从本地缓存中获取
            String svc = cacheRemoteSvcs.getIfPresent(cacheKey);
            if (StringUtils.isBlank(svc)){
                //如果缓存中为空，从redis在获取一次
                String cacheValue = jedisService.getJson(cacheKey);
                if (StringUtils.isNotBlank(cacheValue)) {
                    //如果缓存中存在值且不为“”，代表已经关联上下游服务了，直接返回；
                    cacheRemoteSvcs.put(cacheKey, remoteService);
                    return false;
                }
                if (cacheValue == null) {
                    //如果为null代表第一次过来，没有通过flink上下游关联无法判断，也先跳过
                    return false;
                }
            }else{
                //如果本地缓存中存在值，直接返回
                return false;
            }
        }
        String peerHostname = remoteServiceArr[1];
        //先把当前服务设置span的客户端服务
        String generateService = meta.getString(GENERATE_SERVICE);
        String generateIp = meta.getString(GENERATE_IP);
        if (generateService == null) {
            generateService = s.getString(SERVICE);
        }
        if (generateIp == null) {
            generateIp = s.getString(AGENT_HOSTNAME2);
        }
        //添加远程调用服务 也算到入口isIn=1，不然无法统计到service指标
        s.put(IS_IN, 1);

        String apiKey = s.getString(Constant.API_KEY2);
        initClientService(s,apiKey, generateService, generateIp);
        //将远程调用服务设置为当前span服务
        initComponentService(s, meta, remoteService, peerHostname, SERVICE_TYPE_REMOTE, type);
        //远程调用服务，不算虚拟服务后面不需要丢弃
        s.put(VIRTUAL, false);
        return true;
    }

    private void fillSlow(JSONObject s) {
        String name = s.getString(NAME);
        long duration = s.getLong(DURATION);
        if (null == name) {
            return;
        }
        if (TRACE_OUT_NAMES_HTTP.contains(name)) {
            s.put("slowTime", refreshScopeConfig.getSlowHttp());
            if (duration > refreshScopeConfig.getSlowHttp() * 1000 * 1000) {
                s.put("slow", 1);
            } else {
                s.put("slow", 0);
            }
        } else if (TRACE_OUT_NAMES_RPC.contains(name)) {
            s.put("slowTime", refreshScopeConfig.getSlowRpc());
            if (duration > refreshScopeConfig.getSlowRpc() * 1000 * 1000) {
                s.put("slow", 1);
            } else {
                s.put("slow", 0);
            }
        }
    }

}
