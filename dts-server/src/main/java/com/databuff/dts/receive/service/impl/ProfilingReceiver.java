package com.databuff.dts.receive.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.databuff.common.constants.Constant;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.common.utils.ServiceUtil;
import com.databuff.dts.config.RefreshScopeConfig;
import com.databuff.dts.receive.service.AbstractReceiver;
import com.databuff.dts.receive.service.SpanFieldRegexReplaceService;
import com.databuff.dts.ringbuffer.ParseTrace;
import com.databuff.entity.TraceServiceEntity;
import com.databuff.entity.profiling.v3.*;
import com.databuff.kafka.KafkaSender;
import com.databuff.service.ServiceSyncService;
import com.databuff.sink.OlapTableWriter;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.EnumerablePropertySource;
import org.springframework.core.env.Environment;
import org.springframework.core.env.PropertySource;
import org.springframework.core.env.StandardEnvironment;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.databuff.common.constants.Constant.Trace.SERVICE;
import static com.databuff.common.constants.MetricName.*;

/**
 * @author:yuzhili
 * @date: 2024/4/28
 * @time: 11:34
 */
@Component("ProfilingReceiver")
@Slf4j
public class ProfilingReceiver extends AbstractReceiver {
    public static final String SAMPLE_INTERVAL = "sampleInterval";
    public static final String ENABLED = "enabled";
    public static final String MODE = "mode";
    public static final String PROFILING = "profiling";
    public static final String VALUE = "value";
    // 缓存stack数据。
    private final Cache<String, ProfilingStack> profilingStackCache = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.DAYS)
            .maximumSize(99999)
            .build();


    @Autowired
    private Environment environment;

    @Resource
    private ServiceSyncService serviceSyncService;

    @Autowired
    private RefreshScopeConfig refreshScopeConfig;

    @Autowired
    private SpanFieldRegexReplaceService spanFieldRegexReplaceService;

    /**
     * 从环境中检索与指定属性源名称匹配的所有属性。
     * 将属性解析为JSON对象，并在映射中返回它们。
     *
     * @返回一个映射，其中属性名作为键，对应的JSON对象作为值
     */
    public Map<String, JSONObject> getAllProperties() {
        Map<String, JSONObject> properties = new HashMap<>();
        if (environment instanceof StandardEnvironment) {
            StandardEnvironment standardEnvironment = (StandardEnvironment) environment;
            for (PropertySource<?> propertySource : standardEnvironment.getPropertySources()) {
                switch (propertySource.getName()) {
                    case "bootstrapProperties-databuff/javaagent":
                        if (!(propertySource instanceof EnumerablePropertySource)) {
                            log.warn("Invalid PropertySource: {}", propertySource);
                            break;
                        }
                        final String[] propertyNames = ((EnumerablePropertySource) propertySource).getPropertyNames();
                        for (String propertyName : propertyNames) {
                            final Object property = propertySource.getProperty(propertyName);
                            if (!(property instanceof String)) {
                                continue;
                            }
                            try {
                                if (property != null && ((String) property).trim().startsWith("{") && ((String) property).trim().endsWith("}")) {
                                    JSONObject jsonObject = JSONObject.parseObject((String) property);
                                    properties.put(propertyName, jsonObject);
                                } else {
                                    log.warn("Invalid JSON string: {}", property);
                                }
                            } catch (Exception e) {
                                log.error("Failed to parse JSON string: {}", property, e);
                            }

                        }
                        break;
                }
            }
        }
        return properties;
    }


    /**
     * 每分钟在第0秒上传指标的计划任务。
     * 此方法检索所有属性，处理它们，并记录分析CPU配置数据。
     */
    @Scheduled(cron = "0 * * * * ?")
    public void uploadMetric() {
        final Map<String, JSONObject> allProperties = getAllProperties();
        for (Map.Entry<String, JSONObject> entry : allProperties.entrySet()) {
            final String service = entry.getKey();
            final JSONObject config = entry.getValue();
            if (config == null) {
                continue;
            }
            final JSONObject value = config.getJSONObject(VALUE);
            if (value == null) {
                continue;
            }
            final JSONObject prof = value.getJSONObject(PROFILING);
            if (prof == null) {
                continue;
            }

            Map<String, Number> fields = new HashMap<>();
            fields.put(SAMPLE_INTERVAL, prof.getDoubleValue(SAMPLE_INTERVAL));

            Map<String, String> tags = new HashMap<>();
            tags.put(SERVICE, service);
            tags.put(ENABLED, String.valueOf(prof.getBooleanValue(ENABLED)));
            tags.put(MODE, prof.getString(MODE));
            OtelMetricUtil.logOriginalData(PROFILING_CPU_CONF, fields, tags, System.currentTimeMillis());
        }
    }

    @Autowired
    private OlapTableWriter olapTableWriter;

    @Autowired
    private KafkaSender kafkaSender;

    /**
     * 解析并处理Profiling请求数据
     * 这是Profiling数据处理的核心方法，负责接收JavaAgent上报的性能分析数据，
     * 进行数据转换、缓存去重、分发到Kafka等处理流程
     *
     * @param event 包含Profiling数据的事件对象，由RingBuffer传递
     * @throws IOException 数据处理过程中可能抛出的IO异常
     */
    @Override
    public void parseRequest(ParseTrace event) throws IOException {
        // 记录处理开始时间，用于性能监控统计
        final long currentTimeMillis = System.currentTimeMillis();
        // 记录Profiling数据接收计数器，用于监控数据接收量
        OtelMetricUtil.logCounter(PROFILING_CPU_SINK_CNT, 1L);

        // 从事件中获取Profiling原始JSON数据
        JSONObject profiling = (JSONObject) event.getData();
        if (profiling == null) {
            return;
        }

        // 根据配置决定是否将原格式数据发送到Kafka进行备份
        // 默认不发送，主要用于调试和数据备份场景
        if (refreshScopeConfig.getSendToKafka()) {
            kafkaSender.data2Kafka(profiling, Constant.Kafka.PROFILING_ORIGIN_TOPIC);
        }

        // 将原始JSON数据转换为结构化的SampleDataV3对象
        // 这一步会解析JSON中的各个字段，构建标准化的数据结构
        final SampleDataV3 sampleData = JsonToSampleListConverter.convert(profiling);
        if (sampleData == null) {
            return;
        }

        // 获取线程堆栈样本列表，这是Profiling数据的核心内容
        final List<ThreadStack> samples = sampleData.getSamples();
        if (CollectionUtils.isEmpty(samples)) {
            return;
        }

        // 提取基础信息：服务名、服务实例、API密钥
        final String service = sampleData.getService();
        final String serviceInstance = sampleData.getServiceInstance();
        final String apiKey = sampleData.getApiKey();

        // 获取或生成服务ID
        // 优先从服务同步缓存中获取，如果没有则生成新的服务ID
        String serviceId = "";
        if (StringUtils.isNotBlank(service)) {
            TraceServiceEntity traceServiceEntity = serviceSyncService.getTraceServiceEntity(apiKey, service);
            if (traceServiceEntity != null) {
                serviceId = traceServiceEntity.getId();
            } else {
                // 如果缓存中没有，则根据apiKey和service生成唯一的服务ID
                serviceId = ServiceUtil.generateServiceId(apiKey, service);
            }
        }
        sampleData.setServiceId(serviceId);

        // 标准化samples中的resource字段
        // 对资源名称进行正则替换、截取和标准化处理，确保数据的一致性
        for (int i = 0; i < samples.size(); i++) {
            final ThreadStack threadStack = samples.get(i);
            String resource = threadStack.getResource();
            // 使用正则表达式替换资源名称中的特殊字符和模式
            resource = spanFieldRegexReplaceService.replaceMetricResource(resource);
            // 截取资源名称到指定长度，避免过长的资源名称
            resource = spanFieldRegexReplaceService.substringResource(null, resource, null);
            // 根据资源名称进行进一步的标准化处理
            resource = spanFieldRegexReplaceService.processResourceByName(null,  resource, null);
            threadStack.setResource(resource);
        }

        // 将SampleDataV3转换为不同类型的Profiling实体对象
        // 这些转换器会根据不同的业务需求生成相应的数据结构
        final Set<ProfilingStack> profilingStackList = SampleDataV3Converter.toProfilingStackList(sampleData);
        final Set<ProfilingHotspot> profilingHotspotList = SampleDataV3Converter.toProfilingHotspotList(sampleData);

        // 处理热点数据：将热点信息发送到Kafka
        // 热点数据用于标识性能瓶颈点，帮助快速定位问题
        if (CollectionUtils.isNotEmpty(profilingHotspotList)) {
            for (ProfilingHotspot hotspot : profilingHotspotList) {
                // 注释：从2.8.4版本开始，改为先发送到Kafka，由task-executor消费后写入StarRocks
                // 这样做的好处是：1.解耦数据接收和存储 2.提高系统吞吐量 3.增强系统稳定性
                final String data = JSONObject.toJSONString(hotspot, SerializerFeature.DisableCircularReferenceDetect);
                final String excerptId = hotspot.getExcerptId();
                // 使用excerptId作为Kafka分区键，确保相同热点的数据发送到同一分区
                kafkaSender.data2Kafka(data, Constant.Kafka.PROFILING_HOTSPOT_TOPIC, excerptId);
            }
        }

        // 初始化缓存未命中计数器和获取当前缓存大小
        int missedSize = 0;
        final long estimatedSize = profilingStackCache.estimatedSize();

        // 处理堆栈数据：实现缓存去重机制
        // 这是一个重要的性能优化，避免重复写入相同的堆栈数据
        if (CollectionUtils.isNotEmpty(profilingStackList)) {
            // 过滤出缓存中不存在的数据（缓存未命中的数据）
            // 使用Stream API进行高效的数据过滤和去重处理
            Map<String, ProfilingStack> missed = profilingStackList.stream()
                    .filter(entry -> profilingStackCache.getIfPresent(entry.getExcerptId()) == null)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(
                            i -> i.getExcerptId(),  // 使用excerptId作为Map的key
                            v -> v,                 // 使用ProfilingStack对象作为value
                            (existing, replacement) -> replacement  // 如果key重复，使用新值替换旧值
                    ));

            // 处理缓存未命中的数据：发送到Kafka进行持久化
            if (CollectionUtils.isNotEmpty(missed.values())) {
                missedSize = missed.size();
                for (ProfilingStack stack : missed.values()) {
                    if (stack == null) {
                        continue;
                    }

                    // 注释：从2.8.4版本开始的架构变更说明
                    // 旧方式：直接写入StarRocks数据库（已注释的代码）
                    // 新方式：先发送到Kafka，由task-executor异步消费写入StarRocks
                    // 优势：1.提高响应速度 2.增强系统容错能力 3.支持数据重试机制
                    final String data = JSONObject.toJSONString(stack, SerializerFeature.DisableCircularReferenceDetect);
                    kafkaSender.data2Kafka(data, Constant.Kafka.PROFILING_STACK_TOPIC, stack.getExcerptId());
                }
            }
            // 将已处理的数据加入缓存，避免下次重复处理
            // 这里使用putAll批量操作，提高缓存更新效率
            profilingStackCache.putAll(missed);
        }

        // 标准化sampleBases中的resource字段
        // 对基础堆栈数据的资源名称进行同样的标准化处理
        final List<ThreadStackBase> sampleBases = sampleData.getSampleBases();
        if (CollectionUtils.isNotEmpty(sampleBases)) {
            for (int i = 0; i < sampleBases.size(); i++) {
                final ThreadStackBase threadStackBase = sampleBases.get(i);
                String resource = threadStackBase.getResource();
                // 应用与samples相同的资源名称标准化规则
                resource = spanFieldRegexReplaceService.replaceMetricResource(resource);
                resource = spanFieldRegexReplaceService.substringResource(null, resource, null);
                resource = spanFieldRegexReplaceService.processResourceByName(null, resource, null);
                threadStackBase.setResource(resource);
            }
        }

        // 转换并发送堆栈基础数据到Kafka
        // 堆栈基础数据包含完整的堆栈跟踪信息，用于后续的火焰图生成
        final Set<ProfilingStackBase> profilingStackBaseList = SampleDataV3Converter.toProfilingStackBaseList(sampleData);
        if (CollectionUtils.isNotEmpty(profilingStackBaseList)) {
            for (ProfilingStackBase stack : profilingStackBaseList) {
                if (stack == null) {
                    continue;
                }
                // 序列化堆栈基础数据并发送到专用的Kafka Topic
                final String data = JSONObject.toJSONString(stack, SerializerFeature.DisableCircularReferenceDetect);
                kafkaSender.data2Kafka(data, Constant.Kafka.PROFILING_STACK_BASE_TOPIC, stack.getExcerptId());
            }
        }

        // 记录性能统计数据
        recordPerformanceMetrics(currentTimeMillis, sampleData, samples, profilingHotspotList,
                               profilingStackList, estimatedSize, missedSize, service,
                               serviceInstance, apiKey);
    }

    /**
     * 记录Profiling处理的性能统计数据
     * 收集并发送各种性能指标到自监控系统，用于监控系统性能和排查问题
     *
     * @param startTime 处理开始时间戳
     * @param sampleData 样本数据对象
     * @param samples 线程堆栈样本列表
     * @param profilingHotspotList 热点数据列表
     * @param profilingStackList 堆栈数据列表
     * @param estimatedSize 缓存估计大小
     * @param missedSize 缓存未命中数量
     * @param service 服务名称
     * @param serviceInstance 服务实例
     * @param apiKey API密钥
     */
    private void recordPerformanceMetrics(long startTime, SampleDataV3 sampleData,
                                        List<ThreadStack> samples,
                                        Set<ProfilingHotspot> profilingHotspotList,
                                        Set<ProfilingStack> profilingStackList,
                                        long estimatedSize, int missedSize,
                                        String service, String serviceInstance, String apiKey) {

        // 收集并记录Profiling处理的性能统计数据
        // 这些指标用于监控系统性能和排查问题
        Map<String, Number> fields = new HashMap<>();
        fields.put("cnt", 1L);  // 处理次数计数
        fields.put("costMs", System.currentTimeMillis() - startTime);  // 处理耗时（毫秒）
        fields.put("samples", samples.size());  // 样本数量
        fields.put("hotspotSize", profilingHotspotList.size());  // 热点数据数量
        fields.put("stackSize", profilingStackList.size());  // 堆栈数据数量
        fields.put("stackCacheSize", estimatedSize);  // 缓存当前大小
        fields.put("stackCacheAddSize", missedSize);  // 本次新增缓存数量

        // 收集缓存性能统计信息
        final CacheStats stats = profilingStackCache.stats();
        fields.put("stackCacheHitRate", 100 * stats.hitRate());  // 缓存命中率（百分比）
        fields.put("stackCacheLoadFailureRate", 100 * stats.loadFailureRate());  // 缓存加载失败率
        fields.put("stackCacheMissRate", 100 * stats.missRate());  // 缓存未命中率

        // 设置监控标签，用于数据分组和过滤
        Map<String, String> tags = new HashMap<>();
        tags.put("service", service);  // 服务名
        tags.put("host", sampleData.getHost());  // 主机名
        tags.put("serviceInstance", serviceInstance);  // 服务实例
        tags.put("observerTool", sampleData.getObserverTool());  // 观察工具
        tags.put("apiKey", apiKey);  // API密钥

        // 将性能统计数据发送到自监控系统
        OtelMetricUtil.logOriginalData(PROFILING_CPU_SINK, fields, tags, startTime);
    }

}
