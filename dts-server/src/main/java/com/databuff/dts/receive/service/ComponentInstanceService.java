package com.databuff.dts.receive.service;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.exception.ExceptionHandlingRunnable;
import com.databuff.common.utils.ServiceUtil;
import com.databuff.dts.util.TraceUtil;
import com.databuff.entity.ServiceInstanceEntity;
import com.databuff.entity.TraceServiceEntity;
import com.databuff.kafka.KafkaSender;
import com.databuff.util.MetricsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static com.databuff.common.constants.Constant.Metric.*;
import static com.databuff.common.constants.Constant.Trace.*;
import static com.databuff.common.constants.TSDBIndex.TSDB_METRIC_SERVICE_INSTANCE_TABLE_NAME;
import static com.databuff.common.constants.KafkaTopicConstant.METRIC_TOPIC;

@Service
@Slf4j
public class ComponentInstanceService {

    @Autowired
    private KafkaSender kafkaSender;

    private static final Map<String, ServiceInstanceEntity> componentInstanceService = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduled = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r);
        t.setName("Scheduled-ComponentHeartbeat");
        return t;
    });

    @PostConstruct
    public void init() {
        scheduled.scheduleAtFixedRate(new ExceptionHandlingRunnable(() -> sendComponentServiceInstanceHeartbeat()), 0, 1, TimeUnit.MINUTES);
    }

    public void initServiceInstanceEntity(TraceServiceEntity traceServiceEntity, String serviceInstance, Integer port, String hostName) {
        String key = generateKey(traceServiceEntity.getId(), serviceInstance);
        ServiceInstanceEntity serviceInstanceEntity = componentInstanceService.get(key);
        if (serviceInstanceEntity == null) {
            serviceInstanceEntity = new ServiceInstanceEntity();
            serviceInstanceEntity.setTraceServiceEntity(traceServiceEntity);
            serviceInstanceEntity.setServiceInstance(serviceInstance);
            serviceInstanceEntity.setHostName(hostName);
            Set<Integer> ports = new HashSet<>(1);
            if (port != null) {
                ports.add(port);
            }
            serviceInstanceEntity.setPorts(ports);
            componentInstanceService.putIfAbsent(key, serviceInstanceEntity);
        } else {
            if (StringUtils.isEmpty(serviceInstanceEntity.getHostName()) && StringUtils.isNotEmpty(hostName)) {
                serviceInstanceEntity.setHostName(hostName);
                //获取port去重后放入
                serviceInstanceEntity.getPorts().add(port);
            }
        }
    }

    private void sendComponentServiceInstanceHeartbeat() {
        try {
            for (Map.Entry<String, ServiceInstanceEntity> component : componentInstanceService.entrySet()) {
                ServiceInstanceEntity serviceInstanceEntity = component.getValue();
                TraceServiceEntity traceServiceEntity = serviceInstanceEntity.getTraceServiceEntity();

                JSONObject serviceInstanceMetric = new JSONObject();
                serviceInstanceMetric.put(METRIC_API_KEY, traceServiceEntity.getApikey());
                serviceInstanceMetric.put(TIMESTAMP, System.currentTimeMillis());
                serviceInstanceMetric.put(DATABUFF_DATABASE, MetricsUtil.generateFullDatabase(traceServiceEntity.getApikey(), DATABASE_APM_METRIC));
                serviceInstanceMetric.put(DATABUFF_MEASUREMENT, TSDB_METRIC_SERVICE_INSTANCE_TABLE_NAME);
                serviceInstanceMetric.put(FIELDS, MetricsUtil.getDefaultFields());

                JSONObject tag = new JSONObject();
                tag.put(SERVICE_ID, traceServiceEntity.getId());
                tag.put(SERVICE, TraceUtil.getCustomName(traceServiceEntity));
                tag.put(SERVICE_INSTANCE, serviceInstanceEntity.getServiceInstance());
                tag.put(TAG_VIRTUAL_SERVICE, ServiceUtil.getVirtualServiceValue(traceServiceEntity.isVirtual_service()));
                tag.put("k8sClusterId", "");
                tag.put("k8sNamespace", "");
                tag.put("k8sPodName", "");
                tag.put("k8sContainerId", "");
                tag.put("pid", "");
                tag.put("hostIp", "");
                tag.put("pgName", "");
                tag.put("pgId", "");
                tag.put("containerId", "");
                tag.put("containerName", "");
                String hostName = serviceInstanceEntity.getHostName();
                tag.put("hostname", StringUtils.isEmpty(hostName) ? "" : hostName);
                Set<Integer> ports = serviceInstanceEntity.getPorts();
                String portStr = StringUtils.join(ports, ",");
                tag.put("ports", portStr);
                //根据自定义tag查找对应的组件host
                serviceInstanceMetric.put("tag", tag);
                kafkaSender.data2Kafka(Arrays.asList(serviceInstanceMetric), METRIC_TOPIC, null);
            }
            componentInstanceService.clear();
        } catch (Throwable e) {
            log.error("addComponentHeartbeat error ", e);
        }
    }

    private String generateKey(String serviceId, String serviceInstance) {
        return serviceId + ":" + serviceInstance;
    }
}
