package com.databuff.dts.receive.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.databuff.common.constants.Constant;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.common.utils.ServiceUtil;
import com.databuff.dts.config.RefreshScopeConfig;
import com.databuff.dts.receive.model.SlowConfig;
import com.databuff.dts.receive.processor.DefaultProcessor;
import com.databuff.dts.receive.processor.PoolProcessor;
import com.databuff.dts.receive.processor.Processor;
import com.databuff.dts.receive.processor.in.*;
import com.databuff.dts.receive.processor.other.HeartbeatProcessor;
import com.databuff.dts.receive.processor.out.*;
import com.databuff.dts.receive.processor.pool.DbPoolGetProcessor;
import com.databuff.dts.receive.processor.pool.HttpPoolGetProcessor;
import com.databuff.dts.receive.processor.pool.ObjectPoolGetProcessor;
import com.databuff.dts.receive.service.ComponentInstanceService;
import com.databuff.dts.receive.service.SpanFieldRegexReplaceService;
import com.databuff.dts.receive.service.TraceMiddleProcessService;
import com.databuff.dts.util.BusinessSystemIdUtil;
import com.databuff.dts.util.ProcessorUtil;
import com.databuff.dts.util.TraceUtil;
import com.databuff.entity.TraceServiceEntity;
import com.databuff.kafka.KafkaSender;
import com.databuff.service.JedisService;
import com.databuff.service.ServiceSyncService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.databuff.common.constants.Constant.*;
import static com.databuff.common.constants.Constant.Trace.*;
import static com.databuff.common.constants.MetricName.*;
import static com.databuff.dts.config.common.CommonConstants.TAG_AGENT_HOST;

/**
 * @author:TianMing
 * @date: 2021/8/30
 * @time: 20:41
 */
@Service
@Slf4j
public class TraceMiddleProcessServiceImpl implements TraceMiddleProcessService {
    @Autowired
    private RefreshScopeConfig refreshScopeConfig;
    @Resource
    private ServiceSyncService serviceSyncService;
    @Resource
    private JedisService jedisService;
    @Autowired
    private KafkaSender kafkaSender;
    @Autowired
    private ComponentInstanceService componentInstanceService;
    @Autowired
    private SpanFieldRegexReplaceService spanFieldRegexReplaceService;

    private List<Processor> processors = new ArrayList<>();

    private List<PoolProcessor> poolProcessors = new ArrayList<>();

    @PostConstruct
    public void init() {
        //in processor
        processors.add(new KafkaMQInProcessor(serviceSyncService, refreshScopeConfig));
        processors.add(new RocketMQInProcessor(serviceSyncService, refreshScopeConfig));
        processors.add(new SofaMQInProcessor(serviceSyncService, refreshScopeConfig));
        processors.add(new RabbitMQInProcessor(serviceSyncService, refreshScopeConfig));
        processors.add(new DefaultInProcessor(serviceSyncService, refreshScopeConfig));

        //out processor 区分先后执行
        processors.add(new EsOutProcessor(serviceSyncService, componentInstanceService, jedisService, refreshScopeConfig));
        processors.add(new HttpRpcClientOutProcessor(serviceSyncService, componentInstanceService, jedisService, refreshScopeConfig));
        processors.add(new KafkaMQOutProcessor(serviceSyncService, componentInstanceService, jedisService, refreshScopeConfig));
        processors.add(new RocketMQOutProcessor(serviceSyncService, componentInstanceService, jedisService, refreshScopeConfig));
        processors.add(new SofaMQOutProcessor(serviceSyncService, componentInstanceService, jedisService, refreshScopeConfig));
        processors.add(new RabbitMQOutProcessor(serviceSyncService, componentInstanceService, jedisService, refreshScopeConfig));
        processors.add(new DefaultOutProcessor(serviceSyncService, componentInstanceService, jedisService, refreshScopeConfig));

        //other processor
        processors.add(new HeartbeatProcessor(serviceSyncService, kafkaSender, jedisService, refreshScopeConfig));

        //default processor
        processors.add(new DefaultProcessor(serviceSyncService, refreshScopeConfig));

        //get pool
        poolProcessors.add(new DbPoolGetProcessor());
        poolProcessors.add(new HttpPoolGetProcessor());
        poolProcessors.add(new ObjectPoolGetProcessor());
    }


    @Override
    public List<JSONObject> addServiceIdBySpans(JSONArray spans, Map<String, Object> headerMap) {
        //数据时间与服务器时间对齐
        Long timeDiffNs = headerMap.get(Constant.TIME_DIFF) == null ? null : Long.parseLong(headerMap.get(Constant.TIME_DIFF).toString()) * 1000 * 1000;
        List<JSONObject> jsonObjects = new ArrayList<>();
        Object k8sClusterIdObj = headerMap.get(Constant.CLUSTER_ID);
        String k8sClusterId = k8sClusterIdObj == null ? null : k8sClusterIdObj.toString();
        Map<String, Integer> resourceMaxLengthExceedCountMap = new HashMap<>();
        Map<String, Integer> resourceToStandardCountMap = new HashMap<>();
        boolean allVirtualService = true;
        boolean defaultAllVirtualDiscard = refreshScopeConfig.getAllVirtualDiscard();
        String agentSource = "";
        String spanName = "";
        Boolean showRumTraceparent = refreshScopeConfig.getShowRumTraceparent();
        for (int i = 0; i < spans.size(); i++) {
            JSONObject span = (JSONObject) spans.get(i);
            try {
                //一条链路只有单个span,要判断这个span是否池span,池span也不能算是真实服务的span；如果单个span只有池span，也不需要进行后续操作了
                if (defaultAllVirtualDiscard && spans.size() == 1 && TRACE_POOL_NAMES.contains(span.getString(NAME))) {
                    continue;
                }
                //如果配置了正则，对span字段进行正则替换
                JSONObject meta = span.getJSONObject(META);
                debugTraceparent(showRumTraceparent, meta);
                spanFieldRegexReplaceService.replaceSpanField(span);
                //针对非Databuff得Trace提取服务名和实例
                collectForDataHub(span);
                spanFieldRegexReplaceService.replaceMetaField(meta);
                spanFieldRegexReplaceService.processErrorCode(meta);

                //数据填充
                fillData(span, headerMap, timeDiffNs);
                //判断span的出入口信息
                addInAndOut(span);
                //组成显示服务名，生成服务id
                if (!fillServiceId(span, k8sClusterId)) {
                    continue;
                }
                BusinessSystemIdUtil.fillMissingBusinessIds(span, serviceSyncService);

                if (defaultAllVirtualDiscard && allVirtualService) {
                    allVirtualService = span.getBooleanValue(VIRTUAL);
                }
                //填充池化数据
                fillPoolInfo(i, span, spans);

                //处理resource、rootResource字段
                spanFieldRegexReplaceService.processSpanResource(span, resourceToStandardCountMap);
                spanFieldRegexReplaceService.substringSpanResource(span, resourceMaxLengthExceedCountMap);
                if (meta != null) {
                    String rootResource = meta.getString(ROOT_RESOURCE);
                    if (rootResource != null) {
                        spanFieldRegexReplaceService.processRootResource(meta, null);
                        spanFieldRegexReplaceService.substringRootResource(meta, resourceMaxLengthExceedCountMap);
                    }
                    if (agentSource == null || agentSource.trim().isEmpty()) {
                        agentSource = meta.getString(TRACE_AGENT_SOURCE);
                        spanName = span.getString(NAME);
                    }
                }
                jsonObjects.add(span);
            } catch (Exception e) {
                log.error("trace中间处理异常，span数据丢弃,", e);
                logDiscard(span.getString(NAME));
            }
        }
        //如果是dataHub的数据，全部span虚拟服务也不丢弃，否则会链路不完整
        if (defaultAllVirtualDiscard && allVirtualService && !TRACE_DATA_HUB.equals(agentSource)) {
            Map<String, String> tags = new HashMap<>();
            String hostName = headerMap.get(AGENT_HOSTNAME2).toString();
            tags.put(TAG_AGENT_HOST, hostName);
            tags.put("spanName", spanName);
            tags.put("agentSource", agentSource);
            tags.put("hostName", hostName);
            tags.put("k8sClusterId", k8sClusterId);
            tags.put("type", "allVirtualDiscard");
            OtelMetricUtil.logCounter(TRACE_DISCARD, tags, spans.size());
            return new ArrayList<>();
        }

        for (Map.Entry<String, Integer> entry : resourceMaxLengthExceedCountMap.entrySet()) {
            Map<String, String> tags = new HashMap<>();
            tags.put("name", entry.getKey());
            OtelMetricUtil.logCounter(TRACE_SPAN_RESOURCE_TOO_LONG, tags, entry.getValue());
        }
        for (Map.Entry<String, Integer> entry : resourceToStandardCountMap.entrySet()) {
            Map<String, String> tags = new HashMap<>();
            tags.put("name", entry.getKey());
            OtelMetricUtil.logCounter(TRACE_SPAN_RESOURCE_STANDARD, tags, entry.getValue());
        }
        resourceMaxLengthExceedCountMap.clear();
        resourceToStandardCountMap.clear();
        return jsonObjects;
    }

    private static void debugTraceparent(Boolean showRumTraceparent, JSONObject meta) {
        if (showRumTraceparent) {
            String traceparent = meta.getString("traceparent");
            if (traceparent != null && !traceparent.isEmpty()) {
                OtelMetricUtil.logCounter(METRIC_DEBUG_HIT, 1);
                log.info("found traceparent:{} , meta:{}", traceparent, meta);
            }
        }
    }

    /**
     * datahub数据处理，增加数据源，服务名是否追加数据源
     *
     * @param span
     */
    private void collectForDataHub(JSONObject span) {
        JSONObject meta = span.getJSONObject(META);
        String agentSource = meta.getString(TRACE_AGENT_SOURCE);
        String datasource = TRACE_DATA_SOURCE_DATABUFF;
        if (!TRACE_DATA_HUB.equals(agentSource)) {
            meta.put(DATA_SOURCE, datasource);
            return;
        }
        String service = meta.getString(GENERATE_SERVICE);
        String serviceInstance = meta.getString(GENERATE_IP);
        String colScopeName = meta.getString(COL_SCOPE_NAME);
        if (null != colScopeName && !colScopeName.isEmpty()) {
            colScopeName = colScopeName.split("/")[0];
            if (DATA_HUB_SOURCE_OTLP.equals(colScopeName)) {
                datasource = TRACE_DATA_SOURCE_OTLP;
            } else if (DATA_HUB_SOURCE_SKY.equals(colScopeName)) {
                datasource = TRACE_DATA_SOURCE_SKY;
            }
        }
        meta.put(DATA_SOURCE, datasource);
        service = ServiceUtil.serviceNameAppendSource(datasource, service, refreshScopeConfig.getSvcNameAppendIdentity());
        meta.put(GENERATE_SERVICE, service);
        String spanService = span.getString(SERVICE);
        spanService = ServiceUtil.serviceNameAppendSource(datasource, spanService, refreshScopeConfig.getSvcNameAppendIdentity());
        span.put(SERVICE, spanService);
        String apiKey = span.getString(API_KEY2);
        String hostName = span.getString(AGENT_HOSTNAME2);
        TraceServiceEntity traceServiceEntity = ProcessorUtil.getOrCreateServiceId(apiKey, service, null, SERVICE_TYPE_WEB, null, null, false, datasource, serviceSyncService);
        componentInstanceService.initServiceInstanceEntity(traceServiceEntity, serviceInstance, null, hostName);
    }

    private void fillData(JSONObject s, Map<String, Object> headerMap, Long timeDiffNs) {
        String hostName = headerMap.get(AGENT_HOSTNAME2).toString();
        Object clusterId = headerMap.get(Constant.CLUSTER_ID);
        s.put(HOSTNAME, hostName);
        for (Map.Entry<String, Object> m : headerMap.entrySet()) {
            if (Constant.CLUSTER_ID.equals(m.getKey())) {
                continue;
            }
            s.put(m.getKey(), m.getValue());
        }
        if (timeDiffNs != null) {
            s.put(START, s.getLong(START) + timeDiffNs);
        }
        s.put(END, s.getLong(START) + s.getLong(DURATION));
        //统计开始时间，既span的结束时间
        s.put(START_TIME, (s.getLong(END)) / 1000 / 1000);
        if (StringUtils.isBlank(s.getString(PARENT_ID))) {
            s.put(PARENT_ID, 0);
        }
        if (s.getInteger(ERROR) == null) {
            s.put(ERROR, 0);
        }
        if (s.getInteger(SLOW) == null) {
            s.put(SLOW, 0);
        }
        s.putIfAbsent(TYPE, "");
        String originalService = s.getString(SERVICE);
        //nginx数据格式兼容
        JSONObject metaObj = s.getJSONObject(META);
        if ("nginx".equals(originalService)) {
            Object error = metaObj.get(ERROR);
            if (error != null) {
                metaObj.remove(ERROR);
            }
            s.put(META, metaObj);
        }
        if (s.getInteger(ERROR) == 1 && StringUtils.isBlank(metaObj.getString(ERROR_TYPE))) {
            String errorCode = metaObj.getString(HTTP_STATUS_CODE);
            metaObj.put(ERROR_TYPE, StringUtils.isBlank(errorCode) ? "unknow" : errorCode);
        }
        //直接上报数据中apikey删除避免数据重复存储
        if (metaObj != null && metaObj.containsKey(DF_API_KEY)) {
            metaObj.remove(DF_API_KEY);
        }
        //k8s填充 集群id
        if (clusterId != null && metaObj != null && s.getString(RESOURCE).equals(SYSTEM_HEARTBEAT)) {
            metaObj.put(K8S_CLUSTER_ID, clusterId);
        }
        //处理traceid span_id parent_id 大于19位的问题
        s.put(TRACE_ID, TraceUtil.dealWithIdTooLong(s.get(TRACE_ID)));
        s.put(SPAN_ID, TraceUtil.dealWithIdTooLong(s.get(SPAN_ID)));
        s.put(PARENT_ID, TraceUtil.dealWithIdTooLong(s.get(PARENT_ID)));
        //处理数据中的_dd.开头的字段
        TraceUtil.dealWithDd2Df(metaObj);
        JSONObject metricsObj = s.getJSONObject(METRICS);
        if (metricsObj == null) {
            metricsObj = new JSONObject();
            s.put(METRICS, metricsObj);
        }
        TraceUtil.dealWithDd2Df(metricsObj);
        //兼容处理metric中的db.port,peer.port,partition javaagent2.8.4后版本需要都移到meta中，这里是兼容老版本
        //这里处理完后，后续操作这些都从meta中获取
        TraceUtil.dealWithMetric2Meta(metaObj, metricsObj);
    }


    private void addInAndOut(JSONObject s) {
        String name = s.getString(NAME);
        if (null == s.get(IS_IN) || null == s.get(IS_OUT)) {
            if (TRACE_IN_NAMES.contains(name) || TRACE_OUT_MIDDLEWARE_NAMES.contains(name)) {
                s.put(IS_IN, 1);
            } else {
                s.put(IS_IN, 0);
            }
            if (TRACE_OUT_NAMES.contains(name)) {
                s.put(IS_OUT, 1);
            } else {
                s.put(IS_OUT, 0);
            }
        }
    }

    /**
     * 主要有如下作用
     * 1 获取或者生成服务到mysql和redis中（服务实例则由Metric基础指标来生成）
     * <p>
     * 2 关联服务和服务实例，关联不上则放弃
     * 3 根据ServiceId新增业务系统id ,后续业务系统过滤内部调用与服务在多个系统时中流量去重用
     *
     * @param s
     * @return
     */
    private boolean fillServiceId(JSONObject s, String k8sClusterId) {
        String service = s.getString(SERVICE);
        s.put(REPORT_SERVICE, service);

        String name = s.getString(NAME);
        for (Processor processor : processors) {
            if (processor.match(name)) {
                boolean ret = processor.process(s, name, k8sClusterId);
                if (!ret) {
                    logDiscard(name);
                }
                return ret;
            }
        }
        logDiscard(name);
        return false;
    }

    private void logDiscard(String spanName) {
        Map<String, String> tags = new HashMap<>();
        tags.put("type", "Processor-false-" + spanName);
        OtelMetricUtil.logCounter(TRACE_DISCARD, tags, 1);
    }

    private void fillPoolInfo(int i, JSONObject span, JSONArray spans) {
        String name = span.getString("name");
        if (spans.size() > 1 && i > 0 && TRACE_POOL_GET_NAMES.contains(name)) {
            //必须是两个span以上，且最起码是第二个span，name是池化的
            JSONObject lastSpan = (JSONObject) spans.get(i - 1);
            if (lastSpan != null) {
                for (PoolProcessor processor : poolProcessors) {
                    int ret = processor.match(name, span, lastSpan);
                    if (ret > 0) {
                        processor.process(span, lastSpan, ret);
                    }
                }
            }
        }
    }
}
