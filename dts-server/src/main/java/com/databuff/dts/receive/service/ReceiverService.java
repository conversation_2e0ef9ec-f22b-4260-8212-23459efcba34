package com.databuff.dts.receive.service;

import com.databuff.dts.ringbuffer.ParseTrace;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Map;

/**
 * @author:TianMing
 * @date: 2021/8/26
 * @time: 10:25
 */
public interface ReceiverService {

    void parseRequest(ParseTrace event) throws Exception;

    Object decompress(final HttpServletRequest req) throws IOException;

    Map<String, Object> getHeaders(HttpServletRequest req, String[] keys);

}
