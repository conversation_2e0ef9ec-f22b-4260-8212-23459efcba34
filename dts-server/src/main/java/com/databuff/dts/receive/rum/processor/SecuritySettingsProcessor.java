package com.databuff.dts.receive.rum.processor;

import com.alibaba.fastjson.JSON;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.entity.rum.Attribute;
import com.databuff.entity.rum.LogRecord;
import com.databuff.entity.rum.Resource;
import com.databuff.entity.rum.Span;
import com.databuff.entity.rum.mysql.RumAppSettings;
import com.databuff.entity.rum.web.WebSecuritySettings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.databuff.common.constants.MetricName.*;

/**
 * 字段	说明（页面展示的指标说明）	规则
 * <p>
 * 异常数据剔除阈值	未开启时，系统默认过滤规则会生效，规则为DCL或者完全加载大于FCP的10倍并且FCP、LCP、DCL、完全加载四个指标任意一个指标大于60秒时即过滤这次页面性能数据。
 * 开启时，过滤规则为FCP、LCP、DCL、完全加载四个指标任意一个指标大于设置值即过滤这次页面性能数据，同时系统默认过滤规则会关闭。符合过滤规则的页面性能数据将剔除掉，
 * 不参与页面性能指标计算，但页面发生的JS错误仍然保留且参与JS错误率计算。	默认阈值60秒，默认过滤规则一期已完成
 * <p>
 * 域名白名单	可以通过设置域名白名单，过滤掉非白名单内域名的干扰数据，探针端通过获取平台配置后重新生成探针注入代码，用户重新嵌码后生效，在探针端进行白名单过滤	限制253个字符
 * <p>
 * 机器人数据	开启后，将过滤掉UA中含"Bot"、 "Spider"、"Crawler"、"Yahoo! Slurp"、"NetworkBench"关键词的访问数据，含有以上内容的访问主要是拨测和爬虫数据
 *
 * <AUTHOR>
 * @date 2024/09/18
 */
@Component
@Slf4j
public class SecuritySettingsProcessor {


    public WebSecuritySettings getSecuritySettings(RumAppSettings appSettings) {
        try {
            if (appSettings.getSecuritySettings() == null) {
                return new WebSecuritySettings();
            }
            return JSON.parseObject(appSettings.getSecuritySettings(), WebSecuritySettings.class);
        } catch (Exception e) {
            log.error("Failed to parse security settings ,appSettings:{}", appSettings, e);
            return new WebSecuritySettings();
        }
    }

    public boolean isInvalidUserAgent(Resource resource, WebSecuritySettings webSecuritySettings) {
        if (!webSecuritySettings.isEnableBotFiltering()) {
            return false;
        }

        String userAgent = resource.getAttributes().stream()
                .filter(attr -> "userAgent".equals(attr.getKey()))
                .map(Attribute::getStringValue)
                .findFirst()
                .orElse("").toLowerCase();

        String[] botKeywords = {"bot", "spider", "crawler", "yahoo! slurp", "networkbench"};
        return Arrays.stream(botKeywords).anyMatch(userAgent::contains);
    }


    public boolean isValidPageMetrics(Span span, WebSecuritySettings webSecuritySettings) {
        // 未开启时，系统默认过滤规则会生效，规则为（DCL或者完全加载大于FCP的10倍）并且（FCP、LCP、DCL、完全加载四个指标任意一个指标大于60秒时）时即过滤这次页面性能数据。
        // 开启时，过滤规则为FCP、LCP、DCL、完全加载四个指标任意一个指标大于设置值即过滤这次页面性能数据，同时系统默认过滤规则会关闭。
        // 符合过滤规则的页面性能数据将剔除掉，不参与页面性能指标计算，但页面发生的JS错误仍然保留且参与JS错误率计算。
        long fcp = 0, lcp = 0, dcl = 0, fullLoadTime = 0;
        boolean isCustomThreshold = webSecuritySettings.isEnableAnomalyThreshold();
        long threshold = isCustomThreshold ? webSecuritySettings.getAnomalyThreshold() * 1_000_000_000L : 60_000_000_000L;

        for (Attribute attr : span.getAttributes()) {
            switch (attr.getKey()) {
                case "fcp":
                    fcp = Long.parseLong(attr.getStringValue());
                    break;
                case "lcp":
                    lcp = Long.parseLong(attr.getStringValue());
                    break;
                case "dcl":
                    dcl = Long.parseLong(attr.getStringValue());
                    break;
                case "full_load_time":
                    fullLoadTime = Long.parseLong(attr.getStringValue());
                    break;
            }
        }

        boolean isValid;
        if (isCustomThreshold) {
            // Custom threshold enabled: filter if any metric exceeds the threshold
            isValid = fcp <= threshold && lcp <= threshold && dcl <= threshold && fullLoadTime <= threshold;
        } else {
            // Default rule: filter if (DCL or full load time > 10x FCP) AND (any metric > 60s)
            boolean exceeds10xFCP = (dcl > fcp * 10 || fullLoadTime > fcp * 10);
            boolean anyMetricExceedsThreshold = (fcp > threshold || lcp > threshold || dcl > threshold || fullLoadTime > threshold);
            isValid = !(exceeds10xFCP && anyMetricExceedsThreshold);
        }

        if (!isValid) {
            OtelMetricUtil.logCounter(RUM_TRACE_DISCARD, 1);
            OtelMetricUtil.logCounter(RUM_TRACE_DISCARD_PAGE_METRICS, 1);
            log.debug("isValid:{} ,dcl:{} ,fcp:{} ,fullLoadTime:{} ,lcp:{} ,threshold:{}", false, dcl, fcp, fullLoadTime, lcp, threshold);
        }

        return isValid;
    }

    public boolean isValidDomain(Span span, WebSecuritySettings webSecuritySettings) {
        boolean validDomain = isValidDomain(span.getAttributes(), webSecuritySettings);
        if (!validDomain) {
            OtelMetricUtil.logCounter(RUM_TRACE_DISCARD, 1);
            OtelMetricUtil.logCounter(RUM_TRACE_DISCARD_DOMAIN, 1);
        }
        return validDomain;
    }

    public boolean isValidDomain(LogRecord logRecord, WebSecuritySettings webSecuritySettings) {
        boolean validDomain = isValidDomain(logRecord.getAttributes(), webSecuritySettings);
        if (!validDomain) {
            OtelMetricUtil.logCounter(RUM_LOG_DISCARD, 1);
            OtelMetricUtil.logCounter(RUM_LOG_DISCARD_DOMAIN, 1);
        }
        return validDomain;
    }

    private boolean isValidDomain(List<Attribute> attributes, WebSecuritySettings webSecuritySettings) {
        if (!webSecuritySettings.isEnableDomainWhiteFiltering() ||
                webSecuritySettings.getDomainWhitelist() == null || webSecuritySettings.getDomainWhitelist().isEmpty()) {
            return true;
        }

        List<String> urls = attributes.stream()
                .filter(attr -> "location.href".equals(attr.getKey()) || "http.url".equals(attr.getKey()))
                .map(Attribute::getStringValue)
                .collect(Collectors.toList());

        boolean isValid = urls.stream()
                .anyMatch(url -> webSecuritySettings.getDomainWhitelist().stream()
                        .anyMatch(whitelist -> url.contains(whitelist.getDomain())));
//        if (!isValid) {
//            log.debug("isValidDomain:false ,urls:{} securitySettings.getDomainWhitelist():{}",urls ,securitySettings.getDomainWhitelist());
//        }
        return isValid;
    }
}
