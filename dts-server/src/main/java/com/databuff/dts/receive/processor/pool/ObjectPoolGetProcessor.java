package com.databuff.dts.receive.processor.pool;

import com.alibaba.fastjson.JSONObject;
import com.databuff.dts.receive.processor.PoolBaseProcessor;
import com.databuff.service.ServiceSyncService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import static com.databuff.common.constants.Constant.Trace.*;

@Slf4j
public class ObjectPoolGetProcessor extends PoolBaseProcessor {

    public ObjectPoolGetProcessor() {
    }

    //object_pool.borrow目前只判断jedis,根据obj.type是否在（redis.clients.jedis.Jedis，redis.clients.jedis.BinaryJedis），上个span是否redis
    @Override
    public int match(String name,JSONObject poolSpan, JSONObject lastSpan) {
        //0不匹配，1数据上匹配，2缓存匹配
        int ret = 0;
        if (!POOL_GET_OBJECT.equals(name)){
            return ret ;
        }
        JSONObject meta = poolSpan.getJSONObject(META);
        JSONObject lastMeta = lastSpan.getJSONObject(META);
        if (meta == null || lastMeta == null){
            return ret ;
        }
        try{
            String objType = meta.getString(OBJ_TYPE);
            if (SUPPORTED_REDIS_NAMES.contains(lastSpan.getString("name")) &&  objType != null && SUPPORTED_REDIS_POOL_OBJ_TYPES.contains(objType)){
                return 1;
            }
        }catch (Exception e){
            log.error("ObjectPoolGetProcessor match error",e);
        }
        if (ret==0){
            String poolName = meta.getString(POOL_NAME);
            String svcId = poolSpan.getString(SERVICE_ID);
            String svcInstance = poolSpan.getString(SERVICE_INSTANCE);
            return getPoolService(svcId,svcInstance,"obj",poolName) != null? 2 : 0;
        }
        return ret;
    }

    @Override
    public void process(JSONObject poolSpan, JSONObject lastSpan, int matchType) {
        fillPoolService(matchType,"obj",poolSpan, lastSpan);
        if (matchType==2) {
            //缓存匹配，填充数据后不走后面流程
            return;
        }
        //对象池如果服务端有服务名，需要填充
        String service = lastSpan.getString(SERVER_SERVICE);
        String serviceId = lastSpan.getString(SERVER_SERVICE_ID);
        String serviceInstance = lastSpan.getString(SERVER_SERVICE_INSTANCE);
        if (StringUtils.isNotBlank(service)){
            poolSpan.put(SERVER_SERVICE, service);
        }
        if (StringUtils.isNotBlank(serviceId)){
            poolSpan.put(SERVER_SERVICE_ID, serviceId);
        }
        if (StringUtils.isNotBlank(serviceInstance)){
            poolSpan.put(SERVER_SERVICE_INSTANCE, serviceInstance);
        }
        //缓存池服务信息
        cacheFillPoolSvc(poolSpan.getString(SERVICE_ID),
                poolSpan.getString(SERVICE_INSTANCE),
                "obj",
                poolSpan.getJSONObject(META).getString(POOL_NAME),
                poolSpan.getString(SERVER_SERVICE),
                poolSpan.getString(SERVER_SERVICE_ID),
                poolSpan.getString(SERVER_SERVICE_INSTANCE),
                poolSpan.getJSONObject(META).getString("lastSpanName"),
                poolSpan.getJSONObject(META).getString("lastDbType"));
    }
}
