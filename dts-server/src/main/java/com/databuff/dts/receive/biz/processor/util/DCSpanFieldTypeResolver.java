package com.databuff.dts.receive.biz.processor.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class DCSpanFieldTypeResolver {

    // 这个 Map 需要手动维护！确保它包含了 DCSpan 中所有【显式声明】的字段及其对应的 Java 类型。
    private static final Map<String, DataType> knownFieldTypes;

    static {
        Map<String, DataType> mapBuilder = new HashMap<>();

        // ====================================================================
        //  vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
        //  关键：手动维护区域开始！
        //  请根据 DCSpan.java 中的【显式字段】定义填充此 Map。
        //  新增或修改 DCSpan 字段时，【必须】同步更新此处的映射。
        // ====================================================================

        // --- 顶层字段示例 (根据您之前提供的 DCSpan.java) ---
        mapBuilder.put("hours", DataType.LONG);
        mapBuilder.put("minutes", DataType.LONG);
        mapBuilder.put("startTime", DataType.STRING);
        mapBuilder.put("longStartTime", DataType.LONG);
        mapBuilder.put("is_parent", DataType.INTEGER);
        mapBuilder.put("slow", DataType.INTEGER);
        mapBuilder.put("trace_id", DataType.STRING);
        mapBuilder.put("span_id", DataType.STRING);
        mapBuilder.put("parent_id", DataType.STRING);
        mapBuilder.put("dfApiKey", DataType.STRING);
        mapBuilder.put("resource", DataType.STRING);
        mapBuilder.put("serviceId", DataType.STRING);
        mapBuilder.put("serviceInstance", DataType.STRING);
        mapBuilder.put("clientService", DataType.STRING);
        mapBuilder.put("client_service_id", DataType.STRING);
        mapBuilder.put("clientServiceInstance", DataType.STRING);
        mapBuilder.put("end", DataType.LONG);
        mapBuilder.put("hostName", DataType.STRING);
        mapBuilder.put("error", DataType.INTEGER);
        mapBuilder.put("type", DataType.STRING);
        mapBuilder.put("duration", DataType.LONG);
        mapBuilder.put("isIn", DataType.INTEGER);
        mapBuilder.put("dfHostname", DataType.STRING);
        mapBuilder.put("start", DataType.LONG);
        mapBuilder.put("hostId", DataType.STRING);
        mapBuilder.put("reportService", DataType.STRING);
        mapBuilder.put("service", DataType.STRING);
        mapBuilder.put("name", DataType.STRING);
        mapBuilder.put("isOut", DataType.INTEGER);
        mapBuilder.put("userAgent", DataType.STRING);
        mapBuilder.put("server_service_id", DataType.STRING);
        mapBuilder.put("serverServiceInstance", DataType.STRING);
        mapBuilder.put("serverService", DataType.STRING);
        mapBuilder.put("virtual", DataType.BOOLEAN);
        // protected 字段
        mapBuilder.put("cpuTime", DataType.LONG);
        mapBuilder.put("serviceCode", DataType.STRING);
        mapBuilder.put("clientReportService", DataType.STRING);
        mapBuilder.put("apiKey", DataType.STRING); // getApiKey() 方法处理 null
        mapBuilder.put("errorType", DataType.STRING);
        mapBuilder.put("isSlow", DataType.LONG); // 来自 getIsSlow()
        mapBuilder.put("isVerySlow", DataType.LONG); // 来自 getIsVerySlow()
        mapBuilder.put("slowTime", DataType.LONG);
        mapBuilder.put("serviceType", DataType.STRING);
        mapBuilder.put("clientServiceType", DataType.STRING);
        mapBuilder.put("serverServiceType", DataType.STRING);
        mapBuilder.put("level", DataType.INTEGER);

        // --- 集合/Map 类型字段 ---
        mapBuilder.put("meta", DataType.MAP_STRING_STRING);
        mapBuilder.put("metrics", DataType.MAP_STRING_STRING);
        mapBuilder.put("biz_pid_id", DataType.LIST_STRING);
        mapBuilder.put("client_biz_pid_id", DataType.LIST_STRING);
        mapBuilder.put("server_biz_pid_id", DataType.LIST_STRING);

        // ====================================================================
        //                  结束: 手动维护区域
        // ====================================================================

        knownFieldTypes = Collections.unmodifiableMap(mapBuilder);
        log.info("DCSpanFieldTypeResolver initialized with {} manually defined field types.", knownFieldTypes.size());
    }

    /**
     * 获取 DCSpan 字段路径对应的已知数据类型。
     * 对于 meta 和 metrics 内部的 key，固定返回 STRING。
     *
     * @param fieldPath 字段路径 (e.g., "duration", "meta.http.code", "metrics.order_amount")
     * @return 对应的 DataType 枚举，如果无法确定则返回 UNKNOWN。
     */
    public static DataType getKnownType(String fieldPath) {
        if (!StringUtils.hasText(fieldPath)) {
            return DataType.UNKNOWN;
        }

        // 1. 优先检查是否为显式声明的字段
        DataType knownType = knownFieldTypes.get(fieldPath);
        if (knownType != null) {
            return knownType;
        }

        // 2. 检查是否为 meta 或 metrics 内部的 key
        if (fieldPath.startsWith("meta.") && fieldPath.length() > 5) {
            return DataType.STRING;
        }
        if (fieldPath.startsWith("metrics.") && fieldPath.length() > 8) {
            return DataType.STRING;
        }

        // 3. 无法识别
        log.trace("Field path '{}' is unknown in DCSpanFieldTypeResolver.", fieldPath);
        return DataType.UNKNOWN;
    }
}