package com.databuff.dts.receive.service;

import com.databuff.common.constants.Constant;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

import static com.databuff.common.constants.Constant.CONTENT_LENGTH;

/**
 * @author:TianMing
 * @date: 2021/8/26
 * @time: 11:46
 */
@Slf4j
public abstract class AbstractReceiver implements ReceiverService {

    @Override
    public Object decompress(final HttpServletRequest req) throws IOException {

        // 自定义过滤器，解压
        ServletInputStream inputStream;
        BufferedReader reader = null;
        StringBuilder sb = new StringBuilder();
        try {
            inputStream = req.getInputStream();
            reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        } catch (IOException e) {
            throw e;
        } finally {
            if (reader != null) {
                reader.close();
            }
        }
        return sb.toString();
    }

    /**
     * 获取头信息
     *
     * @param req
     * @return
     */
    @Override
    public Map<String, Object> getHeaders(HttpServletRequest req, String[] keys) {
        Map<String, Object> map = new HashMap<>(keys.length);
        if (keys == null) {
            Enumeration<String> allHeader = req.getHeaderNames();
            while (allHeader.hasMoreElements()) {
                String key = allHeader.nextElement();
                // 排除Cookie字段
                if (key.equalsIgnoreCase("Cookie")) {
                    continue;
                }
                map.put(key, req.getHeader(key));
            }
        } else {
            for (String key : keys) {
                map.put(key, req.getHeader(key));
            }
        }
        //这里将dd的header转换成df的header
        String apiKey = req.getHeader(Constant.API_KEY2) == null ? req.getHeader(Constant.API_KEY) : req.getHeader(Constant.API_KEY2);
        String agentHost = req.getHeader(Constant.AGENT_HOSTNAME2) == null ? req.getHeader(Constant.AGENT_HOSTNAME) : req.getHeader(Constant.AGENT_HOSTNAME2);
        String hostId = req.getHeader(Constant.HOST_ID) == null ? req.getHeader(Constant.AGENT_HOST_ID) : req.getHeader(Constant.HOST_ID);
        map.remove(Constant.AGENT_HOSTNAME);
        map.remove(Constant.API_KEY);
        map.put(Constant.AGENT_HOSTNAME2, agentHost);
        map.put(Constant.API_KEY2, apiKey);
        map.put(Constant.HOST_G_ID, hostId);
        map.put(CONTENT_LENGTH, req.getContentLength());
        // 获取时间差 时间差没法设置到header中，只能放到attribute中
        Object timeDiff = req.getAttribute(Constant.TIME_DIFF);
        if (timeDiff != null) {
            map.put(Constant.TIME_DIFF, timeDiff);
        } else {
            map.remove(Constant.TIME_DIFF);
        }
        return map;
    }
}
