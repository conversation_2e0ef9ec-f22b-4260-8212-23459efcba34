package com.databuff.dts.receive.biz.processor.config;

import com.databuff.dts.receive.biz.processor.model.CompiledEventRule;
import com.databuff.dts.receive.biz.processor.model.CompiledExceptionRule;
import com.databuff.dts.receive.biz.processor.model.ParsedScenarioGraph;
import com.databuff.entity.BizKpiConfig;

import java.util.List;
import java.util.Set;

/**
 * 配置加载器接口 V2.9 (多租户感知)。
 */
public interface ObservabilityConfigLoaderInterface {

    /**
     * 加载并准备【所有租户】的业务可观测性配置。
     */
    void loadAndPrepareConfigs();

    /**
     * 根据 apiKey 和 Service ID 获取可能匹配的事件规则列表。
     * @param apiKey 租户标识
     * @param serviceId 服务 ID
     * @return 编译后的事件规则列表，如果没有匹配则返回空列表。
     */
    List<CompiledEventRule> getEventRulesByServiceId(String apiKey, String serviceId); // <--- 添加 apiKey

    /**
     * 根据 apiKey 和事件 ID 获取已按优先级排序的异常规则列表。
     * @param apiKey 租户标识
     * @param eventId 事件 ID
     * @return 编译后的异常规则列表，如果没有则返回空列表。
     */
    List<CompiledExceptionRule> getExceptionRules(String apiKey, int eventId); // <--- 添加 apiKey

    /**
     * 根据 apiKey 和场景 ID 获取解析后的场景图对象。
     * @param apiKey 租户标识
     * @param scenarioId 场景 ID
     * @return 解析后的场景图对象，如果不存在则返回 null。
     */
    ParsedScenarioGraph getScenarioGraph(String apiKey, int scenarioId); // <--- 添加 apiKey

    /**
     * 根据 apiKey 和场景 ID 获取 KPI 配置对象。
     * @param apiKey 租户标识
     * @param scenarioId 场景 ID
     * @return KPI 配置对象，如果不存在则返回 null。
     */
    BizKpiConfig getKpiConfig(String apiKey, int scenarioId); // <--- 添加 apiKey

    /**
     * 根据 apiKey 和事件 ID 获取其所属的场景 ID 列表。
     * @param apiKey 租户标识
     * @param eventId 事件 ID
     * @return 场景 ID 列表，如果没有关联则返回空列表。
     */
    List<Integer> getScenarioIdsForEvent(String apiKey, int eventId); // <--- 添加 apiKey

    /**
     * 获取【全局】所有条件和 KPI 配置中使用的字段路径集合。
     * (当前设计为全局共享，如果需要按租户隔离，此接口也需修改)
     * @return 包含所有必需字段路径的 Set。
     */
    Set<String> getRequiredFields();

    /**
     * 获取【全局】所有在 BizEvent 配置中声明的 Service ID 集合。
     * 此集合用于快速过滤掉与任何业务事件都无关的 Span。
     * (当前设计为全局共享，如果需要按租户隔离，此接口也需修改)
     * @return 包含所有已配置 Service ID 的不可修改 Set。
     */
    Set<String> getConfiguredServiceIds();

}