package com.databuff.dts.receive.biz.processor.model;

import com.databuff.dts.receive.biz.processor.condition.ExecutableCondition;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

@Getter
@AllArgsConstructor
@ToString(exclude = {"rootCondition"}) // 避免打印复杂的条件树
public class CompiledExceptionRule implements Comparable<CompiledExceptionRule> {
    private String errorType;
    private String errorName;
    private int priority;
    private ExecutableCondition rootCondition; // 编译后的条件树根节点

    @Override
    public int compareTo(CompiledExceptionRule other) {
        return Integer.compare(this.priority, other.priority);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CompiledExceptionRule that = (CompiledExceptionRule) o;
        // 核心标识：优先级、错误类型和错误名称
        return priority == that.priority &&
                Objects.equals(errorType, that.errorType) &&
                Objects.equals(errorName, that.errorName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(errorType, errorName, priority);
    }
}