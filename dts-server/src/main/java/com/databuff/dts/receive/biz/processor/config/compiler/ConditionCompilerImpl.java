package com.databuff.dts.receive.biz.processor.config.compiler;

import com.databuff.common.tsdb.model.LogicalOperator;
import com.databuff.common.tsdb.model.WhereOp;
import com.databuff.dts.receive.biz.processor.condition.AndCondition;
import com.databuff.dts.receive.biz.processor.condition.ExecutableCondition;
import com.databuff.dts.receive.biz.processor.condition.OrCondition;
import com.databuff.dts.receive.biz.processor.condition.SimpleCondition;
import com.databuff.dts.receive.biz.processor.util.DCSpanFieldTypeResolver;
import com.databuff.dts.receive.biz.processor.util.DataType;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * {@link ConditionCompiler} 的默认实现。 (V2 - 根据用户需求调整列表和Map解析逻辑)
 * <p>
 * 负责将从配置（如JSON）中获取的原始条件数据（通常是嵌套的 Map 或 List 结构）
 * 递归地解析并编译成可执行的 {@link ExecutableCondition} 对象树。
 * 此编译器包含了字段类型推断、操作符解析、目标值转换等复杂逻辑。
 * </p>
 * <p>
 * 核心解析逻辑调整以适应特定的JSON输入格式：
 * <ul>
 * <li><b>列表 (List) 解析 ({@link #compileList})</b>:
 * <ul>
 * <li>列表中的每个元素被首先独立递归解析成 {@link ExecutableCondition}。</li>
 * <li>然后，检查原始列表中的所有Map元素：如果它们都通过其内部的 {@code "connector"} 字段共享一个相同的、有效的逻辑连接符（如 "AND" 或 "OR"），则使用该共同连接符来组合已解析的子条件。</li>
 * <li>否则（如连接符不一致、缺失，或列表元素不全是Map），列表中的子条件将默认使用 {@code AND} 连接。</li>
 * </ul>
 * </li>
 * <li><b>Map 解析 ({@link #compileMap})</b>:
 * <ul>
 * <li>优先尝试将Map作为标准的复合条件解析：如果Map包含一个有效的 {@code "connector"} 键，并且其 {@code "left"} (以及可能的 {@code "right"}) 字段是合法的子条件结构（即它们本身是Map或List），则按此复合结构处理。</li>
 * <li>如果上述条件不满足，则尝试将Map作为简单条件解析：如果Map包含 {@code "left"} (作为字符串字段名) 和 {@code "operator"} 键，则调用 {@link #parseSimpleCondition(Map, Set)}。此方法设计为仅关注简单条件相关的键，并忽略该Map中可能存在的其他键。</li>
 * <li>如果两种模式都不匹配，则视为无效Map结构。</li>
 * </ul>
 * </li>
 * </ul>
 * 这种方法旨在更灵活地处理用户提供的、可能将连接逻辑信息嵌入到列表元素Map中的JSON结构，
 * 同时保持对标准简单和复合条件结构的解析能力。
 * </p>
 */
@Component
@Slf4j
public class ConditionCompilerImpl implements ConditionCompiler {

    private final ObjectMapper objectMapper;

    /**
     * 一个静态的、可重用的实例，代表始终为真的条件。
     * 用于空列表条件或复合条件中逻辑上缺失的右操作数（例如，AND/OR 与 True）。
     */
    private static final ExecutableCondition ALWAYS_TRUE_CONDITION = data -> true;

    /**
     * 构造函数，通过Spring依赖注入 {@link ObjectMapper} 实例。
     *
     * @param objectMapper 用于JSON解析和对象转换的Jackson ObjectMapper实例。推荐注入共享实例以提高性能。
     */
    @Autowired
    public ConditionCompilerImpl(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * {@inheritDoc}
     * <p>
     * 这是条件编译的公共入口方法。它接收原始条件数据（通常是 Map 或 List）和
     * 一个用于收集所有遇到的字段路径的 Set。此方法主要负责调用内部的递归解析逻辑
     * ({@link #parseConditionsRecursive})，并处理顶层的任何潜在异常。
     * </p>
     *
     * @param conditionsRaw           从配置源（如JSON）反序列化得到的原始条件数据。
     *                                通常是 {@code List<Map<String, Object>>} 或 {@code Map<String, Object>} 结构。
     * @param requiredFieldsCollector 一个Set，用于在编译过程中收集条件中实际引用的所有字段路径
     *                                (例如, "duration", "meta.http.code")。此Set会被方法修改。
     * @return 编译后的 {@link ExecutableCondition} 树的根节点。
     * 如果原始条件数据为null、空、无效，或在解析/编译过程中发生不可恢复的错误，则返回 {@code null}。
     */
    @Override
    public ExecutableCondition compileConditions(Object conditionsRaw, Set<String> requiredFieldsCollector) {
        log.debug("开始条件编译。原始输入类型: {}", (conditionsRaw != null ? conditionsRaw.getClass().getName() : "null"));
        try {
            // 调用核心递归解析方法
            ExecutableCondition condition = parseConditionsRecursive(conditionsRaw, requiredFieldsCollector);
            log.debug("条件编译完成。结果类型: {}", (condition != null ? condition.getClass().getSimpleName() : "null"));
            return condition;
        } catch (Exception e) {
            // 捕获任何未预料的异常，防止编译过程崩溃
            log.error("条件编译过程中发生严重错误。原始输入: {}. 错误: {}", conditionsRaw, e.getMessage(), e);
            return null; // 编译失败返回 null
        }
    }

    /**
     * 主要的递归条件解析方法。
     * <p>
     * 根据输入对象 {@code conditionJson} 的实际类型（{@link List} 或 {@link Map}），
     * 将其分派到相应的具体处理方法 ({@link #compileList} 或 {@link #compileMap})。
     * 如果输入为 {@code null}，则直接返回 {@code null}（表示无法从此输入构建条件，
     * 但在某些复合条件上下文中，这可能被其调用者解释为 {@code ALWAYS_TRUE_CONDITION}）。
     * 如果输入类型不是List或Map，则视为无效的条件结构，记录警告并返回 {@code null}。
     * </p>
     *
     * @param conditionJson           当前层级的条件数据，预期为 {@link List} 或 {@link Map}。
     *                                也可能为 {@code null}，或在错误情况下为其他类型。
     * @param requiredFieldsCollector 用于在递归过程中收集所有遇到的字段路径的 {@link Set}。
     * @return 构建好的 {@link ExecutableCondition} 子树的根节点；如果解析失败或输入无效，则返回 {@code null}。
     */
    private ExecutableCondition parseConditionsRecursive(Object conditionJson, Set<String> requiredFieldsCollector) {
        if (conditionJson == null) {
            log.trace("递归解析：输入为 null。返回 null。"); // 上层compileMap可能将此视为ALWAYS_TRUE
            return null;
        }
        log.trace("递归解析：处理类型为 {} 的对象。", conditionJson.getClass().getName());

        if (conditionJson instanceof List) {
            return compileList((List<?>) conditionJson, requiredFieldsCollector);
        } else if (conditionJson instanceof Map) {
            @SuppressWarnings("unchecked") // 已通过 instanceof 检查
            Map<String, Object> conditionMap = (Map<String, Object>) conditionJson;
            return compileMap(conditionMap, requiredFieldsCollector);
        } else {
            // 非List或Map的顶层结构（如String、Number）不是有效的条件结构体
            log.warn("递归解析：无法解析条件，遇到非预期的顶层JSON类型: {}。值: '{}'。有效的条件结构应为List或Map。",
                    conditionJson.getClass().getName(), conditionJson);
            return null;
        }
    }

    /**
     * 编译列表结构的条件。
     * <p>
     * 首先，递归编译列表中的每一个元素得到子条件列表。如果任何子条件解析失败，则整个列表编译失败。
     * 然后，检查原始列表中的所有Map元素是否通过其内部的 {@code "connector"} 字段共享一个相同的、
     * 有效的逻辑连接符（如 "AND" 或 "OR"）。如果是，则使用该共同连接符组合已编译的子条件。
     * 否则（如连接符不一致、缺失，或列表元素不全是Map），子条件将默认使用 {@code AND} 连接。
     * 空列表被视作一个始终为真的条件。只有一个子条件的列表直接返回该子条件。
     * </p>
     *
     * @param list                    条件配置列表。
     * @param requiredFieldsCollector 用于收集字段路径的 Set。
     * @return 代表列表逻辑的 {@link ExecutableCondition}。空列表返回 {@code ALWAYS_TRUE_CONDITION}。解析失败返回 {@code null}。
     */
    private ExecutableCondition compileList(List<?> list, Set<String> requiredFieldsCollector) {
        if (list.isEmpty()) {
            log.debug("编译List：输入列表为空，返回 ALWAYS_TRUE_CONDITION。");
            return ALWAYS_TRUE_CONDITION;
        }
        log.trace("编译List：处理大小为 {} 的列表。", list.size());

        List<ExecutableCondition> childrenConditions = new ArrayList<>(list.size());
        for (int i = 0; i < list.size(); i++) {
            Object element = list.get(i);
            ExecutableCondition childCondition = parseConditionsRecursive(element, requiredFieldsCollector);
            if (childCondition == null) { // 子条件解析失败
                log.warn("编译List：解析列表内索引 {} 的元素失败。元素: {}。整个列表编译失败。", i, element);
                return null;
            }
            childrenConditions.add(childCondition);
        }

        // 此处 childrenConditions.size() == list.size() 且不为0
        if (childrenConditions.size() == 1) {
            log.debug("编译List：只有一个可执行的子条件，直接返回该子条件。");
            return childrenConditions.get(0);
        }

        LogicalOperator effectiveConnector = determineListConnector(list); // 推断列表连接符

        if (effectiveConnector == LogicalOperator.OR) {
            return new OrCondition(Collections.unmodifiableList(childrenConditions));
        } else { // AND (默认或显式)
            return new AndCondition(Collections.unmodifiableList(childrenConditions));
        }
    }

    /**
     * 根据列表元素的 "connector" 字段推断列表的连接逻辑。
     *
     * @param originalList 原始的条件对象列表。
     * @return 推断出的 {@link LogicalOperator} (OR 或 AND)。如果无法一致推断，默认为 AND。
     */
    private LogicalOperator determineListConnector(List<?> originalList) {
        // 默认AND
        LogicalOperator determinedConnector = LogicalOperator.AND;
        String commonConnectorStr = null;
        boolean allElementsAreMaps = true;
        boolean connectorsAreConsistent = true;

        if (!(originalList.get(0) instanceof Map)) { // 第一个元素不是Map，无法使用此逻辑
            allElementsAreMaps = false;
        } else {
            Object firstConnectorObj = ((Map<?, ?>) originalList.get(0)).get("connector");
            if (firstConnectorObj instanceof String) {
                commonConnectorStr = (String) firstConnectorObj;
            } else { // 第一个Map就没有connector或类型不对
                connectorsAreConsistent = false;
            }
        }
        // 如果第一个元素不是带有效connector的Map，则无需检查后续，直接用默认AND
        if (!allElementsAreMaps || !connectorsAreConsistent || commonConnectorStr == null) {
            log.debug("编译List：列表元素没有共享的、一致的连接符，或包含非Map元素。使用默认AND逻辑。");
            return LogicalOperator.AND;
        }
        // 检查后续元素是否与第一个元素的connector一致
        for (int i = 1; i < originalList.size(); i++) {
            if (!(originalList.get(i) instanceof Map)) {
                connectorsAreConsistent = false;
                break;
            }
            Map<?, ?> currentMap = (Map<?, ?>) originalList.get(i);
            Object currentMapConnectorObj = currentMap.get("connector");
            if (!(currentMapConnectorObj instanceof String) || !commonConnectorStr.equals(currentMapConnectorObj)) {
                connectorsAreConsistent = false;
                break;
            }
        }

        if (connectorsAreConsistent) { // 再次确认 commonConnectorStr 非null
            try {
                determinedConnector = LogicalOperator.valueOf(commonConnectorStr.toUpperCase());
                log.debug("编译List：列表元素共享连接符 '{}'。", determinedConnector);
            } catch (IllegalArgumentException e) {
                log.warn("编译List：列表元素共享的连接符 '{}' 无效。回退到默认AND逻辑。", commonConnectorStr);
                determinedConnector = LogicalOperator.AND; // 保持默认
            }
        } else {
            log.debug("编译List：列表元素连接符不一致或缺失。使用默认AND逻辑。");
            determinedConnector = LogicalOperator.AND;
        }
        return determinedConnector;
    }


    /**
     * 编译Map结构的条件。
     * <p>
     * 该方法尝试按以下优先级解析Map:
     * <ol>
     * <li><b>标准复合条件</b>: 如果Map包含一个有效的 {@code "connector"} 键 (如 "AND", "OR")，并且其 {@code "left"}
     * (以及二元操作符所需的 {@code "right"}) 字段本身是结构上有效的子条件（即它们是Map或List），
     * 则将此Map作为复合条件处理。其 {@code "left"} 和 {@code "right"} 部分会被递归解析。
     * 如果 {@code "right"} 为null或空列表，对于二元操作符它通常被视为一个 {@code ALWAYS_TRUE_CONDITION}。</li>
     * <li><b>简单条件</b>: 如果Map不符合上述标准复合结构的条件（例如，{@code "left"} 字段不是Map或List，即使存在 {@code "connector"} 键），
     * 但它包含一个字符串类型的 {@code "left"} 字段（作为字段名）和一个有效的 {@code "operator"} 字段，
     * 则尝试将其作为简单条件处理。此时，会调用 {@link #parseSimpleCondition(Map, Set)}，该方法设计为仅关注简单条件
     * 相关的键，并忽略此Map中可能存在的其他键（如 {@code "connector"}）。</li>
     * <li><b>无效结构</b>: 如果Map不符合以上任何一种模式，则视为无法识别的条件结构，解析失败并返回 {@code null}。</li>
     * </ol>
     * </p>
     *
     * @param map                     条件配置Map。
     * @param requiredFieldsCollector 用于收集字段路径的 Set。
     * @return 解析后的 {@link ExecutableCondition}，或 null (解析失败)。
     */
    private ExecutableCondition compileMap(Map<String, Object> map, Set<String> requiredFieldsCollector) {
        log.trace("编译Map: {}", map);
        String connectorStr = map.get("connector") instanceof String ? (String) map.get("connector") : null;
        Object leftRaw = map.get("left");
        Object rightRaw = map.get("right");
        String operatorStr = map.get("operator") instanceof String ? (String) map.get("operator") : null;

        // 优先尝试作为标准复合条件解析
        if (StringUtils.hasText(connectorStr)) {
            boolean leftIsStructure = (leftRaw instanceof Map || leftRaw instanceof List);
            if (leftIsStructure) { // "left"是结构化子条件，表明这可能是一个标准复合条件
                return tryParseAsStandardComposite(map, connectorStr, leftRaw, rightRaw, requiredFieldsCollector);
            } else {
                // "connector"存在，但"left"不是结构化子条件（比如是个字符串字段名）。
                // 这暗示此"connector"可能是给外层列表看的元数据。尝试按简单条件解析。
                log.trace("编译Map：Map有connector '{}' 但其left操作数 '{}' (类型: {}) 不是结构化。尝试作为简单条件。",
                        connectorStr, leftRaw, (leftRaw != null ? leftRaw.getClass().getName() : "null"));
            }
        }

        // 尝试作为简单条件解析 (如果不是复合条件或复合条件尝试失败后回退)
        if (leftRaw instanceof String && StringUtils.hasText(operatorStr)) {
            log.debug("编译Map：解析为简单条件。字段: '{}', 操作符: '{}'。");
            return parseSimpleCondition(map, requiredFieldsCollector);
        }

        log.warn("编译Map：无法将Map解析为已知的条件结构（既非标准复合也非简单）。Map: {}", map);
        return null;
    }

    /**
     * 尝试将Map作为标准复合条件进行解析的辅助方法。
     * 仅当 {@code leftRaw} 是结构化条件 (Map/List) 时被 {@link #compileMap} 调用。
     */
    private ExecutableCondition tryParseAsStandardComposite(Map<String, Object> map, String connectorStr,
                                                            Object leftRaw, Object rightRaw,
                                                            Set<String> requiredFieldsCollector) {
        ExecutableCondition leftCond = parseConditionsRecursive(leftRaw, requiredFieldsCollector);
        ExecutableCondition rightCond;

        if (rightRaw == null || (rightRaw instanceof List && ((List<?>) rightRaw).isEmpty())) {
            log.trace("编译Map（标准复合）：rightRaw为null或空列表，rightCond设为ALWAYS_TRUE_CONDITION。");
            rightCond = ALWAYS_TRUE_CONDITION;
        } else {
            rightCond = parseConditionsRecursive(rightRaw, requiredFieldsCollector);
            if (rightCond == null) { // rightRaw非空/非空列表，但解析失败
                log.warn("编译Map（标准复合）：指定的rightRaw '{}' (非空/非空列表) 解析失败。复合条件Map编译失败。Map: {}", rightRaw, map);
                return null;
            }
        }

        if (leftCond == null) { // leftCond为null意味着左子条件解析失败
            log.warn("编译Map（标准复合）：leftRaw '{}' 解析失败。复合条件Map编译失败。Map: {}", leftRaw, map);
            return null;
        }

        // leftCond 和 rightCond 都已成功获取 (rightCond可能是ALWAYS_TRUE_CONDITION)
        try {
            LogicalOperator connectorOp = LogicalOperator.valueOf(connectorStr.toUpperCase());
            log.debug("编译Map：成功解析为标准复合条件，连接符: '{}'", connectorOp);
            List<ExecutableCondition> children = Arrays.asList(leftCond, rightCond); // 使用Arrays.asList创建不可变支持的列表
            return (connectorOp == LogicalOperator.OR)
                    ? new OrCondition(children) // OrCondition/AndCondition应在其构造函数中处理unmodifiableList
                    : new AndCondition(children);
        } catch (IllegalArgumentException e) {
            log.warn("编译Map：Map具有结构化的左右操作数，但其 'connector' 值 '{}' 无效。复合条件Map编译失败。Map: {}", connectorStr, map);
            return null;
        }
    }


    /**
     * 解析简单条件 (SimpleCondition) 的 Map 配置。
     * 此方法应仅关注简单条件相关的键 (left, operator, right, caseInsensitive, fieldType)，忽略其他无关的键。
     *
     * @param conditionMap            包含简单条件配置的 Map。可能含有无关键。
     * @param requiredFieldsCollector 用于收集字段路径的 Set。
     * @return 构建好的 {@link SimpleCondition} 实例，或 {@code null} (若解析失败)。
     */
    private SimpleCondition parseSimpleCondition(Map<String, Object> conditionMap, Set<String> requiredFieldsCollector) {
        String field = conditionMap.get("left") instanceof String ? (String) conditionMap.get("left") : null;
        String operatorStr = conditionMap.get("operator") instanceof String ? (String) conditionMap.get("operator") : null;
        Object valueRaw = conditionMap.get("right");
        Boolean caseInsensitive = conditionMap.get("caseInsensitive") instanceof Boolean ? (Boolean) conditionMap.get("caseInsensitive") : false;

        if (!StringUtils.hasText(field) || !StringUtils.hasText(operatorStr)) {
            log.warn("解析简单条件：缺少 'left' 字段或 'operator' 字段，或它们不是有效的字符串。Map: {}", conditionMap);
            return null;
        }
        WhereOp parsedOperator = WhereOp.fromSymbol(operatorStr);
        log.trace("解析简单条件：字段 '{}', 原始操作符 '{}', 解析后 WhereOp: {}", field, operatorStr, parsedOperator);

        if (parsedOperator == WhereOp.EQ && !operatorStr.trim().equalsIgnoreCase(WhereOp.EQ.name()) && !operatorStr.trim().equals(WhereOp.EQ.getSymbol())) {
            log.error("解析简单条件：不支持或无效的操作符 '{}' (字段 '{}')。编译失败。", operatorStr, field);
            return null;
        }
        String comparisonType = determineComparisonType(field, parsedOperator, valueRaw, conditionMap);
        if (comparisonType == null && !(parsedOperator == WhereOp.IN || parsedOperator == WhereOp.NOT_IN)) {
            log.error("解析简单条件：无法为字段 '{}' (操作符 {}) 确定比较类型。编译失败。", field, parsedOperator);
            return null;
        }
        Object parsedValue = parseConditionValue(valueRaw, comparisonType, parsedOperator);
        boolean valueIsRequired = !(parsedOperator == WhereOp.IS || parsedOperator == WhereOp.IS_NOT);
        if (parsedValue == null && valueRaw != null && valueIsRequired) {
            log.error("解析简单条件：无法解析字段 '{}' 的目标值 '{}' (比较类型 '{}', 操作符 {})。编译失败。", field, valueRaw, comparisonType, parsedOperator);
            return null;
        }
        requiredFieldsCollector.add(field);
        log.debug("成功解析简单条件: field='{}', op={}, val='{}', type='{}', ignoreCase={}", field, parsedOperator, parsedValue, comparisonType, caseInsensitive);
        return new SimpleCondition(field, parsedOperator, parsedValue, comparisonType, caseInsensitive);
    }

    /**
     * 推断用于比较的字段类型。
     * 优先级：显式fieldType -> 操作符强制 -> 字段已知类型 -> 运行时值Java类型 -> 运行时值格式 -> 默认STRING。
     *
     * @param fieldPath    字段路径 (用于查询已知类型)。
     * @param operator     操作符 (可能影响类型推断)。
     * @param rawValue     条件配置中的原始目标值。
     * @param conditionMap 条件配置的Map，用于检查明确的 "fieldType"。
     * @return 推断出的比较类型 ("NUMERIC", "STRING", "BOOLEAN", "SET_STRING", "SET_LONG") 或 null (用于IN/NOT_IN)。
     */
    private String determineComparisonType(String fieldPath, WhereOp operator, Object rawValue, Map<String, Object> conditionMap) {
        // 0. 显式指定的fieldType
        if (conditionMap.get("fieldType") instanceof String) {
            String explicitFieldType = (String) conditionMap.get("fieldType");
            if (StringUtils.hasText(explicitFieldType)) {
                log.debug("字段 '{}': 使用明确指定的 fieldType '{}'", fieldPath, explicitFieldType.toUpperCase());
                return explicitFieldType.toUpperCase();
            }
        }
        // 1. 操作符强制的类型
        if (EnumSet.of(WhereOp.GT, WhereOp.LT, WhereOp.GTE, WhereOp.LTE).contains(operator)) return "NUMERIC";
        if (EnumSet.of(WhereOp.REGEX, WhereOp.LIKE, WhereOp.NOT_LIKE, WhereOp.START_WITH, WhereOp.END_WITH).contains(operator))
            return "STRING";
        if (operator == WhereOp.IN || operator == WhereOp.NOT_IN) return null; // 由parseConditionValue决定Set的具体类型

        // 2. 字段的已知元数据类型
        DataType knownType = DCSpanFieldTypeResolver.getKnownType(fieldPath);
        log.trace("字段 '{}': 已知DCSpan类型为 '{}'。", fieldPath, knownType);
        if (knownType != null && knownType != DataType.UNKNOWN && knownType != DataType.OBJECT &&
                knownType != DataType.MAP_STRING_STRING && knownType != DataType.LIST_STRING) {
            switch (knownType) {
                case BOOLEAN:
                    return "BOOLEAN";
                case LONG:
                case INTEGER:
                case DOUBLE:
                case NUMERIC:
                    return "NUMERIC";
                case STRING:
                    return "STRING";
            }
        }
        // 3. 目标值的运行时Java类型
        if (rawValue instanceof Boolean) return "BOOLEAN";
        if (rawValue instanceof BigDecimal || rawValue instanceof Number)
            return "NUMERIC"; // Number includes Double, Float etc.
        // 4. 目标值 (String) 的格式推断
        if (rawValue instanceof String) {
            String sVal = ((String) rawValue).trim();
            if (sVal.equalsIgnoreCase("true") || sVal.equalsIgnoreCase("false")) return "BOOLEAN";
            try {
                new BigDecimal(sVal);
                return "NUMERIC";
            } catch (NumberFormatException e) { /* ignore */ }
        }
        // 5. 默认STRING
        log.trace("字段 '{}': 无法精确推断类型，默认比较类型为 STRING。", fieldPath);
        return "STRING";
    }

    /**
     * 解析条件配置中的目标值，将其转换为适当的Java类型以进行比较。
     *
     * @param value              原始目标值 (来自配置)。
     * @param comparisonTypeHint 推断出的比较类型 (对IN/NOT_IN可能为null，表示由值本身决定是SET_STRING还是SET_LONG)。
     * @param operator           操作符 (用于区分IN/NOT_IN 和其他)。
     * @return 解析后的Java对象 (BigDecimal, Boolean, String, Set&lt;Long&gt;, Set&lt;String&gt;)。
     * 如果解析失败，对于IN/NOT_IN返回空Set，其他操作符返回null。
     */
    private Object parseConditionValue(Object value, String comparisonTypeHint, WhereOp operator) {
        // IS NULL/IS NOT NULL 操作符的目标值本身就是null，这是预期的。
        if (value == null && (operator == WhereOp.IS || operator == WhereOp.IS_NOT)) return null;
        // 对于其他操作符，如果目标值（value）本身为null，则解析结果也为null。
        if (value == null) {
            log.trace("解析条件值：原始值为null，操作符为{}。返回null。", operator);
            return null;
        }
        try {
            if (operator == WhereOp.IN || operator == WhereOp.NOT_IN) {
                return parseSetConditionValue(value, comparisonTypeHint);
            } else { // 标量值处理
                String type = comparisonTypeHint != null ? comparisonTypeHint : "STRING"; // 如果推断不出类型，默认按字符串
                return parseScalarConditionValue(value, type);
            }
        } catch (Exception e) { // 捕获所有子解析方法的异常
            log.warn("解析条件目标值 '{}' (类型提示 '{}', 操作符 '{}') 时发生错误: {}", value, comparisonTypeHint, operator, e.getMessage());
            return (operator == WhereOp.IN || operator == WhereOp.NOT_IN) ? Collections.emptySet() : null;
        }
    }

    /**
     * 解析用于 IN / NOT IN 操作符的目标值，将其转换为 {@code Set<String>} 或 {@code Set<Long>}。
     * 输入值 {@code value} 可以是 {@link Collection}，也可以是表示JSON数组的 {@link String}。
     * {@code comparisonTypeHint} 可以是 "SET_STRING", "SET_LONG", 或 null (此时会尝试从数据推断)。
     *
     * @param value              原始目标值。
     * @param comparisonTypeHint 类型提示。
     * @return 解析后的Set。如果解析失败或输入无效，返回空Set。
     */
    private Set<?> parseSetConditionValue(Object value, String comparisonTypeHint) {
        Collection<?> valueCollection = null;
        String jsonArrayStr = null;
        if (value instanceof Collection) {
            valueCollection = (Collection<?>) value;
        } else if (value instanceof String) {
            jsonArrayStr = ((String) value).trim();
            if (!jsonArrayStr.startsWith("[")) jsonArrayStr = "[" + jsonArrayStr; // 简单规范化
            if (!jsonArrayStr.endsWith("]")) jsonArrayStr = jsonArrayStr + "]";
        } else {
            log.warn("IN/NOT IN 操作符的目标值类型 '{}' 不支持。值: {}. 返回空Set。", value.getClass().getName(), value);
            return Collections.emptySet();
        }
        boolean isLongSet = determineSetElementType(comparisonTypeHint, valueCollection, jsonArrayStr);
        log.trace("解析Set条件值：推断/指定元素类型为 {}.", (isLongSet ? "Long" : "String"));
        try {
            return jsonArrayStr != null ? parseSetFromJson(jsonArrayStr, isLongSet) : parseSetFromCollection(valueCollection, isLongSet);
        } catch (Exception e) { // JsonProcessingException from parseSetFromJson
            log.warn("解析Set条件值时出错 (目标元素类型: {}, 输入: {}): {}", (isLongSet ? "SET_LONG" : "SET_STRING"), value, e.getMessage());
            return Collections.emptySet();
        }
    }

    /**
     * 解析标量（非集合）条件值。
     *
     * @param value                  原始值。
     * @param resolvedComparisonType 确定的比较类型 ("NUMERIC", "BOOLEAN", "STRING")。
     * @return 解析后的Java对象 ({@link BigDecimal}, {@link Boolean}, {@link String})，失败则返回 {@code null}。
     */
    private Object parseScalarConditionValue(Object value, String resolvedComparisonType) {
        log.trace("解析标量条件值：原始值 '{}' (类型: {}), 期望比较类型: {}", value, (value != null ? value.getClass().getSimpleName() : "null"), resolvedComparisonType);
        if (value == null) return null;

        switch (resolvedComparisonType) {
            case "NUMERIC":
                if (value instanceof BigDecimal) return value;
                if (value instanceof Number) return new BigDecimal(value.toString()); // 用toString避免精度问题
                try {
                    String sVal = String.valueOf(value).trim();
                    return sVal.isEmpty() ? null : new BigDecimal(sVal);
                } catch (NumberFormatException e) {
                    log.warn("无法将标量值 '{}' 解析为 NUMERIC (BigDecimal)。", value);
                    return null;
                }
            case "BOOLEAN":
                if (value instanceof Boolean) return value;
                String sValBool = String.valueOf(value).trim().toLowerCase();
                if ("true".equals(sValBool)) return Boolean.TRUE;
                if ("false".equals(sValBool)) return Boolean.FALSE;
                try {
                    return new BigDecimal(sValBool).compareTo(BigDecimal.ZERO) != 0;
                } // 数字转布尔
                catch (NumberFormatException e) {
                    log.warn("无法将标量值 '{}' 解析为 BOOLEAN (true/false或数字)。", value);
                    return null;
                }
            case "STRING":
            default:
                return String.valueOf(value); // 对于STRING，null会变成"null"
        }
    }

    /**
     * 辅助方法：确定IN/NOT_IN的Set元素类型是Long还是String。
     * 优先级：显式comparisonTypeHint ("SET_LONG"/"SET_STRING") > 根据第一个非空元素推断 > 默认String。
     *
     * @param comparisonTypeHint 用户指定的类型 ("SET_LONG", "SET_STRING", 或 null)。
     * @param valueCollection    值的Collection形式 (如果可用)。
     * @param jsonArrayStr       值的JSON字符串形式 (如果可用)。
     * @return {@code true} 如果应该是Set&lt;Long&gt;，{@code false} 如果应该是Set&lt;String&gt;。
     */
    private boolean determineSetElementType(String comparisonTypeHint, Collection<?> valueCollection, String jsonArrayStr) {
        if ("SET_LONG".equalsIgnoreCase(comparisonTypeHint)) return true;
        if ("SET_STRING".equalsIgnoreCase(comparisonTypeHint)) return false;
        Object firstElement = findFirstNonNullElement(valueCollection, jsonArrayStr);
        if (firstElement != null) {
            if (firstElement instanceof Number) return true; // 数字类型，倾向于Long
            if (firstElement instanceof String) { // 尝试将字符串解析为数字
                try {
                    new BigDecimal(((String) firstElement).trim());
                    return true;
                } catch (NumberFormatException e) {
                    return false; /* 解析失败，则为String */
                }
            }
        }
        log.trace("无法从元素或类型提示 ('{}') 推断Set元素类型，默认为String。", comparisonTypeHint);
        return false; // 默认String
    }

    /**
     * 辅助方法：从Collection或JSON字符串中查找第一个非null元素，用于类型推断。
     *
     * @param valueCollection 值的Collection形式。
     * @param jsonArrayStr    值的JSON字符串形式。
     * @return 第一个非null元素，如果都为空或找不到则返回null。
     */
    private Object findFirstNonNullElement(Collection<?> valueCollection, String jsonArrayStr) {
        if (valueCollection != null && !valueCollection.isEmpty()) {
            return valueCollection.stream().filter(Objects::nonNull).findFirst().orElse(null);
        } else if (StringUtils.hasText(jsonArrayStr)) {
            try {
                List<Object> tempList = objectMapper.readValue(jsonArrayStr, new TypeReference<List<Object>>() {
                });
                return tempList.stream().filter(Objects::nonNull).findFirst().orElse(null);
            } catch (JsonProcessingException e) {
                log.warn("为类型推断解析JSON数组时出错: '{}'", jsonArrayStr, e);
            }
        }
        return null;
    }

    /**
     * 辅助方法：将JSON数组字符串解析为Set。
     *
     * @param jsonArrayStr JSON数组格式的字符串。
     * @param isLongSet    指示是否应解析为 {@code Set<Long>} (true) 或 {@code Set<String>} (false)。
     * @return 解析后的Set，失败则返回空Set。
     * @throws JsonProcessingException 如果JSON解析失败。
     */
    private Set<?> parseSetFromJson(String jsonArrayStr, boolean isLongSet) throws JsonProcessingException {
        List<Object> tempList = objectMapper.readValue(jsonArrayStr, new TypeReference<List<Object>>() {
        });
        return isLongSet ?
                tempList.stream().map(this::tryParseLong).filter(Objects::nonNull).collect(Collectors.toSet()) :
                tempList.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toSet());
    }

    /**
     * 辅助方法：将Collection解析为Set。
     *
     * @param valueCollection 输入的Collection。
     * @param isLongSet       指示是否应转换为 {@code Set<Long>} (true) 或 {@code Set<String>} (false)。
     * @return 解析后的Set。
     */
    private Set<?> parseSetFromCollection(Collection<?> valueCollection, boolean isLongSet) {
        return isLongSet ?
                valueCollection.stream().map(this::tryParseLong).filter(Objects::nonNull).collect(Collectors.toSet()) :
                valueCollection.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toSet());
    }

    /**
     * 辅助方法：尝试将对象转换为Long。支持Number类型和可以精确解析为Long的String（包括带".0"后缀的）。
     *
     * @param obj 要转换的对象。
     * @return 转换后的Long值，如果无法转换、输入为null或空字符串，或字符串无法精确表示Long，则返回null。
     */
    private Long tryParseLong(Object obj) {
        if (obj == null) return null;
        if (obj instanceof Long) return (Long) obj;
        if (obj instanceof Number) return ((Number) obj).longValue(); // 处理Integer, Double等
        if (obj instanceof String) {
            String s = ((String) obj).trim();
            if (s.isEmpty()) return null;
            try { // 去除可能的 ".0" 后缀 (来自浮点数字符串)
                if (s.endsWith(".0")) s = s.substring(0, s.length() - 2);
                if (s.isEmpty()) return null; // 如果原先是 ".0"
                return new BigDecimal(s).longValueExact(); // 要求是整数值,会因非整数抛ArithmeticException
            } catch (NumberFormatException | ArithmeticException e) {
                log.trace("无法将字符串 '{}' 解析为精确的 Long: {}", obj, e.getMessage());
                return null;
            }
        }
        return null; // 不支持的类型
    }
}