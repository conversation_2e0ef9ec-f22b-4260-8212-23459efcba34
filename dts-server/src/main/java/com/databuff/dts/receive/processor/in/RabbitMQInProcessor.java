package com.databuff.dts.receive.processor.in;

import com.databuff.dts.config.RefreshScopeConfig;
import com.databuff.service.ServiceSyncService;

import static com.databuff.common.constants.Constant.Trace.*;

public class RabbitMQInProcessor extends MQInProcessor {

    public static final String GEN_QUEUE = "amq.gen";

    public RabbitMQInProcessor(ServiceSyncService serviceSyncService, RefreshScopeConfig refreshScopeConfig) {
        super(serviceSyncService, TRACE_NAME_IN_RABBITMQ_CONSUME, TRACE_RABBITMQ_TOPIC, TRACE_RABBITMQ_BROKER, TRACE_COMPONENT_RABBITMQ, refreshScopeConfig);
    }
}
