package com.databuff.dts.receive.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.constants.Constant;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.dts.receive.service.Agent2KafkaService;
import com.databuff.dts.ringbuffer.CustomRingBuffer;
import com.databuff.dts.ringbuffer.Event;
import com.databuff.dts.ringbuffer.ParseTrace;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

import static com.databuff.common.constants.MetricName.RINGBUFFER_FULL;

/**
 * @author:TianMing
 * @date: 2021/11/29
 * @time: 11:09
 */
@Service
@Slf4j
public class Agent2KafkaServiceImpl implements Agent2KafkaService {

    private static final ThreadLocal<byte[]> bufferThreadLocal = ThreadLocal.withInitial(() -> new byte[1024 * 500]);

    @Resource
    private TraceReceiver traceReceiver;

    @Autowired
    private CustomRingBuffer<Event> decompressRingBuffer;

    @Autowired
    private CustomRingBuffer<ParseTrace> parseRingBuffer;

    /**
     * 获取头信息
     */
    private static final String[] HEADER_KEYS = {Constant.CONTENT_TYPE, Constant.AGENT_HOSTNAME, Constant.AGENT_HOSTNAME2, Constant.API_KEY, Constant.API_KEY2, Constant.RUM_API_KEY, Constant.USER_AGENT, Constant.SEC_CH_UA, Constant.HOST_ID, Constant.CLUSTER_ID, Constant.IS_K8S};

    @Override
    public void agentCollect(HttpServletRequest req) {
        publishEvent("agentCollect", req);
    }

    @Override
    public void agentCheckRun(HttpServletRequest req) {
        //v2.7.2版本 agentValidate接口调用频率改为1分一调，更新agent列表替代此功能，这里就不再操作
    }

    @Override
    public void agentCollector(HttpServletRequest req) {
        publishEvent("agentCollector", req);
    }

    @Override
    public void agentTraces(HttpServletRequest req) {
        publishEvent("agentTraces", req);
    }

    @Override
    public void agentV04Traces(HttpServletRequest req) {
        publishEvent("agentV04Traces", req);
    }

    @Override
    public void agentLogs(HttpServletRequest req) {
        publishEvent("agentLogs", req);
    }

    @Override
    public void agentK8s(HttpServletRequest req) {
        publishEvent("agentK8s", req);
    }

    @Override
    public void agentNpms(HttpServletRequest req, JSONObject data) {
        publishEvent("agentNpms", req, data);
    }

    @Override
    public void agentProfiling(HttpServletRequest req, JSONObject data) {
        publishEvent("agentProfiling", req, data);
    }

    @Override
    public void agentNpmReqs(HttpServletRequest req, JSONObject data) {
        publishEvent("agentNpmReqs", req, data);
    }

    private void publishEvent(String agentTraces, HttpServletRequest req) {
        if (agentTraces.endsWith("Traces")) {
            if ((double) (decompressRingBuffer.getRingBuffer().getCursor() - decompressRingBuffer.getRingBuffer().getMinimumGatingSequence()) / decompressRingBuffer.getRingBuffer().getBufferSize() > 0.9) {
                Map<String, String> tags = new HashMap<>();
                tags.put("type", "decompress");
                tags.put("api", agentTraces);
                OtelMetricUtil.logCounter(RINGBUFFER_FULL, tags, 1);
                return;
            }

            try (InputStream inputStream = req.getInputStream(); ByteArrayOutputStream byteStream = new ByteArrayOutputStream(1024 * 1024)) {
                byte[] buffer = bufferThreadLocal.get();
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    byteStream.write(buffer, 0, bytesRead);
                }
                boolean success = decompressRingBuffer.getRingBuffer().tryPublishEvent((event, sequence) -> {
                    // 将数据放入事件对象中
                    event.setType(agentTraces);
                    event.setEncoding(req.getHeader("Content-Encoding"));
                    event.setHeaderMap(traceReceiver.getHeaders(req, HEADER_KEYS));
                    event.setData(byteStream.toByteArray());
                });
                if (success) {
                    // 事件成功发布到 RingBuffer 中
                } else {
                    Map<String, String> tags = new HashMap<>();
                    tags.put("type", "decompress");
                    tags.put("api", agentTraces);
                    OtelMetricUtil.logCounter(RINGBUFFER_FULL, tags, 1);
                }
            } catch (IOException e) {
                log.error("--agent traces receive error-{}", e);
            }
        } else {
            try (InputStream inputStream = req.getInputStream(); ByteArrayOutputStream byteStream = new ByteArrayOutputStream(1024 * 1024)) {
                byte[] buffer = bufferThreadLocal.get();
                int bytesRead;

                int type;

                if (agentTraces.equals("agentK8s")) {
                    // 读取前16个字节，假设你需要计算 `type` 基于这16个字节
                    byte[] first16Bytes = new byte[16];
                    int first16BytesRead = inputStream.read(first16Bytes, 0, 16);
                    if (first16BytesRead == -1) {
                        throw new IOException("Input stream is too short to read 16 bytes.");
                    }
                    // 计算 type - 这里可以自定义计算方法，取第 3 个字节作为 type
                    type = (int) first16Bytes[2]; // 假设使用第3个字节来确定type
                } else {
                    type = 0;
                }

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    byteStream.write(buffer, 0, bytesRead);
                }
                decompressRingBuffer.getRingBuffer().publishEvent((event, sequence) -> {
                    // 将数据放入事件对象中
                    event.setType(agentTraces);
                    event.setEncoding(req.getHeader("Content-Encoding"));
                    event.setHeaderMap(traceReceiver.getHeaders(req, HEADER_KEYS));
                    event.setData(byteStream.toByteArray());
                    event.setDataType(type);
                });
            } catch (IOException e) {
                log.error("--agent traces receive error-{}", e);
            }
        }
    }

    private void publishEvent(String agentTraces, HttpServletRequest req, JSONObject data) {
        Map<String, Object> headerMap = traceReceiver.getHeaders(req, HEADER_KEYS);
        if (agentTraces.equals("agentNpms")) {
            headerMap.put("npmDataType", 1);
        } else if (agentTraces.equals("agentNpmReqs")) {
            headerMap.put("npmDataType", 2);
        }

        parseRingBuffer.getRingBuffer().publishEvent((event1, sequence) -> {
            // 将数据放入事件对象中
            event1.setType(agentTraces);
            event1.setHeaderMap(headerMap);
            event1.setData(data);
            event1.setDataLength(data.size());
            event1.setHeaderMap(headerMap);
        });
    }

}
