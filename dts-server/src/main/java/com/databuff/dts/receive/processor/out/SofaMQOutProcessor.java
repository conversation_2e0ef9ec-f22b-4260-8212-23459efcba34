package com.databuff.dts.receive.processor.out;

import com.databuff.dts.config.RefreshScopeConfig;
import com.databuff.dts.receive.service.ComponentInstanceService;
import com.databuff.service.JedisService;
import com.databuff.service.ServiceSyncService;

import static com.databuff.common.constants.Constant.Trace.*;

public class SofaMQOutProcessor extends MQOutProcessor {

    public SofaMQOutProcessor(ServiceSyncService serviceSyncService, ComponentInstanceService componentInstanceService, JedisService jedisService, RefreshScopeConfig refreshScopeConfig) {
        super(serviceSyncService, componentInstanceService, jedisService, TRACE_NAME_OUT_SOFAMQ_PRODUCER, TRACE_TOPIC, TRACE_BROKER_NAME, TRACE_COMPONENT_SOFAMQ, refreshScopeConfig);
    }
}
