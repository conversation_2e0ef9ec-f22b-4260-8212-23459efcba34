package com.databuff.dts.receive.service.impl;

import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.dts.receive.service.Rum2KafkaService;
import com.databuff.dts.ringbuffer.CustomRingBuffer;
import com.databuff.dts.ringbuffer.ParseTrace;
import com.databuff.dts.util.IpAdrressUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static com.databuff.common.constants.Constant.CONTENT_LENGTH;
import static com.databuff.common.constants.MetricName.*;


@Service
@Slf4j
public class Rum2KafkaServiceImpl implements Rum2KafkaService {

    @Autowired
    private CustomRingBuffer<ParseTrace> parseRingBuffer;

    @Override
    public void rumTraces(HttpServletRequest req) {
        Map<String, Object> headerMap = new HashMap<>();
        String ip = IpAdrressUtil.getIpAdrress(req);
        headerMap.put("ip", ip);
        headerMap.put(CONTENT_LENGTH,  req.getContentLength());

        ParseTrace event = new ParseTrace();
        event.setType("rumTraces");
        //ResourceSpansWrapper
        try {
            byte[] cachedBody = (byte[]) req.getAttribute("cachedBody");
            byte[] bodyBytes;
            if (cachedBody == null) {
                 bodyBytes = IOUtils.toByteArray(req.getInputStream());
            }else {
                 bodyBytes = cachedBody;
            }

            event.setData(bodyBytes);
        } catch (IOException e) {
            log.error("Rum2KafkaServiceImpl rumTraces", e);
            OtelMetricUtil.logCounter(RUM_TRACE_DISCARD, 1);
            return;
        }
        event.setHeaderMap(headerMap);

        boolean success = parseRingBuffer.getRingBuffer().tryPublishEvent((e, sequence) -> {
            e.setType(event.getType());
            e.setData(event.getData());
            e.setHeaderMap(event.getHeaderMap());
        });

        if (success) {
            // 事件成功发布到 RingBuffer 中
        } else {
            Map<String, String> tags = new HashMap<>();
            tags.put("type", "rum_trace");
            tags.put("api", "rum");
            OtelMetricUtil.logCounter(RINGBUFFER_FULL, tags, 1);
        }

    }


    @Override
    public void rumLogs(HttpServletRequest req){
        Map<String, Object> headerMap = new HashMap<>();
        String ip = IpAdrressUtil.getIpAdrress(req);
        headerMap.put("ip", ip);
        headerMap.put(CONTENT_LENGTH,  req.getContentLength());

        ParseTrace event = new ParseTrace();
        event.setType("rumLogs");
        //ResourceLogWrapper
        try {
            byte[] cachedBody = (byte[]) req.getAttribute("cachedBody");
            byte[] bodyBytes;
            if (cachedBody == null) {
                bodyBytes = IOUtils.toByteArray(req.getInputStream());
            }else {
                bodyBytes = cachedBody;
            }

            event.setData(bodyBytes);
        } catch (IOException e) {
            log.error("Rum2KafkaServiceImpl rumLogs", e);
            OtelMetricUtil.logCounter(RUM_TRACE_DISCARD, 1);
            return;
        }
        event.setHeaderMap(headerMap);

        boolean success = parseRingBuffer.getRingBuffer().tryPublishEvent((e, sequence) -> {
            e.setType(event.getType());
            e.setData(event.getData());
            e.setHeaderMap(event.getHeaderMap());
        });

        if (success) {
            // 事件成功发布到 RingBuffer 中
        } else {
            Map<String, String> tags = new HashMap<>();
            tags.put("type", "rum_log");
            tags.put("api", "rum");
            OtelMetricUtil.logCounter(RINGBUFFER_FULL, tags, 1);
        }

    }


    @Override
    public void rumIosTraces(HttpServletRequest req) {
        Map<String, Object> headerMap = new HashMap<>();
        headerMap.put(CONTENT_LENGTH,  req.getContentLength());

        ParseTrace event = new ParseTrace();
        event.setType("rumIosTraces");
        //ResourceSpansWrapper
        try {
            event.setData(IOUtils.toByteArray(req.getInputStream()));
        } catch (IOException e) {
            log.error("Rum2KafkaServiceImpl rumIosTraces", e);
            OtelMetricUtil.logCounter(RUM_IOS_TRACE_DISCARD, 1);
            return;
        }
        event.setHeaderMap(headerMap);

        boolean success = parseRingBuffer.getRingBuffer().tryPublishEvent((e, sequence) -> {
            e.setType(event.getType());
            e.setData(event.getData());
            e.setHeaderMap(event.getHeaderMap());
        });

        if (success) {
            // 事件成功发布到 RingBuffer 中
        } else {
            Map<String, String> tags = new HashMap<>();
            tags.put("type", "rum_ios_trace");
            tags.put("api", "rum");
            OtelMetricUtil.logCounter(RINGBUFFER_FULL, tags, 1);
        }

    }


    @Override
    public void rumIosLogs(HttpServletRequest req){
        Map<String, Object> headerMap = new HashMap<>();
        headerMap.put(CONTENT_LENGTH,  req.getContentLength());

        ParseTrace event = new ParseTrace();
        event.setType("rumIosLogs");
        //ResourceLogWrapper
        try {
            event.setData(IOUtils.toByteArray(req.getInputStream()));
        } catch (IOException e) {
            log.error("Rum2KafkaServiceImpl rumIosLogs", e);
            OtelMetricUtil.logCounter(RUM_IOS_TRACE_DISCARD, 1);
            return;
        }
        event.setHeaderMap(headerMap);

        boolean success = parseRingBuffer.getRingBuffer().tryPublishEvent((e, sequence) -> {
            e.setType(event.getType());
            e.setData(event.getData());
            e.setHeaderMap(event.getHeaderMap());
        });

        if (success) {
            // 事件成功发布到 RingBuffer 中
        } else {
            Map<String, String> tags = new HashMap<>();
            tags.put("type", "rum_ios_log");
            tags.put("api", "rum");
            OtelMetricUtil.logCounter(RINGBUFFER_FULL, tags, 1);
        }

    }

    @Override
    public void rumAndroidTraces(HttpServletRequest req) {
        Map<String, Object> headerMap = new HashMap<>();
        headerMap.put(CONTENT_LENGTH, req.getContentLength());

        ParseTrace event = new ParseTrace();
        event.setType("rumAndroidTraces");
        try {
            event.setData(IOUtils.toByteArray(req.getInputStream()));
        } catch (IOException e) {
            log.error("Rum2KafkaServiceImpl rumAndroidTraces", e);
            OtelMetricUtil.logCounter(RUM_ANDROID_TRACE_DISCARD, 1);
            return;
        }
        event.setHeaderMap(headerMap);

        boolean success = parseRingBuffer.getRingBuffer().tryPublishEvent((e, sequence) -> {
            e.setType(event.getType());
            e.setData(event.getData());
            e.setHeaderMap(event.getHeaderMap());
        });

        if (!success) {
            Map<String, String> tags = new HashMap<>();
            tags.put("type", "rum_android_trace");
            tags.put("api", "rum");
            OtelMetricUtil.logCounter(RINGBUFFER_FULL, tags, 1);
        }
    }

    @Override
    public void rumAndroidLogs(HttpServletRequest req) {
        Map<String, Object> headerMap = new HashMap<>();
        headerMap.put(CONTENT_LENGTH, req.getContentLength());

        ParseTrace event = new ParseTrace();
        event.setType("rumAndroidLogs");
        try {
            event.setData(IOUtils.toByteArray(req.getInputStream()));
        } catch (IOException e) {
            log.error("Rum2KafkaServiceImpl rumAndroidLogs", e);
            OtelMetricUtil.logCounter(RUM_ANDROID_LOG_DISCARD, 1);
            return;
        }
        event.setHeaderMap(headerMap);

        boolean success = parseRingBuffer.getRingBuffer().tryPublishEvent((e, sequence) -> {
            e.setType(event.getType());
            e.setData(event.getData());
            e.setHeaderMap(event.getHeaderMap());
        });

        if (!success) {
            Map<String, String> tags = new HashMap<>();
            tags.put("type", "rum_android_log");
            tags.put("api", "rum");
            OtelMetricUtil.logCounter(RINGBUFFER_FULL, tags, 1);
        }
    }



}
