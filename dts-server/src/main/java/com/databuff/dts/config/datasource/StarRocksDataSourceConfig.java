package com.databuff.dts.config.datasource;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
@MapperScan(basePackages = "com.databuff.dao.starrocks", annotationClass = Mapper.class, sqlSessionFactoryRef = "starrocksSqlSessionFactory")
@ConfigurationProperties(prefix = "spring.olap")
public class StarRocksDataSourceConfig {

    private String driverClassName;
    private String url;
    private String username;
    private String password;

    @Bean
    public DataSource starrocksDataSource() {
        log.info("自定义了动态数据源配置类DataSourceConfig，需排除自动配置数据源DataSourceAutoConfiguration");
        log.info("启动类添加：@SpringBootApplication(exclude={DataSourceAutoConfiguration.class})");
        DruidDataSource druidDataSource = DruidDataSourceBuilder.create().build();
        druidDataSource.setUsername(username);
        druidDataSource.setPassword(password);
        druidDataSource.setQueryTimeout(300);
        druidDataSource.setUrl(url);
        druidDataSource.setValidationQuery("show status;");
        druidDataSource.setDriverClassName(driverClassName);

        return druidDataSource;
    }

    @Bean
    public SqlSessionFactory starrocksSqlSessionFactory(@Qualifier("starrocksDataSource") DataSource starrocksDataSource) {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(starrocksDataSource);
        //添加XML目录
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();

        return getSqlSessionFactory(bean, resolver);
    }

    private SqlSessionFactory getSqlSessionFactory(SqlSessionFactoryBean bean, ResourcePatternResolver resolver) {
        final String mapperPath = "classpath*:mapper/starrocks/*.xml";
        try {
            org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
            configuration.setMapUnderscoreToCamelCase(true);
            configuration.setUseColumnLabel(true);
            log.info("starrocks定义mapper文件存储的路径为：" + mapperPath);
            bean.setMapperLocations(resolver.getResources(mapperPath));
            bean.setConfiguration(configuration);

            return bean.getObject();
        } catch (Exception ex) {
            log.error("获取SqlSessionFactory时出现异常：", ex);
            throw new RuntimeException(ex);
        }
    }

    public String getDriverClassName() {
        return driverClassName;
    }

    public void setDriverClassName(String driverClassName) {
        this.driverClassName = driverClassName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

}