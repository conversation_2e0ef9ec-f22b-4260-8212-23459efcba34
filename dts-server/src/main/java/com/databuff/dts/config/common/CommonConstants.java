package com.databuff.dts.config.common;

import com.google.common.collect.ImmutableSet;

/**
 * 通用常量
 *
 * @Package com.databuff.config.common
 * @company: dacheng
 * @Author: zlh
 * @CreateDate: 2020/5/27
 */
public class CommonConstants {

    public final static String REQUEST_URI_V03_TRACE = "/v0.3/traces";
    public final static String REQUEST_URI_V04_TRACE = "/v0.4/traces";
    public final static String REQUEST_URI_V05_TRACE = "/v0.4/traces";
    public final static String REQUEST_URI_CONFIG_SYNC = "/api/v1/config/sync";
    public final static String REQUEST_URI_UPDATE_LIST = "/api/v1/update/list";
    public final static String REQUEST_URI_AGENT_VALIDATE = "/api/v1/validate";
    public final static String REQUEST_URI_JAVA_AGENT_INIT = "/api/javaagent/init";
    public final static String REQUEST_URI_PROFILING = "/api/v1/profiling";
    public final static String REQUEST_URI_PROFILING_DOWNLOAD = "/v1/profiling/download";
    public final static String REQUEST_RUM = "/rum/v1/traces";
    public final static String REQUEST_RUM_LOGS = "/rum/v1/logs";
    public final static String REQUEST_IOS_RUM_CONFIG = "/rum/v1/ios/config";
    public final static String REQUEST_IOS_RUM = "/rum/v1/ios/traces";
    public final static String REQUEST_IOS_RUM_LOGS = "/rum/v1/ios/logs";
    public final static String REQUEST_ANDROID_RUM_CONFIG = "/rum/v1/android/config";
    public final static String REQUEST_ANDROID_RUM = "/rum/v1/android/traces";
    public final static String REQUEST_ANDROID_RUM_LOGS = "/rum/v1/android/logs";

    public static final ImmutableSet<String> RUM_ENDPOINTS = ImmutableSet.of(
            REQUEST_RUM,
            REQUEST_RUM_LOGS,
            REQUEST_IOS_RUM_CONFIG,
            REQUEST_IOS_RUM,
            REQUEST_IOS_RUM_LOGS,
            REQUEST_ANDROID_RUM_CONFIG,
            REQUEST_ANDROID_RUM,
            REQUEST_ANDROID_RUM_LOGS
    );



    public final static String TAG_AGENT_HOST = "agentHost";
}
