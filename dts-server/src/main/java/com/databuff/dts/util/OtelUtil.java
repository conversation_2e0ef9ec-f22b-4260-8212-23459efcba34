package com.databuff.dts.util;

import com.databuff.common.utils.OtelMetricUtil;

import java.util.HashMap;
import java.util.Map;

import static com.databuff.common.constants.MetricName.DATA_DELAY;

public class OtelUtil {

    public static void logDataDelay(String hostName, String dataType, long delayMs) {
        Map<String, String> tags = new HashMap<>();
        tags.put("hostName", hostName);
        tags.put("dataType", dataType);
        OtelMetricUtil.logHistogram(DATA_DELAY, tags, delayMs);
    }
}
