package com.databuff.dts.util;


import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * @author: wuxin
 * @data: 2020/12/3
 */
public class CommandLineUtils {

    public static void exec(String[] cmds, long waitMillions){
        String cmdDesc = Arrays.toString(cmds);
        try {
            Process exec = Runtime.getRuntime().exec(cmds);
            if (!isExpectedExit(exec, waitMillions, 0)){
                throw new CommandLineException("命令行["+ cmdDesc +"]执行失败:返回值["+exec.exitValue()+"]", cmdDesc);
            }
        } catch (Exception e){
            throw new CommandLineException("命令行["+cmdDesc+"]执行失败", e, cmdDesc);
        }
    }

    public static void exec(String command, long waitMillions) {
        try {
            Process exec = Runtime.getRuntime().exec(command);
            if (!isExpectedExit(exec, waitMillions, 0)){
                throw new CommandLineException("命令行["+ command +"]执行失败:返回值["+exec.exitValue()+"]", command);
            }
        } catch (Exception e){
            throw new CommandLineException("命令行["+command+"]执行失败", e, command);
        }
    }

    public static boolean isExpectedExit(Process exec, long waitMillions,int expected){
        waitUninterrupted(exec, waitMillions);
        if (exec.exitValue() != expected){
            return false;
        }
        return true;
    }

    public static void waitUninterrupted(Process exec, long waitMillions){
        while ((waitMillions=waitFor(exec, waitMillions)) != 0){
            waitFor(exec, waitMillions);
        }
    }

    public static long waitFor(Process exec, long waitMillions){
        long startTime = System.currentTimeMillis();
        try {
            exec.waitFor(waitMillions, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            return waitMillions-(System.currentTimeMillis()-startTime);
        }
        return 0;
    }

    public static class CommandLineException extends RuntimeException{
        private String command;

        public CommandLineException(String command) {
            this.command = command;
        }

        public CommandLineException(String message, String command) {
            super(message);
            this.command = command;
        }

        public CommandLineException(String message, Throwable cause, String command) {
            super(message, cause);
            this.command = command;
        }

        public CommandLineException(Throwable cause, String command) {
            super(cause);
            this.command = command;
        }

        public CommandLineException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace, String command) {
            super(message, cause, enableSuppression, writableStackTrace);
            this.command = command;
        }
    }
}
