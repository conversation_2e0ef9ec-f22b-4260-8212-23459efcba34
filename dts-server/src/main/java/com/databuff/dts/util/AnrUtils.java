package com.databuff.dts.util;

/**
 * ANR (Application Not Responding) 日志工具类
 * <p>
 * 核心功能: 重排ANR日志格式,将主线程堆栈提取并置于日志开头位置, 前端界面优先显示主线程
 */
public class AnrUtils {

    public static String convertAnrTraceFormat(String trace, String systemFileName) {
        return "systemFileName: " + systemFileName + "\n" + convertAnrTraceFormat(trace);
    }

    /**
     * 重排ANR堆栈格式,将主线程堆栈置于最前
     * <p>
     * 处理步骤:
     * 1. 找到并提取主线程号
     * 2. 定位主线程的完整堆栈信息
     * 3. 将主线程堆栈移动到日志开头位置
     * 4. 保留其他线程的堆栈信息在后面
     * 5. 保持额外信息段(如Binary Images)在原始位置不变
     *
     * @param trace 原始ANR堆栈日志
     * @return 重排后的ANR堆栈, 主线程堆栈在最前
     */
    public static String convertAnrTraceFormat(String trace) {
        // 检查是否包含线程堆栈信息
        if (!trace.contains("Backtrace of Thread:")) {
            return trace;
        }

        // 获取主线程编号
        String mainThreadNum = extractMainThreadNumber(trace);
        if (mainThreadNum == null) {
            return trace;
        }

        // 查找各个关键段落的位置
        int callBacktraceIndex = trace.indexOf("Call Backtrace of");
        if (callBacktraceIndex <= 0) {
            return trace;
        }

        // 定位额外信息段的位置
        int binaryImagesIndex = trace.indexOf("Binary Images:");

        // 确定堆栈信息段的结束位置
        int stackTraceEnd = trace.length();
        if (binaryImagesIndex > 0) {
            stackTraceEnd = binaryImagesIndex;
        }

        // 保留头部信息
        String header = trace.substring(0, callBacktraceIndex);
        String callBacktraceLine = trace.substring(callBacktraceIndex, trace.indexOf("\n", callBacktraceIndex) + 1);

        // 提取主线程堆栈
        String mainThreadPattern = "Backtrace of Thread " + mainThreadNum + ":";
        int mainThreadStart = trace.indexOf(mainThreadPattern);
        if (mainThreadStart <= 0) {
            return trace;
        }

        // 获取主线程堆栈的完整内容
        int nextThreadStart = trace.indexOf("Backtrace of Thread", mainThreadStart + mainThreadPattern.length());
        String mainThreadTrace = nextThreadStart > 0 ?
                trace.substring(mainThreadStart, nextThreadStart) :
                trace.substring(mainThreadStart, stackTraceEnd);

        // 移除其他位置的主线程堆栈,避免重复
        String remainingTrace = trace.substring(callBacktraceIndex + callBacktraceLine.length(), stackTraceEnd)
                .replace(mainThreadTrace, "");

        // 获取需要保持原位的额外信息段及其前缀换行符
        String additionalSections = "";
        if (binaryImagesIndex > 0) {
            // 向前查找换行符序列
            int prefixNewlineStart = binaryImagesIndex;
            while (prefixNewlineStart > 0 && trace.charAt(prefixNewlineStart - 1) == '\n') {
                prefixNewlineStart--;
            }
            additionalSections = trace.substring(prefixNewlineStart);
        }

        // 重组堆栈: 头部 + 主线程堆栈 + 其他线程堆栈 + 额外信息段
        if (remainingTrace.trim().isEmpty()) {
            return header + callBacktraceLine + mainThreadTrace.trim() +
                    (additionalSections.isEmpty() ? "" : additionalSections);
        }
        return header + callBacktraceLine + mainThreadTrace.trim() + "\n\n" +
                remainingTrace.trim() +
                (additionalSections.isEmpty() ? "" : additionalSections);

    }

    /**
     * 提取主线程编号
     * 通常主线程会最先出现在ANR日志中
     */
    public static String extractMainThreadNumber(String trace) {
        int index = trace.indexOf("Backtrace of Thread:");
        if (index >= 0) {
            String remaining = trace.substring(index + "Backtrace of Thread:".length()).trim();
            int endIndex = remaining.indexOf('\n');
            if (endIndex > 0) {
                return remaining.substring(0, endIndex).trim();
            }
            return remaining.trim();
        }
        return null;
    }
}
