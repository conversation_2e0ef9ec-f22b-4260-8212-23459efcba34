package com.databuff.dts.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.lionsoul.ip2region.xdb.Searcher;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

@Slf4j
@Component
public class Ip2RegionUtils {

    @Data
    public static class IpInfo {
        /**
         * 国家
         */
        private String country;

        /**
         * 区域
         */
        private String region;

        /**
         * 省份
         */
        private String province;

        /**
         * 城市
         */
        private String city;

        /**
         * ISP 网络供应商
         */
        private String isp;
    }


    private static Searcher searcher;

    @PostConstruct
    public void init() throws IOException {
        ClassPathResource resource = new ClassPathResource("ip2region.xdb");

        InputStream inputStream = resource.getInputStream();
        // 读取ip2region的所有字节数据
        ByteArrayOutputStream bufferStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            bufferStream.write(buffer, 0, bytesRead);
        }
        byte[] res = bufferStream.toByteArray();
        searcher = Searcher.newWithBuffer(res);
        log.info("==ip2region初始化完成==");
    }

    @PreDestroy
    public void destroy() throws IOException {
        if (searcher != null) {
            searcher.close();
        }
    }

    // Add for testing
    protected static void setSearcher(Searcher searcher) {
        Ip2RegionUtils.searcher = searcher;
    }


    public static String getIPInfo(String ip) {
        try {
            return searcher.search(ip);
        } catch (Exception e) {
            log.error("Failed to search IP: " + ip, e);
            return null;
        }
    }

    public static IpInfo getDetailedIPInfo(String ip) {
        try {
            String result = searcher.search(ip);
            return parseIpInfo(result);
        } catch (Exception e) {
            log.error("Failed to search IP: " + ip, e);
            return null;
        }
    }

    private static IpInfo parseIpInfo(String result) {
        if (result == null || result.length() == 0) {
            return null;
        }
        String[] regionSplit = result.split("\\|");
        if (regionSplit.length != 5) {
            return null;
        }

        IpInfo ipInfo = new IpInfo();

        ipInfo.setCountry("0".equals(regionSplit[0]) ? null : regionSplit[0]);
        ipInfo.setRegion("0".equals(regionSplit[1]) ? null : regionSplit[1]);
        ipInfo.setProvince("0".equals(regionSplit[2]) ? null : regionSplit[2]);
        ipInfo.setCity("0".equals(regionSplit[3]) ? null : regionSplit[3]);
        ipInfo.setIsp("0".equals(regionSplit[4]) ? null : regionSplit[4]);

        return ipInfo;
    }


    /**
     * 返回地域 ,不是ip库的区域 目前是省/市
     * 目前不需要返回国家
     * @param ipInfo
     * @return {@link String }
     */
    public static String getRegion(IpInfo ipInfo) {
        if (ipInfo == null || ipInfo.getProvince() == null) {
            return "未知";
        }
        return ipInfo.getCity() == null ? ipInfo.getProvince() : ipInfo.getProvince() + "/" + ipInfo.getCity();
    }


}
