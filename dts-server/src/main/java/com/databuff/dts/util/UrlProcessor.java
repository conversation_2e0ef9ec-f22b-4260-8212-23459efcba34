package com.databuff.dts.util;

import com.databuff.entity.rum.web.UrlAggregationSettings;

import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLDecoder;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class UrlProcessor {


    /**
     * 根据提供的规则聚合URI，将URL转换为聚合后的URI。
     * <p>
     * 聚合规则用于定义如何将不同的URL路径模式归类为统一的聚合URI。规则中可以包含通配符来表示灵活的匹配：
     * <p>
     * *：匹配单个路径段中的零个或多个字符，但不跨越路径分隔符 /。
     * <p>
     * **：匹配零个或多个完整的路径段，能够跨越多个 /。
     * <p>
     * ?：匹配单个路径段中的任意一个字符。
     * <p>
     * <p>
     * 设计思想
     * <p>
     * 逐段匹配：通过将URL路径和规则按照 / 分割为路径段，逐段进行比较，避免了使用复杂的正则表达式，提高了代码的可读性和维护性。
     * <p>
     * 递归匹配：特别处理 ** 通配符，允许其匹配零个或多个路径段。通过递归调用 matchSegments 方法，实现灵活的多层路径匹配。
     * <p>
     * 通配符处理：
     * <p>
     * * 在单个路径段中匹配任意字符，不跨越 /。
     * <p>
     * ** 允许跨越多个路径段，匹配深层次的目录结构。
     * <p>
     * ? 精确匹配单个字符，提供更细粒度的匹配控制。
     * <p>
     * 优先级：在聚合过程中，规则列表按照顺序优先匹配，找到第一个符合的规则即进行聚合，确保规则的优先级和覆盖范围。
     * <p>
     * URL规范化：通过 normalizeUrl 方法，将URL标准化为统一的格式，去除协议、查询参数，并根据需求处理片段部分，为后续的聚合和匹配提供一致的基础。
     * <p>
     * 异常处理：在URL解析或匹配过程中，如果遇到异常（如无效的URL格式），系统会返回规范化后的URL，确保系统的健壮性。
     *
     * @param url   需要聚合的URL。
     * @param rules 聚合规则列表。
     * @return 聚合后的URI。如果URL为空或无效，返回规范化后的URL。
     */
    public static String aggregateUri(String url, List<UrlAggregationSettings.UriAggregationRule> rules) {
        if (url == null) {
            return null;
        }

        try {
            // 规范化URL
            String normalizedUrl = normalizeUrl(url, -1);

            // 提取authority（域名或IP）
            URI uri = new URI(url);
            String authority = uri.getAuthority();

            // 从规范化URL中提取路径和片段
            String pathAndFragment = normalizedUrl.substring(1 + authority.length()); // 去除 "/authority" 前缀

            // 去除片段用于匹配
            String pathToMatch = pathAndFragment.split("#", 2)[0];

            // URL解码路径以便进行匹配
            String decodedPath = URLDecoder.decode(pathToMatch, "UTF-8");

            String matchedRule = null;

            if (rules != null && !rules.isEmpty()) {
                // Sort rules by order (ascending)
                List<UrlAggregationSettings.UriAggregationRule> sortedRules = rules.stream()
                        .sorted(Comparator.comparingInt(UrlAggregationSettings.UriAggregationRule::getOrder))
                        .collect(Collectors.toList());

                for (UrlAggregationSettings.UriAggregationRule rule : sortedRules) {
                    if (matchesRule(decodedPath, rule.getRule())) {
                        matchedRule = rule.getRule();
                        break;
                    }
                }
            }

            if (matchedRule != null) {
                // 返回 "/authority + matchedRule"
                return "/" + authority + matchedRule;
            } else {
                // 返回规范化后的URL
                return normalizedUrl;
            }

        } catch (Exception e) {
            // 出现错误时返回规范化后的URL
            return normalizeUrl(url, -1);
        }
    }

    /**
     * 检查路径是否匹配给定的规则模式，不使用正则表达式。
     *
     * @param path URL的路径部分。
     * @param rule 包含通配符的规则模式。
     * @return 如果匹配则返回true，否则返回false。
     */
    private static boolean matchesRule(String path, String rule) {
        String[] pathSegments = path.split("/");
        String[] ruleSegments = rule.split("/");

        return matchSegments(pathSegments, 0, ruleSegments, 0);
    }

    /**
     * 递归匹配路径段与规则段。
     *
     * @param pathSegments URL路径段数组。
     * @param pIndex       当前路径段索引。
     * @param ruleSegments 规则路径段数组。
     * @param rIndex       当前规则段索引。
     * @return 如果匹配则返回true，否则返回false。
     */
    private static boolean matchSegments(String[] pathSegments, int pIndex, String[] ruleSegments, int rIndex) {
        // 如果路径和规则都完全匹配
        if (pIndex == pathSegments.length && rIndex == ruleSegments.length) {
            return true;
        }

        // 如果规则已经匹配完但路径未匹配完
        if (rIndex == ruleSegments.length) {
            return false;
        }

        // 处理 '**' 通配符
        if (ruleSegments[rIndex].equals("**")) {
            // 尝试匹配零个段
            if (matchSegments(pathSegments, pIndex, ruleSegments, rIndex + 1)) {
                return true;
            }
            // 尝试匹配一个或多个段
            if (pIndex < pathSegments.length) {
                return matchSegments(pathSegments, pIndex + 1, ruleSegments, rIndex);
            }
            return false;
        }

        // 如果路径已经匹配完但规则未匹配完
        if (pIndex == pathSegments.length) {
            return false;
        }

        // 处理 '*' 和 '?' 通配符
        if (matchSegment(pathSegments[pIndex], ruleSegments[rIndex])) {
            return matchSegments(pathSegments, pIndex + 1, ruleSegments, rIndex + 1);
        }

        return false;
    }

    /**
     * 匹配单个路径段与规则段。
     *
     * @param pathSegment URL路径中的一个段。
     * @param ruleSegment 规则中的一个段。
     * @return 如果匹配则返回true，否则返回false。
     */
    private static boolean matchSegment(String pathSegment, String ruleSegment) {
        int p = 0, r = 0;
        int star = -1, match = 0;

        while (p < pathSegment.length()) {
            if (r < ruleSegment.length() && (ruleSegment.charAt(r) == '?' || ruleSegment.charAt(r) == pathSegment.charAt(p))) {
                // 当前字符匹配，或者规则中的 '?' 通配符匹配单个字符
                p++;
                r++;
            } else if (r < ruleSegment.length() && ruleSegment.charAt(r) == '*') {
                // 规则中遇到 '*' 通配符，记录位置
                star = r;
                match = p;
                r++;
            } else if (star != -1) {
                // 回溯到上一个 '*' 通配符
                r = star + 1;
                match++;
                p = match;
            } else {
                // 不匹配
                return false;
            }
        }

        // 路径段结束后，规则段中剩余的 '*' 可以忽略
        while (r < ruleSegment.length() && ruleSegment.charAt(r) == '*') {
            r++;
        }

        return r == ruleSegment.length();
    }

    /**
     * 规范化URL，通过移除协议、查询参数，并在片段以 '/' 开头时包含片段。
     *
     * @param url                   需要规范化的URL。
     * @param urlPathNormalizedType 规范化类型：
     *                              -1: 不进行替换。
     *                              0: 纯数字段替换为 '?'。
     *                              1: 含数字段替换为 '?'。
     * @return 规范化后的URL。
     */
    public static String normalizeUrl(String url, int urlPathNormalizedType) {
        try {
            URI uri = new URI(url);
            String authority = uri.getAuthority();
            String path = uri.getRawPath();
            String fragment = uri.getRawFragment();

            StringBuilder result = new StringBuilder("/" + authority);

            if (path != null && !path.isEmpty()) {
                result.append(path);
            }

            if (fragment != null) {
                String[] fragmentParts = fragment.split("\\?", 2);
                if (fragmentParts[0].startsWith("/")) {
                    result.append("#").append(fragmentParts[0]);
                }
            }

            String normalizedUrl = result.toString();

            switch (urlPathNormalizedType) {
                case -1:
                    return normalizedUrl;
                case 0:
                    return replaceNumericSegments(normalizedUrl);
                case 1:
                    return replaceAlphanumericSegments(normalizedUrl);
                default:
                    return replaceNumericSegments(normalizedUrl);
            }
        } catch (URISyntaxException e) {
            // URL格式错误时，返回原始URL
            return url;
        }
    }

    /**
     * 替换URL中的纯数字路径段为 '?'。
     *
     * @param url 需要处理的URL。
     * @return 处理后的URL。
     */
    public static String replaceNumericSegments(String url) {
        String[] parts = url.split("#", 2);
        String[] segments = parts[0].split("/");
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < segments.length; i++) {
            if (i == 1) {
                // 保留authority部分
                result.append("/").append(segments[i]);
                continue;
            }
            if (segments[i].isEmpty()) continue;
            if (segments[i].matches("^\\d+$")) {
                // 纯数字段替换为 '?'
                result.append("/?");
            } else {
                result.append("/").append(segments[i]);
            }
        }

        if (parts.length > 1) {
            result.append("/#").append(parts[1].split("\\?")[0]);
        }

        return result.toString();
    }

    /**
     * 替换URL中包含数字的路径段为 '?'。
     *
     * @param url 需要处理的URL。
     * @return 处理后的URL。
     */
    public static String replaceAlphanumericSegments(String url) {
        String[] parts = url.split("#", 2);
        String[] segments = parts[0].split("/");
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < segments.length; i++) {
            if (i == 1) {
                // 保留authority部分
                result.append("/").append(segments[i]);
                continue;
            }
            if (segments[i].isEmpty()) continue;
            if (segments[i].matches(".*\\d.*")) {
                // 含数字的段替换为 '?'
                result.append("/?");
            } else {
                result.append("/").append(segments[i]);
            }
        }

        if (parts.length > 1) {
            result.append("/#").append(parts[1].split("\\?")[0]);
        }

        return result.toString();
    }


    public static String extractDomain(String url) {
        if (url == null) {
            return null;
        }
        try {
            URI uri = new URI(url);
            return uri.getHost();
        } catch (URISyntaxException e) {
            return null;
        }
    }

    public static String extractDomainFromProcessedUrl(String processedUrl) {
        if (processedUrl == null) {
            return null;
        }
        int endIndex = processedUrl.indexOf("/", processedUrl.indexOf("/") + 1);
        return endIndex != -1 ? processedUrl.substring(1, endIndex) : processedUrl.substring(1);
    }

    public static String extractPathFromProcessedUrl(String processedUrl) {
        if (processedUrl == null) {
            return null;
        }
        int startIndex = processedUrl.indexOf("/", processedUrl.indexOf("/") + 1);
        return startIndex != -1 ? processedUrl.substring(startIndex) : "";
    }

}
