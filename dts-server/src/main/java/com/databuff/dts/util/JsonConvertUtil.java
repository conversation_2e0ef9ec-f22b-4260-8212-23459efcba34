package com.databuff.dts.util;

import com.alibaba.fastjson.JSONObject;

/**
 * <PERSON><PERSON>和Object的互相转换，转List必须Json最外层加[]，转Object，Json最外层不要加[]
 * <AUTHOR>
 * @date 2019/3/19 15:37
 */
public class JsonConvertUtil {
    /**
     * JSON 转 Object
     */
    public static <T> T jsonToObject(String pojo, Class<T> clazz) {
        return JSONObject.parseObject(pojo, clazz);
    }

    /**
     * Object 转 JSON
     */
    public static <T> String objectToJson(T t){
        return JSONObject.toJSONString(t);
    }
}
