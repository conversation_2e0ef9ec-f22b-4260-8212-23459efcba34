package com.databuff.dts.util;


import org.apache.commons.lang3.StringUtils;

import java.lang.management.ManagementFactory;
import java.net.NetworkInterface;
import java.nio.ByteBuffer;
import java.util.Date;
import java.util.Enumeration;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;

/**
 *  唯一性ID生成器：UUID、ObjectId（MongoDB）、Snowflake
 *  ID相关文章见：http://calvin1978.blogcn.com/articles/uuid.html
 * @author: wuxin
 * @data: 2021/2/23
 */
public class IdUtils {

    /**
     * 获取随机UUID
     * @return
     */
    public static String uuid(boolean simple) {
        String uuid = UUID.randomUUID().toString();
        if (simple){
            uuid = uuid.replaceAll("-", "");
        }
        return uuid;
    }

    /**
     * 简化的UUID，去掉了横线
     * @return
     */
    public static String uuid() {
        return uuid(true);
    }

    public static String objectId() {
        return ObjectId.next();
    }

    public static Snowflake createSnowflake(long workerId, long datacenterId) {
        return new Snowflake(workerId, datacenterId);
    }

    /**
     * MongoDB ID生成策略实现<br>
     * ObjectId由以下几部分组成：
     *
     * 1. Time 时间戳。
     * 2. Machine 所在主机的唯一标识符，一般是机器主机名的散列值。
     * 3. PID 进程ID。确保同一机器中不冲突
     * 4. INC 自增计数器。确保同一秒内产生objectId的唯一性。
     *
     * 参考：http://blog.csdn.net/qxc1281/article/details/54021882
     */
    public static class ObjectId{
        /**
         * 线程安全的下一个随机数,每次生成自增+1
         */
        private static final AtomicInteger NEXT_INC = new AtomicInteger(ThreadLocalRandom.current().nextInt());
        /**
         * 机器信息
         */
        private static final int MACHINE = getMachinePiece() | getProcessPiece();

        /**
         * 给定的字符串是否为有效的ObjectId
         * @param id 字符串
         * @return
         */
        public static boolean verify(String id) {
            if (StringUtils.isBlank(id)) {
                return false;
            }
            id = id.replaceAll("-", "");
            int len = id.length();
            if (len != 24) {
                return false;
            }
            for (int i = 0,c; i < len; i++) {
                c = id.charAt(i);
                if (c >= '0' && c <= '9' || c >= 'a' && c <= 'f' || c >= 'A' && c <= 'F') {
                    continue;
                }
                return false;
            }
            return true;
        }

        /**
         * 获取一个objectId的bytes表现形式
         * @return objectId
         */
        public static byte[] nextBytes() {
            final ByteBuffer bb = ByteBuffer.wrap(new byte[12]);
            bb.putInt((int) (System.currentTimeMillis()));
            bb.putInt(MACHINE);
            bb.putInt(NEXT_INC.getAndIncrement());
            return bb.array();
        }

        /**
         * 获取一个objectId用下划线分割
         *
         * @return objectId
         */
        public static String next() {
            return next("");
        }

        /**
         * 获取一个objectId
         * @param separator 分隔符
         * @return
         */
        public static String next(String separator) {
            byte[] array = nextBytes();
            final StringBuilder buf = new StringBuilder(StringUtils.isBlank(separator) ? 24:(24+2*separator.length()));
            for (int i = 0, t; i < array.length; i++) {
                if (i % 4 == 0 && i != 0) {
                    buf.append(separator);
                }
                t = array[i] & 0xff;
                if (t < 16) {
                    buf.append('0');
                }
                buf.append(Integer.toHexString(t));

            }
            return buf.toString();
        }

        /**
         * 获取机器码
         * @return 机器码
         */
        private static int getMachinePiece() {
            try {
                // 返回机器所有的网络接口
                Enumeration<NetworkInterface> e = NetworkInterface.getNetworkInterfaces();
                StringBuilder netInfo = new StringBuilder();
                while (e.hasMoreElements()) {
                    netInfo.append(e.nextElement().toString());
                }
                return netInfo.toString().hashCode() << 16;
            } catch (Throwable e) {
                // 出问题随机生成,保留后两位
                return (ThreadLocalRandom.current().nextInt()) << 16;
            }
        }

        /**
         * 获取进程码
         * 因为静态变量类加载可能相同,所以获取进程ID + 加载对象的ID值
         * @return
         */
        private static int getProcessPiece() {
            int processId;
            try {
                // 获取进程ID
                String processName = ManagementFactory.getRuntimeMXBean().getName();
                final int idx = processName.indexOf('@');
                processId = idx>0 ? Integer.parseInt(StringUtils.substring(processName, 0, idx)):processName.hashCode();
            } catch (Throwable t) {
                processId = ThreadLocalRandom.current().nextInt();
            }
            ClassLoader loader = IdUtils.class.getClassLoader();
            // 返回对象哈希码,无论是否重写hashCode方法
            int loaderId = (loader!=null) ? System.identityHashCode(loader):0;
            // 进程ID + 对象加载ID,保留前2位
            final String processInfo = Integer.toHexString(processId) + Integer.toHexString(loaderId);
            return processInfo.hashCode() & 0xFFFF;
        }
    }

    /**
     * 创建Twitter的Snowflake 算法生成器。
     *
     * 分布式系统中，有一些需要使用全局唯一ID的场景，有些时候我们希望能使用一种简单一些的ID，并且希望ID能够按照时间有序生成。
     * snowflake的结构如下(每部分用-分开):
     *  0 - 0000000000 0000000000 0000000000 0000000000 0 - 00000 - 00000 - 000000000000
     * 第一位为未使用，接下来的41位为毫秒级时间(41位的长度可以使用69年),
     * 然后是5位datacenterId和5位workerId(10位的长度最多支持部署1024个节点,
     * 最后12位是毫秒内的计数（12位的计数顺序号支持每个节点每毫秒产生4096个ID序号,
     * 一共加起来刚好64位，为一个Long型。(转换成字符串后长度最多19)
     *
     * snowflake生成的ID整体上按照时间自增排序，并且整个分布式系统内不会产生ID碰撞（由datacenter和workerId作区分），并且效率较高。经测试snowflake每秒能够产生26万个ID。
     * 参考：http://www.cnblogs.com/relucent/p/4955340.html
     */
    public static class Snowflake {

        private final long epoch;
        private final long workerIdBits = 5L;
        // 最大支持机器节点数0~31，一共32个
        @SuppressWarnings({"PointlessBitwiseExpression", "FieldCanBeLocal"})
        private final long maxWorkerId = -1L ^ (-1L << workerIdBits);
        private final long dataCenterIdBits = 5L;
        // 最大支持数据中心节点数0~31，一共32个
        @SuppressWarnings({"PointlessBitwiseExpression", "FieldCanBeLocal"})
        private final long maxDataCenterId = -1L ^ (-1L << dataCenterIdBits);
        // 序列号12位
        private final long sequenceBits = 12L;
        // 机器节点左移12位
        private final long workerIdShift = sequenceBits;
        // 数据中心节点左移17位
        private final long dataCenterIdShift = sequenceBits + workerIdBits;
        // 时间毫秒数左移22位
        private final long timestampLeftShift = sequenceBits + workerIdBits + dataCenterIdBits;
        // 序列掩码，用于限定序列最大值不能超过4095
        @SuppressWarnings("FieldCanBeLocal")
        private final long sequenceMask = ~(-1L << sequenceBits);// 4095

        private final long workerId;
        private final long dataCenterId;
        private volatile long sequence = 0L;
        private volatile long lastTimestamp = -1L;
        private final ReentrantLock lock = new ReentrantLock();

        /**
         * 构造
         *
         * @param workerId     终端ID
         * @param dataCenterId 数据中心ID
         */
        public Snowflake(long workerId, long dataCenterId) {
            this(workerId, dataCenterId, false);
        }

        /**
         * 构造
         *
         * @param workerId         终端ID
         * @param dataCenterId     数据中心ID
         * @param periodicClock 是否使用{@link } 获取当前时间戳
         */
        public Snowflake(long workerId, long dataCenterId, boolean periodicClock) {
            this(null, workerId, dataCenterId, periodicClock);
        }

        /**
         * @param epochDate        初始化时间起点（null表示默认起始日期）,后期修改会导致id重复,如果要修改连workerId dataCenterId，慎用
         * @param workerId         工作机器节点id
         * @param dataCenterId     数据中心id
         * @param periodicClock 是否使用{@link } 获取当前时间戳,false使用{@link System#currentTimeMillis()}
         */
        public Snowflake(Date epochDate, long workerId, long dataCenterId, boolean periodicClock) {
            if (null != epochDate) {
                this.epoch = epochDate.getTime();
            } else{
                // Thu, 04 Nov 2010 01:42:54 GMT
                this.epoch = 1288834974657L;
            }
            if (workerId > maxWorkerId || workerId < 0) {
                throw new IllegalArgumentException(String.format("worker Id can't be greater than %s or less than 0", maxWorkerId));
            }
            if (dataCenterId > maxDataCenterId || dataCenterId < 0) {
                throw new IllegalArgumentException(String.format("datacenter Id can't be greater than %s or less than 0", maxDataCenterId));
            }
            this.workerId = workerId;
            this.dataCenterId = dataCenterId;
        }

        /**
         * 根据Snowflake的ID，获取机器id
         * @param id snowflake算法生成的id
         * @return
         */
        public long getWorkerId(long id) {
            return id >> workerIdShift & ~(-1L << workerIdBits);
        }

        /**
         * 根据Snowflake的ID，获取数据中心id
         * @param id snowflake算法生成的id
         * @return
         */
        public long getDataCenterId(long id) {
            return id >> dataCenterIdShift & ~(-1L << dataCenterIdBits);
        }

        /**
         * 根据Snowflake的ID，获取生成时间
         * @param id snowflake算法生成的id
         * @return
         */
        public long getGenerateDateTime(long id) {
            return (id >> timestampLeftShift & ~(-1L << 41L)) + epoch;
        }

        /**
         * 下一个ID
         * @return
         */
        public long nextId() {
            long seq;
            long lts;
            lock.lock();
            try {
                long timestamp = System.currentTimeMillis();
                if (timestamp<this.lastTimestamp) {
                    if(this.lastTimestamp - timestamp < 2000){
                        // 容忍2秒内的回拨，避免NTP校时造成的异常
                        timestamp = lastTimestamp;
                    } else{
                        // 如果服务器时间有问题(时钟后退) 报错。
                        throw new IllegalStateException(String.format("Clock moved backwards. Refusing to generate id for %sms", lastTimestamp - timestamp));
                    }
                }
                if (timestamp == this.lastTimestamp) {
                    long sequence = (this.sequence + 1) & sequenceMask;
                    if (sequence == 0) {
                        timestamp = tilNextMillis(lastTimestamp);
                    }
                    this.sequence = seq = sequence;
                } else {
                    this.sequence = seq = 0L;
                }
                lastTimestamp = lts = timestamp;
            } finally {
                lock.unlock();
            }
            return ((lts - epoch) << timestampLeftShift) | (dataCenterId << dataCenterIdShift) | (workerId << workerIdShift) | seq;
        }

        /**
         * 下一个ID（字符串形式）
         * @return
         */
        public String nextIdStr() {
            return Long.toString(nextId());
        }

        /**
         * 循环等待下一个时间
         * @param lastTimestamp 上次记录的时间
         * @return
         */
        private long tilNextMillis(long lastTimestamp) {
            long timestamp;
            do {
                timestamp = System.currentTimeMillis();
            } while (timestamp == lastTimestamp);

            if (timestamp < lastTimestamp) {
                // 如果发现新的时间戳比上次记录的时间戳数值小，说明操作系统时间发生了倒退，报错
                throw new IllegalStateException(String.format("Clock moved backwards. Refusing to generate id for %sms",lastTimestamp - timestamp));
            }
            return timestamp;
        }
    }

}
