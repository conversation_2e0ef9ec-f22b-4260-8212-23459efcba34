package com.databuff.dts.ringbuffer;

import com.databuff.dts.config.RefreshScopeConfig;
import com.lmax.disruptor.LiteBlockingWaitStrategy;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import com.lmax.disruptor.util.DaemonThreadFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.environment.EnvironmentChangeEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Configuration
public class DecompressRingBufferConfiguration implements ApplicationListener<EnvironmentChangeEvent> {

    @Autowired
    private CustomRingBuffer<ParseTrace> parseTraceRingBuffer;

    @Autowired
    private RefreshScopeConfig refreshScopeConfig;

    @Autowired
    private ApplicationContext applicationContext;

    @Bean
    public CustomRingBuffer<Event> decompressRingBuffer() {
        Disruptor<Event> disruptor = getDisruptor();
        CustomRingBuffer<Event> customRingBuffer = new CustomRingBuffer<>("decompress", refreshScopeConfig.getDecompressHandlerCount(), disruptor, 10000);
        return customRingBuffer;
    }

    /**
     * 根据最新的 decompressHandlerCount 值重新配置 RingBuffer
     */
    private void configureRingBuffer() {
        Disruptor<Event> disruptor = getDisruptor();
        applicationContext.getBean("decompressRingBuffer", CustomRingBuffer.class).reset(disruptor);
    }

    /**
     * 创建并配置 Disruptor
     */
    private Disruptor<Event> getDisruptor() {
        Disruptor<Event> disruptor = new Disruptor<>(Event::new, 1024, DaemonThreadFactory.INSTANCE, ProducerType.MULTI, new LiteBlockingWaitStrategy());
        List<DecompressHandler> handlers = new ArrayList<>();
        for (int i = 0; i < refreshScopeConfig.getDecompressHandlerCount(); i++) {
            handlers.add(new DecompressHandler(parseTraceRingBuffer));
        }
        disruptor.handleEventsWithWorkerPool(handlers.toArray(new DecompressHandler[0]));
        return disruptor;
    }

    /**
     * 监听容器刷新事件，检查 parseHandlerCount 值是否发生变化，如果发生变化，则重新配置 RingBuffer
     */
    @Override
    public void onApplicationEvent(EnvironmentChangeEvent environmentChangeEvent) {
        if (environmentChangeEvent.getKeys().contains("dts.decompressHandler.count")) {
            configureRingBuffer();
        }
    }

}
