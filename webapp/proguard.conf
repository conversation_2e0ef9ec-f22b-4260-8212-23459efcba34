-target 1.8 ##指定java版本号
-dontshrink ##默认是开启的，这里关闭shrink，即不删除没有使用的类/成员
-dontoptimize ##默认是开启的，这里关闭字节码级别的优化
-useuniqueclassmembernames ##对于类成员的命名的混淆采取唯一策略
-adaptclassstrings ## 混淆类名之后，对使用Class.forName('className')之类的地方进行相应替代
-dontusemixedcaseclassnames ## 混淆时不生成大小写混合的类名，默认是可以大小写混合

-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,*Annotation*,EnclosingMethod ##对异常、注解信息在runtime予以保留，不然影响springboot启动
-keepclasseswithmembers public class * { public static void main(java.lang.String[]);} ##保留main方法的类及其方法名
-keepclassmembers enum * { *; }  ##保留枚举成员及方法