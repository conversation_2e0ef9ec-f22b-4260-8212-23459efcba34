spring:
  application:
    name: webapp  # 应用的名称，这个名称会被用作服务发现的服务名。
  cloud:
    zookeeper:
      connection-timeout: 30000  # Zookeeper 客户端的连接超时时间，单位是毫秒。
      session-timeout: 120000  # Zookeeper 客户端的会话超时时间，单位是毫秒。
      config:
        enabled: true  # 是否启用 Zookeeper 配置中心。如果启用，应用会从 Zookeeper 中获取配置信息。
        root: databuff  # Zookeeper 配置中心的根路径。应用的配置信息会存储在这个路径下。
        profileSeparator: ','  # 用于分隔配置文件名和配置文件的 profile 的字符。
      discovery:
        enabled: true  # 是否启用 Zookeeper 服务发现。如果启用，应用会使用 Zookeeper 作为服务发现组件。
      connect-string: 192.168.50.45:12181  # Zookeeper 服务器的连接字符串。
      base-sleep-time-ms: 1000  # 重试策略的基础睡眠时间，单位是毫秒。如果 Zookeeper 客户端连接失败，会在这个时间后进行重试。
      max-retries: 3  # 重试策略的最大重试次数。如果 Zookeeper 客户端连接失败，最多会重试这个次数。
      max-sleep-ms: 3000  # 重试策略的最大睡眠时间，单位是毫秒。如果 Zookeeper 客户端连接失败，每次重试的睡眠时间不会超过这个时间。
      block-until-connected: false  # 是否阻塞应用启动，直到 Zookeeper 客户端连接成功。