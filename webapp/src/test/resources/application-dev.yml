server:
  port: 18080

# actuator配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
        exclude: "env,beans" # 开启所有端点，但是排除env和beans端点

zuul:
  ignoredServices: '*'
  routes:
    api:
      path: /graphql
      serviceId: collector
    login:
      path: /login/account
      serviceId: collector

collector:
  path: /graphql
  ribbon:
    # Point to all backend's restHost:restPort, split by ,
    listOfServers: 127.0.0.1:12800

spring:
  main:
    allow-circular-references: true
  cloud:
    compatibility-verifier:
      enabled: false
  # 上传最大文件限制
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  datasource:
    name: Databuff
    url: ***********************************************************************************************************************************************************************************************
    username: root
    password: 234*(sdlj12
    # 使用Druid数据源
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    druid:
      filters: stat
      maxActive: 20
      initialSize: 1
      maxWait: 60000
      minIdle: 1
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: select 'x'
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxOpenPreparedStatements: 20
  olap:
    driver-class-name: com.mysql.jdbc.Driver
    url: ****************************************
    httpUrl: *************:8040
    feIpPorts: *************:8040
    username: root
    password: Databuff@123
    openMonitor: true
    maxFilterRatio: 0
  redis:
    host: *************
    port: 16379
    timeout: 10000
    password:
    jedis:
      pool:
        max-idle: 8
        min-idle: 0
        max-wait: -1
        max-active: 200
  resources:
    add-mappings: false
  mvc:
    throw-exception-if-no-handler-found: true
  ### xxl-job, email
  mail:
    host:
    port:
    username:
    from:
    password:
    properties:
      mail:
        smtp:
          auth:
          starttls:
            enable:
            required:
          socketFactory:
            class:

  # 使用aop操作日志
  aop:
    auto: true
    proxy-target-class: true

  application:
    name: webapp
    cloud:
      nacos:
        discovery:
          server-addr: http://*************:18848
          file-extension: yaml
          namespace: 72008218-19b0-4960-ab44-a0cbdd8097a0
          username: nacos
          password: nacos
      config:
        username: nacos
        password: nacos
        contextPath: /nacos
      discovery:
        username: nacos
        password: nacos

# mysql config
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    #    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    map-underscore-to-camel-case: true
    cache-enabled: false
  global-config:
    refresh: true
    db-config:
      id-type: auto
      field-strategy: not_empty
      db-column-underline: true
      logic-delete-value: 1
      logic-not-delete-value: 0
      db-type: mysql
  # Mybatis配置Model类对应
#  type-aliases-package: com.databuff.webapp.admin.model

pagehelper:
  params: count=countSql
  # 指定分页插件使用哪种方言
  helper-dialect: mysql
  # 分页合理化参数 pageNum<=0时会查询第一页 pageNum>pages(超过总数时) 会查询最后一页
  reasonable: 'true'
  support-methods-arguments: 'false'

mapper:
  # 通用Mapper的insertSelective和updateByPrimaryKeySelective中是否判断字符串类型!=''
  not-empty: true

#日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果设置为WARN，则低于WARN的信息都不会输出
logging:
  config: classpath:logback-spring.xml
  path: /usr/local/log
  # Debug打印SQL
  level.com.databuff.dao.mysql: debug

swagger:
  enable: true
jasypt:
  encryptor:
    password: 123456   #开发环境密码

# elastic
elasticsearch:
  cluster_name: databuff-es-cluster
  hosts: *************:19200
  port: 19200
  user: elastic
  password: Databuff@123
  poolsize: 6
  #索引前缀
  indexPrefix: dc_
  #保存天数
  expireDays: 30
  max-total: 30
  min-idle: 3
  max-wait-millis: 5000
  soft-min-evictable-idle-time-millis: 300000
  test-on-borrow: true

moredb:
  url: *************:2890
  user: databuff
  password: databuff666
  max-total: 30
  min-idle: 3
  max-wait-millis: 5000
  soft-min-evictable-idle-time-millis: 300000
  test-on-borrow: true
  duration: 7d

kafka:
  ip: *************:9092

org:
  id: databuff
  isDocker: false

#版本
version: "DataBuff|v2.0"
#渠道: 官方000
channel: "000"
# logo路径
logoPath:
# 二维码地址
qrUrl: http://databuff.com:7300

# AccessToken过期时间-5分钟-5*60(秒为单位)
accessTokenExpireTime: 30240000
# RefreshToken过期时间-30分钟-30*60(秒为单位)
refreshTokenExpireTime: 3600
# dczw缓存过期时间-5分钟-5*60(秒为单位)(一般设置与AccessToken过期时间一致)
shiroCacheExpireTime: 3600

# 云通信短信API
sms:
  accessKeyId:
  accessKeySecret:
  regionId:
  signName:
  templateCode:
  enable: false
# 邮件发件人API
mail:
  addr:
  user: DataBuff
  password:
  enable: false
  smtp:
    host: smtp.dacheng-tech.com
    port: 25
    ssl: false


  ### datasource-pool
  #  spring.datasource.type=com.zaxxer.hikari.HikariDataSource
  #  spring.datasource.hikari.minimum-idle=10
  #  spring.datasource.hikari.maximum-pool-size=30
  #  spring.datasource.hikari.auto-commit=true
  #  spring.datasource.hikari.idle-timeout=30000
  #  spring.datasource.hikari.pool-name=HikariCP
  #  spring.datasource.hikari.max-lifetime=900000
  #  spring.datasource.hikari.connection-timeout=10000
  #  spring.datasource.hikari.connection-test-query=SELECT 1
  #  spring.datasource.hikari.validation-timeout=1000

  ### xxl-job, access token
xxl:
  job:
    accessToken: default_token
    ### xxl-job, i18n (default is zh_CN, and you can choose "zh_CN", "zh_TC" and "en")
    i18n: zh_CN
    ## xxl-job, triggerpool max size
    triggerpool:
      fast:
        max: 20
      slow:
        max: 10
    ### xxl-job, log retention days
    logretentiondays: 5

# cmdb同步f5配置
lb:
  cmdb:
    enable: 1 # 0:不启用 1:启用



queryOlapEnabled: true

# dts配置
dts:
  url: http://*************:18111

rum:
  application:
    upload:
      urls: http://*************/rum,https://*************/rum


