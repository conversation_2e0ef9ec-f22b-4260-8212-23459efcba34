package com.databuff.webapp.util;

import org.junit.Test;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class AndroidStackClassifierTest {

    @Test
    public void testSystemClassExamples() {
        // 应归类为系统代码的示例
        assertTrue("Expected android.app.ActivityThread to be SYSTEM",
                AndroidStackClassifier.isSystemClass("android.app.ActivityThread"));
        assertTrue("Expected com.android.server.SystemServer to be SYSTEM",
                AndroidStackClassifier.isSystemClass("com.android.server.SystemServer"));
        assertTrue("Expected androidx.recyclerview.widget.RecyclerView to be SYSTEM",
                AndroidStackClassifier.isSystemClass("androidx.recyclerview.widget.RecyclerView"));
        assertTrue("Expected com.google.android.gms.common.GoogleApiAvailability to be SYSTEM",
                AndroidStackClassifier.isSystemClass("com.google.android.gms.common.GoogleApiAvailability"));
        assertTrue("Expected com.samsung.android.app.phone to be SYSTEM",
                AndroidStackClassifier.isSystemClass("com.samsung.android.app.phone"));
        assertTrue("Expected com.oppo.system.SomeService to be SYSTEM",
                AndroidStackClassifier.isSystemClass("com.oppo.system.SomeService"));
        assertTrue("Expected com.meizu.android.SomeComponent to be SYSTEM",
                AndroidStackClassifier.isSystemClass("com.meizu.android.SomeComponent"));
        assertTrue("Expected com.xiaomi.push.SomePushService to be SYSTEM",
                AndroidStackClassifier.isSystemClass("com.xiaomi.push.SomePushService"));
        assertTrue("Expected android.view.View.-$$Nest$mperformClickInternal(Unknown Source:0) to be SYSTEM",
                AndroidStackClassifier.isSystemClass("android.view.View.-$$Nest$mperformClickInternal(Unknown Source:0)"));
    }

    @Test
    public void testNonSystemClassExamples() {
        // 应归类为非系统代码的示例
        assertFalse("Expected com.mycompany.myapp.MainActivity to be NON-SYSTEM",
                AndroidStackClassifier.isSystemClass("com.mycompany.myapp.MainActivity"));
        assertFalse("Expected com.facebook.react.ReactActivity to be NON-SYSTEM",
                AndroidStackClassifier.isSystemClass("com.facebook.react.ReactActivity"));
        // vivo 的示例：如果未匹配到 "com.vivo.android." 或 "com.vivo.system." 前缀，则归为非系统
        assertFalse("Expected com.vivo.app.SomeActivity to be NON-SYSTEM",
                AndroidStackClassifier.isSystemClass("com.vivo.app.SomeActivity"));
    }

    @Test
    public void testNullClassName() {
        // 当输入 null 时，返回 false
        assertFalse("Expected null to be NON-SYSTEM", AndroidStackClassifier.isSystemClass(null));
    }
}