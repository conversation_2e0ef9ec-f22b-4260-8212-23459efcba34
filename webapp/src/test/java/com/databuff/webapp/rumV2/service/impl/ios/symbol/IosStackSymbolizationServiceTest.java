package com.databuff.webapp.rumV2.service.impl.ios.symbol;

import com.databuff.entity.rum.web.IosBinaryImage;
import com.databuff.entity.rum.web.IosStackFrame;
import com.databuff.webapp.rumV2.service.RumIosSymbolFileService;
import com.databuff.common.utils.TestResourceUtil;
import com.databuff.webapp.util.symbolic.SymbolicManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
@Slf4j
class IosStackSymbolizationServiceTest {

    @InjectMocks
    private IosStackSymbolizationService service;

    @Mock
    private RumIosSymbolFileService symbolFileService;

    @Mock
    private SymbolCache symbolCache;

    @Mock
    private SymbolicManager symbolicManager;

    String sampleTrace = "systemFileName: 16.2 (20C65)\n" +
            "Incident Identifier: 9139064866418834\n" +
            "CrashReporter Key:   1.0.0\n" +
            "Hardware Model:      iPhone14,5\n" +
            "Process:             万岳直播带货 [36338]\n" +
            "Path:                \\/private\\/var\\/containers\\/Bundle\\/Application\\/079D3939-6ADD-47FE-966B-933869DA172F\\/万岳直播带货.app\\/万岳直播带货\n" +
            "Identifier:          万岳直播带货\n" +
            "Version:             2.0.0 (********)\n" +
            "Code Type:           ARM-64\n" +
            "Date\\/Time:           1732618414063\n" +
            "Launch Time:         1732616888091\n" +
            "OS Version:          16.2\n" +
            "Backtrace of Thread: 259\n" +
            "Report Version:      104" +
            "\n" +
            "\n" +
            "Call Backtrace of 10 threads:\n" +
            "Backtrace of Thread 259:\n" +
            "libsystem_kernel.dylib          0x1ca9c8f68 __semwait_signal + 8\n" +
            "libsystem_c.dylib               0x1946b97d8 nanosleep + 220\n" +
            "libsystem_c.dylib               0x1946ba4a4 usleep + 68\n" +
            "万岳直播带货              0x1044231dc  + 48\n" +
            "万岳直播带货              0x1044230fc  + 72\n" +
            "万岳直播带货              0x104422e70  + 272\n" +
            "DatabuffRUM                     0x106d44890  + 140\n" +
            "DatabuffRUM                     0x106cf927c  + 548\n" +
            "DatabuffRUM                     0x106d44238  + 468\n" +
            "UIKitCore                       0x1900a2530 <redacted> + 1196\n" +
            "UIKitCore                       0x1900a2844 <redacted> + 256\n" +
            "UIKitCore                       0x18f3cc9f4 <redacted> + 72\n" +
            "UIKitCore                       0x18f3cc928 <redacted> + 176\n" +
            "UIKitCore                       0x18f3cc834 <redacted> + 496\n" +
            "UIKitCore                       0x18f267da0 <redacted> + 108\n" +
            "UIKitCore                       0x18f72d0b4 <redacted> + 72\n" +
            "UIKitCore                       0x18f87b498 <redacted> + 84\n" +
            "UIKitCore                       0x18fec7410 <redacted> + 172\n" +
            "UIKitCore                       0x18fec65dc <redacted> + 92\n" +
            "CoreFoundation                  0x18d129f34 <redacted> + 28\n" +
            "CoreFoundation                  0x18d13630c <redacted> + 176\n" +
            "CoreFoundation                  0x18d0ba1d0 <redacted> + 244\n" +
            "CoreFoundation                  0x18d0cfb8c <redacted> + 836\n" +
            "CoreFoundation                  0x18d0d4ec0 CFRunLoopRunSpecific + 612\n" +
            "GraphicsServices                0x1c712b368 GSEventRunModal + 164\n" +
            "UIKitCore                       0x18f5ca86c <redacted> + 888\n" +
            "UIKitCore                       0x18f5ca4d0 UIApplicationMain + 340\n" +
            "万岳直播带货              0x10456a9a8 main + 140\n" +
            "\n" +
            "Backtrace of Thread 5635:\n" +
            "libsystem_kernel.dylib          0x1ca9c8f68 __semwait_signal + 8\n" +
            "libsystem_c.dylib               0x1946b97d8 nanosleep + 220\n" +
            "libsystem_c.dylib               0x1946ba4a4 usleep + 68\n" +
            "GPUToolsCore                    0x106a048ac _ZL21smt_poll_thread_entryPv + 148\n" +
            "libsystem_pthread.dylib         0x1db0e66cc _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 14083:\n" +
            "libsystem_kernel.dylib          0x1ca9c8aa8 mach_msg2_trap + 8\n" +
            "libsystem_kernel.dylib          0x1ca9dafc4 mach_msg2_internal + 80\n" +
            "libsystem_kernel.dylib          0x1ca9db204 mach_msg_overwrite + 388\n" +
            "libsystem_kernel.dylib          0x1ca9c8fec mach_msg + 24\n" +
            "CoreFoundation                  0x18d0cead4 <redacted> + 160\n" +
            "CoreFoundation                  0x18d0cfd18 <redacted> + 1232\n" +
            "CoreFoundation                  0x18d0d4ec0 CFRunLoopRunSpecific + 612\n" +
            "Foundation                      0x18741b0d4 <redacted> + 212\n" +
            "Foundation                      0x18741afbc <redacted> + 64\n" +
            "UIKitCore                       0x18f6ff72c <redacted> + 436\n" +
            "Foundation                      0x1874345a8 <redacted> + 716\n" +
            "libsystem_pthread.dylib         0x1db0e66cc _pthread_start + 148\n" +
            "\n" +
            "Binary Images:\n" +
            "       0x10438c000 -        0x104d33fff +万岳直播带货 arm64 <E469052D-2B00-3861-881F-D86D2BFF00E7> \\/private\\/var\\/containers\\/Bundle\\/Application\\/079D3939-6ADD-47FE-966B-933869DA172F\\/万岳直播带货.app\\/万岳直播带货\n" +
            "       0x1055ec000 -        0x1055f3fff libBacktraceRecording.dylib arm64-unknown <C06EA3FB-389F-382E-9F49-AA72821CF99F> \\/usr\\/lib\\/libBacktraceRecording.dylib\n" +
            "       0x1057e8000 -        0x10582bfff libMainThreadChecker.dylib arm64-unknown <********-A136-30A8-856E-AA13124E4133> \\/usr\\/lib\\/libMainThreadChecker.dylib\n" +
            "       0x105624000 -        0x10562ffff libRPAC.dylib arm64-unknown <4083AF68-13C1-35B7-AB80-3333B8B71062> \\/usr\\/lib\\/libRPAC.dylib\n" +
            "       0x1ca9c4000 -        0x1ca9fefe3 libsystem_kernel.dylib arm64-unknown <9DAA5C29-93E0-3768-A3E1-E139995DC4AF> \\/usr\\/lib\\/system\\/libsystem_kernel.dylib\n" +
            "       0x1db0e5000 -        0x1db0f0fff libsystem_pthread.dylib arm64-unknown <F2BA7EC0-F75A-3345-B4F6-F7DA4979B902> \\/usr\\/lib\\/system\\/libsystem_pthread.dylib\n" +
            "       0x18f229000 -        0x190a14fff UIKitCore arm64-unknown <59CBC9B5-30AE-396E-A269-A986640001BC> \\/System\\/Library\\/PrivateFrameworks\\/UIKitCore.framework\\/UIKitCore\n" +
            "       0x1c712a000 -        0x1c7132fff GraphicsServices arm64-unknown <5ADDA888-F387-35F7-87A7-E01FCB9BB928> \\/System\\/Library\\/PrivateFrameworks\\/GraphicsServices.framework\\/GraphicsServices\n";


    String sampleTrace2="systemFileName: 16.2 (20C65)\n" +
            "Incident Identifier: 3659858871737050\n" +
            "CrashReporter Key:   1.0.0\n" +
            "Hardware Model:      iPhone14,5\n" +
            "Process:             WYLiveShopping [91002]\n" +
            "Path:                \\/private\\/var\\/containers\\/Bundle\\/Application\\/792FED85-86B6-4BAA-A7DB-D56D055F7F5D\\/WYLiveShopping.app\\/WYLiveShopping\n" +
            "Identifier:          WYLiveShopping\n" +
            "Version:             2.0.1 (********)\n" +
            "Code Type:           ARM-64\n" +
            "Date\\/Time:           1733297077927\n" +
            "Launch Time:         1733297037164\n" +
            "OS Version:          16.2\n" +
            "Backtrace of Thread: 259\n" +
            "Report Version:      104\n" +
            "\n" +
            "\n" +
            "Call Backtrace of 12 threads:\n" +
            "Backtrace of Thread 259:\n" +
            "libsystem_kernel.dylib          0x1ca9c8f68 __semwait_signal + 8\n" +
            "libsystem_c.dylib               0x1946b97d8 nanosleep + 220\n" +
            "libsystem_c.dylib               0x1946ba4a4 usleep + 68\n" +
            "WYLiveShopping                  0x100f5f858  + 48\n" +
            "WYLiveShopping                  0x100f5f228  + 424\n" +
            "DatabuffRUM                     0x103938d40  + 140\n" +
            "DatabuffRUM                     0x1038ec2f8  + 556\n" +
            "DatabuffRUM                     0x1039386f0  + 472\n" +
            "UIKitCore                       0x1900a2530 <redacted> + 1196\n" +
            "UIKitCore                       0x1900a2844 <redacted> + 256\n" +
            "UIKitCore                       0x18f3cc9f4 <redacted> + 72\n" +
            "UIKitCore                       0x18f3cc928 <redacted> + 176\n" +
            "UIKitCore                       0x18f3cc834 <redacted> + 496\n" +
            "UIKitCore                       0x18f267da0 <redacted> + 108\n" +
            "UIKitCore                       0x18f72d0b4 <redacted> + 72\n" +
            "UIKitCore                       0x18f87b498 <redacted> + 84\n" +
            "UIKitCore                       0x18fec7410 <redacted> + 172\n" +
            "UIKitCore                       0x18fec65dc <redacted> + 92\n" +
            "CoreFoundation                  0x18d129f34 <redacted> + 28\n" +
            "CoreFoundation                  0x18d13630c <redacted> + 176\n" +
            "CoreFoundation                  0x18d0ba1d0 <redacted> + 244\n" +
            "CoreFoundation                  0x18d0cfb8c <redacted> + 836\n" +
            "CoreFoundation                  0x18d0d4ec0 CFRunLoopRunSpecific + 612\n" +
            "GraphicsServices                0x1c712b368 GSEventRunModal + 164\n" +
            "UIKitCore                       0x18f5ca86c <redacted> + 888\n" +
            "UIKitCore                       0x18f5ca4d0 UIApplicationMain + 340\n" +
            "WYLiveShopping                  0x1010a7a20 main + 140\n" +
            "\n" +
            "Backtrace of Thread 4099:\n" +
            "libsystem_kernel.dylib          0x1ca9c8f68 __semwait_signal + 8\n" +
            "libsystem_c.dylib               0x1946b97d8 nanosleep + 220\n" +
            "libsystem_c.dylib               0x1946ba4a4 usleep + 68\n" +
            "GPUToolsCore                    0x1035f48ac _ZL21smt_poll_thread_entryPv + 148\n" +
            "libsystem_pthread.dylib         0x1db0e66cc _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 10243:\n" +
            "libsystem_kernel.dylib          0x1ca9c8fb0 __workq_kernreturn + 8\n" +
            "libsystem_pthread.dylib         0x1db0e5e44 _pthread_wqthread + 364\n" +
            "\n" +
            "Backtrace of Thread 12803:\n" +
            "libsystem_kernel.dylib          0x1ca9c8aa8 mach_msg2_trap + 8\n" +
            "libsystem_kernel.dylib          0x1ca9dafc4 mach_msg2_internal + 80\n" +
            "libsystem_kernel.dylib          0x1ca9cfc0c thread_get_state + 260\n" +
            "DatabuffRUM                     0x1038e3dbc bs_mach_copyMem + 76\n" +
            "DatabuffRUM                     0x1038e2cc8 _bs_backtraceOfThread + 768\n" +
            "DatabuffRUM                     0x1038e3420  + 264\n" +
            "DatabuffRUM                     0x10387f4b8  + 1064\n" +
            "DatabuffRUM                     0x10389f700  + 608\n" +
            "libdispatch.dylib               0x1035505a8 _dispatch_call_block_and_release + 32\n" +
            "libdispatch.dylib               0x10355205c _dispatch_client_callout + 20\n" +
            "libdispatch.dylib               0x1035551e8 _dispatch_continuation_pop + 792\n" +
            "libdispatch.dylib               0x103554438 _dispatch_async_redirect_invoke + 680\n" +
            "libdispatch.dylib               0x103566478 _dispatch_root_queue_drain + 408\n" +
            "libdispatch.dylib               0x103566e74 _dispatch_worker_thread2 + 196\n" +
            "libsystem_pthread.dylib         0x1db0e5dbc _pthread_wqthread + 228\n" +
            "\n" +
            "Backtrace of Thread 15363:\n" +
            "libsystem_kernel.dylib          0x1ca9c8aa8 mach_msg2_trap + 8\n" +
            "libsystem_kernel.dylib          0x1ca9dafc4 mach_msg2_internal + 80\n" +
            "libsystem_kernel.dylib          0x1ca9db204 mach_msg_overwrite + 388\n" +
            "libsystem_kernel.dylib          0x1ca9c8fec mach_msg + 24\n" +
            "CoreFoundation                  0x18d0cead4 <redacted> + 160\n" +
            "CoreFoundation                  0x18d0cfd18 <redacted> + 1232\n" +
            "CoreFoundation                  0x18d0d4ec0 CFRunLoopRunSpecific + 612\n" +
            "Foundation                      0x18741b0d4 <redacted> + 212\n" +
            "Foundation                      0x18741afbc <redacted> + 64\n" +
            "UIKitCore                       0x18f6ff72c <redacted> + 436\n" +
            "Foundation                      0x1874345a8 <redacted> + 716\n" +
            "libsystem_pthread.dylib         0x1db0e66cc _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 18951:\n" +
            "libsystem_kernel.dylib          0x1ca9c8fb0 __workq_kernreturn + 8\n" +
            "libsystem_pthread.dylib         0x1db0e5e44 _pthread_wqthread + 364\n" +
            "\n" +
            "Backtrace of Thread 24835:\n" +
            "libsystem_kernel.dylib          0x1ca9c8aa8 mach_msg2_trap + 8\n" +
            "libsystem_kernel.dylib          0x1ca9dafc4 mach_msg2_internal + 80\n" +
            "libsystem_kernel.dylib          0x1ca9db204 mach_msg_overwrite + 388\n" +
            "libsystem_kernel.dylib          0x1ca9c8fec mach_msg + 24\n" +
            "CoreFoundation                  0x18d0cead4 <redacted> + 160\n" +
            "CoreFoundation                  0x18d0cfd18 <redacted> + 1232\n" +
            "CoreFoundation                  0x18d0d4ec0 CFRunLoopRunSpecific + 612\n" +
            "Foundation                      0x18741b0d4 <redacted> + 212\n" +
            "Foundation                      0x18741af68 <redacted> + 64\n" +
            "DatabuffRUM                     0x10390bfd0  + 248\n" +
            "Foundation                      0x1874345a8 <redacted> + 716\n" +
            "libsystem_pthread.dylib         0x1db0e66cc _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 25859:\n" +
            "libsystem_kernel.dylib          0x1ca9c8f68 __semwait_signal + 8\n" +
            "libsystem_c.dylib               0x1946b97d8 nanosleep + 220\n" +
            "libsystem_c.dylib               0x1946cdad8 sleep + 52\n" +
            "DatabuffRUM                     0x1038fd900  + 212\n" +
            "libsystem_pthread.dylib         0x1db0e66cc _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 34563:\n" +
            "libsystem_kernel.dylib          0x1ca9c8fb0 __workq_kernreturn + 8\n" +
            "libsystem_pthread.dylib         0x1db0e5e44 _pthread_wqthread + 364\n" +
            "\n" +
            "Backtrace of Thread 65303:\n" +
            "libsystem_kernel.dylib          0x1ca9c8aa8 mach_msg2_trap + 8\n" +
            "libsystem_kernel.dylib          0x1ca9dafc4 mach_msg2_internal + 80\n" +
            "libsystem_kernel.dylib          0x1ca9db204 mach_msg_overwrite + 388\n" +
            "libsystem_kernel.dylib          0x1ca9c8fec mach_msg + 24\n" +
            "CoreFoundation                  0x18d0cead4 <redacted> + 160\n" +
            "CoreFoundation                  0x18d0cfd18 <redacted> + 1232\n" +
            "CoreFoundation                  0x18d0d4ec0 CFRunLoopRunSpecific + 612\n" +
            "CFNetwork                       0x18e440078 _CFURLStorageSessionDisableCache + 61088\n" +
            "Foundation                      0x1874345a8 <redacted> + 716\n" +
            "libsystem_pthread.dylib         0x1db0e66cc _pthread_start + 148\n" +
            "\n" +
            "\n" +
            "Binary Images:\n" +
            "       0x100ec4000 -        0x10186ffff +WYLiveShopping arm64 <0FE9EB22-941A-3178-93C8-6E336EFBFD76> \\/private\\/var\\/containers\\/Bundle\\/Application\\/792FED85-86B6-4BAA-A7DB-D56D055F7F5D\\/WYLiveShopping.app\\/WYLiveShopping\n" +
            "       0x18e1e8000 -        0x18e5b1fff CFNetwork arm64-unknown <8A75357D-7E21-3FB3-8B1E-4C0F63E8DC02> \\/System\\/Library\\/Frameworks\\/CFNetwork.framework\\/CFNetwork\n" +
            "       0x18d054000 -        0x18d439fff CoreFoundation arm64-unknown <725E49F4-653B-39BF-9A7A-8A3250911ECB> \\/System\\/Library\\/Frameworks\\/CoreFoundation.framework\\/CoreFoundation\n" +
            "       0x1873d9000 -        0x187d22fff Foundation arm64-unknown <07A92F05-D8EC-327E-AB33-41DB9F77BA16> \\/System\\/Library\\/Frameworks\\/Foundation.framework\\/Foundation\n" +
            "       0x103868000 -        0x103a1bfff DatabuffRUM arm64 <E704FD00-786E-38AA-A490-59F08724BA0B> \\/private\\/var\\/containers\\/Bundle\\/Application\\/792FED85-86B6-4BAA-A7DB-D56D055F7F5D\\/WYLiveShopping.app\\/Frameworks\\/DatabuffRUM.framework\\/DatabuffRUM\n" +
            "       0x10354c000 -        0x10359bfff libdispatch.dylib arm64-unknown <2CC543D5-50B2-3F59-A9A5-2BC757CD7495> \\/usr\\/lib\\/system\\/introspection\\/libdispatch.dylib\n" +
            "       0x1946b4000 -        0x194733ff7 libsystem_c.dylib arm64-unknown <F088D98D-F2A1-3452-996F-9E6BB5139F52> \\/usr\\/lib\\/system\\/libsystem_c.dylib\n" +
            "       0x1db0e5000 -        0x1db0f0fff libsystem_pthread.dylib arm64-unknown <F2BA7EC0-F75A-3345-B4F6-F7DA4979B902> \\/usr\\/lib\\/system\\/libsystem_pthread.dylib\n" +
            "       0x1ca9c4000 -        0x1ca9fefe3 libsystem_kernel.dylib arm64-unknown <9DAA5C29-93E0-3768-A3E1-E139995DC4AF> \\/usr\\/lib\\/system\\/libsystem_kernel.dylib\n" +
            "       0x18f229000 -        0x190a14fff UIKitCore arm64-unknown <59CBC9B5-30AE-396E-A269-A986640001BC> \\/System\\/Library\\/PrivateFrameworks\\/UIKitCore.framework\\/UIKitCore\n" +
            "       0x1c712a000 -        0x1c7132fff GraphicsServices arm64-unknown <5ADDA888-F387-35F7-87A7-E01FCB9BB928> \\/System\\/Library\\/PrivateFrameworks\\/GraphicsServices.framework\\/GraphicsServices\n" +
            "       0x1035ec000 -        0x103643fff GPUToolsCore arm64-unknown <24F08E8B-4E90-3E8E-8617-57869AFE80BE> \\/Developer\\/Library\\/PrivateFrameworks\\/GPUToolsCore.framework\\/GPUToolsCore\n" +
            "\n";



    String sampleTrace3= "systemFileName: 16.2 (20C65)\n" +
            "Identifier:          WYLiveShopping\n" +
            "\n" +
            "Call Backtrace of 12 threads: 1 thread processed\n" +
            "Backtrace of Thread 0:\n" +
            "CoreFoundation 0x18D05DDA4 <redacted> + 164\n" +
            "libobjc.A.dylib 0x18632F89C objc_exception_throw + 60\n" +
            "CoreFoundation 0x18D1CA764 <redacted> + 16\n" +
            "Foundation 0x187442850 <redacted> + 324\n" +
            "Foundation 0x1874A47C0 <redacted> + 152\n" +
            "WYLiveShopping 0x10421B5A4 -[MineSettingViewController setValueFor] + 148\n" +
            "WYLiveShopping 0x10421B088 -[MineSettingViewController tableView:didSelectRowAtIndexPath:] + 552\n" +
            "DatabuffRUM 0x1068B0F6C __58+[DatabuffRUM_UITableView swizzleDidSelectRowAtIndexPath:]_block_invoke.38 + 140\n" +
            "DatabuffRUM 0x106864384 +[DatabuffRUMUtility sniffWithoutDuplicationForObject:selector:sniffingBlock:originalImplementationBlock:] + 556\n" +
            "DatabuffRUM 0x1068B07D0 __58+[DatabuffRUM_UITableView swizzleDidSelectRowAtIndexPath:]_block_invoke_2 + 472\n" +
            "UIKitCore 0x1900A2084 <redacted> + 1196\n" +
            "UIKitCore 0x1900A2744 <redacted> + 256\n" +
            "UIKitCore 0x18F3CC9AC <redacted> + 72\n" +
            "UIKitCore 0x18F3CC878 <redacted> + 176\n" +
            "UIKitCore 0x18F3CC644 <redacted> + 496\n" +
            "UIKitCore 0x18F267D34 <redacted> + 108\n" +
            "UIKitCore 0x18F72D06C <redacted> + 72\n" +
            "UIKitCore 0x18F87B444 <redacted> + 84\n" +
            "UIKitCore 0x18FEC7364 <redacted> + 172\n" +
            "UIKitCore 0x18FEC6580 <redacted> + 92\n" +
            "CoreFoundation 0x18D129F18 <redacted> + 28\n" +
            "CoreFoundation 0x18D13625C <redacted> + 176\n" +
            "CoreFoundation 0x18D0BA0DC <redacted> + 244\n" +
            "CoreFoundation 0x18D0CF848 <redacted> + 836\n" +
            "CoreFoundation 0x18D0D4C5C CFRunLoopRunSpecific + 612\n" +
            "GraphicsServices 0x1C712B2C4 GSEventRunModal + 164\n" +
            "UIKitCore 0x18F5CA4F4 <redacted> + 888\n" +
            "UIKitCore 0x18F5CA37C UIApplicationMain + 340\n" +
            "WYLiveShopping 0x10436399C main + 140\n" +
            "\n" +
            "Backtrace of Thread 1:\n" +
            "libsystem_kernel.dylib 0x1CA9C8A34 semaphore_timedwait_trap + 8\n" +
            "libdispatch.dylib 0x1946715B4 <redacted> + 64\n" +
            "libdispatch.dylib 0x194671BA8 <redacted> + 76\n" +
            "DatabuffRUM 0x1068174BC __50-[DatabuffRUM_Monitor_MainThread registerObserver]_block_invoke + 172\n" +
            "libdispatch.dylib 0x19466F494 <redacted> + 32\n" +
            "libdispatch.dylib 0x194670FC8 <redacted> + 20\n" +
            "libdispatch.dylib 0x194674274 <redacted> + 504\n" +
            "libdispatch.dylib 0x19467388C <redacted> + 584\n" +
            "libdispatch.dylib 0x1946828E0 <redacted> + 396\n" +
            "libdispatch.dylib 0x1946831E0 <redacted> + 164\n" +
            "libsystem_pthread.dylib 0x1DB0E5CD8 _pthread_wqthread + 228\n" +
            "\n" +
            "Backtrace of Thread 2:\n" +
            "libsystem_kernel.dylib 0x1CA9C8AA0 mach_msg2_trap + 8\n" +
            "libsystem_kernel.dylib 0x1CA9DAF74 mach_msg2_internal + 80\n" +
            "libsystem_kernel.dylib 0x1CA9DB080 mach_msg_overwrite + 388\n" +
            "libsystem_kernel.dylib 0x1CA9C8FD4 mach_msg + 24\n" +
            "CoreFoundation 0x18D0CEA34 <redacted> + 160\n" +
            "CoreFoundation 0x18D0CF848 <redacted> + 1232\n" +
            "CoreFoundation 0x18D0D4C5C CFRunLoopRunSpecific + 612\n" +
            "Foundation 0x18741B000 <redacted> + 212\n" +
            "Foundation 0x18741AF7C <redacted> + 64\n" +
            "UIKitCore 0x18F6FF578 <redacted> + 436\n" +
            "Foundation 0x1874342DC <redacted> + 716\n" +
            "libsystem_pthread.dylib 0x1DB0E6638 _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 3:\n" +
            "libsystem_kernel.dylib 0x1CA9C8FA8 __workq_kernreturn + 8\n" +
            "libsystem_pthread.dylib 0x1DB0E5CD8 _pthread_wqthread + 364\n" +
            "\n" +
            "Backtrace of Thread 4:\n" +
            "libsystem_kernel.dylib 0x1CA9C8AA0 mach_msg2_trap + 8\n" +
            "libsystem_kernel.dylib 0x1CA9DAF74 mach_msg2_internal + 80\n" +
            "libsystem_kernel.dylib 0x1CA9DB080 mach_msg_overwrite + 388\n" +
            "libsystem_kernel.dylib 0x1CA9C8FD4 mach_msg + 24\n" +
            "CoreFoundation 0x18D0CEA34 <redacted> + 160\n" +
            "CoreFoundation 0x18D0CF848 <redacted> + 1232\n" +
            "CoreFoundation 0x18D0D4C5C CFRunLoopRunSpecific + 612\n" +
            "Foundation 0x18741B000 <redacted> + 212\n" +
            "Foundation 0x18741AF28 <redacted> + 64\n" +
            "DatabuffRUM 0x106884190 -[DatabuffRUM_ModuleMgr MainLoop:] + 248\n" +
            "Foundation 0x1874342DC <redacted> + 716\n" +
            "libsystem_pthread.dylib 0x1DB0E6638 _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 5:\n" +
            "libsystem_kernel.dylib 0x1CA9C8F60 __semwait_signal + 8\n" +
            "libsystem_c.dylib 0x1946B96FC nanosleep + 220\n" +
            "libsystem_c.dylib 0x1946CDAA4 sleep + 52\n" +
            "DatabuffRUM 0x106875AE4 monitorCachedData + 212\n" +
            "libsystem_pthread.dylib 0x1DB0E6638 _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 6:\n" +
            "libsystem_kernel.dylib 0x1CA9C8AA0 mach_msg2_trap + 8\n" +
            "libsystem_kernel.dylib 0x1CA9DAF74 mach_msg2_internal + 80\n" +
            "libsystem_kernel.dylib 0x1CA9D509C thread_suspend + 112\n" +
            "DatabuffRUM 0x1067EB20C handleExceptions + 168\n" +
            "libsystem_pthread.dylib 0x1DB0E6638 _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 7:\n" +
            "libsystem_kernel.dylib 0x1CA9C8AA0 mach_msg2_trap + 8\n" +
            "libsystem_kernel.dylib 0x1CA9DAF74 mach_msg2_internal + 80\n" +
            "libsystem_kernel.dylib 0x1CA9DB080 mach_msg_overwrite + 388\n" +
            "libsystem_kernel.dylib 0x1CA9C8FD4 mach_msg + 24\n" +
            "DatabuffRUM 0x1067EB20C handleExceptions + 244\n" +
            "libsystem_pthread.dylib 0x1DB0E6638 _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 8:\n" +
            "libsystem_kernel.dylib 0x1CA9C8AA0 mach_msg2_trap + 8\n" +
            "libsystem_kernel.dylib 0x1CA9DAF74 mach_msg2_internal + 80\n" +
            "libsystem_kernel.dylib 0x1CA9DB080 mach_msg_overwrite + 388\n" +
            "libsystem_kernel.dylib 0x1CA9C8FD4 mach_msg + 24\n" +
            "CoreFoundation 0x18D0CEA34 <redacted> + 160\n" +
            "CoreFoundation 0x18D0CF848 <redacted> + 1232\n" +
            "CoreFoundation 0x18D0D4C5C CFRunLoopRunSpecific + 612\n" +
            "CFNetwork 0x18E4311D8 _CFURLStorageSessionDisableCache + 61088\n" +
            "Foundation 0x1874342DC <redacted> + 716\n" +
            "libsystem_pthread.dylib 0x1DB0E6638 _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 9:\n" +
            "libsystem_kernel.dylib 0x1CA9C8AA0 mach_msg2_trap + 8\n" +
            "libsystem_kernel.dylib 0x1CA9DAF74 mach_msg2_internal + 80\n" +
            "libsystem_kernel.dylib 0x1CA9DB080 mach_msg_overwrite + 388\n" +
            "libsystem_kernel.dylib 0x1CA9C8FD4 mach_msg + 24\n" +
            "CoreFoundation 0x18D0CEA34 <redacted> + 160\n" +
            "CoreFoundation 0x18D0CF848 <redacted> + 1232\n" +
            "CoreFoundation 0x18D0D4C5C CFRunLoopRunSpecific + 612\n" +
            "CoreFoundation 0x18D118CA4 CFRunLoopRun + 64\n" +
            "CoreMotion 0x19868D524 CLMotionActivity::isTypeInVehicle(CLMotionActivity::Type) + 22880\n" +
            "libsystem_pthread.dylib 0x1DB0E6638 _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 10:\n" +
            "libsystem_kernel.dylib 0x1CA9C8FA8 __workq_kernreturn + 8\n" +
            "libsystem_pthread.dylib 0x1DB0E5CD8 _pthread_wqthread + 364\n" +
            "\n" +
            "Binary Images:\n" +
            "       0x104180000 -        0x104b2bfff WYLiveShopping arm64 <F850603B-FF6F-3D25-B995-5DA8B05DC5D6> \\/private\\/var\\/containers\\/Bundle\\/Application\\/2ECE86B5-5972-480A-8A3C-933FAD341D83\\/WYLiveShopping.app\\/WYLiveShopping\n" +
            "       0x1067e0000 -        0x106993fff DatabuffRUM arm64 <BD27364C-E8AA-31D6-8A53-BB81AE2DB13F> \\/private\\/var\\/containers\\/Bundle\\/Application\\/2ECE86B5-5972-480A-8A3C-933FAD341D83\\/WYLiveShopping.app\\/Frameworks\\/DatabuffRUM.framework\\/DatabuffRUM\n" +
            "       0x19466d000 -        0x1946b3fff libdispatch.dylib arm64 <24DB930D-870B-31CE-AC69-534D7896B4A8> \\/usr\\/lib\\/system\\/libdispatch.dylib\n" +
            "       0x1946b4000 -        0x194733ff7 libsystem_c.dylib arm64 <F088D98D-F2A1-3452-996F-9E6BB5139F52> \\/usr\\/lib\\/system\\/libsystem_c.dylib\n" +
            "       0x1ca9c4000 -        0x1ca9fefe3 libsystem_kernel.dylib arm64 <9DAA5C29-93E0-3768-A3E1-E139995DC4AF> \\/usr\\/lib\\/system\\/libsystem_kernel.dylib\n" +
            "       0x1db0e5000 -        0x1db0f0fff libsystem_pthread.dylib arm64 <F2BA7EC0-F75A-3345-B4F6-F7DA4979B902> \\/usr\\/lib\\/system\\/libsystem_pthread.dylib\n" +
            "       0x186318000 -        0x18635be1f libobjc.A.dylib arm64 <D6ECFB73-0CA2-3A21-A3A9-19E450D3B49C> \\/usr\\/lib\\/libobjc.A.dylib\n" +
            "       0x1873d9000 -        0x187d22fff Foundation arm64 <07A92F05-D8EC-327E-AB33-41DB9F77BA16> \\/System\\/Library\\/Frameworks\\/Foundation.framework\\/Foundation\n" +
            "       0x18d054000 -        0x18d439fff CoreFoundation arm64 <725E49F4-653B-39BF-9A7A-8A3250911ECB> \\/System\\/Library\\/Frameworks\\/CoreFoundation.framework\\/CoreFoundation\n" +
            "       0x18e1e8000 -        0x18e5b1fff CFNetwork arm64 <8A75357D-7E21-3FB3-8B1E-4C0F63E8DC02> \\/System\\/Library\\/Frameworks\\/CFNetwork.framework\\/CFNetwork\n" +
            "       0x18f229000 -        0x190a14fff UIKitCore arm64 <59CBC9B5-30AE-396E-A269-A986640001BC> \\/System\\/Library\\/PrivateFrameworks\\/UIKitCore.framework\\/UIKitCore\n" +
            "       0x1c712a000 -        0x1c7132fff GraphicsServices arm64 <5ADDA888-F387-35F7-87A7-E01FCB9BB928> \\/System\\/Library\\/PrivateFrameworks\\/GraphicsServices.framework\\/GraphicsServices\n" +
            "       0x19867f000 -        0x198a86fff CoreMotion arm64 <B60E13F1-EEEF-3B76-BB8D-421893A11B6F> \\/System\\/Library\\/Frameworks\\/CoreMotion.framework\\/CoreMotion\n";


    String sampleTrace4 = "systemFileName: 16.2 (20C65)\n" +
            "Incident Identifier: 5391055221153844\n" +
            "CrashReporter Key:   1.0.0\n" +
            "Hardware Model:      iPhone14,5\n" +
            "Process:             WYLiveShopping [3974]\n" +
            "Path:                /private/var/containers/Bundle/Application/906DC85A-0DD3-4D22-B069-FE9980FC6C49/WYLiveShopping.app/WYLiveShopping\n" +
            "Identifier:          WYLiveShopping\n" +
            "Version:             2.0.1 (********)\n" +
            "Code Type:           ARM-64\n" +
            "Date/Time:           1733466071129\n" +
            "Launch Time:         1733466062912\n" +
            "OS Version:          16.2\n" +
            "Backtrace of Thread: 259\n" +
            "Report Version:      104\n" +
            "\n" +
            "\n" +
            "Call Backtrace of 12 threads:\n" +
            "Backtrace of Thread 259:\n" +
            "libsystem_kernel.dylib 0x1ca9c8f68 __semwait_signal + 8\n" +
            "libsystem_c.dylib 0x1946b97d8 nanosleep + 220\n" +
            "libsystem_c.dylib 0x1946cdad8 sleep + 52\n" +
            "MBProgressHUD 0x103a28a5c  + 212\n" +
            "MBProgressHUD 0x103a28070  + 112\n" +
            "WYLiveShopping 0x10222d10c  + 320\n" +
            "WYLiveShopping 0x10222cfb4  + 88\n" +
            "WYLiveShopping 0x10227f470  + 136\n" +
            "AFNetworking 0x1037dcd38  + 220\n" +
            "AFNetworking 0x1037faab4  + 204\n" +
            "libdispatch.dylib 0x10486c5a8 _dispatch_call_block_and_release + 32\n" +
            "libdispatch.dylib 0x10486e05c _dispatch_client_callout + 20\n" +
            "libdispatch.dylib 0x10487e914 _dispatch_main_queue_drain + 1456\n" +
            "libdispatch.dylib 0x10487e354 _dispatch_main_queue_callback_4CF + 44\n" +
            "CoreFoundation 0x18d0ee6d8 <redacted> + 16\n" +
            "CoreFoundation 0x18d0d003c <redacted> + 2036\n" +
            "CoreFoundation 0x18d0d4ec0 CFRunLoopRunSpecific + 612\n" +
            "GraphicsServices 0x1c712b368 GSEventRunModal + 164\n" +
            "UIKitCore 0x18f5ca86c <redacted> + 888\n" +
            "UIKitCore 0x18f5ca4d0 UIApplicationMain + 340\n" +
            "WYLiveShopping 0x1023bfa3c main + 140\n" +
            "\n" +
            "Backtrace of Thread 3843:\n" +
            "libsystem_kernel.dylib 0x1ca9c8fb0 __workq_kernreturn + 8\n" +
            "libsystem_pthread.dylib 0x1db0e5e44 _pthread_wqthread + 364\n" +
            "\n" +
            "Backtrace of Thread 4355:\n" +
            "libsystem_kernel.dylib 0x1ca9c8f68 __semwait_signal + 8\n" +
            "libsystem_c.dylib 0x1946b97d8 nanosleep + 220\n" +
            "libsystem_c.dylib 0x1946ba4a4 usleep + 68\n" +
            "GPUToolsCore 0x1049108ac _ZL21smt_poll_thread_entryPv + 148\n" +
            "libsystem_pthread.dylib 0x1db0e66cc _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 9219:\n" +
            "libsystem_kernel.dylib 0x1ca9c8fb0 __workq_kernreturn + 8\n" +
            "libsystem_pthread.dylib 0x1db0e5e44 _pthread_wqthread + 364\n" +
            "\n" +
            "Backtrace of Thread 14339:\n" +
            "libsystem_kernel.dylib 0x1ca9c8aa8 mach_msg2_trap + 8\n" +
            "libsystem_kernel.dylib 0x1ca9dafc4 mach_msg2_internal + 80\n" +
            "libsystem_kernel.dylib 0x1ca9db204 mach_msg_overwrite + 388\n" +
            "libsystem_kernel.dylib 0x1ca9c8fec mach_msg + 24\n" +
            "CoreFoundation 0x18d0cead4 <redacted> + 160\n" +
            "CoreFoundation 0x18d0cfd18 <redacted> + 1232\n" +
            "CoreFoundation 0x18d0d4ec0 CFRunLoopRunSpecific + 612\n" +
            "Foundation 0x18741b0d4 <redacted> + 212\n" +
            "Foundation 0x18741afbc <redacted> + 64\n" +
            "UIKitCore 0x18f6ff72c <redacted> + 436\n" +
            "Foundation 0x1874345a8 <redacted> + 716\n" +
            "libsystem_pthread.dylib 0x1db0e66cc _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 20739:\n" +
            "libsystem_kernel.dylib 0x1ca9c8fb0 __workq_kernreturn + 8\n" +
            "libsystem_pthread.dylib 0x1db0e5e44 _pthread_wqthread + 364\n" +
            "\n" +
            "Backtrace of Thread 19459:\n" +
            "libsystem_kernel.dylib 0x1ca9c8aa8 mach_msg2_trap + 8\n" +
            "libsystem_kernel.dylib 0x1ca9dafc4 mach_msg2_internal + 80\n" +
            "libsystem_kernel.dylib 0x1ca9cfc0c thread_get_state + 260\n" +
            "DatabuffRUM 0x104c08418 bs_mach_copyMem + 76\n" +
            "DatabuffRUM 0x104c07324 _bs_backtraceOfThread + 768\n" +
            "DatabuffRUM 0x104c07a7c  + 264\n" +
            "DatabuffRUM 0x104ba34b8  + 1064\n" +
            "DatabuffRUM 0x104bc38c8  + 608\n" +
            "libdispatch.dylib 0x10486c5a8 _dispatch_call_block_and_release + 32\n" +
            "libdispatch.dylib 0x10486e05c _dispatch_client_callout + 20\n" +
            "libdispatch.dylib 0x1048711e8 _dispatch_continuation_pop + 792\n" +
            "libdispatch.dylib 0x104870438 _dispatch_async_redirect_invoke + 680\n" +
            "libdispatch.dylib 0x104882478 _dispatch_root_queue_drain + 408\n" +
            "libdispatch.dylib 0x104882e74 _dispatch_worker_thread2 + 196\n" +
            "libsystem_pthread.dylib 0x1db0e5dbc _pthread_wqthread + 228\n" +
            "\n" +
            "Backtrace of Thread 19207:\n" +
            "libsystem_kernel.dylib 0x1ca9c8fb0 __workq_kernreturn + 8\n" +
            "libsystem_pthread.dylib 0x1db0e5e44 _pthread_wqthread + 364\n" +
            "\n" +
            "Backtrace of Thread 29699:\n" +
            "libsystem_kernel.dylib 0x1ca9c8aa8 mach_msg2_trap + 8\n" +
            "libsystem_kernel.dylib 0x1ca9dafc4 mach_msg2_internal + 80\n" +
            "libsystem_kernel.dylib 0x1ca9db204 mach_msg_overwrite + 388\n" +
            "libsystem_kernel.dylib 0x1ca9c8fec mach_msg + 24\n" +
            "CoreFoundation 0x18d0cead4 <redacted> + 160\n" +
            "CoreFoundation 0x18d0cfd18 <redacted> + 1232\n" +
            "CoreFoundation 0x18d0d4ec0 CFRunLoopRunSpecific + 612\n" +
            "Foundation 0x18741b0d4 <redacted> + 212\n" +
            "Foundation 0x18741af68 <redacted> + 64\n" +
            "DatabuffRUM 0x104c30628  + 248\n" +
            "Foundation 0x1874345a8 <redacted> + 716\n" +
            "libsystem_pthread.dylib 0x1db0e66cc _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 24323:\n" +
            "libsystem_kernel.dylib 0x1ca9c8f68 __semwait_signal + 8\n" +
            "libsystem_c.dylib 0x1946b97d8 nanosleep + 220\n" +
            "libsystem_c.dylib 0x1946cdad8 sleep + 52\n" +
            "DatabuffRUM 0x104c21f58  + 212\n" +
            "libsystem_pthread.dylib 0x1db0e66cc _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 33799:\n" +
            "libsystem_kernel.dylib 0x1ca9c8fb0 __workq_kernreturn + 8\n" +
            "libsystem_pthread.dylib 0x1db0e5e44 _pthread_wqthread + 364\n" +
            "\n" +
            "Backtrace of Thread 22287:\n" +
            "libsystem_kernel.dylib 0x1ca9c8aa8 mach_msg2_trap + 8\n" +
            "libsystem_kernel.dylib 0x1ca9dafc4 mach_msg2_internal + 80\n" +
            "libsystem_kernel.dylib 0x1ca9db204 mach_msg_overwrite + 388\n" +
            "libsystem_kernel.dylib 0x1ca9c8fec mach_msg + 24\n" +
            "CoreFoundation 0x18d0cead4 <redacted> + 160\n" +
            "CoreFoundation 0x18d0cfd18 <redacted> + 1232\n" +
            "CoreFoundation 0x18d0d4ec0 CFRunLoopRunSpecific + 612\n" +
            "CFNetwork 0x18e440078 _CFURLStorageSessionDisableCache + 61088\n" +
            "Foundation 0x1874345a8 <redacted> + 716\n" +
            "libsystem_pthread.dylib 0x1db0e66cc _pthread_start + 148\n" +
            "\n" +
            "\n" +
            "Binary Images:\n" +
            "       0x1021dc000 -        0x102b87fff +WYLiveShopping arm64 <552D6896-A42B-3100-AC82-DB192C13F2D8> /private/var/containers/Bundle/Application/906DC85A-0DD3-4D22-B069-FE9980FC6C49/WYLiveShopping.app/WYLiveShopping\n" +
            "       0x1037d4000 -        0x10381ffff AFNetworking arm64 <C6BC12DC-6389-3619-A55F-EF87438F3C60> /private/var/containers/Bundle/Application/906DC85A-0DD3-4D22-B069-FE9980FC6C49/WYLiveShopping.app/Frameworks/AFNetworking.framework/AFNetworking\n" +
            "       0x18e1e8000 -        0x18e5b1fff CFNetwork arm64-unknown <8A75357D-7E21-3FB3-8B1E-4C0F63E8DC02> /System/Library/Frameworks/CFNetwork.framework/CFNetwork\n" +
            "       0x18d054000 -        0x18d439fff CoreFoundation arm64-unknown <725E49F4-653B-39BF-9A7A-8A3250911ECB> /System/Library/Frameworks/CoreFoundation.framework/CoreFoundation\n" +
            "       0x1873d9000 -        0x187d22fff Foundation arm64-unknown <07A92F05-D8EC-327E-AB33-41DB9F77BA16> /System/Library/Frameworks/Foundation.framework/Foundation\n" +
            "       0x103a20000 -        0x103a3ffff MBProgressHUD arm64 <0F65576B-5300-321D-B066-BC83B33973A7> /private/var/containers/Bundle/Application/906DC85A-0DD3-4D22-B069-FE9980FC6C49/WYLiveShopping.app/Frameworks/MBProgressHUD.framework/MBProgressHUD\n" +
            "       0x104b88000 -        0x104d3ffff DatabuffRUM arm64 <141EB58F-5DC8-3128-9B48-0B560A04DF7E> /private/var/containers/Bundle/Application/906DC85A-0DD3-4D22-B069-FE9980FC6C49/WYLiveShopping.app/Frameworks/DatabuffRUM.framework/DatabuffRUM\n" +
            "       0x104868000 -        0x1048b7fff libdispatch.dylib arm64-unknown <2CC543D5-50B2-3F59-A9A5-2BC757CD7495> /usr/lib/system/introspection/libdispatch.dylib\n" +
            "       0x1946b4000 -        0x194733ff7 libsystem_c.dylib arm64-unknown <F088D98D-F2A1-3452-996F-9E6BB5139F52> /usr/lib/system/libsystem_c.dylib\n" +
            "       0x1db0e5000 -        0x1db0f0fff libsystem_pthread.dylib arm64-unknown <F2BA7EC0-F75A-3345-B4F6-F7DA4979B902> /usr/lib/system/libsystem_pthread.dylib\n" +
            "       0x1ca9c4000 -        0x1ca9fefe3 libsystem_kernel.dylib arm64-unknown <9DAA5C29-93E0-3768-A3E1-E139995DC4AF> /usr/lib/system/libsystem_kernel.dylib\n" +
            "       0x18f229000 -        0x190a14fff UIKitCore arm64-unknown <59CBC9B5-30AE-396E-A269-A986640001BC> /System/Library/PrivateFrameworks/UIKitCore.framework/UIKitCore\n" +
            "       0x1c712a000 -        0x1c7132fff GraphicsServices arm64-unknown <5ADDA888-F387-35F7-87A7-E01FCB9BB928> /System/Library/PrivateFrameworks/GraphicsServices.framework/GraphicsServices\n" +
            "       0x104908000 -        0x10495ffff GPUToolsCore arm64-unknown <24F08E8B-4E90-3E8E-8617-57869AFE80BE> /Developer/Library/PrivateFrameworks/GPUToolsCore.framework/GPUToolsCore\n" ;


    String sampleTrace5="systemFileName: 16.2 (20C65)\n" +
            "Identifier:          WYLiveShopping\n" +
            "\n" +
            "Call Backtrace of 15 threads: 1 thread processed\n" +
            "Backtrace of Thread 0:\n" +
            "CoreFoundation 0x18D05DDA4 <redacted> + 164\n" +
            "libobjc.A.dylib 0x18632F89C objc_exception_throw + 60\n" +
            "CoreFoundation 0x18D1CA764 <redacted> + 16\n" +
            "Foundation 0x187442850 <redacted> + 324\n" +
            "Foundation 0x1874A47C0 <redacted> + 152\n" +
            "WYLiveShopping 0x104CDF59C -[MineSettingViewController setValueFor] + 148\n" +
            "WYLiveShopping 0x104CDF080 -[MineSettingViewController tableView:didSelectRowAtIndexPath:] + 552\n" +
            "DatabuffRUM 0x10734130C __58+[DatabuffRUM_UITableView swizzleDidSelectRowAtIndexPath:]_block_invoke.38 + 140\n" +
            "DatabuffRUM 0x1072F4724 +[DatabuffRUMUtility sniffWithoutDuplicationForObject:selector:sniffingBlock:originalImplementationBlock:] + 556\n" +
            "DatabuffRUM 0x107340B70 __58+[DatabuffRUM_UITableView swizzleDidSelectRowAtIndexPath:]_block_invoke_2 + 472\n" +
            "UIKitCore 0x1900A2084 <redacted> + 1196\n" +
            "UIKitCore 0x1900A2744 <redacted> + 256\n" +
            "UIKitCore 0x18F3CC9AC <redacted> + 72\n" +
            "UIKitCore 0x18F3CC878 <redacted> + 176\n" +
            "UIKitCore 0x18F3CC644 <redacted> + 496\n" +
            "UIKitCore 0x18F267D34 <redacted> + 108\n" +
            "UIKitCore 0x18F72D06C <redacted> + 72\n" +
            "UIKitCore 0x18F87B444 <redacted> + 84\n" +
            "UIKitCore 0x18FEC7364 <redacted> + 172\n" +
            "UIKitCore 0x18FEC6580 <redacted> + 92\n" +
            "CoreFoundation 0x18D129F18 <redacted> + 28\n" +
            "CoreFoundation 0x18D13625C <redacted> + 176\n" +
            "CoreFoundation 0x18D0BA0DC <redacted> + 244\n" +
            "CoreFoundation 0x18D0CF848 <redacted> + 836\n" +
            "CoreFoundation 0x18D0D4C5C CFRunLoopRunSpecific + 612\n" +
            "GraphicsServices 0x1C712B2C4 GSEventRunModal + 164\n" +
            "UIKitCore 0x18F5CA4F4 <redacted> + 888\n" +
            "UIKitCore 0x18F5CA37C UIApplicationMain + 340\n" +
            "WYLiveShopping 0x104E2799C main + 140\n" +
            "\n" +
            "Backtrace of Thread 1:\n" +
            "libsystem_pthread.dylib 0x1DB0E5A90 <redacted> + 256\n" +
            "\n" +
            "Backtrace of Thread 2:\n" +
            "libsystem_kernel.dylib 0x1CA9C8FA8 __workq_kernreturn + 8\n" +
            "libsystem_pthread.dylib 0x1DB0E5CD8 _pthread_wqthread + 364\n" +
            "\n" +
            "Backtrace of Thread 3:\n" +
            "libsystem_kernel.dylib 0x1CA9C8AA0 mach_msg2_trap + 8\n" +
            "libsystem_kernel.dylib 0x1CA9DAF74 mach_msg2_internal + 80\n" +
            "libsystem_kernel.dylib 0x1CA9DB080 mach_msg_overwrite + 388\n" +
            "libsystem_kernel.dylib 0x1CA9C8FD4 mach_msg + 24\n" +
            "CoreFoundation 0x18D0CEA34 <redacted> + 160\n" +
            "CoreFoundation 0x18D0CF848 <redacted> + 1232\n" +
            "CoreFoundation 0x18D0D4C5C CFRunLoopRunSpecific + 612\n" +
            "Foundation 0x18741B000 <redacted> + 212\n" +
            "Foundation 0x18741AF7C <redacted> + 64\n" +
            "UIKitCore 0x18F6FF578 <redacted> + 436\n" +
            "Foundation 0x1874342DC <redacted> + 716\n" +
            "libsystem_pthread.dylib 0x1DB0E6638 _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 4:\n" +
            "libsystem_kernel.dylib 0x1CA9C8A34 semaphore_timedwait_trap + 8\n" +
            "libdispatch.dylib 0x1946715B4 <redacted> + 64\n" +
            "libdispatch.dylib 0x194671BA8 <redacted> + 76\n" +
            "DatabuffRUM 0x1072A7668 __50-[DatabuffRUM_Monitor_MainThread registerObserver]_block_invoke + 172\n" +
            "libdispatch.dylib 0x19466F494 <redacted> + 32\n" +
            "libdispatch.dylib 0x194670FC8 <redacted> + 20\n" +
            "libdispatch.dylib 0x194674274 <redacted> + 504\n" +
            "libdispatch.dylib 0x19467388C <redacted> + 584\n" +
            "libdispatch.dylib 0x1946828E0 <redacted> + 396\n" +
            "libdispatch.dylib 0x1946831E0 <redacted> + 164\n" +
            "libsystem_pthread.dylib 0x1DB0E5CD8 _pthread_wqthread + 228\n" +
            "\n" +
            "Backtrace of Thread 5:\n" +
            "libsystem_kernel.dylib 0x1CA9C8FA8 __workq_kernreturn + 8\n" +
            "libsystem_pthread.dylib 0x1DB0E5CD8 _pthread_wqthread + 364\n" +
            "\n" +
            "Backtrace of Thread 6:\n" +
            "libsystem_kernel.dylib 0x1CA9C8AA0 mach_msg2_trap + 8\n" +
            "libsystem_kernel.dylib 0x1CA9DAF74 mach_msg2_internal + 80\n" +
            "libsystem_kernel.dylib 0x1CA9DB080 mach_msg_overwrite + 388\n" +
            "libsystem_kernel.dylib 0x1CA9C8FD4 mach_msg + 24\n" +
            "CoreFoundation 0x18D0CEA34 <redacted> + 160\n" +
            "CoreFoundation 0x18D0CF848 <redacted> + 1232\n" +
            "CoreFoundation 0x18D0D4C5C CFRunLoopRunSpecific + 612\n" +
            "Foundation 0x18741B000 <redacted> + 212\n" +
            "Foundation 0x18741AF28 <redacted> + 64\n" +
            "DatabuffRUM 0x107314530 -[DatabuffRUM_ModuleMgr MainLoop:] + 248\n" +
            "Foundation 0x1874342DC <redacted> + 716\n" +
            "libsystem_pthread.dylib 0x1DB0E6638 _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 7:\n" +
            "libsystem_kernel.dylib 0x1CA9C8F60 __semwait_signal + 8\n" +
            "libsystem_c.dylib 0x1946B96FC nanosleep + 220\n" +
            "libsystem_c.dylib 0x1946CDAA4 sleep + 52\n" +
            "DatabuffRUM 0x107305E84 monitorCachedData + 212\n" +
            "libsystem_pthread.dylib 0x1DB0E6638 _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 8:\n" +
            "libsystem_kernel.dylib 0x1CA9C8AA0 mach_msg2_trap + 8\n" +
            "libsystem_kernel.dylib 0x1CA9DAF74 mach_msg2_internal + 80\n" +
            "libsystem_kernel.dylib 0x1CA9D509C thread_suspend + 112\n" +
            "DatabuffRUM 0x10727B20C handleExceptions + 168\n" +
            "libsystem_pthread.dylib 0x1DB0E6638 _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 9:\n" +
            "libsystem_kernel.dylib 0x1CA9C8AA0 mach_msg2_trap + 8\n" +
            "libsystem_kernel.dylib 0x1CA9DAF74 mach_msg2_internal + 80\n" +
            "libsystem_kernel.dylib 0x1CA9DB080 mach_msg_overwrite + 388\n" +
            "libsystem_kernel.dylib 0x1CA9C8FD4 mach_msg + 24\n" +
            "DatabuffRUM 0x10727B20C handleExceptions + 244\n" +
            "libsystem_pthread.dylib 0x1DB0E6638 _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 10:\n" +
            "libsystem_kernel.dylib 0x1CA9C8FA8 __workq_kernreturn + 8\n" +
            "libsystem_pthread.dylib 0x1DB0E5CD8 _pthread_wqthread + 364\n" +
            "\n" +
            "Backtrace of Thread 11:\n" +
            "libsystem_kernel.dylib 0x1CA9C8FA8 __workq_kernreturn + 8\n" +
            "libsystem_pthread.dylib 0x1DB0E5CD8 _pthread_wqthread + 364\n" +
            "\n" +
            "Backtrace of Thread 13:\n" +
            "libsystem_kernel.dylib 0x1CA9C8AA0 mach_msg2_trap + 8\n" +
            "libsystem_kernel.dylib 0x1CA9DAF74 mach_msg2_internal + 80\n" +
            "libsystem_kernel.dylib 0x1CA9DB080 mach_msg_overwrite + 388\n" +
            "libsystem_kernel.dylib 0x1CA9C8FD4 mach_msg + 24\n" +
            "CoreFoundation 0x18D0CEA34 <redacted> + 160\n" +
            "CoreFoundation 0x18D0CF848 <redacted> + 1232\n" +
            "CoreFoundation 0x18D0D4C5C CFRunLoopRunSpecific + 612\n" +
            "CFNetwork 0x18E4311D8 _CFURLStorageSessionDisableCache + 61088\n" +
            "Foundation 0x1874342DC <redacted> + 716\n" +
            "libsystem_pthread.dylib 0x1DB0E6638 _pthread_start + 148\n" +
            "\n" +
            "Backtrace of Thread 14:\n" +
            "libsystem_kernel.dylib 0x1CA9C9374 __psynch_cvwait + 8\n" +
            "libsystem_pthread.dylib 0x1DB0ECB9C <redacted> + 1232\n" +
            "JavaScriptCore 0x1A07EDFB0 <redacted> + 1364\n" +
            "libsystem_pthread.dylib 0x1DB0E6638 _pthread_start + 148\n" +
            "\n" +
            "Binary Images:\n" +
            "       0x104c44000 -        0x1055effff WYLiveShopping arm64 <0D627AF4-D6F4-302D-8C5E-D63F014DBB2A> \\/private\\/var\\/containers\\/Bundle\\/Application\\/A24752AF-3218-4968-B19F-1DDA0B138883\\/WYLiveShopping.app\\/WYLiveShopping\n" +
            "       0x10726c000 -        0x107423fff DatabuffRUM arm64 <141EB58F-5DC8-3128-9B48-0B560A04DF7E> \\/private\\/var\\/containers\\/Bundle\\/Application\\/A24752AF-3218-4968-B19F-1DDA0B138883\\/WYLiveShopping.app\\/Frameworks\\/DatabuffRUM.framework\\/DatabuffRUM\n" +
            "       0x19466d000 -        0x1946b3fff libdispatch.dylib arm64 <24DB930D-870B-31CE-AC69-534D7896B4A8> \\/usr\\/lib\\/system\\/libdispatch.dylib\n" +
            "       0x1946b4000 -        0x194733ff7 libsystem_c.dylib arm64 <F088D98D-F2A1-3452-996F-9E6BB5139F52> \\/usr\\/lib\\/system\\/libsystem_c.dylib\n" +
            "       0x1ca9c4000 -        0x1ca9fefe3 libsystem_kernel.dylib arm64 <9DAA5C29-93E0-3768-A3E1-E139995DC4AF> \\/usr\\/lib\\/system\\/libsystem_kernel.dylib\n" +
            "       0x1db0e5000 -        0x1db0f0fff libsystem_pthread.dylib arm64 <F2BA7EC0-F75A-3345-B4F6-F7DA4979B902> \\/usr\\/lib\\/system\\/libsystem_pthread.dylib\n" +
            "       0x186318000 -        0x18635be1f libobjc.A.dylib arm64 <D6ECFB73-0CA2-3A21-A3A9-19E450D3B49C> \\/usr\\/lib\\/libobjc.A.dylib\n" +
            "       0x1873d9000 -        0x187d22fff Foundation arm64 <07A92F05-D8EC-327E-AB33-41DB9F77BA16> \\/System\\/Library\\/Frameworks\\/Foundation.framework\\/Foundation\n" +
            "       0x18d054000 -        0x18d439fff CoreFoundation arm64 <725E49F4-653B-39BF-9A7A-8A3250911ECB> \\/System\\/Library\\/Frameworks\\/CoreFoundation.framework\\/CoreFoundation\n" +
            "       0x18e1e8000 -        0x18e5b1fff CFNetwork arm64 <8A75357D-7E21-3FB3-8B1E-4C0F63E8DC02> \\/System\\/Library\\/Frameworks\\/CFNetwork.framework\\/CFNetwork\n" +
            "       0x18f229000 -        0x190a14fff UIKitCore arm64 <59CBC9B5-30AE-396E-A269-A986640001BC> \\/System\\/Library\\/PrivateFrameworks\\/UIKitCore.framework\\/UIKitCore\n" +
            "       0x1c712a000 -        0x1c7132fff GraphicsServices arm64 <5ADDA888-F387-35F7-87A7-E01FCB9BB928> \\/System\\/Library\\/PrivateFrameworks\\/GraphicsServices.framework\\/GraphicsServices\n" +
            "       0x1a06f8000 -        0x1a1bb3fff JavaScriptCore arm64 <8C4289DE-9D14-3823-86C8-C749AF87C8DA> \\/System\\/Library\\/Frameworks\\/JavaScriptCore.framework\\/JavaScriptCore\n";


    private static final String SYSTEM_SYMBOLS_PATH = "symbol/iPhone14,5 16.2 (20C65)/Symbols";
    private static final String WANYUE_DSYM_PATH = "symbol/万岳-卡顿/万岳-卡顿/万岳直播带货.app.dSYM/Contents/Resources/DWARF/万岳直播带货";
    private static final String WYLIVE_DSYM_PATH = "symbol/WYLiveShopping.app.dSYM/Contents/Resources/DWARF/WYLiveShopping";
    private static final String WYLIVE_DSYM_PATH2 = "symbol/WYLiveShopping.app(1).dSYM/Contents/Resources/DWARF/WYLiveShopping";
    private static final String WYLIVE_DSYM_PATH3 = "symbol/WYLiveShopping.app(3).dSYM/Contents/Resources/DWARF/WYLiveShopping";
    private static final String WYLIVE_DSYM_PATH4 = "symbol/WYLiveShopping.app(4).dSYM/Contents/Resources/DWARF/WYLiveShopping";

    private String getSymbolPath(String relativePath) {
        try {
            return TestResourceUtil.getResourcePath(relativePath);
        } catch (IllegalArgumentException e) {
            return "D:/download/" + relativePath;
        }
    }

    private void setupSymbolCache() {
        SymbolCache.BatchLookupResult mockResult = new SymbolCache.BatchLookupResult();
        mockResult.setHitSymbols(new HashMap<>());
        mockResult.setMissedAddresses(new long[]{0x1044231dcL, 0x10456a9a8L});
        when(symbolCache.batchLookup(anyString(), anyLong(), any(long[].class)))
                .thenReturn(mockResult);
    }

    @Test
    void testBinaryImageEdgeCases() {
        String sampleBinaryImages =
                "Identifier:          WYLiveShopping\n" +
                        "\n" +
                        "Call Backtrace of 12 threads: 1 thread processed\n" +
                        "Backtrace of Thread 0:\n" +
                        "CoreFoundation 0x18D05DDA4 <redacted> + 164\n" +
                        "libobjc.A.dylib 0x18632F89C objc_exception_throw + 60\n" +
                        "\n"+
                        "Binary Images:\n" +
                        "       0x18e1e8000 -        0x18e5b1fff CFNetwork arm64-unknown <8A75357D-7E21-3FB3-8B1E-4C0F63E8DC02> \\/System\\/Library\\/Frameworks\\/CFNetwork.framework\\/CFNetwork\n" +
                        "       0x18e1e8000 -        0x18e5b1fff CFNetworkTest arm64-unknown <8A75357D-7E21-3FB3-8B1E-4C0F63E8DC02> \\/unknown\\/path\\/Frameworks\\/CFNetwork.framework\\/CFNetwork\n" +
                        "       0x1021dc000 -        0x102b87fff +WYLiveShopping arm64 <552D6896-A42B-3100-AC82-DB192C13F2D8> \\/private\\/var\\/containers\\/Bundle\\/Application\\/906DC85A-0DD3-4D22-B069-FE9980FC6C49\\/WYLiveShopping.app\\/WYLiveShopping\n";

        List<IosBinaryImage> images = service.parseBinaryImages(sampleBinaryImages);

        assertThat(images).hasSize(3);

        // Conflicting paths case (System + Container path)
        assertThat(images.get(0).isSystemLib()).isTrue();
        assertThat(images.get(0).getPath()).contains("/System/Library/");

        // Unknown path case
        assertThat(images.get(1).isSystemLib()).isFalse();
        assertThat(images.get(1).getPath()).contains("/unknown/path/");

        // Clear user app case
        assertThat(images.get(2).isSystemLib()).isFalse();
        assertThat(images.get(2).getPath()).contains("/private/var/containers/Bundle/Application/");
    }

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // Mock system property for OS check
        System.setProperty("os.name", "Linux");  // Force non-Windows behavior

        // Set system symbols path
        ReflectionTestUtils.setField(service, "systemSymbolsPath", getSymbolPath(SYSTEM_SYMBOLS_PATH));
    }

    @AfterEach
    void tearDown() {
        // Restore original OS property if needed
        System.setProperty("os.name", System.getProperty("os.name"));
    }

    @Test
    void testSymbolizeStack() {
        // Set up mock behavior for this test
        SymbolCache.BatchLookupResult mockResult = new SymbolCache.BatchLookupResult();
        mockResult.setHitSymbols(new HashMap<>());
        mockResult.setMissedAddresses(new long[]{0x1044231dcL, 0x10456a9a8L});
        when(symbolCache.batchLookup(anyString(), anyLong(), any(long[].class)))
                .thenReturn(mockResult);

        SymbolicManager.BatchLookupResult symbolicResult = new SymbolicManager.BatchLookupResult();
        Map<Long, String> hitSymbols = new HashMap<>();
        hitSymbols.put(0x1044231dcL, "symbolized_result_1");
        hitSymbols.put(0x10456a9a8L, "symbolized_result_2");
        symbolicResult.setHitSymbols(hitSymbols);
        when(symbolicManager.batchLookup(anyString(), anyLong(), any(long[].class)))
                .thenReturn(symbolicResult);

        // Mock symbol file service
        when(symbolFileService.getSymCacheFilePath("E469052D-2B00-3861-881F-D86D2BFF00E7"))
                .thenReturn(Optional.of(getSymbolPath(WANYUE_DSYM_PATH)));

        String symbolizedTrace = service.symbolizeStack(sampleTrace, "iPhone14,5");

        // Verify results
        assertThat(symbolizedTrace).isNotNull();
        assertThat(symbolizedTrace).contains("Binary Images:");
        assertThat(symbolizedTrace).contains("Hardware Model:      iPhone14,5");
        assertThat(symbolizedTrace).contains("万岳直播带货");
        assertThat(symbolizedTrace).contains("libsystem_kernel.dylib");

        // Verify correct mock interactions
        verify(symbolFileService).getSymCacheFilePath("E469052D-2B00-3861-881F-D86D2BFF00E7");
    }


    @Test
    void testSymbolizeStack2() {
        setupSymbolCache();
        // Mock only the symbolFileService response
        when(symbolFileService.getSymCacheFilePath("0FE9EB22-941A-3178-93C8-6E336EFBFD76"))
                .thenReturn(Optional.of(getSymbolPath(WYLIVE_DSYM_PATH2)));

        // Execute full symbolization
        String symbolizedTrace = service.symbolizeStack(sampleTrace2, "iPhone14,5");

        System.out.println(symbolizedTrace);
    }

    @Test
    void testSymbolizeStack3() {
        setupSymbolCache();

        // Mock only the symbolFileService response
        when(symbolFileService.getSymCacheFilePath("F850603B-FF6F-3D25-B995-5DA8B05DC5D6"))
                .thenReturn(Optional.of(getSymbolPath(WYLIVE_DSYM_PATH2)));

        // Execute full symbolization
        String symbolizedTrace = service.symbolizeStack(sampleTrace3,"iPhone14,5");

        System.out.println(symbolizedTrace);
    }

    @Test
    void testSymbolizeStack4() {
        setupSymbolCache();

        // Mock only the symbolFileService response
        when(symbolFileService.getSymCacheFilePath("552D6896-A42B-3100-AC82-DB192C13F2D8"))
                .thenReturn(Optional.of(getSymbolPath(WYLIVE_DSYM_PATH3)));

        // Execute full symbolization
        String symbolizedTrace = service.symbolizeStack(sampleTrace4,"iPhone14,5");

        System.out.println(symbolizedTrace);
    }


    @Test
    void testSymbolizeStackCrash5() {
        setupSymbolCache();

        // Mock only the symbolFileService response
        when(symbolFileService.getSymCacheFilePath("0D627AF4-D6F4-302D-8C5E-D63F014DBB2A"))
                .thenReturn(Optional.of(getSymbolPath(WYLIVE_DSYM_PATH4)));

        // Execute full symbolization
        String symbolizedTrace = service.symbolizeStack(sampleTrace5,"iPhone14,5");

        System.out.println(symbolizedTrace);
    }

    @Test
    void testBinaryImagePathPatterns() {
        String escapedPath = "       0x1a06f8000 -        0x1a1bb3fff JavaScriptCore arm64 <8C4289DE-9D14-3823-86C8-C749AF87C8DA> \\/System\\/Library\\/Frameworks\\/JavaScriptCore.framework\\/JavaScriptCore\n";
        String normalPath =   "       0x104908000 -        0x10495ffff GPUToolsCore arm64-unknown <24F08E8B-4E90-3E8E-8617-57869AFE80BE> /Developer/Library/PrivateFrameworks/GPUToolsCore.framework/GPUToolsCore\n" ;
        ;

        Pattern pattern = Pattern.compile(
                "\\s*"                  // 匹配开头的任意空白字符
                        + "(0x[\\da-f]+)"      // 捕获组1: 匹配16进制起始地址，如 0x10438c000
                        + "\\s+-\\s+"          // 匹配中间的横杠分隔符和空白，如 " - "
                        + "0x[\\da-f]+"        // 匹配16进制结束地址，如 0x104d33fff
                        + "\\s+"               // 匹配地址后的空白
                        + "\\+?([^\\s]+)"      // 捕获组2: 可选的+号后跟库名称，如 "+万岳直播带货" 或 "libBacktraceRecording.dylib"
                        + "\\s+"               // 匹配库名称后的空白
                        + ".*?"                // 非贪婪匹配中间的任意字符(arm64等信息)
                        + "<([A-F0-9-]+)>"     // 捕获组3: 匹配UUID，如 <E469052D-2B00-3861-881F-D86D2BFF00E7>
                        + "\\s+"               // 匹配UUID后的空白
                        + "([/\\\\][^\\n]+)"   // 捕获组4: 匹配路径，以斜杠或双反斜杠开头，直到行尾
        );


        assertThat(pattern.matcher(escapedPath).find()).isTrue();
        assertThat(pattern.matcher(normalPath).find()).isTrue();
    }


    @Test
    void testParseBinaryImages() {


        List<IosBinaryImage> images = service.parseBinaryImages(sampleTrace);

        assertThat(images).hasSize(8);

        IosBinaryImage appImage = images.get(0);
        assertThat(appImage.getName()).isEqualTo("万岳直播带货");
        assertThat(appImage.isSystemLib()).isFalse();
        assertThat(appImage.getStartAddress()).isEqualTo(0x10438c000L);
        assertThat(appImage.getUuid()).isEqualTo("E469052D-2B00-3861-881F-D86D2BFF00E7");

        IosBinaryImage systemImage = images.get(1);
        assertThat(systemImage.getName()).isEqualTo("libBacktraceRecording.dylib");
        assertThat(systemImage.isSystemLib()).isTrue();
        assertThat(systemImage.getStartAddress()).isEqualTo(0x1055ec000L);
        assertThat(systemImage.getUuid()).isEqualTo("C06EA3FB-389F-382E-9F49-AA72821CF99F");
    }

    @Test
    void testGroupStackFrames() {
        Map<String, List<IosStackFrame>> groups = service.groupStackFrames(sampleTrace);

        assertThat(groups).hasSize(10); // Total unique libraries in stack trace

        // Check app frames
        List<IosStackFrame> appFrames = groups.get("万岳直播带货");
        assertThat(appFrames).hasSize(4); // 3 frames + main
        assertThat(appFrames.get(0).getAddress()).isEqualTo(0x1044231dcL);
        assertThat(appFrames.get(3).getAddress()).isEqualTo(0x10456a9a8L); // main function

        // Check system library frames
        List<IosStackFrame> kernelFrames = groups.get("libsystem_kernel.dylib");
        assertThat(kernelFrames).hasSize(6); // Combined from different threads
        assertThat(kernelFrames.get(0).getAddress()).isEqualTo(0x1ca9c8f68L);

        // Check UIKitCore frames
        List<IosStackFrame> uikitFrames = groups.get("UIKitCore");
        assertThat(uikitFrames).hasSize(13);
        assertThat(uikitFrames.get(0).getOriginalLine()).contains("<redacted>");

        // Check CoreFoundation frames
        List<IosStackFrame> cfFrames = groups.get("CoreFoundation");
        assertThat(cfFrames).hasSize(8);
        assertThat(cfFrames.get(0).getAddress()).isEqualTo(0x18d129f34L);
    }


    @Test
    void testSymbolizeFrames() {
        setupSymbolCache();

        // Get actual frames and images from sampleTrace
        Map<String, List<IosStackFrame>> frames = service.groupStackFrames(sampleTrace);
        List<IosBinaryImage> images = service.parseBinaryImages(sampleTrace);

        // Mock only the symbolFileService response
        when(symbolFileService.getSymCacheFilePath("E469052D-2B00-3861-881F-D86D2BFF00E7"))
                .thenReturn(Optional.of(getSymbolPath(WANYUE_DSYM_PATH)));

        // Execute symbolization with real AtosUtil
        service.symbolizeFramesWithCache(frames, images,"iPhone14,5","16.2 (20C65)");

        // Verify results
        List<IosStackFrame> appFrames = frames.get("万岳直播带货");
        assertThat(appFrames).hasSize(4);
        assertThat(appFrames.get(0).getAddress()).isEqualTo(0x1044231dcL);
        assertThat(appFrames.get(3).getAddress()).isEqualTo(0x10456a9a8L);
    }


    @Test
    void testStackPattern() {
        Pattern stackPattern = Pattern.compile("([^\\s]+)\\s+(0x[\\da-fA-F]+)\\s+(.*)", Pattern.CASE_INSENSITIVE);

        String line = "WYLiveShopping 0x10421B5A4 -[MineSettingViewController setValueFor] + 148";

        Matcher matcher = stackPattern.matcher(line);
        System.out.println("Matches: " + matcher.matches());
        if(matcher.matches()) {
            System.out.println("Group 1: " + matcher.group(1));
            System.out.println("Group 2: " + matcher.group(2));
            System.out.println("Group 3: " + matcher.group(3));
        }
    }


}
