/*
 * Copyright (c) 2014-2016 <PERSON><PERSON>, This source is a part of
 * Audit4j - An open source auditing framework.
 * http://audit4j.org
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.audit4j.integration.spring;

import com.databuff.common.audit.AuditEntity;
import com.databuff.common.audit.AuditParamUtil;
import com.databuff.common.threadLocal.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.audit4j.core.DatabuffAuditManager;
import org.audit4j.core.dto.DatabuffAnnotationAuditEvent;
import org.audit4j.core.exception.Audit4jRuntimeException;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Collection;

/**
 * The Class AuditAspect.
 *
 * <pre>
 * {@code
 *
 *     <aop:aspectj-autoproxy>
 *         ...
 *         <aop:include name="auditAspect"/>
 *     </aop:aspectj-autoproxy>
 *
 *     <bean id="auditAspect" class="org.audit4j.integration.spring.AuditAspect" />
 *
 * }
 * </pre>
 *
 * <AUTHOR> href="mailto:<EMAIL>">Janith Bandara</a>
 */
@Slf4j
@Aspect
@Component
//public class DatabuffAuditAspect extends AuditAspect {
public class DatabuffAuditAspect {

    /**
     * Audit Aspect.
     *
     * @param joinPoint
     *            the joint point
     * @throws Throwable
     *             the throwable
     */
    @Around("@within(org.audit4j.core.annotation.DatabuffAudit) || @annotation(org.audit4j.core.annotation.DatabuffAudit)")
    public Object audit(final ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();

        if (method.getDeclaringClass().isInterface()) {
            try {
                method = joinPoint.getTarget().getClass()
                        .getDeclaredMethod(joinPoint.getSignature().getName(), method.getParameterTypes());
            } catch (final SecurityException | NoSuchMethodException exception) {
                throw new Audit4jRuntimeException(
                        "Exception occurred while proceeding Audit Aspect in Audit4j Spring Integration", exception);
            }
        }

        DatabuffAnnotationAuditEvent auditEvent = new DatabuffAnnotationAuditEvent(joinPoint.getTarget().getClass(), method, joinPoint.getArgs());
        final String account = ThreadLocalUtil.getAccount();
        if (account != null) {
            auditEvent.setActor(account);
        }
        // Proceed with the join point
        Object result;
        try {
            result = joinPoint.proceed();
            return result;
        } catch (Throwable throwable) {
            // Set the status to request failed
            auditEvent.setStatus(false);
            throw throwable;
        } finally {
            // Audit the method call
            final Collection<AuditEntity> auditEntity = AuditParamUtil.get();
            if (auditEntity != null) {
                auditEvent.setAuditEntity(auditEntity);
            }
            // Set the URL
            try {
                final RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();
                if (requestAttributes instanceof ServletRequestAttributes) {
                    final HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
                    auditEvent.setUrl(request.getRequestURL().toString());
                }
            } catch (Exception e) {
                log.debug("Failed to get the request URL", e);
            }

            DatabuffAuditManager.getInstance().audit(auditEvent);
            // Remove the parameters from ThreadLocal
            AuditParamUtil.remove();
        }
    }
}
