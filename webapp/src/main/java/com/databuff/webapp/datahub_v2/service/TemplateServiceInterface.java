package com.databuff.webapp.datahub_v2.service;

import com.databuff.webapp.datahub_v2.vo.PipelineDetail;
import com.databuff.webapp.datahub_v2.vo.TemplateRequest;
import com.databuff.webapp.datahub_v2.vo.TemplateVO;

import java.util.List;

/**
 * 模板服务接口
 * 提供Pipeline模板的增删改查功能
 */
public interface TemplateServiceInterface {
    
    /**
     * 创建模板
     * 
     * @param name 模板名称（唯一）
     * @param description 模板描述
     * @param dataType 数据类型
     * @param tags 标签
     * @param pipelineDetail Pipeline详情
     * @return 创建成功返回模板ID，失败返回null
     * @throws IllegalArgumentException 如果模板名称已存在
     */
    Integer createTemplate(TemplateRequest templateRequest);
    
    /**
     * 删除模板
     * 
     * @param templateId 模板ID
     * @return 删除成功返回true，失败返回false
     */
    boolean deleteTemplate(Integer templateId);
    
    /**
     * 查询模板列表
     * 
     * @param name 模板名称（可选，模糊查询）
     * @param dataType 数据类型（可选）
     * @param tags 标签（可选，模糊查询）
     * @return 模板列表
     */
    List<TemplateVO> queryTemplates(String name, String dataType, String tags, Integer templateId);
    
    /**
     * 查询模板详情内容
     * 
     * @param templateId 模板ID
     * @return 模板内容对应的Pipeline详情
     * @throws IllegalArgumentException 如果模板不存在
     */
    PipelineDetail getTemplateContent(Integer templateId);
    
    /**
     * 更新模板
     * 
     * @param templateId 模板ID
     * @param name 模板名称（可选）
     * @param description 模板描述（可选）
     * @param dataType 数据类型（可选）
     * @param tags 标签（可选）
     * @param pipelineDetail Pipeline详情（可选）
     * @return 更新成功返回true，失败返回false
     * @throws IllegalArgumentException 如果模板不存在或名称已被占用
     */
    boolean updateTemplate(Integer templateId, String name, String description, String dataType, String tags, PipelineDetail pipelineDetail);
} 