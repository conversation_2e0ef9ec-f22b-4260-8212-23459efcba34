package com.databuff.webapp.datahub_v2.common;

public enum ProcessorParentTypeEnum {
    EXPORTERS("exporters"),
    EXTENSIONS("extensions"),
    PROCESSORS("processors"),
    CONNECTORS("connectors"),
    RECEIVERS("receivers"),
    SERVICE("service"),
    PIPELINE("pipelines"),
    ;

    private final String name;

    ProcessorParentTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static ProcessorParentTypeEnum getByName(String name) {
        for (ProcessorParentTypeEnum processorParentTypeEnum : ProcessorParentTypeEnum.values()) {
            if (processorParentTypeEnum.getName().equals(name)) {
                return processorParentTypeEnum;
            }
        }
        return null;
    }
}
