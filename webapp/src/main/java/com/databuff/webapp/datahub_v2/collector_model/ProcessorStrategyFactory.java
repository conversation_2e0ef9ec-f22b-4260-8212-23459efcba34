package com.databuff.webapp.datahub_v2.collector_model;

import com.databuff.webapp.datahub_v2.collector_model.strategies.OtlpReceiver;
import com.databuff.webapp.datahub_v2.collector_model.strategies.PrometheusRemoteWriteReceiver;
import com.databuff.webapp.datahub_v2.collector_model.strategies.SkyWalkingReceiver;
import com.databuff.webapp.datahub_v2.collector_model.strategies.WebHookEventReceiver;
import com.databuff.webapp.datahub_v2.common.ProcessorParentTypeEnum;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 算子策略静态工厂，根据算子类型返回对应的策略，并执行对应的操作
 */
public class ProcessorStrategyFactory {
    private static final HashMap<String, ProcessorStrategy> strategyMap = new HashMap<>();

    static {
        strategyMap.put("receivers/otlp", new OtlpReceiver());
        strategyMap.put("receivers/skywalking", new SkyWalkingReceiver());
        strategyMap.put("receivers/webhookevent", new WebHookEventReceiver());
        strategyMap.put("receivers/prometheusremotewrite", new PrometheusRemoteWriteReceiver());
    }

    public static String processor(String config, String type, ProcessorParentTypeEnum parentType, String clusterName) {
        String key = parentType.getName() + "/" + type;
        ProcessorStrategy processorStrategy = strategyMap.getOrDefault(key, null);
        if (processorStrategy != null) {
            return processorStrategy.process(config, type, parentType, clusterName);
        }
        return config;
    }

    public static Map<String, Object> processor(Map<String, Object> config, String type, ProcessorParentTypeEnum parentType, String clusterName) {
        String key = parentType.getName() + "/" + type;
        ProcessorStrategy processorStrategy = strategyMap.getOrDefault(key, null);
        if (processorStrategy != null) {
            return processorStrategy.process(config, type, parentType, clusterName);
        }
        return config;
    }

    public static List<Integer> getPorts(Map<String, Object> config, String type, ProcessorParentTypeEnum parentType) {
        String key = parentType.getName() + "/" + type;
        ProcessorStrategy processorStrategy = strategyMap.getOrDefault(key, null);
        if (processorStrategy != null) {
            return processorStrategy.getProcessorPorts(config, type, parentType);
        }
        return Collections.emptyList();
    }
}
