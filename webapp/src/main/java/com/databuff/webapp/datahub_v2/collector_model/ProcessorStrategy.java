package com.databuff.webapp.datahub_v2.collector_model;

import com.databuff.webapp.datahub_v2.common.ProcessorParentTypeEnum;

import java.util.List;
import java.util.Map;

public interface ProcessorStrategy {
    String process(String config, String type, ProcessorParentTypeEnum parentType, String clusterName);

    Map<String, Object> process(Map<String, Object> config, String type, ProcessorParentTypeEnum parentType, String clusterName);

    List<Integer> getProcessorPorts(Map<String, Object> config, String type, ProcessorParentTypeEnum parentType);
}
