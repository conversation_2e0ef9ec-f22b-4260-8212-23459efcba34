package com.databuff.webapp.datahub_v2.collector_model.base;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Map;

/**
 * gRPC 配置类，用于定义 gRPC 相关的连接和传输设置。
 */
@Data
public class GrpcSetting {
    /**
     * gRPC 服务端点地址，例如：localhost:50051
     */
    private String endpoint;

    /**
     * 传输协议类型，例如：tcp、uds（Unix Domain Socket）等
     */
    private String transport;

    /**
     * 数据压缩算法名称，例如：gzip、deflate 等
     */
    private String compression;

    /**
     * 最大并发流数量，用于控制一个连接上的并发流上限
     */
    @JSONField(name = "max_concurrent_streams")
    private Integer maxConcurrentStreams;

    /**
     * 最大接收消息大小（以 MiB 为单位），用于限制接收到的消息的最大尺寸
     */
    @JSONField(name = "max_recv_msg_size_mib")
    private Integer maxRecvMsgSizeMib;

    /**
     * 读取缓冲区大小（字节），用于设置读操作的缓冲区容量
     */
    @JSONField(name = "read_buffer_size")
    private Integer readBufferSize;

    /**
     * 写入缓冲区大小（字节），用于设置写操作的缓冲区容量
     */
    @JSONField(name = "write_buffer_size")
    private Integer writeBufferSize;

    /**
     * 认证配置信息，用于 gRPC 连接的身份验证
     */
    private Map<String, Object> auth;

    /**
     * TLS 配置信息，用于安全传输层协议相关设置
     */
    private Map<String, Object> tls;

    /**
     * Keepalive 心跳机制配置，用于维持连接活跃状态
     */
    private Map<String, Object> keepalive;
}