package com.databuff.webapp.datahub_v2.bo.runningconfig;

import com.databuff.webapp.config.common.CommonHttpResponse;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * DataHub运行时配置类
 * 用于保存集群中所有Pipeline的运行时配置
 */
public class DataHubRunnerConfig implements CommonHttpResponse {
    /**
     * 导出器配置映射，键为导出器名称，值为导出器配置
     */
    private Map<String, Map<String, Object>> exporters;

    /**
     * 扩展配置映射，键为扩展名称，值为扩展配置
     * 通常不需要直接暴露给客户端
     */
    @JsonIgnore
    private Map<String, Map<String, Object>> extensions;

    /**
     * 处理器配置映射，键为处理器名称，值为处理器配置
     */
    private Map<String, Map<String, Object>> processors;

    /**
     * 接收器配置映射，键为接收器名称，值为接收器配置
     */
    private Map<String, Map<String, Object>> receivers;

    /**
     * 连接器配置映射，键为连接器名称，值为连接器配置
     */
    private Map<String, Map<String, Object>> connectors;

    /**
     * 服务配置，包含Pipeline拓扑结构和其他服务级别配置
     */
    private DataHubRunnerServiceConfig service;

    /**
     * 构造函数，初始化所有配置映射
     */
    public DataHubRunnerConfig() {
        service = new DataHubRunnerServiceConfig();
        exporters = new HashMap<>();
        extensions = new HashMap<>();
        processors = new HashMap<>();
        receivers = new HashMap<>();
        connectors = new HashMap<>();
    }

    /* Getter 和 Setter 方法 */

    public Map<String, Map<String, Object>> getExporters() {
        return exporters;
    }

    public void setExporters(Map<String, Map<String, Object>> exporters) {
        this.exporters = exporters;
    }

    public Map<String, Map<String, Object>> getExtensions() {
        return extensions;
    }

    public void setExtensions(Map<String, Map<String, Object>> extensions) {
        this.extensions = extensions;
    }

    public Map<String, Map<String, Object>> getProcessors() {
        return processors;
    }

    public void setProcessors(Map<String, Map<String, Object>> processors) {
        this.processors = processors;
    }

    public Map<String, Map<String, Object>> getReceivers() {
        return receivers;
    }

    public void setReceivers(Map<String, Map<String, Object>> receivers) {
        this.receivers = receivers;
    }

    public Map<String, Map<String, Object>> getConnectors() {
        return connectors;
    }

    public void setConnectors(Map<String, Map<String, Object>> connectors) {
        this.connectors = connectors;
    }

    public DataHubRunnerServiceConfig getService() {
        return service;
    }

    public void setService(DataHubRunnerServiceConfig service) {
        this.service = service;
    }
}