package com.databuff.webapp.datahub_v2.util;

import com.databuff.webapp.datahub_v2.vo.PipelineDetail;
import com.databuff.webapp.datahub_v2.vo.PipelineEdge;

import java.util.*;

/**
 * Pipeline拓扑结构验证工具类
 * 用于检查PipelineDetail中的edge拓扑结构是否存在循环
 */
public class PipelineTopologyValidator {

    /**
     * 检查Pipeline拓扑结构是否存在循环
     * 
     * @param pipelineDetail Pipeline详细信息
     * @return 如果存在循环，返回true；否则返回false
     */
    public static boolean hasCycle(PipelineDetail pipelineDetail) {
        if (pipelineDetail == null || pipelineDetail.getEdges() == null || pipelineDetail.getEdges().isEmpty()) {
            return false;
        }

        // 构建邻接表表示的图
        Map<Integer, List<Integer>> graph = buildGraph(pipelineDetail.getEdges());
        
        // 使用DFS检测环
        return detectCycleWithDFS(graph);
    }
    
    /**
     * 构建邻接表表示的图
     * 
     * @param edges 边列表
     * @return 邻接表表示的图
     */
    private static Map<Integer, List<Integer>> buildGraph(List<PipelineEdge> edges) {
        Map<Integer, List<Integer>> graph = new HashMap<>();
        
        for (PipelineEdge edge : edges) {
            int from = edge.getFrom();
            int to = edge.getTo();
            
            // 添加from到to的边
            graph.computeIfAbsent(from, k -> new ArrayList<>()).add(to);
        }
        
        return graph;
    }
    
    /**
     * 使用深度优先搜索检测图中是否存在环
     * 
     * @param graph 邻接表表示的图
     * @return 如果存在环，返回true；否则返回false
     */
    private static boolean detectCycleWithDFS(Map<Integer, List<Integer>> graph) {
        Set<Integer> visited = new HashSet<>();
        Set<Integer> recursionStack = new HashSet<>();
        
        // 对每个节点进行DFS
        for (Integer node : graph.keySet()) {
            if (!visited.contains(node)) {
                if (dfsUtil(node, graph, visited, recursionStack)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * DFS工具方法，用于检测从指定节点开始的路径是否存在环
     * 
     * @param node 当前节点
     * @param graph 邻接表表示的图
     * @param visited 已访问节点集合
     * @param recursionStack 递归栈，用于检测环
     * @return 如果存在环，返回true；否则返回false
     */
    private static boolean dfsUtil(Integer node, Map<Integer, List<Integer>> graph, 
                                  Set<Integer> visited, Set<Integer> recursionStack) {
        // 将当前节点标记为已访问，并加入递归栈
        visited.add(node);
        recursionStack.add(node);
        
        // 遍历当前节点的所有邻接节点
        List<Integer> neighbors = graph.get(node);
        if (neighbors != null) {
            for (Integer neighbor : neighbors) {
                // 如果邻接节点未被访问，则递归访问
                if (!visited.contains(neighbor)) {
                    if (dfsUtil(neighbor, graph, visited, recursionStack)) {
                        return true;
                    }
                } 
                // 如果邻接节点已在递归栈中，说明存在环
                else if (recursionStack.contains(neighbor)) {
                    return true;
                }
            }
        }
        
        // 回溯时，将当前节点从递归栈中移除
        recursionStack.remove(node);
        return false;
    }
    
    /**
     * 获取Pipeline拓扑结构中的所有环
     * 
     * @param pipelineDetail Pipeline详细信息
     * @return 所有环的列表，每个环是一个节点ID的列表
     */
    public static List<List<Integer>> findAllCycles(PipelineDetail pipelineDetail) {
        if (pipelineDetail == null || pipelineDetail.getEdges() == null || pipelineDetail.getEdges().isEmpty()) {
            return Collections.emptyList();
        }

        // 构建邻接表表示的图
        Map<Integer, List<Integer>> graph = buildGraph(pipelineDetail.getEdges());
        
        // 查找所有环
        return findAllCyclesWithDFS(graph);
    }
    
    /**
     * 使用深度优先搜索查找图中所有的环
     * 
     * @param graph 邻接表表示的图
     * @return 所有环的列表，每个环是一个节点ID的列表
     */
    private static List<List<Integer>> findAllCyclesWithDFS(Map<Integer, List<Integer>> graph) {
        List<List<Integer>> cycles = new ArrayList<>();
        Set<Integer> visited = new HashSet<>();
        List<Integer> path = new ArrayList<>();
        
        // 对每个节点进行DFS
        for (Integer node : graph.keySet()) {
            if (!visited.contains(node)) {
                path.clear();
                findAllCyclesUtil(node, graph, visited, path, cycles);
            }
        }
        
        return cycles;
    }
    
    /**
     * DFS工具方法，用于查找从指定节点开始的所有环
     * 
     * @param node 当前节点
     * @param graph 邻接表表示的图
     * @param visited 已访问节点集合
     * @param path 当前路径
     * @param cycles 所有环的列表
     */
    private static void findAllCyclesUtil(Integer node, Map<Integer, List<Integer>> graph, 
                                         Set<Integer> visited, List<Integer> path, 
                                         List<List<Integer>> cycles) {
        // 将当前节点加入路径
        path.add(node);
        visited.add(node);
        
        // 遍历当前节点的所有邻接节点
        List<Integer> neighbors = graph.get(node);
        if (neighbors != null) {
            for (Integer neighbor : neighbors) {
                // 如果邻接节点已在路径中，说明找到了一个环
                if (path.contains(neighbor)) {
                    int startIndex = path.indexOf(neighbor);
                    List<Integer> cycle = new ArrayList<>(path.subList(startIndex, path.size()));
                    cycle.add(neighbor); // 添加起始节点，使环闭合
                    cycles.add(cycle);
                } 
                // 如果邻接节点未被访问，则递归访问
                else if (!visited.contains(neighbor)) {
                    findAllCyclesUtil(neighbor, graph, visited, path, cycles);
                }
            }
        }
        
        // 回溯时，将当前节点从路径中移除
        path.remove(path.size() - 1);
    }
} 