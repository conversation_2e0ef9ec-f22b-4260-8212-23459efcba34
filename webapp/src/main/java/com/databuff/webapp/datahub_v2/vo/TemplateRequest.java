package com.databuff.webapp.datahub_v2.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * 模板上传请求的结构定义
 */
@Data
@ApiModel(description = "模板上传请求的结构定义")
public class TemplateRequest {
    /**
     * 模板元信息
     */
    @ApiModelProperty(value = "模板元信息", required = true)
    private TemplateMeta meta;
    
    /**
     * Pipeline详情
     */
    @ApiModelProperty(value = "Pipeline详情", required = true)
    private PipelineDetail pipeline;
    
    /**
     * 模板元信息
     */
    @Data
    @ApiModel(description = "模板元信息")
    public static class TemplateMeta {
        /**
         * 模板名称
         */
        @ApiModelProperty(value = "模板名称", required = true, example = "数据处理模板")
        @Pattern(regexp = "^[a-zA-Z0-9_-]{1,64}$", message = "处理器的名称必须由1到64个字符组成，只能包含字母、数字、下划线或连字符")
        private String name;
        
        /**
         * 模板描述
         */
        @ApiModelProperty(value = "模板描述", example = "用于数据处理的Pipeline模板")
        private String description;
        
        /**
         * 数据类型
         */
        @ApiModelProperty(value = "数据类型", required = true, example = "PROMETHEUS")
        private String dataType;
        
        /**
         * 标签，多个标签用逗号分隔
         */
        @ApiModelProperty(value = "标签，多个标签用逗号分隔", example = "监控,数据处理")
        private List<String> tags;
    }
} 