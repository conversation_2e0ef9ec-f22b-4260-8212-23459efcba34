package com.databuff.webapp.datahub_v2.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Pattern;
import java.util.Map;

/**
 * 用于表示处理器配置的类
 */
@ApiModel(description = "处理器配置信息")
public class ProcessorConfig {
    /**
     * 处理器的唯一标识符
     */
    @ApiModelProperty(value = "处理器的唯一标识符， 为负数表示新创建的", example = "1")
    private Integer processorId;

    /**
     * 处理器的引用处理器ID，processorId 为空，如果引用了，则表示不用新增，只创建edge边的关系
     */
    @ApiModelProperty(value = "处理器的引用处理器ID，processorId 为空，如果引用了，则表示不用新增，只创建edge边的关系", example = "1")
    private Integer referenceProcessorId;

    /**
     * 处理器的名称，校验规则为：^[a-zA-Z0-9_-]{1,64}$
     */
    @ApiModelProperty(value = "处理器的名称", example = "数据转换处理器")
    @Pattern(regexp = "^[a-zA-Z0-9_-]{1,64}$", message = "处理器的名称必须由1到64个字符组成，只能包含字母、数字、下划线或连字符")
    private String name;

    /**
     * 处理器的描述信息
     */
    @ApiModelProperty(value = "处理器的描述信息", example = "用于转换数据格式的处理器")
    private String description;

    /**
     * 处理器的类型
     */
    @ApiModelProperty(value = "处理器的类型", example = "TRANSFORM")
    private String type;

    /**
     * 处理器的父类型
     */
    @ApiModelProperty(value = "处理器的父类型", example = "DATA_PROCESSOR")
    private String parentType;

    /**
     * 处理器的具体配置信息，以键值对的形式存储
     */
    @ApiModelProperty(value = "处理器的具体配置信息", example = "{\"inputFormat\": \"JSON\", \"outputFormat\": \"CSV\"}")
    private Map<String, Object> config;

    /**
     * 标记处理器是否为用户收藏
     */
    @ApiModelProperty(value = "是否被用户收藏", example = "true")
    private boolean isFavorite;

    /**
     * 处理器的创建时间戳
     */
    @ApiModelProperty(value = "处理器的创建时间戳", example = "1648888888000")
    private Long createAt;

    /**
     * 处理器的最后更新时间戳
     */
    @ApiModelProperty(value = "处理器的最后更新时间戳", example = "1648888999000")
    private Long updateAt;

    /* Getter 和 Setter 方法 */

    public Long getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Long updateAt) {
        this.updateAt = updateAt;
    }

    public Long getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Long createAt) {
        this.createAt = createAt;
    }

    public boolean isFavorite() {
        return isFavorite;
    }

    public void setFavorite(boolean favorite) {
        isFavorite = favorite;
    }

    public Map<String, Object> getConfig() {
        return config;
    }

    public void setConfig(Map<String, Object> config) {
        this.config = config;
    }

    public String getParentType() {
        return parentType;
    }

    public void setParentType(String parentType) {
        this.parentType = parentType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public @Pattern(regexp = "^[a-zA-Z0-9_-]{1,64}$", message = "处理器的名称必须由1到64个字符组成，只能包含字母、数字、下划线或连字符") String getName() {
        return name;
    }

    public void setName(@Pattern(regexp = "^[a-zA-Z0-9_-]{1,64}$", message = "处理器的名称必须由1到64个字符组成，只能包含字母、数字、下划线或连字符") String name) {
        this.name = name;
    }

    public Integer getReferenceProcessorId() {
        return referenceProcessorId;
    }

    public void setReferenceProcessorId(Integer referenceProcessorId) {
        this.referenceProcessorId = referenceProcessorId;
    }

    public Integer getProcessorId() {
        return processorId;
    }

    public void setProcessorId(Integer processorId) {
        this.processorId = processorId;
    }
}
