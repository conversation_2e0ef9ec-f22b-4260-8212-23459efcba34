package com.databuff.webapp.datahub_v2.controller;

import com.alibaba.fastjson.JSON;
import com.databuff.webapp.config.common.CommonResponse;
import com.databuff.webapp.config.interceptor.DomainManagerRequired;
import com.databuff.webapp.datahub_v2.service.TemplateServiceInterface;
import com.databuff.webapp.datahub_v2.util.PipelineTopologyValidator;
import com.databuff.webapp.datahub_v2.vo.PipelineDetail;
import com.databuff.webapp.datahub_v2.vo.TemplateRequest;
import com.databuff.webapp.datahub_v2.vo.TemplateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

@Api(value = "模板管理接口")
@RestController
@RequestMapping("/datahub/v1/template")
@Validated
public class TemplateController {

    @Autowired
    private TemplateServiceInterface templateService;

    @ApiOperation("创建模板")
    @PostMapping(value = "/create", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @DomainManagerRequired
    public CommonResponse<Integer> createTemplate(
            @ApiParam(value = "模板内容文件(JSON格式)", required = true) @RequestParam("file") MultipartFile file) {
        
        try {
            // 读取文件内容
            String content = new String(file.getBytes(), StandardCharsets.UTF_8);
            
            // 解析JSON内容
            try {
                // 解析为TemplateRequest对象
                TemplateRequest templateRequest = JSON.parseObject(content, TemplateRequest.class);
                
                // 提取模板元信息
                if (templateRequest.getMeta() == null) {
                    return new CommonResponse<>(HttpStatus.BAD_REQUEST.value(), "模板元信息不能为空", null);
                }
                TemplateRequest.TemplateMeta meta = templateRequest.getMeta();
                // 确保name不为空
                if (meta.getName() == null || meta.getName().trim().isEmpty()) {
                    return new CommonResponse<>(HttpStatus.BAD_REQUEST.value(), "模板名称不能为空", null);
                }
                
                // 确保dataType不为空
                if (meta.getDataType() == null || meta.getDataType().trim().isEmpty()) {
                    return new CommonResponse<>(HttpStatus.BAD_REQUEST.value(), "数据类型不能为空", null);
                }
                
                // 提取Pipeline详情
                PipelineDetail pipelineDetail = templateRequest.getPipeline();
                if (pipelineDetail == null) {
                    return new CommonResponse<>(HttpStatus.BAD_REQUEST.value(), "Pipeline信息不能为空", null);
                }
                
                // 校验拓扑结构
                try {
                    validatePipelineTopology(pipelineDetail);
                } catch (IllegalArgumentException e) {
                    return new CommonResponse<>(HttpStatus.BAD_REQUEST.value(), e.getMessage(), null);
                }
                
                // 创建模板
                try {
                    Integer templateId = templateService.createTemplate(templateRequest);
                    return new CommonResponse<>(HttpStatus.OK.value(), "success", templateId);
                } catch (IllegalArgumentException e) {
                    return new CommonResponse<>(HttpStatus.BAD_REQUEST.value(), e.getMessage(), null);
                }
                
            } catch (Exception e) {
                return new CommonResponse<>(HttpStatus.BAD_REQUEST.value(), "模板内容格式不正确: " + e.getMessage(), null);
            }
            
        } catch (IOException e) {
            return new CommonResponse<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), "读取文件失败: " + e.getMessage(), null);
        }
    }

    @ApiOperation("删除模板")
    @DeleteMapping("/{templateId}")
    @DomainManagerRequired
    public CommonResponse<Boolean> deleteTemplate(
            @ApiParam(value = "模板ID", required = true) @PathVariable Integer templateId) {
        
        boolean result = templateService.deleteTemplate(templateId);
        if (result) {
            return new CommonResponse<>(HttpStatus.OK.value(), "success", true);
        } else {
            return new CommonResponse<>(HttpStatus.NOT_FOUND.value(), "模板不存在", false);
        }
    }

    @ApiOperation("查询模板列表")
    @GetMapping("/list")
    @DomainManagerRequired
    public CommonResponse<List<TemplateVO>> queryTemplates(
            @ApiParam(value = "模板名称(模糊查询)") @RequestParam(required = false) String name,
            @ApiParam(value = "数据类型") @RequestParam(required = false) String dataType,
            @ApiParam(value = "标签(模糊查询)") @RequestParam(required = false) String tags) {
        
        List<TemplateVO> templates = templateService.queryTemplates(name, dataType, tags, null);
        
        return new CommonResponse<>(HttpStatus.OK.value(), "success", templates);
    }

    @ApiOperation("获取模板详情内容")
    @GetMapping("/{templateId}/content")
    @DomainManagerRequired
    public CommonResponse<PipelineDetail> getTemplateContent(
            @ApiParam(value = "模板ID", required = true) @PathVariable Integer templateId) {
        
        try {
            PipelineDetail detail = templateService.getTemplateContent(templateId);
            return new CommonResponse<>(HttpStatus.OK.value(), "success", detail);
        } catch (IllegalArgumentException e) {
            return new CommonResponse<>(HttpStatus.BAD_REQUEST.value(), e.getMessage(), null);
        }
    }

    @ApiOperation("更新模板")
    @PutMapping(value = "/{templateId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @DomainManagerRequired
    public CommonResponse<Boolean> updateTemplate(
            @ApiParam(value = "模板ID", required = true) @PathVariable Integer templateId,
            @ApiParam(value = "模板名称") @RequestParam(required = false) String name,
            @ApiParam(value = "模板描述") @RequestParam(required = false) String description,
            @ApiParam(value = "数据类型") @RequestParam(required = false) String dataType,
            @ApiParam(value = "标签，多个标签用逗号分隔") @RequestParam(required = false) String tags,
            @ApiParam(value = "模板内容文件(JSON格式)") @RequestParam(value = "file", required = false) MultipartFile file) {
        
        PipelineDetail pipelineDetail = null;
        
        // 如果提供了文件，解析为PipelineDetail
        if (file != null && !file.isEmpty()) {
            try {
                String content = new String(file.getBytes(), StandardCharsets.UTF_8);
                
                try {
                    pipelineDetail = JSON.parseObject(content, PipelineDetail.class);
                    
                    // 校验拓扑结构
                    validatePipelineTopology(pipelineDetail);
                } catch (IllegalArgumentException e) {
                    return new CommonResponse<>(HttpStatus.BAD_REQUEST.value(), e.getMessage(), null);
                } catch (Exception e) {
                    return new CommonResponse<>(HttpStatus.BAD_REQUEST.value(), "模板内容格式不正确: " + e.getMessage(), null);
                }
            } catch (IOException e) {
                return new CommonResponse<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), "读取文件失败: " + e.getMessage(), null);
            }
        }
        
        try {
            boolean result = templateService.updateTemplate(templateId, name, description, dataType, tags, pipelineDetail);
            return new CommonResponse<>(HttpStatus.OK.value(), "success", result);
        } catch (IllegalArgumentException e) {
            return new CommonResponse<>(HttpStatus.BAD_REQUEST.value(), e.getMessage(), null);
        }
    }
    
    @ApiOperation("获取模板详情(不包含内容)")
    @GetMapping("/{templateId}")
    @DomainManagerRequired
    public CommonResponse<TemplateVO> getTemplate(
            @ApiParam(value = "模板ID", required = true) @PathVariable Integer templateId) {

        List<TemplateVO> templates = templateService.queryTemplates(null, null, null, templateId);

        return new CommonResponse<>(HttpStatus.OK.value(), "success", !templates.isEmpty() ? templates.get(0) : null);
    }


    /**
     * 验证Pipeline拓扑结构
     *
     * @param pipelineDetail Pipeline详情
     * @throws IllegalArgumentException 如果拓扑结构不合法
     */
    private void validatePipelineTopology(PipelineDetail pipelineDetail) {
        if (pipelineDetail.getEdges() == null || pipelineDetail.getEdges().isEmpty()) {
            throw new IllegalArgumentException("Pipeline必须包含至少一条边");
        }

        if (pipelineDetail.processorConfigConvertMap() == null || pipelineDetail.processorConfigConvertMap().isEmpty()) {
            throw new IllegalArgumentException("Pipeline必须包含至少一个处理器");
        }

        // 检查拓扑是否存在循环
        if (PipelineTopologyValidator.hasCycle(pipelineDetail)) {
            throw new IllegalArgumentException("Pipeline拓扑结构中存在循环，请检查并修正");
        }
    }
} 