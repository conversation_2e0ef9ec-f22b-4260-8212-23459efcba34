package com.databuff.webapp.datahub_v2.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.databuff.webapp.config.common.CommonResponse;
import com.databuff.webapp.config.interceptor.DomainManagerRequired;
import com.databuff.webapp.datahub_v2.dto.PipelineListQuery;
import com.databuff.webapp.datahub_v2.service.PipelineServiceInterface;
import com.databuff.webapp.datahub_v2.util.CommonValidate;
import com.databuff.webapp.datahub_v2.util.MapObjectUtil;
import com.databuff.webapp.datahub_v2.vo.PageResult;
import com.databuff.webapp.datahub_v2.vo.PipelineDetail;
import com.databuff.webapp.datahub_v2.vo.PipelineList;
import com.databuff.webapp.datahub_v2.vo.ProcessorConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Api(value = "DataHub pipeline 管理接口 ")
@RestController
@Validated
@RequestMapping("/datahub/v1/pipeline")
public class PipelineController {

    @Autowired
    private PipelineServiceInterface pipelineService;


    @GetMapping("/names")
    @ApiOperation("查询所有Pipeline名称列表")
    @DomainManagerRequired
    public CommonResponse<List<String>> queryPipelineNameList() {
        List<String> allPipelineNames = pipelineService.getAllPipelineNames();
        return new CommonResponse<>(HttpStatus.OK.value(), "success", allPipelineNames);
    }

    @PostMapping("/list")
    @ApiOperation("查询Pipeline列表")
    @DomainManagerRequired
    public CommonResponse<PageResult<PipelineList>> queryPipelineList(
            @ApiParam(value = "查询参数", required = true) @RequestBody PipelineListQuery query) {
        PageResult<PipelineList> result = pipelineService.queryPipelineList(query);
        return new CommonResponse<>(HttpStatus.OK.value(), "success", result);
    }

    @PostMapping("/create")
    @ApiOperation("创建Pipeline")
    @DomainManagerRequired
    public CommonResponse<PipelineDetail> createPipeline(
            @ApiParam(value = "Pipeline详情", required = true) @Valid @RequestBody PipelineDetail pipelineDetail) {
        try {
            PipelineDetail result = pipelineService.createPipeline(pipelineDetail);
            return new CommonResponse<>(HttpStatus.OK.value(), "success", result);
        } catch (Exception e) {
            return new CommonResponse<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage(), null);
        }
    }

    @ApiOperation("保存Pipeline")
    @PostMapping("/save")
    @DomainManagerRequired
    public CommonResponse<PipelineDetail> savePipeline(@Valid @RequestBody PipelineDetail pipelineDetail) {
        try {
            PipelineDetail savedPipeline = pipelineService.savePipeline(pipelineDetail);
            return new CommonResponse<>(HttpStatus.OK.value(), "success", savedPipeline);
        } catch (Exception e) {
            return new CommonResponse<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage(), null);
        }
    }

    @ApiOperation("根据模板创建Pipeline")
    @PostMapping("/createFromTemplate")
    @DomainManagerRequired
    public CommonResponse<PipelineDetail> createPipelineFromTemplate(
            @ApiParam(value = "模板ID", required = true) @RequestParam Integer templateId,
            @ApiParam(value = "集群ID", required = true) @RequestParam Integer clusterId,
            @RequestBody PipelineDetail pipelineCreateInfo) {
        try {
            PipelineDetail pipelineDetail = pipelineService.createPipelineFromTemplate(templateId, clusterId, pipelineCreateInfo);
            return new CommonResponse<>(HttpStatus.OK.value(), "success", pipelineDetail);
        } catch (IllegalArgumentException e) {
            return new CommonResponse<>(HttpStatus.BAD_REQUEST.value(), e.getMessage(), null);
        } catch (Exception e) {
            return new CommonResponse<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), "Failed to create pipeline from template", null);
        }
    }

    @PutMapping("/{pipelineId}/status/{status}")
    @ApiOperation("更新Pipeline状态")
    @DomainManagerRequired
    public CommonResponse<PipelineDetail> updatePipelineStatus(
            @ApiParam(value = "Pipeline ID", required = true) @PathVariable Integer pipelineId,
            @ApiParam(value = "状态", required = true) @PathVariable String status) {
        try {
            PipelineDetail result = pipelineService.updatePipelineStatus(pipelineId, status);
            return new CommonResponse<>(HttpStatus.OK.value(), "success", result);
        } catch (IllegalArgumentException e) {
            return new CommonResponse<>(HttpStatus.BAD_REQUEST.value(), e.getMessage(), null);
        }
    }

    @PutMapping("/batch/status/{status}")
    @ApiOperation("批量更新Pipeline状态")
    @DomainManagerRequired
    public CommonResponse<Boolean> batchUpdatePipelineStatus(
            @ApiParam(value = "Pipeline ID列表", required = true) @RequestBody List<Integer> pipelineIds,
            @ApiParam(value = "状态", required = true) @PathVariable String status) {
        try {
            Boolean results = pipelineService.batchUpdatePipelineStatus(pipelineIds, status);
            return new CommonResponse<>(HttpStatus.OK.value(), "success", results);
        } catch (IllegalArgumentException e) {
            return new CommonResponse<>(HttpStatus.BAD_REQUEST.value(), e.getMessage(), null);
        }
    }

    @DeleteMapping("/batch")
    @ApiOperation("批量删除Pipeline")
    @DomainManagerRequired
    public CommonResponse<Boolean> batchDeletePipelines(
            @ApiParam(value = "Pipeline ID列表", required = true) @RequestBody List<Integer> pipelineIds) {
        try {
            Boolean result = pipelineService.batchDeletePipelines(pipelineIds);
            return new CommonResponse<>(HttpStatus.OK.value(), "success", result);
        } catch (IllegalStateException e) {
            return new CommonResponse<>(HttpStatus.BAD_REQUEST.value(), e.getMessage(), null);
        } catch (Exception e) {
            return new CommonResponse<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), "Failed to delete pipelines: " + e.getMessage(), null);
        }
    }

    @DeleteMapping("/processor/{processorId}")
    @ApiOperation("删除Processor")
    @DomainManagerRequired
    public CommonResponse<Void> deleteProcessor(
            @ApiParam(value = "Processor ID", required = true) @PathVariable Integer processorId) {
        try {
            boolean result = pipelineService.deleteProcessor(processorId);
            if (result) {
                return new CommonResponse<>(HttpStatus.OK.value(), "success", null);
            } else {
                return new CommonResponse<>(HttpStatus.NOT_FOUND.value(), "Processor not found", null);
            }
        } catch (IllegalStateException e) {
            return new CommonResponse<>(HttpStatus.BAD_REQUEST.value(), e.getMessage(), null);
        }
    }

    @GetMapping("/pipelineListDetail")
    @ApiOperation("pipeline列表详情查询")
    public CommonResponse<List<PipelineDetail>> pipelinesDetail(HttpServletResponse response,
                                                                @ApiParam(value = "Pipeline ID列表", required = true) @RequestParam List<Integer> pipelineIds) {
        try {
            // 查询Pipeline详情列表
            List<PipelineDetail> pipelineDetails = pipelineService.getPipelineDetailList(pipelineIds);

            return new CommonResponse<>(HttpStatus.OK.value(), "success", pipelineDetails);
        } catch (Exception e) {
            throw new RuntimeException("Pipeline 列表详情获取失败: " + e.getMessage());
        }
    }

    @GetMapping("/export")
    @ApiOperation("导出Pipeline配置")
    public void exportPipelines(HttpServletResponse response,
                                @ApiParam(value = "Pipeline ID列表", required = true) @RequestParam List<Integer> pipelineIds,
                                @ApiParam(value = "导出格式", allowableValues = "json", defaultValue = "json")
                                @RequestParam(defaultValue = "json") String format) {

        try {
            // 查询Pipeline详情列表
            List<PipelineDetail> pipelineDetails = pipelineService.getPipelineDetailList(pipelineIds);

            // 转换为JSON格式
            String jsonContent = MapObjectUtil.convertObjectToJson(pipelineDetails);
            byte[] content = jsonContent.getBytes(StandardCharsets.UTF_8);

            // 将文件内容存储在HttpServletRequest中
            // 设置响应头
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=pipelines.json");
            response.setContentLength(content.length);
            try (ServletOutputStream outputStream = response.getOutputStream()) {
                outputStream.write(content);
            }
        } catch (Exception e) {
            throw new RuntimeException("文件下载失败: " + e.getMessage());
        }
    }

    @PostMapping("/import")
    @ApiOperation("导入Pipeline配置")
    @DomainManagerRequired
    public CommonResponse<List<PipelineDetail>> importPipelines(
            @ApiParam(value = "Pipeline配置文件(JSON格式)", required = true) @RequestParam("file") MultipartFile file) {

        try {
            // 读取文件内容
            String content = new String(file.getBytes(), StandardCharsets.UTF_8);

            // 解析JSON内容为通用对象，避免绑定错误
            List<JSONObject> rawPipelines = JSON.parseArray(content, JSONObject.class);
            List<PipelineDetail> pipelineDetails = new ArrayList<>();

            for (JSONObject rawPipeline : rawPipelines) {
                // 修改Pipeline名称，添加_import后缀
                String name = rawPipeline.getString("name");
                if (!CommonValidate.isValidName(name)) {
                    throw new IllegalArgumentException("Invalid pipeline name: " + name);
                }
                rawPipeline.put("name", name + "_import" + "_" + System.currentTimeMillis());

                // 修改所有处理器名称
                JSONObject processorConfigMap = rawPipeline.getJSONObject("processorConfigMap");
                if (processorConfigMap != null) {
                    for (String key : processorConfigMap.keySet()) {
                        JSONObject processor = processorConfigMap.getJSONObject(key);
                        if (processor != null) {
                            String processorName = processor.getString("name");
                            if (!CommonValidate.isValidName(processorName)) {
                                throw new IllegalArgumentException("Invalid pipeline name: " + name);
                            }
                            processor.put("name", processorName + "_import" + "_" + System.currentTimeMillis());
                        }
                    }
                }

                // 重新转换为Java对象
                PipelineDetail pipeline = JSON.toJavaObject(rawPipeline, PipelineDetail.class);
                pipelineDetails.add(pipeline);
            }

            // 保存导入的Pipeline
            List<PipelineDetail> savedPipelines = pipelineDetails.stream()
                    .map(pipelineService::createPipeline)
                    .collect(Collectors.toList());

            return new CommonResponse<>(HttpStatus.OK.value(), "success", savedPipelines);
        } catch (Exception e) {
            return new CommonResponse<>(HttpStatus.BAD_REQUEST.value(), "Failed to import pipelines", null);
        }
    }

    @PostMapping("/{pipelineId}/copy/{pipelineName}")
    @ApiOperation("复制Pipeline")
    @DomainManagerRequired
    public CommonResponse<PipelineDetail> copyPipeline(
            @ApiParam(value = "Pipeline ID", required = true) @PathVariable Integer pipelineId,
            @ApiParam(value = "Pipeline名称", required = true) @PathVariable String pipelineName) {
        try {
            // 1. 查询原始Pipeline详情
            List<Integer> pipelineIds = new ArrayList<>();
            pipelineIds.add(pipelineId);
            List<PipelineDetail> pipelineDetails = pipelineService.getPipelineDetailList(pipelineIds);
            if (pipelineDetails.isEmpty()) {
                return new CommonResponse<>(HttpStatus.NOT_FOUND.value(), "Pipeline not found", null);
            }

            // 2. 将原始Pipeline转换为JSON对象
            PipelineDetail originalPipeline = pipelineDetails.get(0);

            // 3. 修改JSON对象属性
            // 清空ID
            originalPipeline.setPipelineId(null);
            originalPipeline.setName(pipelineName);
            // 修改名称添加_copy后缀
//            String name = pipelineJson.getString("name");
//            if (name != null) {
//                pipelineJson.put("name", name + "_copy" + "_" + System.currentTimeMillis());
//            }

            // 4. 处理处理器配置
            Map<String, ProcessorConfig> processorConfigMap = originalPipeline.getProcessorConfigMap();
            if (processorConfigMap != null) {
                for (String key : processorConfigMap.keySet()) {
                    ProcessorConfig processorConfig = processorConfigMap.get(key);
                    if (processorConfig != null) {
                        // 清空处理器ID
                        processorConfig.setProcessorId(null);

                        // 修改处理器名称添加_copy后缀
                        String processorName = processorConfig.getName();
                        if (processorName != null) {
                            processorConfig.setName(processorName + "_copy" + "_" + System.currentTimeMillis());
                        }
                    }
                }
            }

            // 5. 转换回Java对象并创建新Pipeline
            PipelineDetail copiedPipeline = pipelineService.createPipeline(originalPipeline);

            return new CommonResponse<>(HttpStatus.OK.value(), "success", copiedPipeline);
        } catch (Exception e) {
            return new CommonResponse<>(HttpStatus.BAD_REQUEST.value(), "Failed to copy pipeline", null);
        }
    }

    @GetMapping("/{pipelineId}")
    @ApiOperation("获取Pipeline详情")
    @DomainManagerRequired
    public CommonResponse<PipelineDetail> getPipelineDetail(
            @ApiParam(value = "Pipeline ID", required = true) @PathVariable Integer pipelineId) {
        try {
            PipelineDetail result = pipelineService.getPipelineDetail(pipelineId);
            if (result == null) {
                return new CommonResponse<>(HttpStatus.NOT_FOUND.value(), "Pipeline不存在", null);
            }
            return new CommonResponse<>(HttpStatus.OK.value(), "success", result);
        } catch (Exception e) {
            return new CommonResponse<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取Pipeline详情失败: " + e.getMessage(), null);
        }
    }
}
