package com.databuff.webapp.datahub_v2.util;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 端口提取工具类
 */
public class PortExtractor {

    // 匹配端口的正则表达式：支持 ":8080", "0.0.0.0:9094", "http://0.0.0.0:8442" 等格式
    private static final Pattern PORT_PATTERN = Pattern.compile("(?:https?://)?(?:[\\w.]+)?(?::\\d{1,5})");
    
    // 提取具体端口号的正则表达式
    private static final Pattern PORT_NUMBER_PATTERN = Pattern.compile(":(\\d{1,5})");
    
    // 识别HTTP/GRPC前缀的正则表达式
    private static final Pattern HTTP_PATTERN = Pattern.compile("https?://");
    private static final Pattern GRPC_PATTERN = Pattern.compile("grpc://");

    /**
     * 从配置中提取端口信息
     * @param config 处理器配置
     * @return 端口号，如果未找到则返回null
     */
    public static Integer extractPortFromConfig(Map<String, Object> config) {
        if (config == null) {
            return null;
        }
        
        // 递归查找配置中可能包含端口的值
        String portValue = findPortInConfig(config);
        
        if (portValue != null) {
            // 提取端口号
            Matcher portMatcher = PORT_NUMBER_PATTERN.matcher(portValue);
            if (portMatcher.find()) {
                try {
                    return Integer.parseInt(portMatcher.group(1));
                } catch (NumberFormatException e) {
                    return null;
                }
            }
        }
        
        return null;
    }
    
    /**
     * 从配置中提取协议类型和端口信息
     * @param config 处理器配置
     * @return 包含协议类型和端口的映射，其中端口为Integer类型
     */
    public static Map<String, String> extractProtocolAndPort(Map<String, Object> config) {
        Map<String, String> result = new HashMap<>();
        
        if (config == null) {
            return result;
        }
        
        String portValue = findPortInConfig(config);
        if (portValue == null) {
            return result;
        }
        
        // 提取端口号
        Matcher portMatcher = PORT_NUMBER_PATTERN.matcher(portValue);
        if (portMatcher.find()) {
            try {
                String port = portMatcher.group(1);
                result.put("port",port);
                
                // 判断协议类型
                if (HTTP_PATTERN.matcher(portValue).find()) {
                    result.put("protocol", "http");
                } else if (GRPC_PATTERN.matcher(portValue).find()) {
                    result.put("protocol", "grpc");
                } else {
                    result.put("protocol", "http");
                }
            } catch (NumberFormatException e) {
                // 如果端口不是有效的数字，则忽略
            }
        }
        
        return result;
    }
    
    /**
     * 递归查找配置中包含端口的字符串
     * @param config 处理器配置
     * @return 找到的包含端口的字符串，如果未找到则返回null
     */
    private static String findPortInConfig(Object config) {
        if (config instanceof String) {
            String value = (String) config;
            if (PORT_PATTERN.matcher(value).find()) {
                return value;
            }
        } else if (config instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> mapConfig = (Map<String, Object>) config;
            
            // 优先检查常见的端口配置键
            String[] portKeys = {"address", "url", "host", "endpoint", "server", "listen"};
            for (String key : portKeys) {
                if (mapConfig.containsKey(key)) {
                    String found = findPortInConfig(mapConfig.get(key));
                    if (found != null) {
                        return found;
                    }
                }
            }
            
            // 继续检查其他键
            for (Object value : mapConfig.values()) {
                String found = findPortInConfig(value);
                if (found != null) {
                    return found;
                }
            }
        } else if (config instanceof Iterable) {
            @SuppressWarnings("unchecked")
            Iterable<Object> iterableConfig = (Iterable<Object>) config;
            for (Object item : iterableConfig) {
                String found = findPortInConfig(item);
                if (found != null) {
                    return found;
                }
            }
        }
        
        return null;
    }
    
    /**
     * 生成webhook URL
     * @param protocol 协议类型 (http 或 grpc)
     * @param clusterName 集群名称
     * @param port 端口号
     * @return 生成的webhook URL
     */
    public static String generateWebhookUrl(String protocol, Integer port, String clusterName) {
        if ("http".equalsIgnoreCase(protocol)) {
            return "/datahub/" + clusterName + "/" + port;
        } else if ("grpc".equalsIgnoreCase(protocol)) {
            return ":" + port;
        }
        return null;
    }
} 