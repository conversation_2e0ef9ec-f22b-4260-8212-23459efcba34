# 算子调试接口使用指南

## 接口概述

算子调试接口允许开发者通过HTTP请求调试数据处理算子的配置，无需部署完整的数据处理管道，就能验证算子的处理效果。

## 接口详情

- **URL**: `/datahub/v1/processor/debug`
- **方法**: POST
- **Content-Type**: application/json

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|-----|-----|-----|-----|
| input | String | 是 | 输入数据，JSON字符串格式 |
| processors | Object | 是 | 待调试的算子配置，Map结构 |

### 请求示例

```json
{
  "input": "{\"metrics/12/opentelemetry_metrics\":{\"receivers\":[\"otlp/12/opentelemetry_metrics\"],\"processors\":[],\"exporters\":[\"databuffhttp/12/opentelemetry_metrics\"]},\"test\":\"腾讯云【首单特惠】3️⃣年468元 轻量服务器2核4G6M 🧑‍🤝‍🧑拼团成功加赠3️⃣个月～\",\"test_num\":123456}",
  "processors": {
    "attributes/example": {
      "actions": [
        {
          "key": "metrics/12/opentelemetry_metrics.processors",
          "action": "delete"
        },
        {
          "key": "fenda_test",
          "action": "insert",
          "value": "fenda_test"
        }
      ]
    },
    "attributes/example02": {
      "actions": [
        {
          "key": "tttt",
          "action": "insert",
          "value": "ooooooo"
        }
      ]
    },
    "attributes/example03": {
      "actions": [
        {
          "key": "test_num",
          "action": "delete"
        }
      ]
    },
    "attributes/example04": {
      "actions": [
        {
          "key": "test",
          "action": "hash"
        }
      ]
    }
  }
}
```

## 响应参数

| 参数名 | 类型 | 说明 |
|-----|-----|-----|
| success | Boolean | 请求是否成功 |
| errorMessage | String | 错误信息，当success为false时有值 |
| data | Object | 调试结果数据 |

### 响应示例 (成功)

```json
{
  "success": true,
  "data": {
    "result": {
      "metrics/12/opentelemetry_metrics": {
        "receivers": [
          "otlp/12/opentelemetry_metrics"
        ],
        "exporters": [
          "databuffhttp/12/opentelemetry_metrics"
        ]
      },
      "fenda_test": "fenda_test",
      "tttt": "ooooooo",
      "test": "2cf24dba5fb0a30e"
    },
    "logs": [
      "Deleted key 'metrics/12/opentelemetry_metrics.processors'",
      "Inserted 'fenda_test' with value 'fenda_test'",
      "Inserted 'tttt' with value 'ooooooo'",
      "Deleted key 'test_num'",
      "Hashed value for key 'test'"
    ],
    "executionTime": "12ms"
  }
}
```

### 响应示例 (失败)

```json
{
  "success": false,
  "errorMessage": "未找到可用的集群"
}
```

## 算子配置说明

算子配置是一个Map结构，键为算子名称，值为算子的具体配置：

```json
{
  "算子名称1": {
    "配置参数1": "值1",
    "配置参数2": "值2"
  },
  "算子名称2": {
    "配置参数1": "值1",
    "配置参数2": "值2"
  }
}
```

### 支持的算子类型

1. **attributes**：属性处理算子，用于处理JSON对象的属性
   - actions：动作列表，支持的动作包括：
     - delete：删除指定key
     - insert：添加新key及其值
     - update：更新已有key的值
     - hash：将key对应的值进行哈希处理
     - rename：重命名key

2. **filter**：过滤算子，用于过滤数据
   - condition：过滤条件，表达式格式

3. **transform**：转换算子，用于转换数据格式
   - from：源格式
   - to：目标格式

## 注意事项

1. 输入数据(input)必须是合法的JSON字符串，由于需要在JSON中传递JSON，所以字符串中的双引号需要进行转义。
2. 系统会自动查找第一个可用的集群（未删除且已启用的集群）进行调试，确保集群可访问且正常运行。
3. 算子配置必须符合系统规范，错误的配置可能导致调试失败。
4. 算子的处理结果根据算子类型不同会有不同的输出格式，请根据具体算子类型查看结果。

## 调试流程

1. 前端构造请求体，包含输入数据和算子配置。
2. 发送POST请求到调试接口。
3. 接口查询可用集群并转发请求。
4. 集群的调试服务对数据进行处理并返回结果。
5. 接口将结果返回给前端。

## 代码调用示例

### JavaScript

```javascript
async function debugProcessor() {
  const requestData = {
    input: '{"metrics/12/opentelemetry_metrics":{"receivers":["otlp/12/opentelemetry_metrics"],"processors":[],"exporters":["databuffhttp/12/opentelemetry_metrics"]},"test":"测试数据","test_num":123456}',
    processors: {
      "attributes/example": {
        "actions": [
          {
            "key": "test",
            "action": "hash"
          }
        ]
      }
    }
  };

  try {
    const response = await fetch('/datahub/v1/processor/debug', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log('调试结果:', result.data);
    } else {
      console.error('调试失败:', result.errorMessage);
    }
  } catch (error) {
    console.error('请求错误:', error);
  }
}
```

### Java

```java
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import java.util.HashMap;
import java.util.Map;

public class ProcessorDebugClient {
    
    private final RestTemplate restTemplate = new RestTemplate();
    private final String baseUrl = "http://localhost:8080";
    
    public Map<String, Object> debugProcessor(String inputData, Map<String, Object> processors) {
        String url = baseUrl + "/datahub/v1/processor/debug";
        
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("input", inputData);
        requestBody.put("processors", processors);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
        
        ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);
        
        return response.getBody();
    }
} 