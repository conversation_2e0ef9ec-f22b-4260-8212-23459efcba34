package com.databuff.webapp.util;

import com.databuff.common.utils.PathResolver;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.exception.ZipException;
import net.lingala.zip4j.model.FileHeader;
import net.lingala.zip4j.model.ZipParameters;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.http.util.Asserts;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.Assert;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.*;

/**
 * @author:TianMing
 */
@Slf4j
public class FileUtil {

    /**
     * 读取properities类型文件，并转换成map
     * @param file
     * @return
     * @throws IOException
     */
    public static Map<String,String> fileToMap(String file){
        Map<String,String>  result = new HashMap<>();
        try (FileReader fr = new FileReader(file)){
            BufferedReader br = new BufferedReader(fr);
            String line = br.readLine();
            while (line!=null){
                line  = line.trim();
                if (line.length()!=0&&!line.startsWith("#")&&line.contains("=")){
                    String[] sts = line.split("=");
                    if (!StringUtils.isEmpty(sts[1])){
                        result.put(sts[0],sts[1]);
                    }
                }
                line = br.readLine();
            }
        } catch (IOException e) {
        }
        return result;
    }

    public static boolean delete(String fileName) {
        File file = new File(fileName);
        if (!file.exists()) {
            return false;
        } else {
            if (file.isFile())
                return deleteFile(fileName);
            else
                return deleteDirectory(fileName);
        }
    }

    /**
     * 删除单个文件
     *
     * @param fileName 要删除的文件的文件名
     * @return 单个文件删除成功返回true，否则返回false
     */
    public static boolean deleteFile(String fileName) {
        File file = new File(fileName);
        // 如果文件路径所对应的文件存在，并且是一个文件，则直接删除
        if (file.exists() && file.isFile()) {
            if (file.delete()) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * 删除目录及目录下的文件
     *
     * @param dir 要删除的目录的文件路径
     * @return 目录删除成功返回true，否则返回false
     */
    public static boolean deleteDirectory(String dir) {
        // 如果dir不以文件分隔符结尾，自动添加文件分隔符
        if (!dir.endsWith(File.separator))
            dir = dir + File.separator;
        File dirFile = new File(dir);
        // 如果dir对应的文件不存在，或者不是一个目录，则退出
        if ((!dirFile.exists()) || (!dirFile.isDirectory())) {
            return false;
        }
        boolean flag = true;
        // 删除文件夹中的所有文件包括子目录
        File[] files = dirFile.listFiles();
        for (int i = 0; i < files.length; i++) {
            // 删除子文件
            if (files[i].isFile()) {
                flag = deleteFile(files[i].getAbsolutePath());
                if (!flag)
                    break;
            }
            // 删除子目录
            else if (files[i].isDirectory()) {
                flag = deleteDirectory(files[i]
                        .getAbsolutePath());
                if (!flag)
                    break;
            }
        }
        if (!flag) {
            return false;
        }
        // 删除当前目录
        if (dirFile.delete()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取windows/linux的项目根目录
     *
     * @return
     */
    public static String getConTextPath() {
        String fileUrl = Thread.currentThread().getContextClassLoader().getResource("").getPath();
        if ("usr".equals(fileUrl.substring(1, 4))) {
            fileUrl = (fileUrl.substring(0, fileUrl.length() - 16));//linux
        } else {
            fileUrl = (fileUrl.substring(1, fileUrl.length() - 16));//windows
        }
        return fileUrl;
    }

    /**
     * 字符串转数组
     *
     * @param str      字符串
     * @param splitStr 分隔符
     * @return
     */
    public static String[] StringToArray(String str, String splitStr) {
        String[] arrayStr = null;
        if (!"".equals(str) && str != null) {
            if (str.indexOf(splitStr) != -1) {
                arrayStr = str.split(splitStr);
            } else {
                arrayStr = new String[1];
                arrayStr[0] = str;
            }
        }
        return arrayStr;
    }

    /**
     * 读取文件
     *
     * @param Path
     * @return
     */
    public static String ReadFile(String Path) {
        BufferedReader reader = null;
        StringBuilder laststr = new StringBuilder();
        try (
                FileInputStream fileInputStream = new FileInputStream(Path);
                InputStreamReader inputStreamReader = new InputStreamReader(fileInputStream, "UTF-8");

        ){
            reader = new BufferedReader(inputStreamReader);
            String tempString = null;
            while ((tempString = reader.readLine()) != null) {
                laststr.append(tempString);
            }
            reader.close();
        } catch (IOException e) {
            log.error("error{}",e);
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    log.error("error{}",e);
                }
            }
        }
        return laststr.toString();
    }

    /**
     * 获取文件夹下所有文件的名称 + 模糊查询（当不需要模糊查询时，queryStr传空或null即可）
     * 1.当路径不存在时，map返回retType值为1
     * 2.当路径为文件路径时，map返回retType值为2，文件名fileName值为文件名
     * 3.当路径下有文件夹时，map返回retType值为3，文件名列表fileNameList，文件夹名列表folderNameList
     *
     * @param folderPath 路径
     * @param queryStr   模糊查询字符串
     * @return
     */
    public static HashMap<String, Object> getFilesName(String folderPath, String queryStr) {
        HashMap<String, Object> map = new HashMap<>();
        List<String> fileNameList = new ArrayList<>();//文件名列表
        List<String> folderNameList = new ArrayList<>();//文件夹名列表
        File f = new File(folderPath);
        if (!f.exists()) { //路径不存在
            map.put("retType", "1");
        } else {
            boolean flag = f.isDirectory();
            if (flag == false) { //路径为文件
                map.put("retType", "2");
                map.put("fileName", f.getName());
            } else { //路径为文件夹
                map.put("retType", "3");
                File fa[] = f.listFiles();
                queryStr = queryStr == null ? "" : queryStr;//若queryStr传入为null,则替换为空（indexOf匹配值不能为null）
                for (int i = 0; i < fa.length; i++) {
                    File fs = fa[i];
                    if (fs.getName().indexOf(queryStr) != -1) {
                        if (fs.isDirectory()) {
                            folderNameList.add(fs.getName());
                        } else {
                            fileNameList.add(fs.getName());
                        }
                    }
                }
                map.put("fileNameList", fileNameList);
                map.put("folderNameList", folderNameList);
            }
        }
        return map;
    }

    /**
     * 以行为单位读取文件，读取到最后一行
     *
     * @param filePath
     * @return
     */
    public static List<String> readFileContent(String filePath, Charset encoding) {
        List<String> listContent = new ArrayList<>();
        try (
                BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(filePath), encoding))
        ) {
            String tempString;
            while ((tempString = reader.readLine()) != null) {
                listContent.add(tempString);
            }
        } catch (IOException e) {
            log.error("error{}", e);
        }
        return listContent;
    }

    /**
     * 读取指定行数据 ，注意：0为开始行
     *
     * @param filePath
     * @param lineNumber
     * @return
     */
    public static String readLineContent(String filePath, int lineNumber) throws IOException{
        BufferedReader reader = null;
        FileReader fileReader = null;

        String lineContent = "";
        try {
            fileReader = new FileReader(filePath);
            reader = new BufferedReader(fileReader);
            int line = 0;
            while (line <= lineNumber) {
                lineContent = reader.readLine();
                line++;
            }
        }finally {
            if (fileReader != null) {
                try {
                    fileReader.close();
                } catch (IOException e1) {
                }
            }
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {
                }
            }
        }
        return lineContent;
    }

    /**
     * 读取从beginLine到endLine数据（包含beginLine和endLine），注意：0为开始行
     *
     * @param filePath
     * @param beginLineNumber 开始行
     * @param endLineNumber   结束行
     * @return
     */
    public static List<String> readLinesContent(String filePath, int beginLineNumber, int endLineNumber) {
        List<String> listContent = new ArrayList<>();
        try (
                BufferedReader reader = new BufferedReader(new FileReader(filePath));
        ){
            int count = 0;
            String content = reader.readLine();
            while (content != null) {
                if (count >= beginLineNumber && count <= endLineNumber) {
                    listContent.add(content);
                }
                content = reader.readLine();
                count++;
            }
        } catch (Exception e) {
        }
        return listContent;
    }

    /**
     * 读取若干文件中所有数据
     *
     * @param listFilePath
     * @return
     */
    public static List<String> readFileContent_list(List<String> listFilePath) {
        List<String> listContent = new ArrayList<>();
        for (String filePath : listFilePath) {
            File file = new File(filePath);
            BufferedReader reader = null;
            try {
                reader = new BufferedReader(new FileReader(file));
                String tempString = null;
                int line = 1;
                // 一次读入一行，直到读入null为文件结束
                while ((tempString = reader.readLine()) != null) {
                    listContent.add(tempString);
                    line++;
                }
                reader.close();
            } catch (IOException e) {
                log.error("error{}",e);
            } finally {
                if (reader != null) {
                    try {
                        reader.close();
                    } catch (IOException e1) {
                    }
                }
            }
        }
        return listContent;
    }

    /**
     * 文件数据写入（如果文件夹和文件不存在，则先创建，再写入）
     *
     * @param filePath
     * @param content
     * @param flag     true:如果文件存在且存在内容，则内容换行追加；false:如果文件存在且存在内容，则内容替换
     */
    public static String fileLinesWrite(String filePath, String content, boolean flag) {
        String filedo = "write";
        FileWriter fw = null;
        PrintWriter pw = null ;
        try {
            File file = new File(filePath);
            //如果文件夹不存在，则创建文件夹
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            if (!file.exists()) {//如果文件不存在，则创建文件,写入第一行内容
                if(!file.createNewFile()){
                    log.warn("createNewFile fail");
                }
                fw = new FileWriter(file);
                filedo = "create";
            } else {//如果文件存在,则追加或替换内容
                fw = new FileWriter(file, flag);
            }
            pw = new PrintWriter(fw);
            pw.println(content);
            pw.flush();
            fw.flush();
        } catch (IOException e) {
            log.error("error{}",e);
        } finally {
            if (pw !=null){
                try {
                    pw.close();
                } catch (Exception e) {
                    log.error("error{}",e);
                }
            }
            if (fw !=null){
                try {
                    fw.close();
                } catch (Exception e) {
                    log.error("error{}",e);
                }
            }
        }
        return filedo;
    }

    /**
     * 写出数据
     *
     * @param str
     */
    public static void write(String str, OutputStream outputStream) throws IOException {
        BufferedInputStream bis = null;
        ByteArrayInputStream byteArrayInputStream = null;
        try {
            byteArrayInputStream = new ByteArrayInputStream(str.getBytes("UTF-8"));
            // 实现文件下载
            byte[] buffer = new byte[1024];
            //写出输入流的数据
            bis = new BufferedInputStream(byteArrayInputStream);
            int i = bis.read(buffer);
            while (i != -1) {
                outputStream.write(buffer, 0, i);
                i = bis.read(buffer);
            }
        } finally {
            if (bis != null) {
                bis.close();
            }
            if (byteArrayInputStream != null) {
                byteArrayInputStream.close();
            }
            if (outputStream != null) {
                outputStream.close();
            }
        }
    }

    public static HttpServletResponse write(String filename,String path, HttpServletResponse response) throws IOException {
        InputStream fis = null ;
        OutputStream toClient = null ;
        try {
            path = URLDecoder.decode(path, "UTF-8");
            ClassPathResource classPathResource = new ClassPathResource(path);
            Assert.isTrue(classPathResource.exists(), String.format("文件【%s】不存在", path));
            fis = classPathResource.getInputStream();
            // 清空response
            response.reset();
            // 设置response的Header
            response.addHeader("Content-Disposition", "attachment;filename=" +  URLEncoder.encode(filename, "UTF-8"));
            response.addHeader("Content-Length", "" + classPathResource.contentLength());
            response.setContentType("application/octet-stream");
            toClient = new BufferedOutputStream(response.getOutputStream());
            byte[] buffer = new byte[fis.available()];
            int i = fis.read(buffer);
            while (i != -1)
            {
                toClient.write(buffer);
                i = fis.read(buffer);
            }
            toClient.flush();
        }  finally {
            try {
                if (fis!=null){
                    fis.close();
                }
            } catch (IOException e) {
                log.error("error{}",e);
            }
            try {
                if (toClient!=null){
                    toClient.close();
                }
            } catch (IOException e) {
                log.error("error{}",e);
            }


        }
        return response;
    }


    /**
     * 写文件
     *
     * @param ins
     * @param out
     */
    public static void writeIntoOut(InputStream ins, OutputStream out) throws IOException {
        writeIntoOut(ins, out, 10000, true);
    }

    /**
     * 写文件
     *
     * @param ins
     * @param out
     */
    public static void writeIntoOut(InputStream ins, OutputStream out, int batchSize, boolean autoClose) throws IOException {
        byte[] bb = new byte[batchSize];
        try {
            int cnt = ins.read(bb);
            while (cnt > 0) {
                out.write(bb, 0, cnt);
                cnt = ins.read(bb);
            }
            out.flush();
        } finally {
            if (autoClose) {
                try {
                    ins.close();
                } finally {
                    out.close();
                }
            }
        }
    }

    /**
     * 判断list中元素是否完全相同（完全相同返回true,否则返回false）
     *
     * @param list
     * @return
     */
    private static boolean hasSame(List<? extends Object> list) {
        if (null == list)
            return false;
        return 1 == new HashSet<Object>(list).size();
    }

    /**
     * 判断list中是否有重复元素（无重复返回true,否则返回false）
     *
     * @param list
     * @return
     */
    private static boolean hasSame2(List<? extends Object> list) {
        if (null == list)
            return false;
        return list.size() == new HashSet<Object>(list).size();
    }

    /**
     * 增加/减少天数
     *
     * @param date
     * @param num
     * @return
     */
    public static Date DateAddOrSub(Date date, int num) {
        Calendar startDT = Calendar.getInstance();
        startDT.setTime(date);
        startDT.add(Calendar.DAY_OF_MONTH, num);
        return startDT.getTime();
    }
    //https://www.cnblogs.com/chenhuan001/p/6575053.html

    /**
     * 递归删除文件或者目录
     *
     * @param file_path
     */
    public static void deleteEveryThing(String file_path) {
        try {
            File file = new File(file_path);
            if (!file.exists()) {
                return;
            }
            if (file.isFile()) {
                if(!file.delete()){
                    log.warn("delete fail");
                }
            } else {
                File[] files = file.listFiles();
                for (int i = 0; i < files.length; i++) {
                    String root = files[i].getAbsolutePath();//得到子文件或文件夹的绝对路径
                    deleteEveryThing(root);
                }
                if(!file.delete()){
                    log.warn("delete fail");
                }
            }
        } catch (Exception e) {
            log.error("error{}",e);
            System.out.println("删除文件失败");
        }
    }

    /**
     * 创建目录
     *
     * @param dir_path
     */
    public static void mkDir(String dir_path) {
        File myFolderPath = new File(dir_path);
        try {
            if (!myFolderPath.exists()) {
                myFolderPath.mkdir();
            }
        } catch (Exception e) {
            System.out.println("新建目录操作出错");
            log.error("error{}",e);
        }
    }

    //https://blog.csdn.net/lovoo/article/details/77899627

    /**
     * 判断指定的文件是否存在。
     *
     * @param fileName
     * @return
     */
    public static boolean isFileExist(String fileName) {
        return new File(fileName).isFile();
    }

    /**
     * 判断指定的文件是否存在。
     *
     * @param fileName
     * @return
     */
    public static boolean exist(String fileName) {
        return new File(fileName).exists();
    }

    /* 得到文件后缀名
     *
     * @param fileName
     * @return
     */
    public static String getFileExt(String fileName) {
        int point = fileName.lastIndexOf('.');
        int length = fileName.length();
        if (point == -1 || point == length - 1) {
            return "";
        } else {
            return fileName.substring(point + 1, length);
        }
    }

    /* 得到文件名,排除后缀的
     *
     * @param fileName
     * @return
     */
    public static String getFileNameStr(String fileName) {
        int point = fileName.lastIndexOf('.');
        int length = fileName.length();
        if (point == -1 || point == length - 1) {
            return fileName;
        } else {
            return fileName.substring(0, point);
        }
    }

    /**
     * 删除文件夹及其下面的子文件夹
     *
     * @param dir
     * @throws IOException
     */
    public static void deleteDir(File dir) throws IOException {
        if (dir.isFile())
            throw new IOException("IOException -> BadInputException: not a directory.");
        File[] files = dir.listFiles();
        if (files != null) {
            for (int i = 0; i < files.length; i++) {
                File file = files[i];
                if (file.isFile()) {
                    if(!dir.delete()){
                        log.warn("delete fail");
                    }
                } else {
                    deleteDir(file);
                }
            }
        }
        if(!dir.delete()){
            log.warn("delete fail");
        }
    }

    /**
     * 复制文件
     *
     * @param src
     * @param dst
     * @throws Exception
     */
    public static void copy(File src, File dst) throws Exception {
        int BUFFER_SIZE = 4096;
        InputStream in = null;
        OutputStream out = null;
        try {
            in = new BufferedInputStream(new FileInputStream(src), BUFFER_SIZE);
            out = new BufferedOutputStream(new FileOutputStream(dst), BUFFER_SIZE);
            byte[] buffer = new byte[BUFFER_SIZE];
            int len = 0;
            while ((len = in.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
        } catch (Exception e) {
            throw e;
        } finally {
            if (null != in) {
                try {
                    in.close();
                } catch (IOException e) {
                    log.error("error{}",e);
                }
                in = null;
            }
            if (null != out) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("error{}",e);
                }
                out = null;
            }
        }
    }


    /**
     * 对字节数组字符串进行Base64解码并生成pdf
     *
     * @param fileStr      数据
     * @param fileFilePath 保存全路径地址
     * @return
     */
    public static boolean generateBase64StringToFile(String fileStr, String fileFilePath) {
        if (fileStr == null) {
            return false;
        }
        OutputStream out = null;
        try {
            //Base64解码
            byte[] b = Base64.decodeBase64(fileStr);
            for (int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {//调整异常数据
                    b[i] += 256;
                }
            }
            //生成pdf
            out = new FileOutputStream(fileFilePath);
            out.write(b);
            out.flush();
            return true;
        } catch (Exception e) {
            log.error("error{}",e);
            return false;
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("error{}",e);
                }
            }
        }
    }


    /**
     * 将文件转换成Base64编码
     * 将文件转化为字节数组字符串，并对其进行Base64编码处理
     *
     * @param localFilePath 待处理图片
     * @return
     */
    public static String getFileBase64StrByLocalFile(String localFilePath) {

        FileInputStream in = null;
        byte[] data = null;
        //读取图片字节数组
        try {
            in = new FileInputStream(localFilePath);
            data = new byte[in.available()];
            int len = 0;
            while ((len = in.read(data)) > 0) {

            }

        } catch (IOException e) {
            log.error("error{}",e);
        }finally {
            if (in != null){
                try {
                    in.close();
                } catch (IOException e) {
                    log.error("error{}",e);
                }
            }
        }
        return new String(Base64.encodeBase64(data));
    }

    public static InputStream getTemplateInputStream(String fileName) throws Exception {
        return new ByteArrayInputStream(getTemplate(fileName));
    }

    public static byte[] getTemplate(String fileName) throws Exception {
        Assert.hasText(fileName, "fileName cannot be empty");
        byte[] bytes = getByteClassPathFile("/templates/" + fileName);
        Assert.isTrue(bytes != null && bytes.length > 0, String.format("模板文件[%s]不存在", fileName));
        return bytes;
    }

    /**
     * 从resources目录下读取文件流
     * 注意:jar包运行时，无法读取File对象，只能读取流对象，
     */
    public static byte[] getByteClassPathFile(String path) throws Exception {
        path = URLDecoder.decode(path, "UTF-8");
        ClassPathResource classPathResource = new ClassPathResource(path);
        Assert.isTrue(classPathResource.exists(), String.format("文件【%s】不存在", path));
        try (InputStream in = classPathResource.getInputStream()) {
            return FileCopyUtils.copyToByteArray(in);
        }
    }

    public static ZipFile doZip(String targetPath, ZipParameters parameters, String password, File... files) throws ZipException {
        ZipFile zipFile = new ZipFile(targetPath);
        if (password != null) {
            zipFile.setPassword(password.toCharArray());
        }
        if (files != null && files.length > 0) {
            for (File file : files) {
                if (file.isDirectory()) {
                    zipFile.addFolder(file, parameters);
                } else {
                    zipFile.addFile(file, parameters);
                }
            }
        }
        return zipFile;
    }

    public static boolean unzip(String sourcePath, String targetPath, String password) {
        ZipFile zipFile = new ZipFile(sourcePath);
        Asserts.check(zipFile.isValidZipFile(), "不合法的压缩文件,可能已被损坏");
        if (password != null) {
            zipFile.setPassword(password.toCharArray());
        }
        try {
            zipFile.extractAll(targetPath);
            return true;
        } catch (ZipException e) {
            log.error("解压文件流IO异常,{}",e);
        }finally {
            try {
                zipFile.close();
            } catch (IOException e) {

            }
        }
        return false;
    }

    public static boolean unzipFile(String sourcePath, String targetPath, String password) {
        try (ZipFile zipFile = new ZipFile(sourcePath)) {
            Asserts.check(zipFile.isValidZipFile(), "不合法的压缩文件,可能已被损坏");
            if (password != null) {
                zipFile.setPassword(password.toCharArray());
            }

            for (FileHeader header : zipFile.getFileHeaders()) {
                if (header.isDirectory()) continue;
                File outFile = new File(targetPath, header.getFileName());
                outFile.getParentFile().mkdirs();

                try (InputStream is = zipFile.getInputStream(header);
                     OutputStream os = new FileOutputStream(outFile)) {
                    byte[] buffer = new byte[2048];
                    int len;
                    while ((len = is.read(buffer)) > 0) {
                        os.write(buffer, 0, len);
                    }
                }
                // 解压每个文件后释放一次资源
                System.gc();
            }
            log.info("解压完成: {} => {}", sourcePath, targetPath);
            return true;
        } catch (Exception e) {
            log.error("解压文件失败: {}", e.getMessage(), e);
            return false;
        } finally {
            // 解压完建议记录 JVM 当前内存占用
            long used = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
            log.info("解压完成后 JVM 内存使用情况: {} MB", used / 1024 / 1024);
        }
    }

    public static boolean unzipByShell(String zipPath, String targetPath, String password) {
        List<String> command = new ArrayList<>();
        command.add("unzip");
        if (password != null) {
            command.add("-P");
            command.add(password);
        }
        command.add(zipPath);
        command.add("-d");
        command.add(targetPath);

        ProcessBuilder builder = new ProcessBuilder(command);
        builder.redirectErrorStream(true);
        try {
            Process process = builder.start();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    log.info("[unzip] {}", line);
                }
            }
            int exitCode = process.waitFor();
            return exitCode == 0;
        } catch (Exception e) {
            log.error("系统命令解压失败: {}", e.getMessage(), e);
            return false;
        }
    }


    public static boolean decompressTarGz(String tarGzPath, String targetDir) {
        byte[] buffer = new byte[8192]; // 显式 buffer，控制大小
        try (
                FileInputStream fis = new FileInputStream(tarGzPath);
                BufferedInputStream bis = new BufferedInputStream(fis);
                GzipCompressorInputStream gis = new GzipCompressorInputStream(bis);
                TarArchiveInputStream tis = new TarArchiveInputStream(gis)
        ) {
            TarArchiveEntry entry;
            while ((entry = tis.getNextTarEntry()) != null) {
                File outFile = new File(targetDir, entry.getName());
                if (entry.isDirectory()) {
                    outFile.mkdirs();
                    continue;
                }

                outFile.getParentFile().mkdirs();
                try (OutputStream os = new FileOutputStream(outFile)) {
                    int len;
                    while ((len = tis.read(buffer)) != -1) {
                        os.write(buffer, 0, len);
                    }
                }

                // 主动 null 出流引用帮助 GC 回收
                buffer = new byte[8192]; // 重置一次 buffer
                System.gc();
            }

            log.info("解压完成: {} -> {}", tarGzPath, targetDir);
            return true;
        } catch (IOException e) {
            log.error("解压 tar.gz 文件失败: {}", e.getMessage(), e);
            return false;
        }
    }

    public static boolean unTarGzFileByShell(String sourcePath, String targetPath) {
        try {
            List<String> command = Arrays.asList("tar", "-xzf", sourcePath, "-C", targetPath);
            ProcessBuilder builder = new ProcessBuilder(command);
            builder.redirectErrorStream(true);
            Process process = builder.start();

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    log.info("[untar] {}", line);
                }
            }

            int exitCode = process.waitFor();
            log.info("Shell 解压结束, exitCode={}", exitCode);
            return exitCode == 0;
        } catch (Exception e) {
            log.error("Shell 解压 tar.gz 失败: {}", e.getMessage(), e);
            return false;
        }
    }


    public static File multipartFileToFile(MultipartFile file) throws IOException {
        InputStream ins = null ;
        File toFile = new File(file.getOriginalFilename());
        OutputStream os =null ;
        try{
            ins = file.getInputStream();
            os = new FileOutputStream(toFile);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
        }catch (Exception e){
            log.error("error:{}",e);
        }finally {
            if (os!=null){
                os.close();
            }
            if (ins!=null){
                ins.close();
            }
        }
        return toFile;
    }


    public static long fileSize(String file){
        return new File(file).length();
    }

    public static Date lastModified(String file){
        return new Date(new File(file).lastModified());
    }

    public static boolean sizeEquals(String originalFile, String targetFile){
        return fileSize(originalFile)==fileSize(targetFile);
    }

    public static boolean lastModifiedDateEquals(String originalFile, String targetFile){
        return lastModified(originalFile).equals(lastModified(targetFile));
    }

    /**
     * 组成全路径名
     * @param path
     * @param fileName
     * @return
     */
    public static String fullPath(String path, String fileName){
        return path.endsWith(File.separator) ?(path+fileName):(path+File.separator+fileName);
    }


    public static boolean deleteLocalFile(String filePath) {
        String resolvedFilePath = PathResolver.resolveRelativePath(filePath);
        File file = new File(resolvedFilePath);
        if (!file.exists()) {
            log.warn("File does not exist: {}", filePath);
            return false;
        }

        boolean deleted = file.delete();
        if (deleted) {
            log.info("Successfully deleted file: {}", filePath);
        } else {
            if (!file.canWrite()) {
                log.error("File cannot be deleted due to insufficient permissions: {}", filePath);
            } else {
                log.error("Failed to delete file for unknown reasons: {}", filePath);
            }
            file.deleteOnExit();
            log.info("Scheduled file for deletion on JVM exit: {}", filePath);
        }

        return deleted;
    }
}