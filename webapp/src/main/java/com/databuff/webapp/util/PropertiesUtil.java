package com.databuff.webapp.util;

import com.databuff.common.exception.CustomException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.Properties;

/**
 * Properties工具
 * <AUTHOR>
 * @date 2019/3/19 17:29
 */
public class PropertiesUtil {

    /**
     * LOGGER
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(PropertiesUtil.class);

    /**
     * PROP
     */
    private static final Properties PROP = new Properties();

    /**
     * 读取配置文件
     * @param fileName
     * @return void
     * <AUTHOR>
     * @date 2019/3/19 17:29
     */
    public static void readProperties(String fileName) {
        InputStream in = null;
        try {
            in = PropertiesUtil.class.getResourceAsStream("/" + fileName);
            BufferedReader bf = new BufferedReader(new InputStreamReader(in));
            PROP.load(bf);
        } catch (IOException e) {
            LOGGER.error("PropertiesUtil工具类读取配置文件出现IOException异常:" + e.getMessage());
            throw new CustomException("PropertiesUtil工具类读取配置文件出现IOException异常:" + e.getMessage());
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (IOException e) {
                LOGGER.error("PropertiesUtil工具类读取配置文件出现IOException异常:" + e.getMessage());
            }
        }
    }

    /**
     * 根据key读取对应的value
     * @param key
     * @return java.lang.String
     * <AUTHOR>
     * @date 2019/3/19 17:29
     */
    public static String getProperty(String key){
        return PROP.getProperty(key);
    }


    /**
     * 获取类 所在jar包路径目录
     * @param thisClass
     * @return
     */
    public static String getJarPath(Class<?> thisClass) throws UnsupportedEncodingException {
        String path = thisClass.getProtectionDomain().getCodeSource().getLocation().getPath();
        try {
            path = java.net.URLDecoder.decode(path, "UTF-8");
            path = path.substring(0,path.lastIndexOf("."));
            path = path.substring(0,path.lastIndexOf("/")+1);
            int firstIndex = path.lastIndexOf(System.getProperty("path.separator")) + 1;
            path = path.substring(firstIndex);
        }catch (Exception e){
            LOGGER.error("PropertiesUtil工具类读getJarPath异常:" + e.getMessage());
        }
        return path ;
    }
}
