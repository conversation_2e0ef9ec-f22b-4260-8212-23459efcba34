package com.databuff.webapp.util;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import javax.net.ssl.*;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * http请求工具类
 *不依赖于任何框架，使用HttpURLConnection
 */
@Slf4j
public class HttpBaseUtil {

    /**
     * get请求
     *
     * @param url
     * @param params 请求参数
     * @return
     */
    public static String get(String url, Map<String, String> params) {
        return get(url, params, null);
    }

    /**
     * get请求
     *
     * @param url
     * @param params  请求参数
     * @param headers 请求头
     * @return
     */
    public static String get(String url, Map<String, String> params, Map<String, String> headers) {
        return request(mapToString(url, params, "?"), null, headers, "GET");
    }



    // long timeout = httpRequest.getAdvancedOptions().getRequestOptions().getTimeout();
    //        String cookies = httpRequest.getAdvancedOptions().getRequestOptions().getCookies();
    //        String bodyType = httpRequest.getAdvancedOptions().getRequestBody().getBodyType();
    //        String authType = httpRequest.getAdvancedOptions().getIndentityAuthorization().getAuthType();
    //        String userName = httpRequest.getAdvancedOptions().getIndentityAuthorization().getUserName();
    //        String password = httpRequest.getAdvancedOptions().getIndentityAuthorization().getPassword();
    //        boolean followRedirects = httpRequest.getAdvancedOptions().getRequestOptions().isFollowRedirects();
    public static RequestResult getTest(String url, Map<String, String> params, Map<String, String> headers, String body, String bodyType, long timeout, String cookies, String authType, String userName, String password, boolean followRedirects) {
        return requestTest(mapToString(url, params, "?"), params, headers, "GET", null, body, bodyType, timeout, cookies, authType, userName, password, followRedirects);
//        return requestTest(mapToString(url, params, "?"), null, headers, "GET", body, bodyType, timeout, cookies, authType, userName, password, followRedirects);
    }


    /**
     * 异步get请求
     *
     * @param url
     * @param params       请求参数
     * @param onHttpResult 请求回调
     * @return
     */
    public static void getAsyn(String url, Map<String, String> params, OnHttpResult onHttpResult) {
        getAsyn(url, params, null, onHttpResult);
    }

    /**
     * 异步get请求
     *
     * @param url
     * @param params       请求参数
     * @param headers      请求头
     * @param onHttpResult 请求回调
     * @return
     */
    public static void getAsyn(String url, Map<String, String> params, Map<String, String> headers, OnHttpResult onHttpResult) {
        requestAsyn(mapToString(url, params, "?"), null, headers, "GET", onHttpResult);
    }

    /**
     * post请求
     *
     * @param url
     * @param params 请求参数
     * @return
     */
    public static String post(String url, Map<String, String> params) {
        return post(url, params, null);
    }

    /**
     * post请求
     *
     * @param url
     * @param params  请求参数
     * @param headers 请求头
     * @return
     */
    public static String post(String url, Map<String, String> params, Map<String, String> headers) {
        return request(url, mapToString(null, params, null), headers, "POST");
    }


    public static RequestResult postTest(String url, Map<String, String> params, Map<String, String> headers, String body, String bodyType, long timeout, String cookies, String authType, String userName, String password, boolean followRedirects) {
        return requestTest(mapToString(url, params, "?"), params, headers, "POST", null, body, bodyType, timeout, cookies, authType, userName, password, followRedirects);
    }


    /**
     * 异步post请求
     *
     * @param url
     * @param params 请求参数
     * @return
     */
    public static void postAsyn(String url, Map<String, String> params, OnHttpResult onHttpResult) {
        postAsyn(url, params, null, onHttpResult);
    }

    /**
     * 异步post请求
     *
     * @param url
     * @param params  请求参数
     * @param headers 请求头
     * @return
     */
    public static void postAsyn(String url, Map<String, String> params, Map<String, String> headers, OnHttpResult onHttpResult) {
        requestAsyn(url, mapToString(null, params, null), headers, "POST", onHttpResult);
    }

    /**
     * put请求
     *
     * @param url
     * @param params 请求参数
     * @return
     */
    public static String put(String url, Map<String, String> params) {
        return put(url, params, null);
    }

    /**
     * put请求
     *
     * @param url
     * @param params  请求参数
     * @param headers 请求头
     * @return
     */
    public static String put(String url, Map<String, String> params, Map<String, String> headers) {
        return request(url, mapToString(null, params, null), headers, "PUT");
    }


    public static RequestResult putTest(String url, Map<String, String> params, Map<String, String> headers, String body, String bodyType, long timeout, String cookies, String authType, String userName, String password, boolean followRedirects) {
        return requestTest(mapToString(url, params, "?"), params, headers, "PUT", null, body, bodyType, timeout, cookies, authType, userName, password, followRedirects);
    }


    /**
     * 异步put请求
     *
     * @param url
     * @param params 请求参数
     * @return
     */
    public static void putAsyn(String url, Map<String, String> params, OnHttpResult onHttpResult) {
        putAsyn(url, params, null, onHttpResult);
    }

    /**
     * 异步put请求
     *
     * @param url
     * @param params  请求参数
     * @param headers 请求头
     * @return
     */
    public static void putAsyn(String url, Map<String, String> params, Map<String, String> headers, OnHttpResult onHttpResult) {
        requestAsyn(url, mapToString(null, params, null), headers, "PUT", onHttpResult);
    }

    /**
     * delete请求
     *
     * @param url
     * @param params 请求参数
     * @return
     */
    public static String delete(String url, Map<String, String> params) {
        return delete(url, params, null);
    }

    /**
     * delete请求
     *
     * @param url
     * @param params  请求参数
     * @param headers 请求头
     * @return
     */
    public static String delete(String url, Map<String, String> params, Map<String, String> headers) {
        return request(mapToString(url, params, "?"), null, headers, "DELETE");
    }


    public static RequestResult deleteTest(String url, Map<String, String> params, Map<String, String> headers, String body, String bodyType, long timeout, String cookies, String authType, String userName, String password, boolean followRedirects) {
        return requestTest(mapToString(url, params, "?"), params, headers, "DELETE", null, body, bodyType, timeout, cookies, authType, userName, password, followRedirects);
    }

    /**
     * 异步delete请求
     *
     * @param url
     * @param params 请求参数
     * @return
     */
    public static void deleteAsyn(String url, Map<String, String> params, OnHttpResult onHttpResult) {
        deleteAsyn(url, params, null, onHttpResult);
    }

    /**
     * 异步delete请求
     *
     * @param url
     * @param params  请求参数
     * @param headers 请求头
     * @return
     */
    public static void deleteAsyn(String url, Map<String, String> params, Map<String, String> headers, OnHttpResult onHttpResult) {
        requestAsyn(mapToString(url, params, "?"), null, headers, "DELETE", onHttpResult);
    }

    /**
     * 表单请求
     *
     * @param url
     * @param params  请求参数
     * @param headers 请求头
     * @param method  请求方式
     * @return
     */
    public static String request(String url, String params, Map<String, String> headers, String method) {
        return request(url, params, headers, method, "application/x-www-form-urlencoded");
    }


//    public static RequestResult requestTest(String url, String params, Map<String, String> headers, String method, String body, String bodyType, long timeout, String cookies, String authType, String userName, String password, boolean followRedirects) {
//        return requestTest(url, params, headers, method, "application/x-www-form-urlencoded", body, bodyType, timeout, cookies, authType, userName, password, followRedirects);
//    }


    @Data
    @Getter
    @Setter
    @ToString
    public static class RequestResult {
        private String responseBody;
        private String responseHeader;
        private Integer statusCode;
        private Long responseTime;
        private String requestBody;
        private String requestHeader;
    }


    public static String constructHeaderFieldsForString(Map<String, String> headerFields) {
        StringBuilder builder = new StringBuilder();
        Set<String> headerNames = headerFields.keySet();

        for (String headerName : headerNames) {
            if (headerName != null && !headerName.equals("") && !headerName.equals("null")) {
                builder.append(headerName).append(":");
                String headerValue = headerFields.get(headerName);
                builder.append(headerValue);
                builder.append("\r\n");
            }
        }

        return builder.toString();
    }


    public static String constructHeaderFields(Map<String, List<String>> headerFields) {
        StringBuilder builder = new StringBuilder();
        Set<String> headerNames = headerFields.keySet();

        for (String headerName : headerNames) {
            if (headerName != null && !headerName.equals("") && !headerName.equals("null")) {
                builder.append(headerName).append("=");
                List<String> headerValues = headerFields.get(headerName);

                for (String headerValue : headerValues) {
                    builder.append(headerValue).append(",");
                }

                builder.deleteCharAt(builder.length() - 1);
                builder.append("\r\n");
            }
        }

        return builder.toString();
    }


    private static String digest2HexString(byte[] digest)
    {
        StringBuilder digestString= new StringBuilder();
        int low, hi ;

        for(int i=0; i < digest.length; i++)
        {
            low =  ( digest[i] & 0x0f ) ;
            hi  =  ( (digest[i] & 0xf0)>>4 ) ;
            digestString.append(Integer.toHexString(hi));
            digestString.append(Integer.toHexString(low));
        }
        return digestString.toString();
    }


    public static RequestResult requestTest(String url, Map<String, String> params, Map<String, String> headers, String method, String mediaTypeStr, String bodyStr, String bodyType, long timeout, String cookies, String authType, String userName, String password, boolean followRedirects) {

        long startTime = System.currentTimeMillis();

        Map<String, String> requestHeaders = new HashMap<>();

        String result = null;
        if (url == null || url.trim().isEmpty()) {
            return null;
        }
        method = method.toUpperCase();
        OutputStreamWriter writer = null;
        InputStream in = null;
        ByteArrayOutputStream resOut = null;

        RequestResult requestResult = new RequestResult();
        OkHttpClient client = new OkHttpClient();
        try {
            Request.Builder requestBuilder = new Request.Builder();
            MediaType mediaType
                    = MediaType.parse(bodyType != null ? bodyType : mediaTypeStr);
            RequestBody body = RequestBody.create(mediaType, bodyStr==null?"":bodyStr);
            requestBuilder.method(method,body);

            if (cookies != null && !cookies.equals("")) {
                requestBuilder.addHeader("Cookie", cookies);
            }
            if (authType.equals("httpBasicAuth")) {
                String auth = userName + ":" + password;

                byte[] encodedAuth = org.apache.commons.net.util.Base64.encodeBase64(auth.getBytes(StandardCharsets.UTF_8));
                String authHeaderValue = "Basic " + new String(encodedAuth);
                requestBuilder.addHeader("Authorization", authHeaderValue);
//                requestHeaders.put("Authorization", authHeaderValue);
            } else if (authType.equals("digestAuth")) {
                MessageDigest md5 = null;
                try {
                    md5 = MessageDigest.getInstance("MD5");
                } catch (NoSuchAlgorithmException e) {
                    e.printStackTrace();
                }
                md5.update(password.getBytes());

                String digestedPass = digest2HexString(md5.digest());
                String credentials = userName + ":" + digestedPass;
                requestBuilder.addHeader("Authorization", "Digest " + credentials);
//                requestHeaders.put("Authorization", "Digest " + credentials);
            }

            // 添加请求头
            if (headers != null) {
                Iterator<String> iterator = headers.keySet().iterator();
                while (iterator.hasNext()) {
                    String key = iterator.next();
                    requestBuilder.addHeader(key, headers.get(key));
//                    requestHeaders.put(key, headers.get(key));
                }
            }

            // 添加参数
            StringBuilder sb = new StringBuilder();
            String paramStr = "";
            if (params != null && params.size()>0) {
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    sb.append(entry.getKey()).append("=").append(entry.getValue());
                }
                paramStr = sb.toString();
                if (!url.contains("?")&&(paramStr!=null&&paramStr.length()>0)){
                    url = url+"?";
                }
                url = url+paramStr;
            }
            requestBuilder.url(url);
            client = new OkHttpClient.Builder()
                    .connectTimeout(timeout, TimeUnit.SECONDS)
                    .readTimeout(timeout, TimeUnit.SECONDS)
                    .followRedirects(followRedirects)
                    .build();
            Request request = requestBuilder.build();

            Response response = client.newCall(request).execute();
            String requestHeaderStr = request.headers().toString();
            String responseHeaderStr = constructHeaderFields(response.headers().toMultimap());
            int responseCode = response.code();
            if (responseCode >= 300) {
                requestResult = new RequestResult();
                requestResult.requestHeader = requestHeaderStr;
                requestResult.responseHeader = responseHeaderStr;
                requestResult.responseBody = "HTTP Request is not success, Response code is " + responseCode;
                requestResult.responseTime = 0L;
                requestResult.statusCode = responseCode;
                requestResult.requestBody = bodyStr;
                return requestResult;
            }
            // 获取返回数据
            in = response.body().byteStream();
            resOut = new ByteArrayOutputStream();
            byte[] bytes = new byte[1024];
            int len;
            while ((len = in.read(bytes)) != -1) {
                resOut.write(bytes, 0, len);
            }
            result = resOut.toString();
            long endTime = System.currentTimeMillis();
            long timeCost = endTime - startTime;

            requestResult.responseHeader = responseHeaderStr;
            requestResult.responseBody = result;
            requestResult.responseTime = timeCost;
            requestResult.statusCode = responseCode;

            return requestResult;
        }catch (MalformedURLException e) {
            requestResult.responseBody = e.getMessage();
            requestResult.statusCode = -1;
            log.error("HttpBaseUtil MalformedURLException error {}",e);
            return requestResult;
        } catch (IOException e) {
            requestResult.responseBody = e.getMessage();
            requestResult.statusCode = -1;
            log.error("HttpBaseUtil IOException error {}",e);
            return requestResult;
        } finally {
            if (resOut != null) {
                try {
                    resOut.close();
                } catch (IOException e) {
                    log.error("HttpBaseUtil resOut.close IOException error {}", e);
                }
            }
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    log.error("HttpBaseUtil in.close IOException error {}", e);
                }
            }
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    log.error("HttpBaseUtil writer.close IOException error {}", e);
                }
            }
        }

    }



//    public static RequestResult requestTest(String url, String params, Map<String, String> headers, String method, String mediaType, String body, String bodyType, long timeout, String cookies, String authType, String userName, String password, boolean followRedirects) {
//
//        long startTime = System.currentTimeMillis();
//
//        Map<String, String> requestHeaders = new HashMap<>();
//
//        String result = null;
//        if (url == null || url.trim().isEmpty()) {
//            return null;
//        }
//        method = method.toUpperCase();
//        OutputStreamWriter writer = null;
//        InputStream in = null;
//        ByteArrayOutputStream resOut = null;
//
//        RequestResult requestResult = null;
//
//        try {
//
//            if (url.startsWith("https")) {
//                try {
//                    HttpsURLConnection.setDefaultHostnameVerifier(new HttpBaseUtil.NullHostNameVerifier());
//                    SSLContext sc = SSLContext.getInstance("TLS");
//                    sc.init(null, trustAllCerts, new SecureRandom());
//                    HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//
//            URL httpUrl = new URL(url);
//            HttpURLConnection conn = (HttpURLConnection) httpUrl.openConnection();
//            if (method.equals("POST") || method.equals("PUT")) {
//                conn.setDoOutput(true);
//                conn.setUseCaches(false);
//            }
//            conn.setReadTimeout((int)timeout * 1000);
//            conn.setConnectTimeout((int)timeout * 1000);
//            conn.setRequestMethod(method);
//            conn.setRequestProperty("Accept-Charset", "utf-8");
//            requestHeaders.put("Accept-Charset", "utf-8");
//
//            conn.setRequestProperty("Content-Type", bodyType != null ? bodyType : mediaType );
//            requestHeaders.put("Content-Type", bodyType != null ? bodyType : mediaType );
//
//            conn.setRequestProperty("Cookie", cookies);
//            if (cookies != null && !cookies.equals("")) {
//                requestHeaders.put("Cookie", cookies);
//            }
//
//            conn.setInstanceFollowRedirects(followRedirects);
//
//            if (authType.equals("httpBasicAuth")) {
//                String auth = userName + ":" + password;
//
//                byte[] encodedAuth = org.apache.commons.net.util.Base64.encodeBase64(auth.getBytes(StandardCharsets.UTF_8));
//                String authHeaderValue = "Basic " + new String(encodedAuth);
//                conn.setRequestProperty("Authorization", authHeaderValue);
//                requestHeaders.put("Authorization", authHeaderValue);
//            } else if (authType.equals("digestAuth")) {
//                MessageDigest md5 = null;
//                try {
//                    md5 = MessageDigest.getInstance("MD5");
//                } catch (NoSuchAlgorithmException e) {
//                    e.printStackTrace();
//                }
//                md5.update(password.getBytes());
//
//                String digestedPass = digest2HexString(md5.digest());
//                String credentials = userName + ":" + digestedPass;
//                conn.setRequestProperty("Authorization", "Digest " + credentials);
//                requestHeaders.put("Authorization", "Digest " + credentials);
//            }
//
//            requestResult = new RequestResult();
//
//            // 添加请求头
//            if (headers != null) {
//                Iterator<String> iterator = headers.keySet().iterator();
//                while (iterator.hasNext()) {
//                    String key = iterator.next();
//                    conn.setRequestProperty(key, headers.get(key));
//                    requestHeaders.put(key, headers.get(key));
//                }
//            }
//
//            String requestHeaderStr = null;
//            // 添加参数
//            if (params != null) {
//                conn.setRequestProperty("Content-Length", String.valueOf(params.length()));
//                if (params.length() != 0) {
//                    requestHeaders.put("Content-Length", String.valueOf(params.length()));
//                }
//
//
//                requestHeaderStr = constructHeaderFieldsForString(requestHeaders);
//                requestResult.requestHeader = requestHeaderStr;
//                requestResult.requestBody = body == null ? params : body;
//
//                if (method.equals("POST") || method.equals("PUT")) {
//                    writer = new OutputStreamWriter(conn.getOutputStream());
//                    writer.write(params);
//                    writer.flush();
//                }
//            }
//
//
//            Set<Map.Entry<String, String>> entries = headers.entrySet();
//            for (Map.Entry<String, String> entry : entries) {
//                requestHeaders.put(entry.getKey(), entry.getValue());
//            }
//
//
//            Map<String, List<String>> responseHeaderFields = conn.getHeaderFields();
//            String responseHeaderStr = constructHeaderFields(responseHeaderFields);
//
//            // 判断连接状态
//            int responseCode = conn.getResponseCode();
//            if (responseCode >= 300) {
//                requestResult = new RequestResult();
//                requestResult.requestHeader = requestHeaderStr;
//                requestResult.responseHeader = responseHeaderStr;
//                requestResult.responseBody = "HTTP Request is not success, Response code is " + conn.getResponseCode();
//                requestResult.responseTime = 0L;
//                requestResult.statusCode = responseCode;
//                requestResult.requestBody = params;
//                return requestResult;
//
////                throw new RuntimeException("HTTP Request is not success, Response code is " + conn.getResponseCode());
//            }
//
//            // 获取返回数据
//            in = conn.getInputStream();
//            resOut = new ByteArrayOutputStream();
//            byte[] bytes = new byte[1024];
//            int len;
//            while ((len = in.read(bytes)) != -1) {
//                resOut.write(bytes, 0, len);
//            }
//            result = resOut.toString();
//            // 断开连接
//            conn.disconnect();
//
//            long endTime = System.currentTimeMillis();
//
//            long timeCost = endTime - startTime;
//
//
//            requestResult.responseHeader = responseHeaderStr;
//            requestResult.responseBody = result;
//            requestResult.responseTime = timeCost;
//            requestResult.statusCode = responseCode;
//
//            return requestResult;
//
//        } catch (MalformedURLException e) {
//            requestResult.responseBody = e.getMessage();
//            requestResult.statusCode = -1;
//            log.error("HttpBaseUtil MalformedURLException error {}",e);
//            return requestResult;
//        } catch (IOException e) {
//            requestResult.responseBody = e.getMessage();
//            requestResult.statusCode = -1;
//            log.error("HttpBaseUtil IOException error {}",e);
//            return requestResult;
//        } finally {
//            if (resOut != null) {
//                try {
//                    resOut.close();
//                } catch (IOException e) {
//                    log.error("HttpBaseUtil resOut.close IOException error {}",e);
//                }
//            }
//            if (in != null) {
//                try {
//                    in.close();
//                } catch (IOException e) {
//                    log.error("HttpBaseUtil in.close IOException error {}",e);
//                }
//            }
//            if (writer != null) {
//                try {
//                    writer.close();
//                } catch (IOException e) {
//                    log.error("HttpBaseUtil writer.close IOException error {}",e);
//                }
//            }
//        }
//    }



    public static class NullHostNameVerifier implements HostnameVerifier {
        @Override
        public boolean verify(String arg0, SSLSession arg1) {
            // TODO Auto-generated method stub
            return true;
        }
    }



    static TrustManager[] trustAllCerts = new TrustManager[] { new X509TrustManager() {
        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
            // TODO Auto-generated method stub
        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
            // TODO Auto-generated method stub
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            // TODO Auto-generated method stub
            return null;
        }
    } };



    /**
     * http请求
     *
     * @param url
     * @param params    请求参数
     * @param headers   请求头
     * @param method    请求方式
     * @param mediaType 参数类型,application/json,application/x-www-form-urlencoded
     * @return
     */
    public static String request(String url, String params, Map<String, String> headers, String method, String mediaType) {
        String result = null;
        if (url == null || url.trim().isEmpty()) {
            return null;
        }
        method = method.toUpperCase();
        OutputStreamWriter writer = null;
        InputStream in = null;
        ByteArrayOutputStream resOut = null;
        try {

            if (url.startsWith("https")) {
                try {
                    HttpsURLConnection.setDefaultHostnameVerifier(new HttpBaseUtil.NullHostNameVerifier());
                    SSLContext sc = SSLContext.getInstance("TLS");
                    sc.init(null, trustAllCerts, new SecureRandom());
                    HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            URL httpUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) httpUrl.openConnection();
            if (method.equals("POST") || method.equals("PUT")) {
                conn.setDoOutput(true);
                conn.setUseCaches(false);
            }
            conn.setReadTimeout(80000);
            conn.setConnectTimeout(50000);
            conn.setRequestMethod(method);
            conn.setRequestProperty("Accept-Charset", "utf-8");
            conn.setRequestProperty("Content-Type", mediaType);
            // 添加请求头
            if (headers != null) {
                Iterator<String> iterator = headers.keySet().iterator();
                while (iterator.hasNext()) {
                    String key = iterator.next();
                    conn.setRequestProperty(key, headers.get(key));
                }
            }
            // 添加参数
            if (params != null) {
                conn.setRequestProperty("Content-Length", String.valueOf(params.length()));
                writer = new OutputStreamWriter(conn.getOutputStream());
                writer.write(params);
                writer.flush();
            }
            // 判断连接状态
            if (conn.getResponseCode() >= 300) {
                throw new RuntimeException("HTTP Request is not success, Response code is " + conn.getResponseCode());
            }

            Map<String, List<String>> headerFields = conn.getHeaderFields();

            // 获取返回数据
            in = conn.getInputStream();
            resOut = new ByteArrayOutputStream();
            byte[] bytes = new byte[1024];
            int len;
            while ((len = in.read(bytes)) != -1) {
                resOut.write(bytes, 0, len);
            }
            result = resOut.toString();
            // 断开连接
            conn.disconnect();
        } catch (MalformedURLException e) {
            log.error("HttpBaseUtil MalformedURLException error {}",e);
        } catch (IOException e) {
            log.error("HttpBaseUtil IOException error {}",e);
        } finally {
            if (resOut != null) {
                try {
                    resOut.close();
                } catch (IOException e) {
                    log.error("HttpBaseUtil resOut.close IOException error {}",e);
                }
            }
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    log.error("HttpBaseUtil in.close IOException error {}",e);
                }
            }
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    log.error("HttpBaseUtil writer.close IOException error {}",e);
                }
            }
        }
        return result;
    }

    /**
     * 异步表单请求
     *
     * @param url
     * @param params       请求参数
     * @param headers      请求头
     * @param method       请求方式
     * @param onHttpResult 请求回调
     * @return
     */
    public static void requestAsyn(String url, String params, Map<String, String> headers, String method, OnHttpResult onHttpResult) {
        requestAsyn(url, params, headers, method, "application/x-www-form-urlencoded", onHttpResult);
    }

    /**
     * 异步http请求
     *
     * @param url
     * @param params       请求参数
     * @param headers      请求头
     * @param method       请求方式
     * @param mediaType    参数类型,application/json,application/x-www-form-urlencoded
     * @param onHttpResult 请求回调
     */
    public static void requestAsyn(String url, String params, Map<String, String> headers, String method, String mediaType, OnHttpResult onHttpResult) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    String result = request(url, params, headers, method, mediaType);
                    onHttpResult.onSuccess(result);
                } catch (Exception e) {
                    onHttpResult.onError(e.getMessage());
                }
            }
        }).start();
    }

    private static String mapToBodyObj(Map<String, String> params) {
        String result = "";
        JSONObject jsonObject = new JSONObject();

        if (params != null) {
            Set<String> keys = params.keySet();
            for (String key : keys) {
                String value = params.get(key);
                jsonObject.put(key, value);
            }
        }

        result = jsonObject.toJSONString();
        return result;
    }


    /**
     * map转成string
     */
    private static String mapToString(String url, Map<String, String> params, String first) {
        StringBuilder sb;
        if (url != null) {
            sb = new StringBuilder(url);
        } else {
            sb = new StringBuilder();
        }
        if (params != null) {
            boolean isFirst = true;
            Iterator<String> iterator = params.keySet().iterator();
            while (iterator.hasNext()) {
                String key = iterator.next();
                if (isFirst) {
                    if (first != null) {
                        sb.append(first);
                    }
                    isFirst = false;
                } else {
                    sb.append("&");
                }
                sb.append(key);
                sb.append("=");
                sb.append(params.get(key));
            }
        }
        return sb.toString();
    }

    /**
     * 异步请求回调
     */
    public interface OnHttpResult {
        void onSuccess(String result);

        void onError(String message);
    }



    public static void main(String[] args) {
//        RequestResult requestResult = requestTest("http://192.168.50.107:29200/dc_source_metrics_48de1e73e87a67986ec0ef049c4d26dc_20220614", "", new HashMap<>(), "GET", null, null,  60, "", "", "", "", false);
//        System.out.println(requestResult);

        String body = "<ClientInstRequest><Events><E><T>Event.ClientInst</T><IG>A1E8EF91CC724AC7BB1E6594EE706D0B</IG><TS>1655178525486</TS><D><![CDATA[[{\"T\":\"CI.BoxModel\",\"FID\":\"CI\",\"Name\":\"v2.8\",\"SV\":\"4\",\"P\":{\"C\":16,\"N\":1,\"I\":\"5m\",\"S\":\"C+BD+T\",\"M\":\"V+L+M+MT+E+N+C+K+BD\",\"T\":8651,\"K\":\"LI.b_algo+LI.b_ans+19k+1ud+17s+SERP,5028.1+SERP,5371.1+SERP,5124.1+IMG.rms_img+SERP,5145.1+SERP,5165.1+SERP,5185.1+SERP,5204.1+SERP,5224.1+SERP,5248.1+SERP,5273.1+SERP,5293.1+SERP,5313.1+SERP,5456.1+6o9+cn.bing.com+https+xmlhttprequest+img+s.cn.bing.net+rp%2Fajd4P2gLuC9ICyk5BTNHL3oybcE.br.js+script+rp%2FjLAw67Y0F8ph3rY-aZOF_BwSdiw.br.js+rp%2FZlVckd9hZ3nUxfPoTz0H1WO9lOE.br.js+rp%2Fvt4Nt13NvJrnIruLOKMQzyLTIpo.br.js+rp%2Fijchd2BmUQhQL83v5TsxGHjpEfo.br.js+rp%2FlTyJEJwo1EaUKXYty0oRNlIep7I.br.js+rp%2FGyuq2bqitqDJM0BeAkbKXGlQXNw.br.js+clientinst+audio+rp%2Fn21aGRCN5EKHB3qObygw029dyNU.br.js+rp%2FXJ8OmILbNhm0zU9tdkuGYeXVPRQ.br.js+rp%2FUftfQbYuKvGGEUHPU3QGHYd90Z8.br.js+rp%2F06bQtOdvnqIODKnOBKJedLV7FUg.br.js+rp%2Fzlfm-hC70pZAs62UVTTl3KShKOE.br.js+rp%2FSL56Tp0mN8y00mLp67HAn5P_JWU.br.js+rp%2FF6Ik6GDr7EDlqwASojyq1fMHyEA.br.js+rp%2FOvNvOrSFC7iM0Dxwvgsl7OgM-04.br.js+103+rp%2Fy1tiMssL1_ZRGIkBjxDYmR2kX8o.br.js+11e+rp%2F8w26ODmd1hk4C30WJtfkdBYFSfE.br.js+12n+rp%2FJmCRB93ls1uvStbw9A9f1_sLaQY.br.js+13s+140+rp%2FV_QXiq3LUExks6KpMooFxv4mGg8.br.js+152+rp%2FID-70CBAEOXh6Nwxga-CxgpUq4k.br.js+175+rp%2FSJ2DuVgzGDDj9ZSjoOVTYnxBcKQ.br.js+18e+19t+19v+19x+1ep+1er+19y+1a2+1b9+css+1cc+1cf+2ti+mousemove\",\"F\":0},\"V\":\"5h/0/0/189/101/@4/3u/1/e/visible/default+9p//////@3////\",\"L\":\"5l/0/NAV.b_scopebar/SERP,5016.1/4g/2q/13c/13/2/T/-1+5l/1/H1.b_logo//o/1m/38/i/4/T/-1+5l/2/DIV.b_searchboxForm/SERP,5024.1/4g/17/i2/1c/3/T/-1+5l/3/DIV#id_h/@5/16o/17/0/0/2/T/2+9q/3///134//3k/1e//T/+9q/4/SPAN.sb_count//50/47/30/u/4/T/-1+9q/5/SPAN.ftrB/SERP,5363.1/80/47/2k/u/4/T/-1+9q/6/LI.b_ans b_top b_topborder/@6/4g/51/i0/59/4/T/-1+9q/7/@0/@7/4g/at/i0/ap/4/T/-1+9q/8/@0/@9/4g/ls/i0/35/4/T/-1+9q/9/@0/@a/4g/p7/i0/35/4/T/-1+9q/a/@0/@b/4g/sm/i0/35/4/T/-1+9q/b/@0/@c/4g/w1/i0/35/4/T/-1+9q/c/@0/@d/4g/zg/i0/35/4/T/-1+9q/d/@0/@e/4g/12v/i0/35/4/T/-1+9q/e/@0/@f/4g/16a/i0/35/4/T/-1+9q/f/@0/@g/4g/19p/i0/35/4/T/-1+9q/g/@0/@h/4g/1d4/i0/35/4/T/-1+9q/h/@1/SERP,5432.1/4g/1gj/i0/8q/4/T/-1+9q/i/LI.b_msg b_canvas/SERP,5057.1/4g/1pj/i0/11/4/T/-1+9q/j/LI.b_pag/SERP,5475.1/4g/1qu/i0/2p/4/T/-1+9q/k/LI#mfa_root/SERP,5489.1/@1e/vz/3s/3s/4/T/-1+@2/k///0/0////T/+9q/l/@1/@i/oo/51/d4/av/4/T/-1+@2/l//////aq//T/+9q/m/FOOTER#b_footer/SERP,5042.1/0/@3/@4/1c/1/T/-1+5l/n/IMG#id_p/@5/0/0/0/0/4/T/2+@2/o/IMG#ev_enlarge_img//0/0/0/0/5/T/2+9q/p/IMG#embDBC9F88/@6/4z/9f/g/g/k/T/2+@2/q/@8/@7/4e/b9/g/g/9/T/2+@2/r/@8/@9/4e/m8/g/g/9/T/2+@2/s/@8/@a/4e/pn/g/g/9/T/2+9q/t/IMG.siteicon rms_img/@b/4e/t2/g/g/8/T/2+@2/u/@8/@c/4e/wh/g/g/9/T/2+@2/v/@8/@d/4e/zw/g/g/9/T/2+@2/w/@8/@e/4e/13b/g/g/9/T/2+@2/x/@8/@f/4e/16q/g/g/9/T/2+@2/y/@8/@g/4e/1a5/g/g/9/T/2+@2/z/@8/@h/4e/1dk/g/g/9/T/2+9q/10/@8/@i/p9/6t/c/c/a/T/-1+9q/11/@8/@i/p9/7z/c/c/a/T/-1+9q/12/@8/@i/p9/95/c/c/a/T/-1+9q/13/@8/@i/p9/aa/c/c/a/T/-1+9q/14/@8/@i/p9/bg/c/c/a/T/-1+9q/15/@8/@i/p9/cm/c/c/a/T/-1+9q/16/@8/@i/p9/ds/c/c/a/T/-1+9q/17/@8/@i/p9/ey/c/c/a/T/-1+@2/18/IFRAME#ev_iframe_talk//0/0/0/0/4/T/2\",\"E\":\"@j/2/X/1o/+@j/6/X/1o/+@j/7/X/1o/+@j/8/X/1o/+@j/9/X/1o/+@j/a/X/1o/+@j/b/X/1o/+@j/c/X/1o/+@j/d/X/1o/+@j/e/X/1o/+@j/f/X/1o/+@j/g/X/1o/+@j/h/X/1o/+@j/i/X/1o/+@j/j/X/1o/+@j/k/X/2s/+@j/l/X/4y/\",\"N\":\"-1/0//@k/search/navigation/c5/@l/2/2/2/c/3b/8q+52/1//@k/rp%2Fi3t3vGbFsQ-XpUOCob6i88omBUs.png/@1t/1b/@l/52/52/52/55/6a/6d+56/2//@k/first-paint//0/@l/-1/-1/-1/-1/-1/-1+56/3//@k/first-contentful-paint//0/@l/-1/-1/-1/-1/-1/-1+cf/4//@k/cpt/@n/3n/@l/cf/cf/cf/ch/g0/g2+ci/5//@k/ls%2Flsp.aspx/beacon/2d/@l/ci/ci/ci/cn/ev/ew+cw/6//@k/rp%2FOW1PDvms6OEodYtc9Dn5Bdu2Hbk.br.js/@q/15/@l/cw/cw/cw/cz/dx/e1+em/7//@k/@p/@m/1i/@l/em/em/em/et/g0/g5+en/8//@k/@r/@m/1j/@l/en/en/en/ev/g0/g7+en/9//@k/@s/@m/1l/@l/en/en/en/ev/g1/g9+eo/a//@k/@t/@m/1i/@l/eo/eo/eo/ev/g0/g6+ep/b//@k/@u/@m/2f/@l/ep/ep/ep/ev/g1/h4+eq/c//@k/@v/@m/1t/@l/eq/eq/eq/ew/g5/gj+er/d//@k/@w/@m/1q/@l/er/er/er/f3/g5/gh+es/e//@k/@z/@m/1p/@l/es/es/es/f3/g5/gh+eu/f//@k/@10/@m/1o/@l/eu/eu/eu/f6/g6/gi+eu/g//@k/@11/@m/1n/@l/eu/eu/eu/f7/g5/gi+f3/h//@k/@12/@m/1g/@l/f3/f3/f3/fb/ga/gk+f3/i//@k/@13/@m/1g/@l/f3/f3/f3/fb/ga/gj+f4/j//@k/@14/@m/1g/@l/f4/f4/f4/fb/gb/gk+f8/k//@k/@15/@m/1c/@l/f8/f8/f8/fc/ga/gk+fa/l//@k/@16/@m/1q/@l/fa/fa/fa/fj/gl/h1+fb/m//@k/@18/@m/1s/@l/fb/fb/fb/fk/gl/h3+fe/n//@k/@1a/@m/1n/@l/fe/fe/fe/fk/gl/h1+ff/o//@k/@1c/@m/1o/@l/ff/ff/ff/fk/gl/h3+fj/p//@k/@1f/@m/1r/@l/fj/fj/fj/ft/h0/ha+fj/q//@k/@1h/@m/1r/@l/fj/fj/fj/ft/h0/hb+fk/r//@k/@1j/@m/1q/@l/fk/fk/fk/fu/h0/ha+fk/s//@k/hasrr/@n/2y/@l/fk/fk/fk/fu/ii/ij+fl/t/q/@o/th/@n/1j/@l/fl/fl/fl/fu/gn/h4+fl/u/r/@o/th/@n/1i/@l/fl/fl/fl/fu/gm/h3+fn/v/w/@o/th/@n/1g/@l/fn/fn/fn/fu/gn/h4+gb/w//@k/@p/@q/1b/@l/gb/gb/gb/gn/hl/hn+i2/x//@k/@r/@q/17/@l/i2/i2/i2/i5/j3/j9+jc/y//@k/@s/@q/16/@l/jc/jc/jc/je/kg/ki+ko/z//@k/@t/@q/16/@l/ko/ko/ko/kt/lt/lu+lx/10//@k/@u/@q/18/@l/lx/lx/lx/m0/my/n5+og/11//@k/@v/@q/19/@l/og/og/og/oj/pk/pp+pw/12//@k/@w/@q/18/@l/pw/pw/pw/q2/r3/r4+q4/13//@k/@x/@n/2y/@l/q4/q4/q4/qc/t1/t3+q5/14//@k/@x/@n/36/@l/q5/q5/q5/qc/ta/tb+q8/15//@k/ec%2Fstart.mp3/@y/6c/@l/q8/q8/q8/qc/wi/wk+q8/16//@k/ec%2Fstop.mp3/@y/2y/@l/q8/q8/q8/qc/t1/t7+r7/17//@k/@z/@q/12/@l/r7/r7/r7/r9/s7/s9+sb/18//@k/@10/@q/1e/@l/sb/sb/sb/sd/te/tq+tw/19//@k/@11/@q/16/@l/tw/tw/tw/tz/uw/v3+v8/1a//@k/@12/@q/15/@l/v8/v8/v8/vd/wb/wd+we/1b//@k/@13/@q/11/@l/we/we/we/wh/xe/xg+xi/1c//@k/@14/@q/12/@l/xi/xi/xi/xm/yj/yl+yr/1d//@k/@15/@q/14/@l/yr/yr/yr/yt/zs/zw+@17/1e//@k/@16/@q/13/@l/@17/@17/@17/105/115/116+@19/1f//@k/@18/@q/14/@l/@19/@19/@19/11h/12h/12i+@1b/1g//@k/@1a/@q/13/@l/@1b/@1b/@1b/12r/13p/13r+@1d/1h//@k/@1c/@q/18/@l/@1d/@1d/@1d/@1e/14z/151+@1g/1i//@k/@1f/@q/15/@l/@1g/@1g/@1g/158/166/167+172/1j//bingciservice.koreasouth.cloudapp.azure.com/QRTrigger%2FHook/@m/43/@l/-1/0/-1/-1/-1/1b6+@1i/1k//@k/@1h/@q/17/@l/@1i/@1i/@1i/17e/18b/18d+@1k/1l//@k/@1j/@q/10/@l/@1k/@1k/@1k/18g/19d/19e+@1l/1m//@k/@x/@n/2m/@l/@1l/@1l/@1l/@1n/@1v/1cg+@1m/1n//@k/@x/@n/4w/@l/@1m/@1m/@1m/@1q/@1o/@1p+@1n/1o//@k/@x/@n/4u/@l/@1n/@1n/@1n/@1r/@1o/@1p+@1q/1p//@k/@x/@n/4t/@l/@1q/@1q/@1q/@1r/@1o/1es+@1s/1q//@o/th/@n/l/@l/@1s/@1s/@1s/1bc/1bo/1bu+@1u/1r//@o/th/@1t/j/@l/@1u/@1u/@1u/@1v/1ct/1cv+@1w/1s//@k/@x/@n/2d/@l/@1w/@1w/@1w/2tm/2vv/2vw\",\"C\":\"5y/2/@1x/mouse/0/lp/1p/0+6r/////ne/1q/+7e/////pi/1w/+7u/////r0/22/+8a/////t1/29/+8s/////ut/2g/+98/////wh/2m/+9o/0////xy/2u/+bv/////10x/3e/+fw/////12y/4b/+je/////14a/4t/+nf/////15j/5h/+r6/////16w/62/+t6/////188/6t/\",\"BD\":\"5y/@1x/1655178518\"}]]]></D></E></Events><STS>1655178525486</STS></ClientInstRequest>";

//        RequestResult requestResult = requestTest("https://cn.bing.com/fd/ls/lsp.aspx", "", new HashMap<>(), "POST", body, "application/xml",  60, "", "", "", "", true);
//        System.out.println(requestResult);

    }

}