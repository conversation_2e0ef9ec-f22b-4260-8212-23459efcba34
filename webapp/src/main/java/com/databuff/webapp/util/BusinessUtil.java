package com.databuff.webapp.util;

import com.alibaba.fastjson.JSONObject;
import com.databuff.entity.Business;
import com.google.common.base.Joiner;

import java.util.ArrayList;
import java.util.List;

public class BusinessUtil {

    public static String joinBusinessNames(List<Business> businessList) {
        if (businessList == null || businessList.size() == 0) {
            return null;
        }
        List<String> names = new ArrayList<>();
        for (Business business : businessList) {
            names.add(business.getName());
        }
        return Joiner.on(",").join(names);
    }

    public static List<JSONObject> getBusinessInfos(List<Business> businessList) {
        if (businessList == null || businessList.size() == 0) {
            return null;
        }
        List<JSONObject> infos = new ArrayList<>();
        for (Business business : businessList) {
            JSONObject info = new JSONObject();
            info.put("id", business.getId());
            info.put("name", business.getName());
            infos.add(info);
        }
        return infos;
    }

    public static List<Integer> getBusinessParentIds(List<Business> businessList) {
        if (businessList == null || businessList.size() == 0) {
            return null;
        }
        List<Integer> ids = new ArrayList<>();
        for (Business business : businessList) {
            ids.add(business.getPid());
        }
        return ids;
    }
}
