package com.databuff.webapp.util;

import com.databuff.entity.rum.mysql.RumSourceMap;
import com.databuff.webapp.rumV2.service.RumSourceMapService;
import com.google.debugging.sourcemap.SourceMapConsumerV3;
import com.google.debugging.sourcemap.SourceMapParseException;
import com.google.debugging.sourcemap.proto.Mapping;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class SourceMapUtil {


    /**
     * ### 正则表达式的分解与解释
     * <p>
     * 1. **`^\\s*(?:at\\s+)?`**
     *    - **目的**：匹配行开头的空白字符，和可能出现的“at”前缀。
     *    - **解释**：
     *      - `^` 表示行的开头。
     *      - `\\s*` 匹配0或多个空白字符，以适配不同的格式。
     *      - `(?:at\\s+)?` 匹配可能出现的 `at ` 前缀，在JavaScript的错误堆栈中，`at`通常表示函数调用。
     *    - **适配场景**：JavaScript堆栈追踪格式中，一些行前有 `at`，例如 `at myFunction (http://example.com/file.js:10:20)`。
     * <p>
     * 2. **`(?: ... )`（主要匹配组）**
     *    - **目的**：用于捕获函数名和位置信息。
     *    - **解释**：
     *      - `(?: ... )` 表示非捕获分组，整个括号内容会被视为一组，但不会被单独捕获为一个组，起到结构划分的作用。
     *    - **适配场景**：帮助匹配在不同位置可能出现的函数名或文件路径格式。
     * <p>
     * 3. **`([^()@\\s]+)`（捕获组1：函数名）**
     *    - **目的**：捕获函数名，函数名必须由非括号、@符号和空白字符组成。
     *    - **解释**：
     *      - `[^()@\\s]+` 匹配任意不包含括号 `()`、@ 符号和空白字符的字符序列。
     *    - **适配场景**：JavaScript堆栈中通常格式为 `at functionName (URL:line:column)` 或 `functionName@URL:line:column`，其中 `functionName` 是函数名称。
     * <p>
     * 4. **`\\s*`**
     *    - **目的**：匹配函数名后的可选空白字符。
     *    - **解释**：空白字符有时用于分隔函数名和文件路径，在此处可以匹配 0 个或多个空白字符。
     *    - **适配场景**：函数名和位置（URL）之间可能存在空格，因此允许捕获这些空格。
     *
     * 5. **`(?: ... )`（匹配位置信息的子组）**
     *    - **目的**：捕获位置信息，支持使用括号或 @ 符号分隔的文件路径。
     *    - **解释**：
     *      - `(?: ... )` 表示非捕获分组，用于结构化文件路径的格式。
     *    - **适配场景**：适配JavaScript中常见的堆栈格式。
     * <p>
     * 6. **`\\(([^\\)]+)\\)`（捕获组2：URL或文件路径，带括号）**
     *    - **目的**：捕获以括号包围的文件URL或路径。
     *    - **解释**：
     *      - `\\(` 和 `\\)` 分别匹配左括号和右括号。
     *      - `([^\\)]+)` 表示捕获括号内的 URL 或路径，直到右括号结束。
     *    - **适配场景**：适配形如 `at functionName (http://example.com/file.js:10:20)` 的堆栈格式，其中 URL 位于括号内。
     * <p>
     * 7. **`@([^\\s]+)`（捕获组3：URL或文件路径，使用 @ 分隔）**
     *    - **目的**：匹配使用 @ 符号后面跟随的文件 URL 或路径。
     *    - **解释**：
     *      - `@` 符号表示文件路径的前缀。
     *      - `([^\\s]+)` 表示捕获 @ 后面的 URL 或路径，直到遇到空白字符为止。
     *    - **适配场景**：适配形如 `functionName@http://example.com/file.js:10:20` 的堆栈格式。
     * <p>
     * 8. **`([^\\s]+)`（捕获组4：URL或文件路径，无函数名）**
     *    - **目的**：匹配没有函数名时的文件路径。
     *    - **解释**：
     *      - `([^\\s]+)` 表示捕获 URL 或路径，直到遇到空白字符。
     *    - **适配场景**：适配形如 `http://example.com/file.js:10:20` 的堆栈格式，不包含函数名。
     * <p>
     * 9. **`(?::(\\d+)(?::(\\d+))?)?`（捕获行号和列号）**
     *    - **目的**：捕获文件路径后面的行号和列号。
     *    - **解释**：
     *      - `:` 固定匹配行号与列号之间的分隔符。
     *      - `(\\d+)` 表示匹配一个或多个数字，捕获为行号（捕获组5）。
     *      - `(?::(\\d+))?` 表示可选的列号（捕获组6），允许行号之后带或不带列号。
     *    - **适配场景**：适配 `http://example.com/file.js:10:20` 中的行号和列号。
     * <p>
     * ### 总结
     * <p>
     * 该正则表达式 `STACK_LINE_PATTERN` 设计用于支持多种JavaScript堆栈格式，包括以下格式：
     * <p>
     * - `at functionName (URL:line:column)`：常见的带函数名、括号包围 URL、行号和列号的格式。
     * - `functionName@URL:line:column`：带函数名和 @ 分隔的 URL、行号和列号。
     * - `URL:line:column`：不带函数名，直接提供 URL、行号和列号的格式。
     * - `at URL:line`：没有列号的简化格式。
     * <p>
     * 这些不同格式的兼容性确保了该正则表达式可以解析多种 JavaScript 错误堆栈，以方便后续通过源映射文件映射到源代码的具体位置。
     */
    private static final Pattern STACK_LINE_PATTERN = Pattern.compile(
            "^\\s*(?:at\\s+)?" +      // 匹配行开头的可选空白字符以及可选的“at”前缀，JavaScript的堆栈跟踪中常见的格式。
                    "(?:" +                   // 开始主要匹配组，目的是捕获函数名和位置信息。
                    "(" +                 // 捕获组1：可选的函数名（不包括括号、@符号或空白字符）。
                    "[^()@\\s]+" +    // 匹配函数名，函数名不包含括号“()”、@符号和空白字符。
                    ")" +
                    "\\s*" +              // 匹配函数名后的可选空白符。
                    "(?:" +               // 开始捕获位置信息的分组，支持不同的 JavaScript 堆栈格式。
                    "\\(" +           // 匹配括号“(”，常用于函数调用后的文件URL或位置。
                    "([^\\)]+)" + // 捕获组2：匹配括号内的URL或文件路径，直到遇到右括号“)”。
                    "\\)" +           // 匹配右括号“)”。
                    "|" +             // 或者支持另一种格式，用@符号分隔的文件路径。
                    "@" +             // 匹配@符号，例如“函数名@URL”格式。
                    "([^\\s]+)" + // 捕获组3：匹配@符号后的文件URL，直到空白字符结束。
                    ")" +
                    "|" +                 // 或者，支持没有函数名时的堆栈格式。
                    "([^\\s]+)" +         // 捕获组4：没有函数名时直接匹配文件路径/URL，直到空白字符结束。
                    ")" +
                    "(?::(\\d+)(?::(\\d+))?)?" // 捕获行号和列号：首先是行号，之后是可选的列号。
    );

    private static final String MAP_FILE_EXTENSION = ".map";

    /**
     * 解析错误堆栈并使用源映射(source map)将其转换为原始源代码位置。
     *
     * <p>此方法接收一个压缩后的JavaScript错误堆栈跟踪，并使用相应的源映射将其转换为
     * 原始源代码中的位置。这对于调试压缩后的前端代码非常有用。</p>
     *
     * <p>使用示例:</p>
     * <pre>
     * String compressedErrorStack = "at t.exports.e (https://example.com/bundle.js:1:1000)";
     * int appId = 123;
     * RumSourceMapMapper mapper = new RumSourceMapMapper();
     *
     * String originalErrorStack = SourceMapUtil.parseErrorStack(compressedErrorStack, appId, mapper);
     * System.out.println(originalErrorStack);
     * // 输出可能类似于: at originalFunction (https://example.com/original.js:10:20)
     * </pre>
     *
     * @param errorStack          需要解析的压缩后的错误堆栈字符串。每行应包含一个堆栈跟踪项。
     * @param appId               应用ID，用于从数据库中检索正确的源映射。
     * @param rumSourceMapService 用于获取源映射的服务对象。此服务负责从本地存储中检索源映射文件。
     * @return 解析后的错误堆栈字符串，包含原始源代码的位置信息。
     * @throws IllegalArgumentException 如果errorStack或rumSourceMapMapper为null。
     */
    public static String parseErrorStack(String errorStack, int appId, RumSourceMapService rumSourceMapService) {
        validateInput(errorStack, rumSourceMapService);

        String[] lines = errorStack.split("\n");

        // Check if the first line is a stack trace line
        Matcher firstLineMatcher = STACK_LINE_PATTERN.matcher(lines[0]);
        boolean firstLineIsStack = firstLineMatcher.find();

        // If the first line is not a stack trace, treat it as the error message
        String errorMessage = firstLineIsStack ? "" : lines[0];

        Set<String> uniqueFileNames = identifyUniqueFileNames(lines, firstLineIsStack);
        if (uniqueFileNames.isEmpty()) {
            log.warn("No stack files found after parsing the uploaded stack trace");
            return errorStack;
        }

        Map<String, SourceMapConsumerV3> sourceMapConsumers = fetchAndParseSourceMaps(appId, uniqueFileNames, rumSourceMapService);

        String parsedStack = parseStackTrace(lines, sourceMapConsumers);
        return errorMessage.isEmpty() ? parsedStack : errorMessage + "\n" + parsedStack;
    }

    private static void validateInput(String errorStack, RumSourceMapService rumSourceMapService) {
        if (errorStack == null || rumSourceMapService == null) {
            throw new IllegalArgumentException("Error stack and rumSourceMapService cannot be null");
        }
    }

    private static Set<String> identifyUniqueFileNames(String[] lines, boolean includeFirstLine) {
        Set<String> uniqueFileNames = new HashSet<>();
        int startIndex = includeFirstLine ? 0 : 1;

        for (int i = startIndex; i < lines.length; i++) {
            String line = lines[i];
            Matcher matcher = STACK_LINE_PATTERN.matcher(line);
            if (matcher.find()) {
                String fullUrl = getFullUrl(matcher);
                if (fullUrl != null && (fullUrl.startsWith("http") || fullUrl.startsWith("file"))) {
                    String fileName = extractFileName(fullUrl);
                    if (fileName != null && !fileName.isEmpty()) {
                        uniqueFileNames.add(fileName + MAP_FILE_EXTENSION);
                    }
                }
            }
        }
        return uniqueFileNames;
    }

    private static String getFullUrl(Matcher matcher) {
        String fullUrl = matcher.group(2);
        if (fullUrl == null) fullUrl = matcher.group(3);
        if (fullUrl == null) fullUrl = matcher.group(4);
        return fullUrl;
    }

    private static String extractFileName(String fullUrl) {
        if (fullUrl == null || !fullUrl.contains("/")) {
            return null;
        }
        // Get the part after the last '/'
        String fileNameWithParams = fullUrl.substring(fullUrl.lastIndexOf('/') + 1);
        // Find the position of '.js'
        int jsExtensionIndex = fileNameWithParams.indexOf(".js");
        if (jsExtensionIndex == -1) {
            // If .js is not found, return null or handle accordingly
            return null;
        }
        // Include '.js' extension
        String fileName = fileNameWithParams.substring(0, jsExtensionIndex + 3);
        return fileName;
    }

    private static Map<String, SourceMapConsumerV3> fetchAndParseSourceMaps(int appId, Set<String> uniqueFileNames, RumSourceMapService rumSourceMapService) {
        List<String> fileNamesList = new ArrayList<>(uniqueFileNames);
        List<RumSourceMap> sourceMaps = rumSourceMapService.getSourceMaps(appId, fileNamesList);
        Map<String, SourceMapConsumerV3> sourceMapConsumers = new HashMap<>(sourceMaps.size());

        for (RumSourceMap sourceMap : sourceMaps) {
            try {
                SourceMapConsumerV3 consumer = new SourceMapConsumerV3();
                consumer.parse(sourceMap.getContent());
                sourceMapConsumers.put(sourceMap.getFileName(), consumer);
            } catch (SourceMapParseException e) {
                log.error("Failed to parse source map for file: {}", sourceMap.getFileName(), e);
            }
        }
        return sourceMapConsumers;
    }

    private static String parseStackTrace(String[] lines, Map<String, SourceMapConsumerV3> sourceMapConsumers) {
        return Arrays.stream(lines)
                .map(line -> {
                    Matcher matcher = STACK_LINE_PATTERN.matcher(line);
                    return matcher.find() ? parseStackTraceLine(line, matcher, sourceMapConsumers) : line;
                })
                .collect(Collectors.joining("\n"));
    }


    private static String parseStackTraceLine(String line, Matcher matcher, Map<String, SourceMapConsumerV3> sourceMapConsumers) {
        // 提取行前面的空白字符
        String linePrefix = line.substring(0, line.indexOf(line.trim()));

        String functionName = matcher.group(1);
        String fullUrl = getFullUrl(matcher);
        String lineNumberStr = matcher.group(5);
        String columnNumberStr = matcher.group(6);

        if (fullUrl == null) {
            return line;
        }

        int lineNumber = -1;
        int columnNumber = -1;

        // Extract line and column numbers from the URL if not captured separately
        if ((lineNumberStr == null || columnNumberStr == null) && fullUrl.matches(".*:\\d+:\\d+$")) {
            Pattern urlPattern = Pattern.compile("(.+):(\\d+):(\\d+)$");
            Matcher urlMatcher = urlPattern.matcher(fullUrl);
            if (urlMatcher.find()) {
                fullUrl = urlMatcher.group(1);
                lineNumberStr = urlMatcher.group(2);
                columnNumberStr = urlMatcher.group(3);
            }
        }

        if (lineNumberStr == null || columnNumberStr == null) {
            return line;
        }

        try {
            lineNumber = Integer.parseInt(lineNumberStr);
            columnNumber = Integer.parseInt(columnNumberStr);
        } catch (NumberFormatException e) {
            log.error("Invalid line or column number in stack trace: {}", line, e);
            return line;
        }

        String fileName = extractFileName(fullUrl);

        SourceMapConsumerV3 consumer = sourceMapConsumers.get(fileName + MAP_FILE_EXTENSION);
        if (consumer != null) {
            try {
                Mapping.OriginalMapping mapping = consumer.getMappingForLine(lineNumber, columnNumber);
                if (mapping != null && mapping.getOriginalFile() != null) {
                    String originalFunctionName = mapping.getIdentifier();
                    String finalFunctionName = originalFunctionName != null ? originalFunctionName : (functionName != null ? functionName : "");

                    String newLine;
                    if (line.trim().contains("@")) {
                        newLine = String.format("%s@%s:%d:%d",
                                finalFunctionName,
                                mapping.getOriginalFile(),
                                mapping.getLineNumber(),
                                mapping.getColumnPosition());
                    } else if (StringUtils.isNotBlank(finalFunctionName)) { // 带函数名的格式
                        newLine = String.format("at %s (%s:%d:%d)",
                                finalFunctionName,
                                mapping.getOriginalFile(),
                                mapping.getLineNumber(),
                                mapping.getColumnPosition());
                    } else { // 仅URL格式
                        newLine = String.format("at  (%s:%d:%d)",
                                mapping.getOriginalFile(),
                                mapping.getLineNumber(),
                                mapping.getColumnPosition());
                    }

                    return linePrefix + newLine.trim();
                }
            } catch (Exception e) {
                log.error("Error while mapping line for file: {}", fileName, e);
            }
        }

        // 如果没有解析到源码映射,保持完全原始的行
        return line;
    }
}
