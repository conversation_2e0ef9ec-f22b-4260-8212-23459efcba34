package com.databuff.webapp.util;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang.StringUtils;

import javax.servlet.ServletRequest;
import java.io.BufferedReader;
import java.util.HashMap;
import java.util.Map;

public class ServletRequestUtils {

    public static Map<String, Object> parseParamsFromRequest(ServletRequest request) {
        Map<String,Object> params = new HashMap<String, Object>();
        BufferedReader br;

        try {
            br = request.getReader();
            String str;
            StringBuilder wholeStr = new StringBuilder();
            while((str = br.readLine()) != null){
                wholeStr.append(str);
            }
            if(StringUtils.isNotEmpty(wholeStr.toString())){
                params = JSON.parseObject(wholeStr.toString(),Map.class);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return params;
    }
}
