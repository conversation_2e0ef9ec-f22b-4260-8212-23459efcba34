package com.databuff.webapp.util;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Author zlh
 * Date 2020/2/13
 */
public class NumberUtil {

    private static AtomicInteger counter =  new AtomicInteger();
    /**
     * 转换百分比为数字
     * @param str
     * @return
     * @throws ParseException
     */
    public static double getNumToPercent(String str) {
        NumberFormat nf= NumberFormat.getPercentInstance();
        Number num= null;
        try {
            num = nf.parse(str);
        } catch (ParseException e) {
        }
        return Double.valueOf(num+"");
    }

    /**
     * 数字比较
     * @param num1
     * @param num2
     * @return
     */
    public static boolean numberCompare(double num1,double num2){
        return num1 > num2;
    }
    /**
     * 数字比较
     * @param num1
     * @param num2
     * @return
     */
    public static boolean numberCompare(String num1,String num2) {
        return NumberUtil.getNumToPercent(num1) > NumberUtil.getNumToPercent(num2);
    }

    public static void setCounter(int value){
        counter.set(value);
    }

    /**
     * 原子自增
     */
    public static int getCount(){
        // 线程安全,以原子方式将当前值加1，注意：这里返回的是自增前的值。
        return counter.getAndIncrement();
    }

    /**
     * 解析double字符串、保留2位小数
     */
    /**
     * 四舍五入,保留两位小数.
     */
    public static String decimal(final double value) {
        if (Double.isNaN(value) || Double.isInfinite(value)) {
            return "0";
        }
        if (value>0&&value<0.01d){
            return "0.01";
        }
        return String.valueOf(new BigDecimal(value).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
    }

    /**
     * 解析double字符串、保留n位小数
     */
    /**
     * 四舍五入,保留两位小数.
     */
    public static String decimal(final double value,int scale) {
        if (Double.isNaN(value) || Double.isInfinite(value)) {
            return "0";
        }
        double sum = 1;
        StringBuilder zero = new StringBuilder();
        for (int i = 0; i < scale; i++) {
            sum *=10;
            if (i>=1){
                zero.append("0");
            }
        }
        if (value>0&&value<(1/sum)){
            return "0."+zero+"1";
        }
        return String.valueOf(new BigDecimal(value).setScale(scale, BigDecimal.ROUND_HALF_UP).doubleValue());
    }

    public static void main(String[] args) throws Exception{


        System.out.println(decimal(0.001,4));
        System.out.println(decimal(0.001,3));
        System.out.println(decimal(0.001,2));
        System.out.println(decimal(0.001,1));
        System.out.println(decimal(33.14124,4));
        System.out.println(decimal(33.14124,2));
        System.out.println(decimal(33.14124,0));
//        System.out.println(decimal(0.001));
//        System.out.println(decimal(33.14124));
//        System.out.println(decimal(33.14924));

//        double num1 = NumberUtil.getNumToPercent("33.09%");
//        double num2 = NumberUtil.getNumToPercent("33.09%");
//        System.out.println(NumberUtil.numberCompare(num1,0.8));
    }

}
