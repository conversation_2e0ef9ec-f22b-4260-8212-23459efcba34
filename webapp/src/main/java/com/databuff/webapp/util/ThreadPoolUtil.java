package com.databuff.webapp.util;

import com.alibaba.ttl.threadpool.TtlExecutors;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ThreadPoolUtil {

    public static final ExecutorService EXECUTOR = TtlExecutors.getTtlExecutorService(Executors.newFixedThreadPool(50));
    public static final ExecutorService ENDPOINT_EXECUTOR = TtlExecutors.getTtlExecutorService(Executors.newFixedThreadPool(50));
    public static final ExecutorService METRIC_EXECUTOR = TtlExecutors.getTtlExecutorService(Executors.newFixedThreadPool(50));
}
