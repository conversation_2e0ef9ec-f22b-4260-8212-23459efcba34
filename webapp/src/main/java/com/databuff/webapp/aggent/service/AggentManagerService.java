package com.databuff.webapp.aggent.service;

import com.alibaba.fastjson.JSONObject;
import com.databuff.entity.AgentEntity;
import com.databuff.entity.AgentLog;
import com.databuff.entity.AgentPack;
import com.databuff.entity.extend.AgentSearch;
import com.databuff.webapp.aggent.model.AgentKeyValue;
import com.databuff.webapp.config.common.CommonResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author:TianMing
 * @date: 2021/7/13
 * @time: 20:13
 */
public interface AggentManagerService {

    JSONObject onlineInfo(HttpServletRequest request);

    List<AgentEntity> list(AgentSearch search);
    AgentEntity agentInfo(String id, String apiKey);
    List<JSONObject> timeDiffTop(String apiKey);

    List<JSONObject> versionSts(String apiKey);

    List<AgentEntity> listMoreInfo(List<AgentEntity> list);

    List<AgentKeyValue> uploadNumTop(HttpServletRequest request, AgentSearch search);

    List<AgentKeyValue> cupUsageTop(HttpServletRequest request, AgentSearch search);

    void upgradePackageUpload(MultipartFile file,int currentChunk,int totalChunks,String fileIdentifier, CommonResponse commonResponse, HttpServletRequest request);

    List<AgentPack> packList(String apiKey);

    int delPack(String apiKey, Integer id);

    CommonResponse modifyConfig(JSONObject param);

    CommonResponse submitUpPlan(JSONObject param);

    int delAgentUpdata( String id,String apiKey);

    JSONObject getUpdateProgress(String apiKey,Integer operation);

    String getDcSite();

    List<AgentLog> agentLogList(AgentLog search);

    int updateAgentLogNew(AgentLog agentLog);
    void readAgentLog(HttpServletResponse response,Integer id, Integer type, String apiKey);


}
