package com.databuff.webapp.aggent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.databuff.entity.MetaConfigEntity;
import com.databuff.entity.SaasDashboard;
import com.databuff.webapp.aggent.model.DatabuffPlugin;
import com.databuff.webapp.aggent.model.DatabuffPluginDashboard;
import com.databuff.webapp.aggent.model.DatabuffPluginDashboardDic;
import com.databuff.webapp.aggent.model.DatabuffPluginMetric;
import com.databuff.webapp.aggent.model.DfDashboard;
import com.databuff.webapp.aggent.model.PluginMetric;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @package com.databuff.webapp.aggent.mapper
 * @company: dacheng
 * @author: zlh
 * @createDate: 2021/7/26
 */
@Mapper
@Repository("webappPluginMapper")
public interface PluginMapper extends BaseMapper {
    /**
     * 查询所有插件
     * @return
     * @param databuffPlugin
     */
    List<DatabuffPlugin> queryPluginList(DatabuffPlugin databuffPlugin);

    /**
     * 基础设施插件市场展示的插件
     * @return
     */
    @Select("SELECT plugin_collector FROM `dc_databuff_plugin_dic` where base_show =1 ")
    List<String> queryBasePluginCollector();

    /**
     * 根据id修改插件检测数据时间
     * @param databuffPlugin
     */
    void updatePluginCheckDataTimeByApiKeyDicId(DatabuffPlugin databuffPlugin);

    /**
     * 删除仪表盘根据dashboardId
     * @param dashboardId
     */
    @Delete("delete from dashboard where id = #{dashboardId}")
    void delDashboardByDashboardId(@Param("dashboardId") long dashboardId);

    /**
     * 根据dashboard title查询仪表盘id
     * @param title
     * @param orgId
     * @return Integer
     */
    @Select("select id from dashboard where title = #{title} and org_id = #{orgId} and folder_id in ('2','3')")
    Long getDashboardIdByTitle(@Param("title") String title, @Param("orgId") long orgId);

    /**
     * 根据apiKey查找orgId
     * @param apiKey
     * @return
     */
    @Select("select id from `org` where `name`=#{apiKey}")
    Long getOrgIdByApiKey(@Param("apiKey") String apiKey);

    /**
     * 删除仪表盘版本
     * @param dashboardId
     */
    @Delete("delete from dashboard_version where dashboard_id = #{dashboardId}")
    void delDashboardVersionByDashboardId(long dashboardId);

    /**
     * 根据插件名称查询仪表盘模版sql
     * @param pluginName
     * @return
     */
    @Select("select plugin_dashboard,plugin_tags from dc_databuff_plugin_dashboard_dic where dashboard_name=#{pluginName}")
    DatabuffPluginDashboardDic getDashboardPlugDic(@Param("pluginName") String pluginName);


    /**
     * 保存仪表盘标签
     * @param dashboardId
     * @param tag
     */
    @Insert("insert into dashboard_tag(dashboard_id,term) values(#{dashboardId},#{tag})")
    void addDashboardTag(@Param("dashboardId") long dashboardId, @Param("tag") String tag);

    /**
     * 根据仪表盘id删除仪表盘标签
     * @param dashboardId
     */
    @Delete("delete from dashboard_tag where dashboard_id=#{dashboardId}")
    void delDashboardTagByDashId(@Param("dashboardId") long dashboardId);

    /**
     * 根据API key 插件id查询插件安装信息
     * @param databuffPlugin
     * @return
     */
    DatabuffPlugin getPluginByApiKeyPlugId(DatabuffPlugin databuffPlugin);

    /**
     * 插入插件安装状态
     * @param databuffPlugin
     */
    void addPlugStatus(DatabuffPlugin databuffPlugin);

    /**
     * 保存主机仪表盘到grafana dashboard
     * @param dashboard
     */
    void addPlugDashboard(SaasDashboard dashboard);

    /**
     * 查询仪表盘目录id
     * @param title
     * @param orgId
     * @return
     */
    @Select("select id from dashboard where title=#{title} and org_id=#{orgId}")
    Long getFolderId(@Param("title") String title, @Param("orgId") long orgId);

    /**
     * 查找数据源名称
     * @param orgId
     * @return
     */
    @Select("select `name` from data_source where org_id=#{orgId}")
    String getDataSourceByOrgId(@Param("orgId") long orgId);

    /**
     * 查询仪表盘uid
     * @param apiKey
     * @param title
     * @return
     */
    String getDashboardUId(@Param("apiKey") String apiKey, @Param("title") String title,@Param("folderId") Integer folderId );

    /**
     * 查询仪表盘uids
     * @param apiKey
     * @param folderId
     * @return
     */
    List<DfDashboard> getDashboardUIds(@Param("apiKey") String apiKey, @Param("folderId") Integer folderId );


    /**
     * 查询仪表盘data
     * @param apiKey
     * @param title
     * @return
     */
    String getDashboardData(@Param("apiKey") String apiKey, @Param("title") String title);

    /**
     * 根据插件dic id查找仪表盘字典
     * @param databuffPlugin
     * @return
     */
    @Select("select * from dc_databuff_plugin_dashboard_dic where plugin_dic_id = #{id}")
    List<DatabuffPluginDashboardDic> findPluginDashboardDicByPluginDicId(DatabuffPlugin databuffPlugin);

    /**
     * 保存插件安装的仪表盘id等信息
     * @param pluginDashboard
     */
    void savePlugDashboardInfo(DatabuffPluginDashboard pluginDashboard);

    /**
     * 根据插件字典id查询插件仪表盘信息
     * @param databuffPlugin
     * @return
     */
    @Select("select * from dc_databuff_plugin_dashboard where api_key=#{apiKey} and plugin_dic_id = #{id}")
    List<DatabuffPluginDashboard> findPlugDashboardIdByPluginDicId(DatabuffPlugin databuffPlugin);

    /**
     * 删除插件仪表盘信息
     * @param databuffPlugin
     */
    @Delete("delete from dc_databuff_plugin_dashboard where api_key=#{apiKey} and plugin_dic_id = #{id}")
    void delPlugDashboardIdByPluginDicId(DatabuffPlugin databuffPlugin);

    /**
     * 根据apiKey查询菜单路径
     * @param apiKey
     * @return
     */
    List<String> getMenuPathByApiKey(@Param("apiKey") String apiKey);

    /**
     * 根据用户查询菜单路径
     * @param account
     * @return
     */
    List<String> getMenuPathByUser(@Param("account") String account);

    /**
     * 获取该apiKey下的开启的插件指标
     * @param app
     * @param apiKey
     * @return
     */
    DatabuffPluginMetric getPluginOpenMetrics(@Param("app") String app,@Param("apiKey") String apiKey);

    List<DatabuffPluginMetric> getAllPluginOpenMetrics(@Param("apiKey") String apiKey);

    /**
     * 保存apiKey下插件开启指标
     * @param pluginMetric
     * @return
     */
    int savePluginOpenMetrics(DatabuffPluginMetric pluginMetric);


    List<PluginMetric> defaultOpenMetric(@Param("apiKey") String apiKey);

    List<PluginMetric> defaultOpenMetricByApp(@Param("apiKey") String apiKey,@Param("apps") List<String> apps);

    List<DatabuffPlugin> getFullPluginList();

    void updatePluginBaseShowByIds(@Param("ids") List<Long> ids, @Param("baseShow") Integer baseShow);

    void updatePluginIsInstallByIds(@Param("ids") List<Long> ids, @Param("isInstall") Integer isInstall);

    void removePluginMetricForward(@Param("apps") List<String> apps);

    MetaConfigEntity getAuthPluginsConfig(@Param("code") String code);
}
