package com.databuff.webapp.admin.operationlog.service.impl;

import com.databuff.dao.mysql.UserMapper;
import com.databuff.entity.User;
import com.databuff.webapp.admin.operationlog.mapper.SysLogMapper;
import com.databuff.webapp.admin.operationlog.model.SysLog;
import com.databuff.webapp.admin.operationlog.service.SysLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: louis
 * @Date: 2019/4/1 上午9:14
 */
@Service
public class SysLogServiceImpl implements SysLogService {
    @Autowired
    private SysLogMapper sysLogMapper;
    @Autowired
    UserMapper userMapper;

    @Override
    public int save(SysLog sysLog)
    {
        sysLog.setApiKey("**********");
        return sysLogMapper.saveSysLog(sysLog);
    }

    @Override
    public String getApiKeyByName(String account) {
        User userDto = userMapper.selectOne(account);
        return userDto.getCId();
    }
}
