package com.databuff.webapp.admin.operationlog.mapper;



import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Insert;

import org.apache.ibatis.annotations.Mapper;
import com.databuff.webapp.admin.operationlog.model.SysLog;
import org.springframework.stereotype.Repository;

/**
 * @Author: louis
 * @Date: 2019/4/1 下午3:09
 */

@Mapper
@Repository
public interface SysLogMapper extends BaseMapper {
    /**
     * 创建用户
     * @param syslog
     * @return
     */
    @Insert("INSERT INTO dc_syslog (id, account, create_time,args,action,class_name,ip,api_key) " +
            "VALUES (#{id}, #{account}, #{createTime},#{args},#{action},#{className},#{ip},#{apiKey})")
    int saveSysLog(SysLog syslog);
}
