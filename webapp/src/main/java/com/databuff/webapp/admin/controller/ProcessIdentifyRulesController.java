package com.databuff.webapp.admin.controller;

import com.databuff.webapp.apm.model.*;
import com.databuff.webapp.apm.service.ProcessIdentifyRulesService;
import com.databuff.webapp.config.common.CommonResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

import static com.databuff.metric.moredb.SQLParser.API_KEY;

@RestController
@RequestMapping("/processIdentifyRules")
@Api("进程识别规则")
@Slf4j
public class ProcessIdentifyRulesController {

    @Autowired
    private ProcessIdentifyRulesService processIdentifyRulesService;

    @ApiOperation(value = "新增一条识别规则")
    @PostMapping(value = "/insert")
    public CommonResponse insert(HttpServletRequest httpServletRequest, @RequestBody InsertProcessIdentifyRuleRequest request) throws Exception {
        CommonResponse commonResponse = new CommonResponse<>();
        String apiKey = (String) httpServletRequest.getAttribute(API_KEY);
        request.setApiKey(apiKey);

        processIdentifyRulesService.insertProcessIdentifyRules(request);

        return commonResponse;
    }

    @ApiOperation(value = "获取所有识别规则")
    @PostMapping(value = "/list")
    public CommonResponse list(HttpServletRequest httpServletRequest, @RequestBody ListProcessIdentifyRuleRequest request) throws Exception {
        CommonResponse commonResponse = new CommonResponse<>();
        String apiKey = (String) httpServletRequest.getAttribute(API_KEY);
        request.setApiKey(apiKey);

        ListProcessIdentifyRuleResponse response = processIdentifyRulesService.listProcessIdentifyRules(request);
        commonResponse.setData(response);

        return commonResponse;
    }

    @ApiOperation(value = "删除识别规则")
    @PostMapping(value = "/delete")
    public CommonResponse delete(HttpServletRequest httpServletRequest, @RequestBody DeleteProcessIdentifyRuleRequest request) throws Exception {
        CommonResponse commonResponse = new CommonResponse<>();
        String apiKey = (String) httpServletRequest.getAttribute(API_KEY);
        request.setApiKey(apiKey);

        if (request.getId() == null) {
            throw new RuntimeException("id不能为空");
        }

        processIdentifyRulesService.deleteProcessIdentifyRules(request);

        return commonResponse;
    }

    @ApiOperation(value = "启停识别规则")
    @PostMapping(value = "/updateStatus")
    public CommonResponse updateStatus(HttpServletRequest httpServletRequest, @RequestBody UpdateStatusProcessIdentifyRuleRequest request) throws Exception {
        CommonResponse commonResponse = new CommonResponse<>();
        String apiKey = (String) httpServletRequest.getAttribute(API_KEY);
        request.setApiKey(apiKey);

        if (request.getId() == null) {
            throw new RuntimeException("id不能为空");
        }

        processIdentifyRulesService.updateStatusProcessIdentifyRules(request);

        return commonResponse;
    }

    @ApiOperation(value = "编辑识别规则")
    @PostMapping(value = "/edit")
    public CommonResponse edit(HttpServletRequest httpServletRequest, @RequestBody EditProcessIdentifyRuleRequest request) throws Exception {
        CommonResponse commonResponse = new CommonResponse<>();
        String apiKey = (String) httpServletRequest.getAttribute(API_KEY);
        request.setApiKey(apiKey);

        if (request.getId() == null) {
            throw new RuntimeException("id不能为空");
        }

        processIdentifyRulesService.editProcessIdentifyRules(request);

        return commonResponse;
    }

}
