package com.databuff.webapp.admin.service;

import com.alibaba.fastjson.JSONObject;
import com.databuff.entity.GroupEntity;
import com.databuff.entity.GroupRuleEntity;
import com.databuff.entity.RoleGroupRelation;
import com.databuff.entity.domainObj.DomainManagerSearch;
import com.databuff.webapp.config.common.CommonResponse;

import java.util.List;

public interface GroupService {

    void getGroupAuth(CommonResponse commonResponse, JSONObject params) throws Exception;

    void updateGroupAuth(CommonResponse commonResponse, JSONObject params) throws Exception;
    List<GroupEntity> getGroupList(CommonResponse<List<GroupEntity>> commonResponse, JSONObject params) throws Exception;

    GroupEntity addGroup(CommonResponse<GroupEntity> commonResponse, JSONObject params) throws Exception;

    GroupEntity updateGroup(CommonResponse<Object> commonResponse, JSONObject params) throws Exception;

    void deleteGroupById(CommonResponse<Object> commonResponse, JSONObject params) throws Exception;

    List<GroupRuleEntity> getRuleList(CommonResponse<List<GroupRuleEntity>> commonResponse, JSONObject params) throws Exception;

    GroupRuleEntity addRule(CommonResponse<GroupRuleEntity> commonResponse, JSONObject params) throws Exception;

    GroupRuleEntity updateRule(CommonResponse<GroupRuleEntity> commonResponse, JSONObject params) throws Exception;

    void deleteRuleById(CommonResponse<GroupRuleEntity> commonResponse, JSONObject params) throws Exception;

    /**
     * 未分配管理域实体统计
     * @param apiKey
     * @return
     */
    CommonResponse unGroupObjsStat(String apiKey);
    CommonResponse unGroupObjsList(DomainManagerSearch search);

    /**
     * 获取管理域下的角色
     * @param apiKey
     * @param roleId
     */
    CommonResponse getRoleGroups(String apiKey, Integer roleId);

    /**
     * 绑定角色到管理域
     * @param relations
     * @param apiKey
     */
    CommonResponse bindRoleToGroup(List<RoleGroupRelation> relations,String apiKey);
    /**
     * 删除角色的管理域绑定
     * @param relation
     */
    CommonResponse unbindRoleFromGroup(RoleGroupRelation relation);
}
