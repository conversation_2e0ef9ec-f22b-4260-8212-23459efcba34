package com.databuff.webapp.admin.controller;

import com.alibaba.fastjson.JSONObject;
import com.databuff.webapp.admin.service.SingleLoginService;
import com.databuff.webapp.config.common.CommonResponse;
import com.databuff.webapp.config.common.ImcResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @author:TianMing
 * @date: 2022/2/18
 * @time: 11:51
 */
@Api(value = "单点登录对接")
@RestController
@Slf4j
@RequestMapping("/singleLogin")
public class SingleLoginController {


    @Autowired
    private SingleLoginService singleLoginService ;

    @ApiOperation(value = "IMC 帐号同步")
    @PostMapping(value = "/imc/callback")
    @ResponseBody
    public ImcResponse callback(@RequestBody JSONObject param) {
        log.info(param.toJSONString());

        ImcResponse imcResponse = singleLoginService.callback(param);

        return imcResponse;
    }



    @ApiOperation(value = "IMC 单点登录")
    @PostMapping(value = "/imc/authlogin")
    @ResponseBody
    public CommonResponse<Object> authlogin(@RequestBody JSONObject param) {
        CommonResponse<Object> imcResponse = singleLoginService.authlogin(param);
        log.info(param.toJSONString());
        return imcResponse;
    }

}
