package com.databuff.webapp.admin.service;

import com.databuff.webapp.admin.model.system.Audit;

import java.util.List;

/**
 * @package com.databuff.service
 * @company: dacheng
 * @author: zlh
 * @createDate: 2021/5/8
 */
public interface AuditService {
    /**
     * 审计查询
     * @param audit
     * @return
     */
    List<Audit> selectAudit(Audit audit);

    /**
     * 删除日志
     * @param audits
     */
    void delLogsByIds(List<Audit> audits);

    /**
     * 清空日志
     */
    void truncateLogs();

    /**
     * 批量查询日志
     * @param audit
     * @return
     */
    List<Audit> selectAuditByDate(Audit audit);
}
