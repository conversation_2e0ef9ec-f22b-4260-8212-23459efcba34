package com.databuff.webapp.admin.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.databuff.common.audit.AuditEntity;
import com.databuff.common.constants.Constant;
import com.databuff.common.exception.BusinessException;
import com.databuff.common.utils.DateUtils;
import com.databuff.common.utils.StringUtil;
import com.databuff.service.JedisService;
import com.databuff.webapp.admin.mapper.SystemMapper;
import com.databuff.webapp.admin.model.network.NetworkDto;
import com.databuff.webapp.admin.model.system.SysBaseDto;
import com.databuff.webapp.admin.service.SystemService;
import com.databuff.webapp.util.IniUtil;
import lombok.extern.slf4j.Slf4j;
import org.audit4j.core.annotation.DatabuffAudit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;

/**
 * @package: com.databuff.service.impl
 * @Description: 系统配置
 * @company: dacheng
 * @author: wangfuxin
 * @createDate: 2021-01-19
 **/
@Slf4j
@Service
public class SystemServiceImpl implements SystemService {

    @Autowired
    private SystemMapper systemMapper;
    @Resource
    private JedisService jedisService;

    /**
     * 网卡名称，网卡路径
     */
    private static Map<String, String> fileMap = new HashMap<>();

    /**
     * 网络配置文件路径
     */
    private static final String SYS_CONFIG_PATH = "/etc/sysconfig/network-scripts/";
//    private static final String SYS_CONFIG_PATH = "/Users/<USER>/Desktop/network";

    /**
     * 获取系统时间
     */
    @Override
    public String getDate() {
        return DateUtils.formatDateTime(new Date());
    }

    /**
     * 修改系统时间
     */
    @Override
    @DatabuffAudit(action = "修改", entityType = "系统设置")
    public void modifyDate(JSONObject jsonObject) {
        String date = jsonObject.getString("date");
        if (StringUtil.isBlank(date)) {
            throw new BusinessException("请填写时间");
        }
        try {
            // 修改时间
            String cmd = "date -s '" + jsonObject.getString("date") + "'";
            Runtime.getRuntime().exec(new String[]{"/bin/sh", "-c", cmd});

            Date dateStr = DateUtils.strToDate(date, null);
            Date currDate = DateUtils.strToDate("", DateUtils.getDate());
            // 根据修改的时间更新license的时间
            long endTime = systemMapper.getLicenseEndTime() - DateUtils.dateDiff(dateStr, currDate);
            systemMapper.updateLicenseEndTime(endTime);
            // 修改自动ntp设置
            systemMapper.updateSysBaseSetNtpAuto(0, new Date());
            AuditEntity.builder()
                    .name("系统时间")
                    .outcome("修改成功")
                    .add();
        } catch (Exception e) {
            AuditEntity.builder()
                    .name("系统时间")
                    .outcome("修改失败")
                    .add();
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 获取基础设置
     */
    @Override
    public SysBaseDto getSystemBase() {
        SysBaseDto sysBaseDto = systemMapper.getSysBase();
        if (sysBaseDto == null) {
            throw new BusinessException("未找到基础设置信息");
        }
        // 减去预留的30天
        sysBaseDto.setDeleteIntervalDays(sysBaseDto.getDeleteIntervalDays() - 30);
        return sysBaseDto;
    }

    /**
     * 设置ntp时间同步
     */
    @Override
    @DatabuffAudit(action = "修改", entityType = "系统设置")
    public void ntpServer(JSONObject jsonObject) throws IOException {
        String ntpServer = jsonObject.getString("ntpServer");
        Integer ntpAuto = jsonObject.getInteger("ntpAuto");
        if (StringUtil.isBlank(ntpServer)) {
            throw new BusinessException("ntp服务器地址");
        }
        if (!ping(ntpServer)) {
            throw new BusinessException("ntp服务器无法连接");
        }
        systemMapper.updateSysBase(ntpServer, ntpAuto, new Date());
        String cmd = "ntpdate -u " + ntpServer;
        try {
            Runtime.getRuntime().exec(new String[]{"/bin/sh", "-c", cmd});
            AuditEntity.builder()
                    .name("ntp服务器")
                    .outcome("修改成功")
                    .add();
        } catch (Exception e) {
            AuditEntity.builder()
                    .name("ntp服务器")
                    .outcome("修改失败")
                    .add();
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 设置页面超时
     */
    @Override
    @DatabuffAudit(action = "修改", entityType = "系统设置")
    public void updatePageTimeOut(JSONObject jsonObject) {
        Integer pageTimeOut = jsonObject.getInteger("pageTimeOut");
        //设置在线用户TTL时间
        jedisService.updateKeysTtl(Constant.PREFIX_SHIRO_REFRESH_TOKEN + "*", pageTimeOut);
        //设置页面过期时间缓存
        jedisService.setObject(Constant.REFRESH_TOKEN_EXPIRETIME, pageTimeOut);
        //保存设置
        systemMapper.updatePageTimeOut(pageTimeOut, new Date());
        AuditEntity.builder()
                .name("页面超时")
                .outcome("修改成功")
                .add();
    }

    /**
     * 设置数据储存
     */
    @Override
    @DatabuffAudit(action = "修改", entityType = "系统设置")
    public void setDataStored(JSONObject jsonObject) {
        Integer day = jsonObject.getInteger("day");
        Integer occupancyRate = jsonObject.getInteger("occupancyRate");
        if (day == null || day <= 0) {
            throw new BusinessException("请选择正确的时间");
        }
        if (occupancyRate == null || occupancyRate <= 0) {
            throw new BusinessException("请选择正确的占用比例");
        }
        // 多预留30天
        day = day + 30;
        systemMapper.updateDeleteIntervalDays(day, occupancyRate);
        AuditEntity.builder()
                .name("数据储存时间")
                .outcome("修改成功")
                .add();
    }


    /**
     * 获取网卡信息，网卡信息从文件中获取，描述从数据库获取
     */
    @Override
    public JSONArray netConfig() {
        List<String> configList = new ArrayList<>();
        File file = new File(SYS_CONFIG_PATH);
        if (!file.exists()) {
            return null;
        }
        File[] files = file.listFiles();
        if (null == files || files.length == 0) {
            return null;
        }
        // 获取所有网卡文件
        for (File netFile : files) {
            if (!netFile.isDirectory() && netFile.getAbsolutePath().contains("ifcfg-") && !netFile.getAbsolutePath().endsWith(".bak")) {
                log.info("netConfig文件:" + netFile.getAbsolutePath());
                configList.add(netFile.getAbsolutePath());
            }
        }
        updateNteWork(configList);

        List<NetworkDto> list = systemMapper.getNetwork();
        Map<String, String> map = new HashMap<>();
        for (NetworkDto networkDto : list) {
            map.put(networkDto.getDevice(), networkDto.getDescription());
        }
        // 整合数据
        JSONArray array = new JSONArray();
        for (String config : configList) {
            IniUtil ini = new IniUtil(config);
            JSONObject data = ini.getPropertyToJson();
            data.put("description", map.get(data.getString("DEVICE")));
            array.add(data);
        }
        return array;
    }

    /**
     * 更新网络配置数据
     *
     * @param configList 配置文件
     */
    private void updateNteWork(List<String> configList) {
        // 重新保存网卡文件
        fileMap = new HashMap<>();
        for (String config : configList) {
            IniUtil ini = new IniUtil(config);
            // 获取配置文件中的网卡名称
            String device = ini.getProperty("DEVICE");
            fileMap.put(device, config);
            // 添加数据库
            if (systemMapper.getOneNetwork(device) == null) {
                NetworkDto networkDto = new NetworkDto();
                networkDto.setDevice(device);
                networkDto.setDescription("这是网口-" + device);
                networkDto.setCreateTime(new Date());
                networkDto.setUpdateTime(new Date());
                systemMapper.insertNetwork(networkDto);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public synchronized String editNetConfigAndRestart(JSONObject jsonObject) {
        try {
            if (editNetConfig(jsonObject)) {
                Runtime.getRuntime().exec("service docker restart");
                log.info("docker服务开始重启");
            }
        } catch (Exception es) {
            throw new BusinessException(es.getMessage());
        }
        return "success";
    }

    /**
     * 编辑网卡配置
     */
    @Override
    public Boolean editNetConfig(JSONObject jsonObject) throws Exception {
        String device = jsonObject.getString("DEVICE");
        if (StringUtil.isBlank(device)) {
            throw new BusinessException("网卡名称为空");
        }
        String configFile = fileMap.get(device);
        if (StringUtil.isBlank(configFile)) {
            throw new BusinessException("未找到对应网卡");
        }
        IniUtil ini = new IniUtil(configFile);
        // 旧的ip地址
        String orignalIp = ini.getProperty("IPADDR");
        // ip地址
        String ipAddr = jsonObject.getString("IPADDR");
        if (StringUtil.isBlank(ipAddr)) {
            throw new BusinessException("没有指定ip地址，无法操作");
        }
        // 如果修改后的ip能被ping通
        if (ping(ipAddr)) {
            throw new BusinessException("ip[" + ipAddr + "]地址正在使用");
        }
        // 判断其他网卡文件是否使用此ip
        for (Map.Entry<String, String> entry : fileMap.entrySet()) {
            IniUtil in = new IniUtil(entry.getValue());
            String ip = in.getProperty("IPADDR");
            if (ipAddr.equals(ip) && !entry.getKey().equals(device)) {
                String inDevice = in.getProperty("DEVICE");
                throw new BusinessException("ip[" + ipAddr + "]地址已被[" + inDevice + "]使用");
            }
        }
        ini.setProperty("IPADDR", ipAddr);
        // 网关
        String gateway = jsonObject.getString("GATEWAY");
        // 掩码
        String netmask = jsonObject.getString("NETMASK");
        // 是否启用
        String onBoot = jsonObject.getString("ONBOOT");
        String dns1 = jsonObject.getString("DNS1");
        String dns2 = jsonObject.getString("DNS2");
        // 描述
        String description = jsonObject.getString("description");
        if (StringUtil.isNotBlank(gateway)) {
            ini.setProperty("GATEWAY", gateway);
        }
        if (StringUtil.isNotBlank(netmask)) {
            ini.setProperty("NETMASK", netmask);
        }
        if (StringUtil.isNotBlank(onBoot)) {
            if (onBoot.equals("yes") || onBoot.equals("no")) {
                ini.setProperty("ONBOOT", onBoot);
            }
        }
        if (StringUtil.isNotBlank(dns1)) {
            ini.setProperty("DNS1", dns1);
        }
        if (StringUtil.isNotBlank(dns2)) {
            ini.setProperty("DNS2", dns2);
        }
        if (StringUtil.isNotBlank(description)) {
            NetworkDto networkDto = new NetworkDto();
            networkDto.setDevice(jsonObject.getString("DEVICE"));
            networkDto.setUpdateTime(new Date());
            networkDto.setDescription(description);
            systemMapper.updateNetworkByDevice(networkDto);
        }
        if (!Objects.equals(orignalIp, ipAddr) && Objects.equals(onBoot, "yes") && ping(orignalIp)) {
            // ip修改，重启docker
            log.info("[" + orignalIp + "]->[" + ipAddr + "]，需要重启docker服务");
            Process p = Runtime.getRuntime().exec("service network restart");
            p.waitFor();
            return true;
        } else {
            Process p = Runtime.getRuntime().exec("service network restart");
            p.waitFor();
            log.info("网络服务重启完成");
            return false;
        }

    }

    /**
     * 判断主机地址能不能 ping 通，此处主机地址可以域名或者IP
     *
     * @param host 主机ip
     * @return true能；false不能
     */
    private boolean ping(String host) throws IOException {
        try {
            InetAddress inetAddress = InetAddress.getByName(host);
            return inetAddress.isReachable(5 * 1000);
        } catch (UnknownHostException e1) {
            return false;
        }
    }

}
