package com.databuff.webapp.resp.action.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.common.constants.CommonConstants;
import com.databuff.common.constants.Constant;
import com.databuff.common.exception.CustomException;
import com.databuff.common.utils.CsvUtils;
import com.databuff.common.utils.DateUtils;
import com.databuff.common.utils.StringUtil;
import com.databuff.dao.mysql.NotifyReceiveUserMapper;
import com.databuff.dao.mysql.RespPolicyMapper;
import com.databuff.entity.*;
import com.databuff.service.DomainManagerObjService;
import com.databuff.service.GroupFilterService;
import com.databuff.webapp.resp.action.service.NotifyReceiveUserService;
import com.databuff.webapp.resp.action.service.RespPolicyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.DataOutputStream;
import java.util.*;
import java.util.function.Function;

import static com.databuff.metric.moredb.SQLParser.WAY;

/**
 * @author:TianMing
 * @date: 2023/12/13
 * @time: 10:44
 */
@Service
@Slf4j
public class RespPolicyServiceImpl implements RespPolicyService {
    @Resource
    private RespPolicyMapper respPolicyMapper;
    @Autowired
    private NotifyReceiveUserService NotifyReceiveUserService;
    @Resource
    private NotifyReceiveUserMapper receiveUserMapper;

    @Autowired
    private DomainManagerObjService domainManagerObjService;

    @Autowired
    private GroupFilterService groupFilterService;

    public final static List<String> SORT_FIELD = Arrays.asList(new String[]{
            "CREATED_TIME","UPDATED_TIME"
    });

    @Override
    public RespPolicy selectOne(HttpServletRequest request, Integer id) {
        String apiKey = request.getAttribute("apiKey").toString();
        final QueryWrapper<RespPolicy> eq = new QueryWrapper<RespPolicy>()
                .eq("id", id)
                .eq("api_key", apiKey);
        final boolean allEntityPermission = domainManagerObjService.hasAllEntityPermission();

        if (allEntityPermission) {
            return respPolicyMapper.selectOne(eq);
        }
        final Collection<String> gids = domainManagerObjService.getGidFromThread();
        if (gids == null || gids.size() <= 0) {
            eq.isNull("gid");
        } else {
            eq.and(wrapper -> wrapper.isNull("gid").or().in("gid", gids));
        }
        return respPolicyMapper.selectOne(eq);
    }

    @Override
    public RespPolicy selectOne(QueryWrapper<RespPolicy> queryWrapper) {
        final boolean allEntityPermission = domainManagerObjService.hasAllEntityPermission();

        if (allEntityPermission) {
            return respPolicyMapper.selectOne(queryWrapper);
        }
        final Collection<String> gids = domainManagerObjService.getGidFromThread();
        if (gids == null || gids.size() <= 0) {
            queryWrapper.isNull("gid");
        } else {
            queryWrapper.and(wrapper -> wrapper.isNull("gid").or().in("gid", gids));
        }
        return respPolicyMapper.selectOne(queryWrapper);
    }

    @Override
    public Long selectCount(QueryWrapper<RespPolicy> queryWrapper) {
        final boolean allEntityPermission = domainManagerObjService.hasAllEntityPermission();

        if (allEntityPermission) {
            return respPolicyMapper.selectCount(queryWrapper);
        }
        final Collection<String> gids = domainManagerObjService.getGidFromThread();
        if (gids == null || gids.size() <= 0) {
            queryWrapper.isNull("gid");
        } else {
            queryWrapper.and(wrapper -> wrapper.isNull("gid").or().in("gid", gids));
        }
        return respPolicyMapper.selectCount(queryWrapper);
    }

    @Override
    public List<RespPolicyRet> pageAll(RespPolicyParams searchParams) {
        if (StringUtils.isNotBlank(searchParams.getSortOrder()) && !Constant.SORT_ORDER.contains(searchParams.getSortOrder().toUpperCase())){
            throw new CustomException("排序类型必须为DESC或ASC");
        }else{
            searchParams.setSortOrder(searchParams.getSortOrder().toUpperCase());
        }
        if (StringUtils.isNotBlank(searchParams.getSortField()) && !SORT_FIELD.contains(StringUtil.camelToUnderscore(searchParams.getSortField()).toUpperCase())){
            throw new CustomException("该字段不在排序字段中");
        }else{
            searchParams.setSortField(StringUtil.camelToUnderscore(searchParams.getSortField()).toUpperCase());
        }
        final boolean allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        searchParams.setAllEntityPermission(allEntityPermission);
        searchParams.setDomainManagerStatus(domainManagerObjService.getDomainManagerStatusOpen());
        if (!allEntityPermission) {
            final Collection<String> gids = domainManagerObjService.getGidFromThread();
            searchParams.setGids(gids);
        }
        return respPolicyMapper.pageAllGid(searchParams);
    }

    @Override
    public boolean saveBatch(Collection<RespPolicy> entityList, int batchSize) {
        return false;
    }

    @Override
    public boolean saveOrUpdateBatch(Collection<RespPolicy> entityList, int batchSize) {
        return false;
    }

    @Override
    public boolean updateBatchById(Collection<RespPolicy> entityList, int batchSize) {
        return false;
    }

    @Override
    public boolean saveOrUpdate(RespPolicy entity) {
        return false;
    }

    @Override
    public RespPolicy getOne(Wrapper<RespPolicy> queryWrapper, boolean throwEx) {
        return respPolicyMapper.selectOne(queryWrapper);
    }

    @Override
    public Map<String, Object> getMap(Wrapper<RespPolicy> queryWrapper) {
        return null;
    }

    @Override
    public <V> V getObj(Wrapper<RespPolicy> queryWrapper, Function<? super Object, V> mapper) {
        return null;
    }

    @Override
    public BaseMapper<RespPolicy> getBaseMapper() {
        return respPolicyMapper;
    }

    @Override
    public Class<RespPolicy> getEntityClass() {
        return null;
    }


    @Override
    public void exportRespPolicyRecord(RespPolicyParams searchParams, HttpServletResponse response) {
        List<RespPolicyRet> itemList = this.pageAll(searchParams);
        // 创建输出流以及待下载的文件名称
        response.reset();
        // 导出文件列头
        List<String> titles = Arrays.asList("响应策略ID","响应策略名称","策略状态","筛选条件","响应条件","响应动作类型","响应动作","创建时间","更新时间","创建者","更新者");
        List<List<String>> rets = new ArrayList<>();
        for (RespPolicyRet policy : itemList){
            List<String> list = new ArrayList<>();
            list.add(policy.getId().toString());
            list.add(policy.getPolicyName());
            list.add(policy.getEnabled() ? CommonConstants.ACTION_ENABLE : CommonConstants.ACTION_DISABLE);
            String filterConditions = policy.getFilterConditions()==null?"":policy.getFilterConditions().toString();
            filterConditions = "\"" + filterConditions.replace("\"", "\"\"") + "\"";
            list.add(filterConditions);
            String respConditions = policy.getRespConditions()==null?"":policy.getRespConditions().toString();
            respConditions = "\"" + respConditions.replace("\"", "\"\"") + "\"";
            list.add(respConditions);
            list.add(policy.getActionType()==1?"通知":"数据外发");
            String respActions = policy.getRespActions()==null?"":policy.getRespActions().toString();
            respActions = "\"" + respActions.replace("\"", "\"\"") + "\"";
            list.add(respActions);
            list.add(DateUtils.formatDateTime(policy.getCreatedTime()));
            list.add(DateUtils.formatDateTime(policy.getUpdatedTime()));
            list.add(policy.getCreator());
            list.add(policy.getEditor());
            rets.add(list);
        }
        // 创建流对象
        try (
                DataOutputStream os = new DataOutputStream(response.getOutputStream())
        ) {
            CsvUtils.exportCsv(titles, rets,"ResponsePolicyRecords.csv",response);
            // 刷新流
            os.flush();
        } catch (Exception e) {
            log.error("exportTestRecord error {}", e);
        }

    }

    @Override
    public void fillWechatDingtalkUidByPhone(RespPolicy respPolicy) {
        JSONArray respActions = respPolicy.getRespActions();
        List<Integer> uidList = new ArrayList<>();
        for (int i = 0; i < respActions.size(); i++) {
            JSONObject respAction = respActions.getJSONObject(i);
            String way = respAction.getString(WAY);
            if (Constant.Notify.DINGTALK.equals(way)||Constant.Notify.WECHAT.equals(way)){
                if (respAction.getJSONArray("uids")==null||respAction.getIntValue("isSingle")==0){
                    continue;
                }
                List<Integer> uids = respAction.getJSONArray("uids").toJavaList(Integer.class);
                uidList.addAll(uids);
            }
        }
        ReceiveUserParams searchParams = new ReceiveUserParams();
        searchParams.setApiKey(respPolicy.getApiKey());
        searchParams.setIds(uidList);
        List<RcvUser> rcvUsers = receiveUserMapper.pageAll(searchParams);
        rcvUsers.forEach(r-> NotifyReceiveUserService.fillWechatDingtalkUidByPhone(r));
    }

    /**
     * @param respPolicy
     */
    @Override
    public void insert(RespPolicy respPolicy) {
        if (respPolicy == null) {
            return;
        }
        if (respPolicy.getGid() != null) {
            respPolicyMapper.insert(respPolicy);
            return;
        }

        final boolean allEntityPermission = domainManagerObjService.hasAllEntityPermission() || !DomainManagerObjService.hasAnnotate();
        Collection<String> gids = domainManagerObjService.getGidFromThread(respPolicy.getGid());
        if (allEntityPermission || CollectionUtils.isEmpty(gids)) {
            gids = domainManagerObjService.getEnabledGids();
            final Boolean domainManagerStatus = domainManagerObjService.getDomainManagerStatusOpen();
            ;
            if (!CollectionUtils.isEmpty(gids) && domainManagerStatus) {
                for (String gid : gids) {
                    respPolicy.setId(null);
                    respPolicy.setGid(gid);
                    respPolicyMapper.insert(respPolicy);
                }
            } else {
                respPolicy.setId(null);
                respPolicy.setGid(null);
                respPolicyMapper.insert(respPolicy);
            }
        } else {
            for (String gid : gids) {
                respPolicy.setId(null);
                respPolicy.setGid(gid);
                respPolicyMapper.insert(respPolicy);
            }
        }
    }

    /**
     * @param id
     * @return
     */
    @Override
    public RespPolicy selectById(Integer id) {
        final boolean allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        if (allEntityPermission) {
            return respPolicyMapper.selectById(id);
        }
        final Collection<String> gids = domainManagerObjService.getGidFromThread();
        final QueryWrapper<RespPolicy> queryWrapper;
        if (gids == null || gids.size() <= 0) {
            queryWrapper = new QueryWrapper<RespPolicy>().eq("id", id).isNull("gid");
        } else {
            queryWrapper = new QueryWrapper<RespPolicy>()
                    .eq("id", id)
                    .and(i -> i.isNull("gid").or().in("gid", gids));
        }
        return respPolicyMapper.selectOne(queryWrapper);
    }

    /**
     * @param ids
     * @param apiKey
     * @return
     */
    @Override
    public Object deleteBatchIds(List<Integer> ids, String apiKey) {
        return respPolicyMapper.deleteBatchIds(ids, apiKey);
    }

    /**
     * 根据gid批量删除响应策略
     *
     * @param gid 响应策略的gid
     * @return 删除的记录数
     */
    @Override
    public int deleteBatchByGid(String gid) {
        return respPolicyMapper.deleteBatchByGid(gid);
    }

    /**
     * 查询由Admin创建的响应策略
     *
     * @return 返回由Admin创建的响应策略列表
     */
    @Override
    public List<RespPolicyRet> findByCreatorAdmin() {
        return respPolicyMapper.findByCreatorAdmin();
    }
}
