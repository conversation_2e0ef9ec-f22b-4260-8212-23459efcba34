package com.databuff.webapp.resp.action.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.databuff.entity.RespPolicy;
import com.databuff.entity.RespPolicyParams;
import com.databuff.entity.RespPolicyRet;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author:TianMing
 * @date: 2023/12/13
 * @time: 9:35
 */
public interface RespPolicyService extends IService<RespPolicy> {

    RespPolicy selectOne(HttpServletRequest request, Integer id);

    RespPolicy selectOne(QueryWrapper<RespPolicy> queryWrapper);

    Long selectCount(QueryWrapper<RespPolicy> queryWrapper);

    List<RespPolicyRet> pageAll(RespPolicyParams searchParams);

    void exportRespPolicyRecord(RespPolicyParams searchParams, HttpServletResponse response);

    void fillWechatDingtalkUidByPhone(RespPolicy respPolicy);

    void insert(RespPolicy respPolicy);

    RespPolicy selectById(Integer id);

    Object deleteBatchIds(List<Integer> ids, String apiKey);

    /**
     * 根据gid批量删除响应策略
     *
     * @param gid 响应策略的gid
     * @return 删除的记录数
     */
    int deleteBatchByGid(String gid);

    /**
     * 查询由Admin创建的响应策略
     *
     * @return 返回由Admin创建的响应策略列表
     */
    List<RespPolicyRet> findByCreatorAdmin();
}
