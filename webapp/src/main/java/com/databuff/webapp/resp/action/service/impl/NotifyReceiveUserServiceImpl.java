package com.databuff.webapp.resp.action.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.service.NotifyService;
import com.databuff.common.constants.Constant;
import com.databuff.common.utils.dingtalk.DingTalkUtils;
import com.databuff.common.utils.dingtalk.NotifyDingTalkConfig;
import com.databuff.common.utils.wechat.NotifyWeChatConfig;
import com.databuff.common.utils.wechat.WeChatUtils;
import com.databuff.dao.mysql.NotifyReceiveUserMapper;
import com.databuff.entity.RcvUser;
import com.databuff.service.JedisService;
import com.databuff.webapp.resp.action.service.NotifyReceiveUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

/**
 * @author:TianMing
 * @date: 2023/12/13
 * @time: 10:44
 */
@Service
@Slf4j
public class NotifyReceiveUserServiceImpl implements NotifyReceiveUserService {
    @Resource
    private NotifyReceiveUserMapper receiveUserMapper;
    @Resource
    private NotifyService notifyService;
    @Resource
    private JedisService jedisService;
    @Override
    public RcvUser selectOne(HttpServletRequest request, Integer id) {
        String apiKey = request.getAttribute("apiKey").toString();
        RcvUser rcvUser = receiveUserMapper.selectOne(new QueryWrapper<RcvUser>()
                .eq("id",id)
                .eq("api_key",apiKey));
        return rcvUser;
    }

    @Override
    public RcvUser selectOneByName(HttpServletRequest request, String name) {
        String apiKey = request.getAttribute("apiKey").toString();
        RcvUser rcvUser = receiveUserMapper.selectOne(new QueryWrapper<RcvUser>()
                .eq("rcv_name", name)
                .eq("api_key", apiKey));
        return rcvUser;
    }

    @Override
    public RcvUser fillWechatDingtalkUidByPhone(String apiKey,Integer id) {
        RcvUser rcvUser = receiveUserMapper.selectOne(new QueryWrapper<RcvUser>()
                .eq("id",id)
                .eq("api_key",apiKey));
        if (rcvUser==null){
            return null ;
        }
        return this.fillWechatDingtalkUidByPhone(rcvUser);
    }

    @Override
    @Async
    public RcvUser fillWechatDingtalkUidByPhone(RcvUser rcvUser) {

        if (StringUtils.isBlank(rcvUser.getPhone())||(StringUtils.isNotBlank(rcvUser.getWechatUid())&&StringUtils.isNotBlank(rcvUser.getDingtalkUid()))){
            return rcvUser;
        }
        String apiKey = rcvUser.getApiKey();
        String dingTalkUserId = "";
        try{
            NotifyDingTalkConfig dingTalkConfig = notifyService.getDingTalkConfig(apiKey, Constant.Notify.DINGTALK);
            if (dingTalkConfig.getEnable() != 0 && dingTalkConfig.getTenantEnable() != 0) {
                if (StringUtils.isBlank(dingTalkConfig.getAppkey())||StringUtils.isBlank(dingTalkConfig.getAppsecret())||dingTalkConfig.getDingAgentId()==null){
                    log.warn("钉钉通知Appkey,Appsecret,agentid存在空值！");
                    return rcvUser;
                }
                String md5Hex = DigestUtils.md5Hex(dingTalkConfig.getAppkey()+"_"+dingTalkConfig.getAppsecret());
                String redisKey = "notify:dingTalk:"+apiKey + ":" + md5Hex;
                String token = jedisService.getJson(redisKey);
                if (StringUtils.isBlank(token)){
                    token = DingTalkUtils.getAccessToken(dingTalkConfig.getAppkey(), dingTalkConfig.getAppsecret());
                    int expiresIn = 7200;
                    //access_token的有效期为7200秒（2小时），有效期内重复获取会返回相同结果并自动续期，过期后获取会返回新的access_token。
                    //这里将过去时间减去200秒避免这边没过期，钉钉服务器已经过期了。
                    jedisService.setJson(redisKey,token,expiresIn>200?(expiresIn-200):1);
                }
                dingTalkUserId = DingTalkUtils.getUserIdByMobile(token,rcvUser.getPhone());
            }
        }catch (Exception e){
            log.warn("通知填充钉钉用户id错误：{}",e.getMessage());
        }
        String weChatUserId = "";
        try{
            NotifyWeChatConfig weChatConfig = notifyService.getWeChatConfig(apiKey, Constant.Notify.WECHAT);
            if (weChatConfig.getEnable() != 0 && weChatConfig.getTenantEnable() != 0) {
                if (StringUtils.isBlank(weChatConfig.getCorpid())||StringUtils.isBlank(weChatConfig.getCorpsecret())||weChatConfig.getWechatAgentId()==null){
                    log.warn("企业微信通知Corpid,Corpsecret,agentid存在空值！");
                    return rcvUser;
                }
                String md5Hex = DigestUtils.md5Hex(weChatConfig.getCorpid()+"_"+weChatConfig.getCorpsecret());
                String redisKey = "notify:wechat:"+apiKey + ":" + md5Hex;
                String token = jedisService.getJson(redisKey);
                if (StringUtils.isBlank(token)){
                    token = WeChatUtils.getToken(weChatConfig.getCorpid(), weChatConfig.getCorpsecret());
                    int expiresIn = 7200;
                    //access_token的有效期为7200秒（2小时），有效期内重复获取会返回相同结果并自动续期，过期后获取会返回新的access_token。
                    //这里将过去时间减去200秒避免这边没过期，钉钉服务器已经过期了。
                    jedisService.setJson(redisKey,token,expiresIn>200?(expiresIn-200):1);
                }
                JSONObject userIdInfo = WeChatUtils.getUserIdByMobile(token, rcvUser.getPhone());
                weChatUserId = userIdInfo.getString("userid");
            }
        }catch (Exception e){
            log.warn("通知填充微信用户id错误：{}",e.getMessage());
        }
        if (StringUtils.isBlank(dingTalkUserId) && StringUtils.isBlank(weChatUserId)){
            return rcvUser ;
        }
        if (StringUtils.isNotBlank(dingTalkUserId)){
            rcvUser.setDingtalkUid(dingTalkUserId);
        }
        if (StringUtils.isNotBlank(weChatUserId)){
            rcvUser.setWechatUid(weChatUserId);
        }
        receiveUserMapper.updateById(rcvUser);
        return rcvUser;
    }

    @Override
    public String fillWechatDingtalkUidByPhoneAndType(String apiKey, Integer id, String type) {
        RcvUser rcvUser = receiveUserMapper.selectOne(new QueryWrapper<RcvUser>()
                .eq("id",id)
                .eq("api_key",apiKey));
        if (rcvUser == null){
            return "绑定失败";
        }
        if (StringUtils.isBlank( rcvUser.getPhone() ) ){
            return "请先添加手机号码";
        }
        if ( StringUtils.isNotBlank( rcvUser.getWechatUid() ) && StringUtils.isNotBlank( rcvUser.getDingtalkUid() ) ) {
            return "已绑定";
        }
        String dingTalkUserId = "";
        if (Objects.equals(type, "dingTalk")) {
            try{
                NotifyDingTalkConfig dingTalkConfig = notifyService.getDingTalkConfig(apiKey, Constant.Notify.DINGTALK);
                if (dingTalkConfig.getEnable() != 0 && dingTalkConfig.getTenantEnable() != 0) {
                    if (StringUtils.isBlank(dingTalkConfig.getAppkey()) || StringUtils.isBlank(dingTalkConfig.getAppsecret()) || dingTalkConfig.getDingAgentId() == null){
                        log.warn("钉钉通知Appkey, Appsecret, agentid存在空值！");
                        return "钉钉通知配置异常，请联系管理员";
                    }
                    String md5Hex = DigestUtils.md5Hex(dingTalkConfig.getAppkey() + "_" + dingTalkConfig.getAppsecret());
                    String redisKey = "notify:dingTalk:" + apiKey + ":" + md5Hex;
                    String token = jedisService.getJson(redisKey);
                    if (StringUtils.isBlank(token)){
                        token = DingTalkUtils.getAccessToken(dingTalkConfig.getAppkey(), dingTalkConfig.getAppsecret());
                        int expiresIn = 7200;
                        //access_token的有效期为7200秒（2小时），有效期内重复获取会返回相同结果并自动续期，过期后获取会返回新的access_token。
                        //这里将过去时间减去200秒避免这边没过期，钉钉服务器已经过期了。
                        jedisService.setJson(redisKey,token,expiresIn>200?(expiresIn-200):1);
                    }
                    dingTalkUserId = DingTalkUtils.getUserIdByMobile(token,rcvUser.getPhone());
                } else {
                    return "请先开启平台钉钉通知功能";
                }
            }catch (Exception e){
                log.warn("通知填充钉钉用户id错误：{}",e.getMessage());
                return "绑定钉钉用户失败,"+e.getMessage().split(",")[0];
            }
        }
        String weChatUserId = "";
        if (Objects.equals(type, "wechat")) {
            try{
                NotifyWeChatConfig weChatConfig = notifyService.getWeChatConfig(apiKey, Constant.Notify.WECHAT);
                if (weChatConfig.getEnable() != 0 && weChatConfig.getTenantEnable() != 0) {
                    if (StringUtils.isBlank(weChatConfig.getCorpid())||StringUtils.isBlank(weChatConfig.getCorpsecret())||weChatConfig.getWechatAgentId()==null){
                        log.warn("企业微信通知Corpid,Corpsecret,agentid存在空值！");
                        return "企业微信通知配置异常，请联系管理员";
                    }
                    String md5Hex = DigestUtils.md5Hex(weChatConfig.getCorpid() + "_" + weChatConfig.getCorpsecret());
                    String redisKey = "notify:wechat:"+apiKey + ":" + md5Hex;
                    String token = jedisService.getJson(redisKey);
                    if (StringUtils.isBlank(token)){
                        token = WeChatUtils.getToken(weChatConfig.getCorpid(), weChatConfig.getCorpsecret());
                        int expiresIn = 7200;
                        //access_token的有效期为7200秒（2小时），有效期内重复获取会返回相同结果并自动续期，过期后获取会返回新的access_token。
                        //这里将过去时间减去200秒避免这边没过期，钉钉服务器已经过期了。
                        jedisService.setJson(redisKey,token, expiresIn>200?(expiresIn-200):1);
                    }
                    JSONObject userIdInfo = WeChatUtils.getUserIdByMobile(token, rcvUser.getPhone());
                    weChatUserId = userIdInfo.getString("userid");
                } else {
                    return "请先开启平台企业微信通知功能";
                }
            }catch (Exception e){
                log.warn("通知填充微信用户id错误：{}",e.getMessage());
                return "绑定企业微信用户失败,"+e.getMessage().split(",")[0];
            }
        }
        if ( (Objects.equals(type, "dingTalk") && StringUtils.isBlank(dingTalkUserId)) || (Objects.equals(type, "wechat") && StringUtils.isBlank(weChatUserId))){
            return "绑定失败";
        }
        if (StringUtils.isNotBlank(dingTalkUserId)){
            rcvUser.setDingtalkUid(dingTalkUserId);
        }
        if (StringUtils.isNotBlank(weChatUserId)){
            rcvUser.setWechatUid(weChatUserId);
        }
        receiveUserMapper.updateById(rcvUser);
        return "SUCCESS";
    }


    @Override
    public boolean saveBatch(Collection<RcvUser> entityList, int batchSize) {
        return false;
    }

    @Override
    public boolean saveOrUpdateBatch(Collection<RcvUser> entityList, int batchSize) {
        return false;
    }

    @Override
    public boolean updateBatchById(Collection<RcvUser> entityList, int batchSize) {
        return false;
    }

    @Override
    public boolean saveOrUpdate(RcvUser entity) {
        return false;
    }

    @Override
    public RcvUser getOne(Wrapper<RcvUser> queryWrapper, boolean throwEx) {
        return null;
    }

    @Override
    public Map<String, Object> getMap(Wrapper<RcvUser> queryWrapper) {
        return null;
    }

    @Override
    public <V> V getObj(Wrapper<RcvUser> queryWrapper, Function<? super Object, V> mapper) {
        return null;
    }

    @Override
    public BaseMapper<RcvUser> getBaseMapper() {
        return null;
    }

    @Override
    public Class<RcvUser> getEntityClass() {
        return null;
    }
}
