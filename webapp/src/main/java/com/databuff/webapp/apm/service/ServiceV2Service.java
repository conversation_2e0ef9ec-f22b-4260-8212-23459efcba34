package com.databuff.webapp.apm.service;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.tsdb.model.TSDBSeries;
import com.databuff.entity.ServiceInstanceEntity;
import com.databuff.entity.TraceServiceAndInstance;
import com.databuff.entity.TraceServiceEntity;
import com.databuff.entity.extend.MetricSearch;
import com.databuff.entity.extend.ServiceSearch;
import com.databuff.webapp.config.common.CommonResponse;
import com.databuff.webapp.util.KeyValue;

import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author:TianMing
 * @date: 2021/7/4
 * @time: 11:03
 */
public interface ServiceV2Service {

    JSONObject list(ServiceSearch search) throws Exception;

    JSONObject dbList(ServiceSearch search) throws Exception;

    JSONObject mqList(ServiceSearch search) throws Exception;

    JSONObject cacheList(ServiceSearch search) throws Exception;
    JSONObject remoteCallList(ServiceSearch search) throws Exception;

    JSONObject componentList(ServiceSearch search, String measurement) throws Exception;


    /**
     * 服务端点列表
     *
     * @param search
     * @return
     */
    JSONObject endpoints(ServiceSearch search);

    Map<String, Set<String>> resources(ServiceSearch search);

    Map<String, List<Map<String,String>>> resources2(ServiceSearch search);

    /**
     * 慢sql分布top
     *
     * @param search
     * @return
     */
    JSONObject slowSqlTops(ServiceSearch search);


    /**
     * 编辑服务tag
     *
     * @param info
     * @return
     */
    CommonResponse customServiceTag(JSONObject info, CommonResponse commonResponse);

    /**
     * 服务详情三大指标趋势图
     *
     * @param search
     * @param apiKey
     * @return
     */
    Object serviceDetailTrendChart(JSONObject search, String apiKey) throws ParseException, Exception;

    Object businessDetailTrendChart(JSONObject search, String apiKey) throws ParseException, Exception;

    /**
     * 延迟分布图
     *
     * @param search
     * @return
     */
    JSONObject distributionStats(ServiceSearch search);

    JSONObject upDownCallEndpoints(ServiceSearch search);

    JSONObject upDownCallInfo(ServiceSearch search);

    JSONObject callGraphStats(ServiceSearch search, Set<String> graphStats);

    Map<String, List<String>> poolGetNames(ServiceSearch search);

    JSONObject callMqDelayGraphStats(ServiceSearch search);

    /**
     * 资源分析调用使用，此页面分析的是针对资源span 层面的查询
     * <p>
     * 1.请求和错误数量 2.错误数 3.延迟百分比 50、75、90、95、99、max百分位
     *
     * @param search
     * @return
     */
    JSONObject graphStats(ServiceSearch search);

    /**
     * 获取服务基本信息列表
     *
     * @param search
     * @return
     */
    List<JSONObject> queryServices(JSONObject search);

    /**
     * 只获取服务信息列表
     *
     * @param search
     * @return
     */
    List<TraceServiceEntity> queryBasicServices(ServiceSearch search);
    List<TraceServiceEntity> basicAllServices(ServiceSearch search);

    /**
     * 获取单个服务基础信息（包含权限是否有权限）
     *
     * @param search
     * @return
     */
    JSONObject serviceInfo(ServiceSearch search);

    JSONObject resourceInfo(ServiceSearch search);

    JSONObject getServiceInstanceRelations( String serviceId, String serviceInstance, String resource, String componentType, long start, long end, String apiKey);

    JSONObject getServiceResourceRelations( String serviceId, String resource, String componentType, long start, long end, String apiKey);

    List<JSONObject> queryServices(ServiceSearch search);

    Boolean updateService(JSONObject updateJson);

    Map<String, String> getServiceIdNameMap(String apiKey, List<String> serviceIds);

    Map<String, KeyValue> getListServiceHealthStatus(List<String> servicesId, String toTime, String apiKey);

    List<ServiceInstanceEntity> getServiceInstance(String apiKey,String serviceId, String serviceInstance, long fromTimeMs, long toTimeMs);

    Map<String, List<ServiceInstanceEntity>> getServiceInstanceMap(Set<String> serviceIds, long fromTimeMs, long toTimeMs);

    TraceServiceAndInstance getBasicServiceInstance(String serviceId, String serviceInstance, long fromTimeMs, long toTimeMs);

    List<ServiceInstanceEntity> getServiceInstanceByContainerId(String apiKey, String containerId, long fromTimeMs, long toTimeMs);

    Set<String> getK8sNamespaceList(JSONObject search);

    /**
     * 取服务所属的ComponentTypes
     *
     * @param search
     * @return {@link List }<{@link String }>
     */
    List<String> getComponentTypes(ServiceSearch search);

    Map<String, List<ServiceInstanceEntity>> batchGetServiceInstance1(Set<String> serviceIds, long from, long to);

    List<TSDBSeries> metricStats(MetricSearch search);

    List<TSDBSeries> resourceStats(ServiceSearch search);
}
