package com.databuff.webapp.apm.service.impl.business.system;

import com.databuff.common.constants.Constant;
import com.databuff.common.constants.TSDBIndex;
import com.databuff.common.tsdb.TSDBOperateUtil;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.Aggregation;
import com.databuff.common.tsdb.model.TSDBResultSet;
import com.databuff.common.tsdb.model.Where;
import com.databuff.common.utils.DateUtils;
import com.databuff.dao.mysql.BusinessMapper;
import com.databuff.metric.dto.MetricByGroupGraph;
import com.databuff.webapp.apm.model.BusinessResourceUsageRequest;
import com.databuff.webapp.apm.model.BusinessResourceUsageType;
import com.sun.istack.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.databuff.common.constants.Constant.Metric.DEFAULT_FIELD;

@Service
@Slf4j
public class BusinessSystemResourceService {
    @Autowired
    private BusinessMapper businessMapper;

    @Autowired
    private BusinessSystemCommonService businessSystemCommonService;

    @Autowired
    private TSDBOperateUtil tsdbOperateUtil;

    @Autowired
    private BusinessSystemPermissionService businessSystemPermissionService;

    /**
     * 当前业务系统关联的主机 容器 的CPU使用率与内存使用率
     *
     * @param search
     * @return {@link List }<{@link MetricByGroupGraph }>
     * @throws Exception
     */
    public List<MetricByGroupGraph> getResourceUsage(BusinessResourceUsageRequest search) throws Exception {

        businessSystemPermissionService.validateBusinessSystemPermission(search.getBizId());

        List<MetricByGroupGraph> metricByGroupGraphs = new ArrayList<>();
        List<String> ids = new ArrayList<>();
        String measurement = "";
        String field = "";
        String idTag = "";

        switch (search.getUsageType()) {
            case host:
                Set<String> allServiceIdsBySysId = businessMapper.getAllServiceIdsBySysId(search.getBizId());
                if (!allServiceIdsBySysId.isEmpty()) {
                    String serviceIdIn = joinWithSingleQuotes(allServiceIdsBySysId.stream());

                    QueryBuilder queryBuilder = new QueryBuilder()
                            .setDatabaseName(search.getApiKey() + "_" + TSDBIndex.TSDB_APM_METRIC_DATABASE_NAME)
                            .setMeasurement(TSDBIndex.TSDB_METRIC_SERVICE_INSTANCE_TABLE_NAME)
                            .setInterval(search.getInterval())
                            .addWhere(Where.neq("hostname", ""))
                            .addWhere(Where.in("serviceId", allServiceIdsBySysId))
                            .addWhere(Where.gte("time", DateUtils.tsdbTimeToMilliseconds(search.getFromTime())))
                            .addWhere(Where.lt("time", DateUtils.tsdbTimeToMilliseconds(search.getToTime())))
                            .addGroupBy("hostname")
                            .addAgg(Aggregation.of(" sum(" + DEFAULT_FIELD + ")", DEFAULT_FIELD));

                    TSDBResultSet resultSet = tsdbOperateUtil.executeQuery(queryBuilder);

                    if (resultSet == null || resultSet.getResults() == null || resultSet.getResults().isEmpty() ||
                            resultSet.getResults().get(0) == null || resultSet.getResults().get(0).getSeries() == null ||
                            resultSet.getResults().get(0).getSeries().isEmpty()) {
                        return metricByGroupGraphs;
                    }

                    resultSet.getResults().get(0).getSeries().forEach(series -> {
                        ids.add(series.getTags().get("hostname"));
                    });
                }
                idTag = "host";
                break;
            case container:
                Set<String> dockerServiceIds = businessMapper.getAllServiceIdsBySysId(search.getBizId());
                if (!dockerServiceIds.isEmpty()) {
                    QueryBuilder queryBuilder = new QueryBuilder()
                            .setDatabaseName(search.getApiKey() + "_" + TSDBIndex.TSDB_APM_METRIC_DATABASE_NAME)
                            .setMeasurement(TSDBIndex.TSDB_METRIC_SERVICE_INSTANCE_TABLE_NAME)
                            .setInterval(search.getInterval())
                            .addWhere(Where.neq("containerId", ""))
                            .addWhere(Where.in("serviceId", dockerServiceIds))
                            .addWhere(Where.gte("time", DateUtils.tsdbTimeToMilliseconds(search.getFromTime())))
                            .addWhere(Where.lt("time", DateUtils.tsdbTimeToMilliseconds(search.getToTime())))
                            .addGroupBy("containerId")
                            .addAgg(Aggregation.of(" sum(" + DEFAULT_FIELD + ")", DEFAULT_FIELD));

                    TSDBResultSet resultSet = tsdbOperateUtil.executeQuery(queryBuilder);

                    if (resultSet == null || resultSet.getResults() == null || resultSet.getResults().isEmpty() ||
                            resultSet.getResults().get(0) == null || resultSet.getResults().get(0).getSeries() == null ||
                            resultSet.getResults().get(0).getSeries().isEmpty()) {
                        return metricByGroupGraphs;
                    }

                    resultSet.getResults().get(0).getSeries().forEach(series -> {
                        ids.add(series.getTags().get("containerId"));
                    });
                }
                idTag = "container_id";
                break;
            default:
                throw new IllegalArgumentException("不支持的UsageType: " + search.getUsageType());
        }

        if (ids.isEmpty()) {
            return metricByGroupGraphs;
        }

        // system.cpu system.mem要 host
        switch (search.getUtilType()) {
            case cpu:
                measurement = search.getUsageType() == BusinessResourceUsageType.host ? "system.cpu" : "docker.cpu";
                field = "usage";
                break;
            case memory:
                measurement = search.getUsageType() == BusinessResourceUsageType.host ? "system.mem" : "docker.mem";
                field = search.getUsageType() == BusinessResourceUsageType.host ? "usage" : "in_use";
                break;
            default:
                throw new IllegalArgumentException("不支持的UtilType: " + search.getUtilType());
        }

        QueryBuilder queryBuilder;
        switch (search.getType()) {
            case avg:
                queryBuilder = new QueryBuilder()
                        .setDatabaseName(search.getApiKey() + "_" + Constant.Metric.DATABASE_INFRASTRUCTURE)
                        .setMeasurement(measurement)
                        .setInterval(search.getInterval())
                        .addWhere(Where.in(idTag, ids))
                        .addWhere(Where.gte("time", DateUtils.tsdbTimeToMilliseconds(search.getFromTime())))
                        .addWhere(Where.lt("time", DateUtils.tsdbTimeToMilliseconds(search.getToTime())))
                        .addAgg(Aggregation.of(" sum(" + field + ")", field));
                break;
            case top:
                List<String> topIds = getTop10IdsByMetric(search, measurement, field, idTag, ids);
                if (topIds.isEmpty()) {
                    return metricByGroupGraphs;
                }
                queryBuilder = new QueryBuilder()
                        .setDatabaseName(search.getApiKey() + "_" + Constant.Metric.DATABASE_INFRASTRUCTURE)
                        .setMeasurement(measurement)
                        .setInterval(search.getInterval())
                        .addWhere(Where.in(idTag, topIds))
                        .addWhere(Where.gte("time", DateUtils.tsdbTimeToMilliseconds(search.getFromTime())))
                        .addWhere(Where.lt("time", DateUtils.tsdbTimeToMilliseconds(search.getToTime())))
                        .addGroupBy(idTag)
                        .addAgg(Aggregation.of(" sum(" + field + ")", field));
                break;
            default:
                throw new IllegalArgumentException("不支持的Type: " + search.getType());
        }

        TSDBResultSet resultSet = tsdbOperateUtil.executeQuery(queryBuilder);
        metricByGroupGraphs = businessSystemCommonService.convertToMetricByGroupGraph(resultSet, search.getUtilType().name().toLowerCase());

        return metricByGroupGraphs;
    }

    private static @NotNull String joinWithSingleQuotes(Stream<String> allServiceIdsBySysId) {
        return allServiceIdsBySysId
                .map(s -> "'" + s + "'")
                .collect(Collectors.joining(","));
    }


    private List<String> getTop10IdsByMetric(BusinessResourceUsageRequest search, String measurement, String field, String idTag, List<String> ids) throws Exception {
        QueryBuilder queryBuilder = new QueryBuilder()
                .setDatabaseName(search.getApiKey() + "_" + Constant.Metric.DATABASE_INFRASTRUCTURE)
                .setMeasurement(measurement)
                .addWhere(Where.in(idTag, ids))
                .addWhere(Where.gte("time", DateUtils.tsdbTimeToMilliseconds(search.getFromTime())))
                .addWhere(Where.lt("time", DateUtils.tsdbTimeToMilliseconds(search.getToTime())))
                .addGroupBy(idTag)
                .addAgg(Aggregation.of(" sum(" + field + ")", field))
                .setLimit(10);

        // 查询并排序取前10
        TSDBResultSet resultSet = tsdbOperateUtil.executeQuery(queryBuilder);

        if (resultSet == null || resultSet.getResults() == null || resultSet.getResults().isEmpty() ||
                resultSet.getResults().get(0) == null || resultSet.getResults().get(0).getSeries() == null) {
            return new ArrayList<>();
        }

        return resultSet.getResults().get(0).getSeries().stream()
                .map(series -> series.getTags().get(idTag))
                .collect(Collectors.toList());
    }
}
