package com.databuff.webapp.apm.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public abstract class BusinessServiceCallBaseRequest {

    private String apiKey;

    @ApiModelProperty(value = "开始时间", example = "2021-07-15 00:00:00")
    private String fromTime;

    @ApiModelProperty(value = "结束时间", example = "2021-07-15 23:59:59")
    private String toTime;

    @ApiModelProperty(value = "来源服务ID")
    private String srcServiceId;

    @ApiModelProperty(value = "目标服务ID")
    private String dstServiceId;

    @ApiModelProperty(value = "来源业务ID", example = "1")
    private String srcBizId;

    @ApiModelProperty(value = "目标业务ID", example = "1")
    private String dstBizId;

    @ApiModelProperty(value = "搜索发起端服务")
    private String srcServiceQuery;
    @ApiModelProperty(value = "搜索接收端服务")
    private String dstServiceQuery;

}
