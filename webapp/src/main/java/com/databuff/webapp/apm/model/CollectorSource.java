package com.databuff.webapp.apm.model;

import com.alibaba.fastjson.JSONArray;
import lombok.Data;

import javax.persistence.Column;

@Data
public class CollectorSource {

    private Long id;

    private Integer index;

    private String name;

    private String resource;

    private String description;

    private String serviceId;

    private String serviceName;

    private Integer propertySource;

    private String propertyName;

    private Integer getValueMethod;

    private boolean state;

    /**
     * @since 2.9.0
     * java方法描述列表
     */
    @Column(name = "java_methods", columnDefinition = "JSON COMMENT 'Java方法描述列表'")
    private JSONArray javaMethods;

    @Column(name = "source_key", columnDefinition = "VARCHAR(255) COMMENT '参数表达式'")
    private String sourceKey;

}