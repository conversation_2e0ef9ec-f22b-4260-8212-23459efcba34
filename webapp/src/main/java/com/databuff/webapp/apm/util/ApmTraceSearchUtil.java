package com.databuff.webapp.apm.util;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.constants.Constant;
import com.databuff.common.constants.TSDBIndex;
import com.databuff.common.tsdb.TSDBOperateUtil;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.*;
import com.databuff.common.utils.PercentageLatencyUtil;
import com.databuff.common.utils.SqlUtil;
import com.databuff.entity.extend.ServiceSearch;
import com.databuff.webapp.apm.model.ServiceEdge;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.databuff.common.constants.Constant.Trace.SERVICE_ID;
import static com.databuff.common.constants.MetricConstant.*;
import static com.databuff.common.constants.TSDBIndex.*;

/**
 * @author:TianMing
 * @date: 2021/10/12
 * @time: 13:38
 */
@Slf4j
@Component
public class ApmTraceSearchUtil {
    @Autowired
    private TSDBOperateUtil tsdbOperateUtil;
    // 数据库类型
    @Value("${tsdb.db:moredb}")
    private String db;

    /**
     * 获取span数据的延迟百分比
     *
     * @return
     */
    public void percentageLatencyForData(String apiKey, JSONObject data, List<Where> wheres, List<String> groupBys, String tsdbTable, int... percents) {
        Map<String, Double> latency = percentageLatency(apiKey, wheres, groupBys, tsdbTable, percents);
        for (Map.Entry<String, Double> entry : latency.entrySet()) {
            data.put(entry.getKey(), entry.getValue());
        }
    }

    /**
     * 从TSDB桶中近似获取数据的延迟百分比
     *
     * @return
     */
    public Map<String, Double> percentageLatency(String apiKey, List<Where> wheres, List<String> groupBys, String tsdbTable, int... percents) {
        Map<String, Double> ret = new HashMap<>(6);
        QueryBuilder queryBuilder = new QueryBuilder()
                .setDatabaseName(apiKey, TSDBIndex.TSDB_APM_METRIC_DATABASE_NAME)
                .setMeasurement(tsdbTable)
                .addAllWhere(wheres)
                .addAllGroupBy(groupBys);
        for (int item : percents) {
            queryBuilder.addAgg(Aggregation.of(AggFun.PERCENTILE, item + "", "p" + item + "Latency"));
        }


        try {
            TSDBResultSet resultSet = tsdbOperateUtil.apmPercentageLatency(queryBuilder);

//            TSDBResultSet resultSet = tsdbOperateUtil.executeQuery(queryBuilder);
            List<TSDBResult> rets = resultSet.getResults();
            List<TSDBSeries> seriesList = rets.get(0).getSeries();
            if (seriesList == null) {
                return ret;
            }
            seriesList.forEach(s -> s.getValues().forEach(v -> {
                        for (int i = 1; i < s.getColumns().size(); i++) {
                            try {
                                ret.put(s.getColumns().get(i), ((Number) v.get(i)).doubleValue());
                            } catch (Throwable e) {
                                log.error("percentageLatency error", e);
                            }
                        }
                    })
            );
        } catch (Exception e) {
            log.error("查询ApmTraceSearchUtil percentageLatency异常", e);
        }
        return ret;
    }

    /**
     * 拼接tsdb 服务指标查询 Builder
     *
     * @param services
     * @param search
     * @param fromTimeMs
     * @param toTimeMs
     * @param isManualSortPage
     * @return
     */
    public QueryBuilder getServiceTSDBBuilder(List<JSONObject> services, String groupBy, ServiceSearch search, long fromTimeMs, long toTimeMs, Boolean isManualSortPage, String measurement) {
        QueryBuilder queryBuilder = new QueryBuilder()
                .setDatabaseName(search.getApiKey(), TSDBIndex.TSDB_APM_METRIC_DATABASE_NAME)
                .setMeasurement(measurement)
                .addAgg(Aggregation.of(AggFun.SUM, SERVICE_CALLS, "callCnt"));

        if (search.getShowFields().contains("reqRate") || "reqRate".equals(search.getSortField())) {
            long timeInterval = toTimeMs / 1000 - fromTimeMs / 1000;
            queryBuilder.addAgg(Aggregation.of("sum(" + SERVICE_CALLS + ") / " + timeInterval, "reqRate"));
        }
        if (search.getShowFields().contains("errCnt") || "errCnt".equals(search.getSortField())) {
            queryBuilder.addAgg(Aggregation.of(AggFun.SUM, SERVICE_ERR_CNT, "errCnt"));
        }
        if (search.getShowFields().contains("errRate") || "errRate".equals(search.getSortField())) {
            queryBuilder.addAgg(Aggregation.of("sum(" + SERVICE_ERR_CNT + ") / sum(" + SERVICE_CALLS + ")", "errRate"));
        }
        if (search.getShowFields().contains("avgLatency") || "avgLatency".equals(search.getSortField())) {
            queryBuilder.addAgg(Aggregation.of("sum(" + SERVICE_SUM_RESP_TIME + ") / sum(" + SERVICE_CALLS + ")", "avgLatency"));
        }
        if (search.getShowFields().contains("slowPercent") || "slowPercent".equals(search.getSortField())) {
            queryBuilder.addAgg(Aggregation.of("sum(" + COMPONENT_SLOW_CALLS + ") / sum(" + SERVICE_CALLS + ") * 100", "slowPercent"));
        }
        if (search.getShowFields().contains("slowCnt") || "slowCnt".equals(search.getSortField())) {
            queryBuilder.addAgg(Aggregation.of(AggFun.SUM, COMPONENT_SLOW_CALLS, "slowCnt"));
        }
        if (search.getShowFields().contains("apdex") || "apdex".equals(search.getSortField())) {
            queryBuilder.addAgg(Aggregation.of(AggFun.AVG, SERVICE_APDEX, "apdex"));
        }
        if ((search.getShowFields().contains("p50Latency") || "p50Latency".equals(search.getSortField())) && db.equals(Constant.TS_DB.MOREDB)) {
            queryBuilder.addAgg(Aggregation.of(AggFun.PERCENTILE, "50", "p50Latency"));
        }
        if ((search.getShowFields().contains("p75Latency") || "p75Latency".equals(search.getSortField())) && db.equals(Constant.TS_DB.MOREDB)) {
            queryBuilder.addAgg(Aggregation.of(AggFun.PERCENTILE, "75", "p75Latency"));
        }
        if ((search.getShowFields().contains("p90Latency") || "p90Latency".equals(search.getSortField())) && db.equals(Constant.TS_DB.MOREDB)) {
            queryBuilder.addAgg(Aggregation.of(AggFun.PERCENTILE, "90", "p90Latency"));
        }
        if ((search.getShowFields().contains("p95Latency") || "p95Latency".equals(search.getSortField())) && db.equals(Constant.TS_DB.MOREDB)) {
            queryBuilder.addAgg(Aggregation.of(AggFun.PERCENTILE, "95", "p95Latency"));
        }
        if ((search.getShowFields().contains("p99Latency") || "p99Latency".equals(search.getSortField())) && db.equals(Constant.TS_DB.MOREDB)) {
            queryBuilder.addAgg(Aggregation.of(AggFun.PERCENTILE, "99", "p99Latency"));
        }
        if ((search.getShowFields().contains("p100Latency") || "p100Latency".equals(search.getSortField())) && db.equals(Constant.TS_DB.MOREDB)) {
            queryBuilder.addAgg(Aggregation.of(AggFun.PERCENTILE, "100", "p100Latency"));
        }
        if (search.getShowFields().contains("allPLatency") && db.equals(Constant.TS_DB.MOREDB)) {
            //只有moredb的才可以直接用sql查百分位
            queryBuilder.addAgg(Aggregation.of(AggFun.PERCENTILE, "50", "p50Latency"))
                    .addAgg(Aggregation.of(AggFun.PERCENTILE, "75", "p75Latency"))
                    .addAgg(Aggregation.of(AggFun.PERCENTILE, "90", "p90Latency"))
                    .addAgg(Aggregation.of(AggFun.PERCENTILE, "95", "p95Latency"))
                    .addAgg(Aggregation.of(AggFun.PERCENTILE, "99", "p99Latency"))
                    .addAgg(Aggregation.of(AggFun.PERCENTILE, "100", "p100Latency"));
        }
        List<String> serviceIds = services.stream().map(s -> s.getString(SERVICE_ID)).collect(Collectors.toList());
        queryBuilder.addWhere(Where.in(groupBy, serviceIds));
        SqlUtil.whereAndGroupByTime(queryBuilder, fromTimeMs, toTimeMs, groupBy, null);
        if (isManualSortPage != null && !isManualSortPage) {
            //排序
            getSortOrderBuilder(queryBuilder, search);
        }
        return queryBuilder;
    }


    /**
     * TSDB返回结果转换
     *
     * @param rets
     * @param fieldName
     * @return
     */
    public Map<String, JSONObject> getTSDbServiceRetMap(List<TSDBResult> rets, String fieldName) {
        Map<String, JSONObject> serviceTSDBRetMap = new LinkedHashMap<>();
        if (rets == null || rets.size() == 0) {
            return serviceTSDBRetMap;
        }
        List<TSDBSeries> seriesList = rets.get(0).getSeries();
        if (seriesList == null) {
            return serviceTSDBRetMap;
        }
        seriesList.forEach(s -> s.getValues().forEach(v -> {
                JSONObject tsdbRet = new JSONObject();
                for (int i = 0; i < s.getColumns().size(); i++) {
                    tsdbRet.put(s.getColumns().get(i), v.get(i));
                }
                if (s.getTags() != null) {
                    tsdbRet.putAll(s.getTags());
                }
                String serviceId = tsdbRet.get(fieldName).toString();
                    serviceTSDBRetMap.put(serviceId, tsdbRet);
                })
        );
        return serviceTSDBRetMap;
    }

    /**
     * 填充服务请求次数
     *
     * @param serviceMap
     * @param search
     */
    public void addServiceCallCnt(Map<String, JSONObject> serviceMap, ServiceSearch search) {
        List<String> serviceIds = new ArrayList<>(serviceMap.keySet());
        QueryBuilder queryBuilder = new QueryBuilder()
                .setDatabaseName(search.getApiKey(), TSDBIndex.TSDB_APM_METRIC_DATABASE_NAME)
                .setMeasurement(TSDB_METRIC_SERVICE_TABLE_NAME)
                .addAgg(Aggregation.of(AggFun.SUM, SERVICE_CALLS, "callCnt"))
                .addWhere(Where.in("serviceId", serviceIds))
                .addWhere(Where.eq("apikey", search.getApiKey()));
        SqlUtil.whereAndGroupByTime(queryBuilder, search.getFromTimeVul(), search.getToTimeVul(), "serviceId", null);
        try {
            TSDBResultSet resultSet = tsdbOperateUtil.executeQuery(queryBuilder);
            List<TSDBResult> rets = resultSet.getResults();
            List<TSDBSeries> seriesList = rets.get(0).getSeries();
            if (seriesList != null) {
                seriesList.forEach(s -> {
                    String serviceId = s.getTags().get(SERVICE_ID);
                    Double callCnt = Double.parseDouble(s.getValues().get(0).get(1).toString());
                    JSONObject service = serviceMap.get(serviceId);
                    service.put("callCnt", callCnt);
                });
            }
        } catch (Exception e) {
            log.error("addServiceCallCnt search error{}", e.getMessage());
        }
    }


    /**
     * 从TSDB桶中近似获取数据的延迟百分比
     *
     * @return
     */
    public Map<Long, Long> percentageLatency(String apiKey, List<Where> wheres, List<String> groupBys, String tsdbTable, int size) {
        Map<Long, Long> ret = new LinkedHashMap<>(6);
        QueryBuilder queryBuilder = new QueryBuilder()
                .setDatabaseName(apiKey + "_" + TSDBIndex.TSDB_APM_METRIC_DATABASE_NAME)
                .setMeasurement(tsdbTable);

        String percentileB = "histogramField";
        List<Aggregation> aggregations = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            aggregations.add(Aggregation.of(AggFun.SUM, percentileB + i, "percentileB" + i));
        }
        queryBuilder.addAllAgg(aggregations);
        queryBuilder.addAllWhere(wheres);
        queryBuilder.addAllGroupBy(groupBys);
        try {
            TSDBResultSet resultSet = tsdbOperateUtil.executeQuery(queryBuilder);
            List<TSDBResult> rets = resultSet.getResults();
            List<TSDBSeries> seriesList = rets.get(0).getSeries();
            if (seriesList == null) {
                return ret;
            }
            seriesList.forEach(s -> s.getValues().forEach(v -> {
                        for (int i = 0; i < s.getColumns().size(); i++) {
                            String column = s.getColumns().get(i);
                            if (!column.startsWith("percentileB")) {
                                continue;
                            }
                            Integer index = Integer.valueOf(column.split("percentileB")[1]);
                            long[] latencyInterval = PercentageLatencyUtil.getIndexTimeInterval(index);
                            if (v.get(i) == null) {
                                ret.put(latencyInterval[0] * 1000, 0L);
                                continue;
                            }
                            Number num = (Number) v.get(i);
                            Long val = num == null ? 0 : num.longValue();
                            ret.put(latencyInterval[0] * 1000, val);
                        }
                    })
            );
        } catch (Exception e) {
            log.error("查询ApmTraceSearchUtil percentageLatency异常", e);
        }
        return ret;
    }

    /**
     * 传入耗时，查询TSDB 返回百分比
     *
     * @param total
     * @param maxDuration ns
     * @param duration    ns
     * @param apiKey
     * @param wheres
     * @param groupBys
     * @param tsdbTable
     * @return
     */
    public Long percentileByDuration(Long total, Long maxDuration, Long duration, String apiKey, List<Where> wheres, List<String> groupBys, String tsdbTable) {
        byte i = PercentageLatencyUtil.computeMicrosIndex(duration / 1000);
        //查询当前耗时属于第几个桶
        int index = i & 0xFF;
        //返回当前桶的区间范围
        long[] timeInterval = PercentageLatencyUtil.getIndexTimeInterval(index);
        //转换ns
        long latencyIntervalStart = timeInterval[0] * 1000;
        long latencyIntervalEnd = timeInterval[1] * 1000;
        if (index == 110 || latencyIntervalEnd > maxDuration) {
            //如果在最后一个桶则最大值为最大延迟 , 或者桶的结束时间大于最大延迟，则桶的结束时间为最大延迟
            //转换ns
            latencyIntervalEnd = maxDuration;
        }
        Map<Long, Long> percentageMap = this.percentageLatency(apiKey, wheres, groupBys, tsdbTable, index + 1);

        //把percentageMap按key的从小到大排序
        Map<Long, Long> sortedMap = percentageMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
        long c2 = sortedMap.get(sortedMap.keySet().toArray()[sortedMap.size() - 1]);
        long countIndex = (long) Math.ceil((double) (duration - latencyIntervalStart) / (latencyIntervalEnd - latencyIntervalStart) * c2);
        if (sortedMap.size() > 1) {
            //求最后两个的密度
            long c1 = sortedMap.get(sortedMap.keySet().toArray()[sortedMap.size() - 2]);
            long[] time1Interval = PercentageLatencyUtil.getIndexTimeInterval(index - 1);
            double z1 = c1 / (time1Interval[1] * 1000 - time1Interval[0] * 1000);

            double z2 = c2 / (latencyIntervalEnd - latencyIntervalStart);

            //按占比计算百分位数的延迟 相等取平均值，密度变大乘系数1.2，密度变小乘系数0.8
            if (z1 != 0 && z2 > z1) {
                //向上取整
                countIndex = (long) Math.ceil(countIndex * 1.2);
            } else if (z1 != 0 && z2 < z1) {
                countIndex = (long) Math.ceil(countIndex * 0.8);
            }
        }
        //将sortedMap的value求和
        long sum = sortedMap.values().stream().mapToLong(Long::longValue).sum();
        long thisDurationCount = sum - (c2 - countIndex);
        Double thisPercentile = Math.floor((double) thisDurationCount / total * 100);

        return thisPercentile.longValue();

    }


    public QueryBuilder getSortOrderBuilder(QueryBuilder queryBuilder, ServiceSearch search) {
        //排序分页
        String sortField = search.getSortField();
        String sortOrder = search.getSortOrder();
        Integer offset = search.getOffset();
        Integer size = search.getSize();
        if (StringUtils.isNotBlank(sortField) && StringUtils.isNotBlank(sortOrder)) {
            queryBuilder.addOrderBy(new OrderBy(sortField, "ASC".equals(sortOrder.toUpperCase())));
        }
        if (offset != null && size != null) {
            queryBuilder.setLimit(size);
            queryBuilder.setOffset(offset);
        }
        return queryBuilder;
    }

    public Double getApmTSDBResultSingle(QueryBuilder queryBuilder) {
        Double ret = 0.0;
        try {
            List<TSDBResult> rets = tsdbOperateUtil.executeQuery(queryBuilder).getResults();
            if (rets == null || rets.size() == 0) {
                return ret;
            }
            List<TSDBSeries> seriesList = rets.get(0).getSeries();
            if (seriesList == null || seriesList.size() == 0) {
                return ret;
            }
            ret = ((Number) seriesList.get(0).getValues().get(0).get(1)).doubleValue();
            return ret;
        } catch (Exception e) {
            log.error("getApmTSDBResultSingle error", e);
        }
        return ret;
    }

    /**
     * 获取服务关系结果
     *
     * @param queryBuilder
     * @return
     */
    public List<JSONObject> queryServiceEdges(QueryBuilder queryBuilder) {
        List<JSONObject> result = new ArrayList<>();
        try {
            TSDBResultSet resultSet = tsdbOperateUtil.executeQuery(queryBuilder);
            List<TSDBResult> rets = resultSet.getResults();
            if (rets == null || rets.size() == 0) {
                return result;
            }
            List<TSDBSeries> seriesList = rets.get(0).getSeries();
            if (seriesList == null) {
                return result;
            }
            seriesList.forEach(s -> s.getValues().forEach(v -> {
                        JSONObject tsdbRet = new JSONObject();
                        for (int i = 0; i < s.getColumns().size(); i++) {
                            tsdbRet.put(s.getColumns().get(i), v.get(i));
                        }
                        if (s.getTags() != null) {
                            tsdbRet.putAll(s.getTags());
                        }
                        result.add(tsdbRet);
                    })
            );
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return result;
    }

    /**
     * 获取服务关系结果
     *
     * @return
     */
    public List<ServiceEdge> allServiceEdges(QueryBuilder queryBuilder) {
        List<ServiceEdge> result = new ArrayList<>();
        try {
            List<TSDBResult> rets = tsdbOperateUtil.executeQuery(queryBuilder).getResults();
            if (rets == null || rets.size() == 0) {
                return result;
            }
            List<TSDBSeries> seriesList = rets.get(0).getSeries();
            if (seriesList == null) {
                return result;
            }
            for (TSDBSeries s : seriesList) {
                s.getValues().forEach(v -> {
                    ServiceEdge moreDBRet = new ServiceEdge();
                    moreDBRet.setTime((Long) v.get(s.getColumnIndex("time")));
                    moreDBRet.setErrCnt(((Number) v.get(s.getColumnIndex("errCnt"))).longValue());
                    moreDBRet.setAllCnt(((Number) v.get(s.getColumnIndex("allCnt"))).longValue());
                    moreDBRet.setAvgLatency(((Number) v.get(s.getColumnIndex("avgLatency"))).doubleValue());
                    moreDBRet.setErrRate(((Number) v.get(s.getColumnIndex("errRate"))).doubleValue());
                    moreDBRet.setAllTime(((Number) v.get(s.getColumnIndex("allTime"))).longValue());
                    Map<String, String> tags = s.getTags();
                    moreDBRet.setServiceId(tags.get("serviceId"));
                    moreDBRet.setSrcServiceId(tags.get("srcServiceId"));
                    result.add(moreDBRet);
                });
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return result;
    }

    /**
     * tsdb 时间趋势图结果转换
     *
     * @return
     */
    public Map<String, Map<Long, Double>> getTSDBServiceTrendChartGroupRetMap(QueryBuilder queryBuilder, String byName) {
        Map<String, Map<Long, Double>> map = new LinkedHashMap<>(16);
        try {
            List<TSDBResult> rets = tsdbOperateUtil.executeQuery(queryBuilder).getResults();
            List<TSDBSeries> series = rets.get(0).getSeries();
            if (series == null) {
                return map;
            }
            for (TSDBSeries serie : series) {
                Map<Long, Double> map1 = new LinkedHashMap<>(16);
                String errorType = serie.getTags().get(byName);
                serie.getValues().stream().filter(Objects::nonNull).forEach(v -> {
                    Number value = (Number) v.get(1);
                    Double target = value == null ? null : value.doubleValue();
                    map1.put(((Number) v.get(0)).longValue(), target);
                });
                map.put(errorType, map1);
            }
        } catch (Exception e) {
            log.error("TSDB 结果 趋势图查询异常", e);
            return new HashMap<>(0);
        }
        return map;
    }


    /**
     * tsdb 时间趋势图结果转换
     *
     * @return
     */
    public Map<Long, Double> getTSDBServiceTrendChartRetMap(QueryBuilder queryBuilder) {
        Map<Long, Double> map = new LinkedHashMap<>(16);
        try {
            List<TSDBResult> rets = tsdbOperateUtil.executeQuery(queryBuilder).getResults();
            List<TSDBSeries> series = rets.get(0).getSeries();
            if (series == null || series.isEmpty()) {
                return map;
            }
            series.get(0).getValues().stream().filter(Objects::nonNull).forEach(v -> {
                Number value = (Number) v.get(1);
                Double target = value == null ? null : value.doubleValue();
                map.put(((Number) v.get(0)).longValue(), target);
            });
        } catch (Exception e) {
            log.error("tsdb 结果 趋势图查询异常", e);
            return new HashMap<>(0);
        }
        return map;
    }


    /**
     * 拼接tsdb 服务资源数量查询builder
     *
     * @param search
     * @param fromTimeMs
     * @param toTimeMs
     * @return
     */
    public QueryBuilder getResourceCountTsdbBuilder(ServiceSearch search, long fromTimeMs, long toTimeMs, String measurements) {
        QueryBuilder queryBuilder = new QueryBuilder()
                .setDatabaseName(search.getApiKey() + "_" + TSDBIndex.TSDB_APM_METRIC_DATABASE_NAME)
                .setMeasurement(measurements)
                .addAgg(Aggregation.of(AggFun.SUM, COMPONENT_CALLS, "callCnt"))
                .addAllWhere(getResourceWhereBuilder(search, fromTimeMs, toTimeMs))
                .addAllGroupBy(getResourceGroupByBuilder(measurements));
        return queryBuilder;
    }

    public QueryBuilder getReqContributorBuilder(List<String> serviceIds, ServiceSearch search, long fromTimeMs, long toTimeMs, String measurements) {
        //查来源服务，需要把服务id置为null
        search.setServiceId(null);
        search.setServiceIds(null);
        List<Where> wheres = getResourceWhereBuilder(search, fromTimeMs, toTimeMs);

        if (serviceIds != null && serviceIds.size() > 0) {
            wheres.add(Where.in("serviceId", serviceIds));
        }
        QueryBuilder queryBuilder = new QueryBuilder()
                .setDatabaseName(search.getApiKey() + "_" + TSDBIndex.TSDB_APM_METRIC_DATABASE_NAME)
                .setMeasurement(measurements)
                .addAgg(Aggregation.of(AggFun.SUM, COMPONENT_CALLS, "callCnt"))
                .addAllWhere(wheres)
                .addWhere(Where.neq("srcServiceId", ""))
                .addAllGroupBy(Arrays.asList("srcServiceId", "srcService"));
        //排序分页
        return getSortOrderBuilder(queryBuilder, search);
    }

    public QueryBuilder getReqContributorCountBuilder(List<String> serviceIds, ServiceSearch search, long fromTimeMs, long toTimeMs, String measurements) {
        //查来源服务，需要把服务id置为null
        search.setServiceId(null);
        search.setServiceIds(null);
        List<Where> wheres = getResourceWhereBuilder(search, fromTimeMs, toTimeMs);

        if (serviceIds != null && serviceIds.size() > 0) {
            wheres.add(Where.in("serviceId", serviceIds));
        }
        QueryBuilder queryBuilder = new QueryBuilder()
                .setDatabaseName(search.getApiKey() + "_" + TSDBIndex.TSDB_APM_METRIC_DATABASE_NAME)
                .setMeasurement(measurements)
                .addAgg(Aggregation.of(AggFun.SUM, COMPONENT_CALLS, "callCnt"))
                .addAllWhere(wheres)
                .addWhere(Where.neq("srcServiceId", ""))
                .addAllGroupBy(Arrays.asList("srcServiceId", "srcService"));
        return queryBuilder;
    }

    /**
     * 拼接tsdb Builder 服务资源指标查询sql
     *
     * @param search
     * @param fromTimeMs
     * @param toTimeMs
     * @param isManualSortPage
     * @return
     */
    public QueryBuilder getResourceTsdbBuilder(ServiceSearch search, long fromTimeMs, long toTimeMs, boolean isManualSortPage, String measurements) {

        List<Where> wheres = getResourceWhereBuilder(search, fromTimeMs, toTimeMs);
        List<String> groupBys = getResourceGroupByBuilder(measurements);
        QueryBuilder queryBuilder = new QueryBuilder()
                .setDatabaseName(search.getApiKey() + "_" + TSDBIndex.TSDB_APM_METRIC_DATABASE_NAME)
                .setMeasurement(measurements)
                .addAgg(Aggregation.of(AggFun.SUM, COMPONENT_CALLS, "callCnt"))
                .addAgg(Aggregation.of(AggFun.SUM, COMPONENT_SLOW_CNT, "slowCnt"))
                .addAgg(Aggregation.of(AggFun.SUM, COMPONENT_SUM_DURATION, "totalTime"))
                .addAgg(Aggregation.of("sum(" + COMPONENT_CALLS + ") / " + (toTimeMs / 1000 - fromTimeMs / 1000), "reqRate"))
                .addAgg(Aggregation.of(AggFun.SUM, COMPONENT_ERR_CNT, "errCnt"))
                .addAgg(Aggregation.of("sum(" + COMPONENT_ERR_CNT + ") / sum(cnt)", "errRate"))
                .addAgg(Aggregation.of("sum(" + COMPONENT_SLOW_CNT + ") / sum(cnt)", "slowRate"))
                .addAgg(Aggregation.of("sum(" + COMPONENT_SUM_DURATION + ") / sum(" + COMPONENT_CALLS + ")", "avgLatency"))
                .addAllWhere(wheres)
                .addAllGroupBy(groupBys);
        if (db.equals(Constant.TS_DB.MOREDB)) {
            //只有moredb的才可以直接用sql查百分位
            queryBuilder.addAgg(Aggregation.of(AggFun.PERCENTILE, "50", "p50Latency"))
                    .addAgg(Aggregation.of(AggFun.PERCENTILE, "75", "p75Latency"))
                    .addAgg(Aggregation.of(AggFun.PERCENTILE, "90", "p90Latency"))
                    .addAgg(Aggregation.of(AggFun.PERCENTILE, "95", "p95Latency"))
                    .addAgg(Aggregation.of(AggFun.PERCENTILE, "99", "p99Latency"))
                    .addAgg(Aggregation.of(AggFun.PERCENTILE, "100", "p100Latency"));
        }
        if (measurements.equals(TSDB_METRIC_MQ_TABLE_NAME)) {
            //包含mq
            queryBuilder.addAgg(Aggregation.of(" sum(\"" + COMPONENT_DELAY + "\") / sum(" + COMPONENT_CALLS + ")", "avgDelay"));
        }

        if (!isManualSortPage) {
            //排序分页
            queryBuilder = getSortOrderBuilder(queryBuilder, search);
        }
        return queryBuilder;
    }

    public List<Where> getResourceWhereBuilder(ServiceSearch search, Long fromTimeMs, Long toTimeMs) {
        List<Where> wheres = new ArrayList<>();
        if (fromTimeMs != null) {
            wheres.add(Where.gte("time", fromTimeMs));
        }
        if (toTimeMs != null) {
            wheres.add(Where.lt("time", toTimeMs));
        }
        if (!CollectionUtils.isEmpty(search.getServiceIds())) {
            wheres.add(Where.in("serviceId", search.getServiceIds()));
        }

        if (StringUtils.isNotBlank(search.getServiceId())) {
            wheres.add(Where.eq("serviceId", search.getServiceId()));
        }
        if (StringUtils.isNotBlank(search.getSrcServiceId())) {
            wheres.add(Where.eq("srcServiceId", search.getSrcServiceId()));
        }
        if (StringUtils.isNotBlank(search.getServiceInstance())) {
            wheres.add(Where.eq("serviceInstance", search.getServiceInstance()));
        }
        if (StringUtils.isNotBlank(search.getServiceInstanceQuery())) {
            wheres.add(Where.like("serviceInstanceQuery", search.getServiceInstanceQuery()));
        }
        if (StringUtils.isNotBlank(search.getSrcServiceInstance())) {
            wheres.add(Where.eq("srcServiceInstance", search.getSrcServiceInstance()));
        }
        if (StringUtils.isNotBlank(search.getSrcServiceInstanceQuery())) {
            wheres.add(Where.like("srcServiceInstanceQuery", search.getSrcServiceInstanceQuery()));
        }
        if (StringUtils.isNotBlank(search.getResource())) {
            wheres.add(Where.eq("resource", search.getResource()));
        }
        if (StringUtils.isNotBlank(search.getResourceQuery())) {
            wheres.add(Where.like("resource", search.getResourceQuery()));
        }
        if (StringUtils.isNotBlank(search.getRootResource())) {
            wheres.add(Where.eq("rootResource", search.getRootResource()));
        }
        if (StringUtils.isNotBlank(search.getRootResourceQuery())) {
            wheres.add(Where.like("rootResource", search.getRootResourceQuery()));
        }
        if (search.getIsIn() != null) {
            wheres.add(Where.eq("isIn", search.getIsIn()));
        }
        if (search.getIsOut() != null) {
            wheres.add(Where.eq("isOut", search.getIsOut()));
        }
        if (search.getIsSlow() != null) {
            wheres.add(Where.eq("isSlow", search.getIsSlow()));
        }
        if (StringUtils.isNotBlank(search.getUrl())) {
            wheres.add(Where.eq("url", search.getUrl()));
        }
        if (StringUtils.isNotBlank(search.getUrlQuery())) {
            wheres.add(Where.like("url", search.getUrlQuery()));
        }
        if (StringUtils.isNotBlank(search.getHttpCode())) {
            wheres.add(Where.eq("httpCode", search.getHttpCode()));
        }
        if (StringUtils.isNotBlank(search.getHttpCodeQuery())) {
            wheres.add(Where.like("httpCode", search.getHttpCodeQuery()));
        }
        if (StringUtils.isNotBlank(search.getMethod())) {
            wheres.add(Where.eq("method", search.getMethod()));
        }
        if (StringUtils.isNotBlank(search.getMethodQuery())) {
            wheres.add(Where.like("method", search.getMethodQuery()));
        }
        if (StringUtils.isNotBlank(search.getTopic())) {
            wheres.add(Where.eq("topic", search.getTopic()));
        }
        if (StringUtils.isNotBlank(search.getTopicQuery())) {
            wheres.add(Where.like("topic", search.getTopicQuery()));
        }
        if (StringUtils.isNotBlank(search.getGroup())) {
            wheres.add(Where.eq("group", search.getGroup()));
        }
        if (StringUtils.isNotBlank(search.getGroupQuery())) {
            wheres.add(Where.like("group", search.getGroupQuery()));
        }
        if (StringUtils.isNotBlank(search.getPartition())) {
            wheres.add(Where.eq("partition", search.getPartition()));
        }
        if (StringUtils.isNotBlank(search.getPartitionQuery())) {
            wheres.add(Where.like("partition", search.getPartitionQuery()));
        }
        if (StringUtils.isNotBlank(search.getBroker())) {
            wheres.add(Where.eq("broker", search.getBroker()));
        }
        if (StringUtils.isNotBlank(search.getBrokerQuery())) {
            wheres.add(Where.like("broker", search.getBrokerQuery()));
        }
        if (StringUtils.isNotBlank(search.getSqlDatabase())) {
            wheres.add(Where.eq("sqlDatabase", search.getSqlDatabase()));
        }
        if (StringUtils.isNotBlank(search.getSqlDatabaseQuery())) {
            wheres.add(Where.like("sqlDatabase", search.getSqlDatabaseQuery()));
        }
        if (StringUtils.isNotBlank(search.getSqlContent())) {
            wheres.add(Where.eq("sqlContent", search.getSqlContent()));
        }
        if (StringUtils.isNotBlank(search.getSqlContentQuery())) {
            wheres.add(Where.like("sqlContent", search.getSqlContentQuery()));
        }
        if (StringUtils.isNotBlank(search.getSqlOperation())) {
            wheres.add(Where.eq("sqlOperation", search.getSqlOperation()));
        }
        if (StringUtils.isNotBlank(search.getSqlOperationQuery())) {
            wheres.add(Where.like("sqlOperation", search.getSqlOperationQuery()));
        }
        if (StringUtils.isNotBlank(search.getCommand())) {
            wheres.add(Where.eq("command", search.getCommand()));
        }
        if (StringUtils.isNotBlank(search.getCommandQuery())) {
            wheres.add(Where.like("command", search.getCommandQuery()));
        }
        if (StringUtils.isNotBlank(search.getException())) {
            wheres.add(Where.eq("exceptionName", search.getException()));
        }
        if (StringUtils.isNotBlank(search.getExceptionQuery())) {
            wheres.add(Where.like("exceptionName", search.getExceptionQuery()));
        }
        return wheres;
    }

    public List<Aggregation> getAggFieldsByComponentType(ServiceSearch search) {
        List<Aggregation> aggregations = new ArrayList<>();
        String componentType = search.getComponentType();
        if (StringUtils.isNotBlank(componentType) && componentType.equals(TSDB_METRIC_MQ_TABLE_NAME)) {
            //包含mq
            aggregations.add(Aggregation.of(AggFun.SUM, COMPONENT_DELAY, "sumDelay"));
            aggregations.add(Aggregation.of(AggFun.SUM, MQ_BODY_LENGTH, "sumMqBodyLength"));
            aggregations.add(Aggregation.of("sum(\"" + COMPONENT_DELAY + "\") / sum(" + COMPONENT_CALLS + ")", "avgDelay"));
            aggregations.add(Aggregation.of("sum(" + MQ_BODY_LENGTH + ") / sum(" + COMPONENT_CALLS + ")", "avgMqBodyLength"));
        }
        List<String> bodyLengthTables = Arrays.asList(TSDB_METRIC_RPC_TABLE_NAME, TSDB_METRIC_HTTP_TABLE_NAME, TSDB_METRIC_REDIS_TABLE_NAME);
        if (StringUtils.isNotBlank(componentType) && bodyLengthTables.contains(componentType)) {
            //包含http rpc db redis
            aggregations.add(Aggregation.of(AggFun.SUM, RESPONSE_BODY_LENGTH, "sumRespBodyLength"));
            aggregations.add(Aggregation.of(AggFun.SUM, REQUEST_BODY_LENGTH, "sumeqBodyLength"));
            aggregations.add(Aggregation.of("sum(" + RESPONSE_BODY_LENGTH + ") / sum(" + COMPONENT_CALLS + ")", "avgRespBodyLength"));
            aggregations.add(Aggregation.of("sum(" + REQUEST_BODY_LENGTH + ") / sum(" + COMPONENT_CALLS + ")", "avgReqBodyLength"));
        }
        if (StringUtils.isNotBlank(componentType) && componentType.equals(TSDB_METRIC_DB_TABLE_NAME)) {
            //包含db
            aggregations.add(Aggregation.of(AggFun.SUM, COMPONENT_READ_ROWS, "sumReadRows"));
            aggregations.add(Aggregation.of(AggFun.SUM, COMPONENT_UPDATE_ROWS, "sumUpdateRows"));
            aggregations.add(Aggregation.of("sum(" + COMPONENT_READ_ROWS + ") / sum(" + COMPONENT_CALLS + ")", "avgReadRows"));
            aggregations.add(Aggregation.of("sum(" + COMPONENT_UPDATE_ROWS + ") / sum(" + COMPONENT_CALLS + ")", "avgUpdateRows"));
        }
        return aggregations;
    }


    public List<String> getResourceGroupByBuilder(String measurement) {
        List<String> groupBys = new ArrayList<>();
        groupBys.add("serviceId");
        groupBys.add("resource");
        if (measurement.equals(TSDB_METRIC_MQ_TABLE_NAME)) {
            //包含mq
            groupBys.add("topic");
            groupBys.add("broker");
            groupBys.add("group");
            groupBys.add("partition");
            groupBys.add("type");
        }
        if (measurement.equals(TSDB_METRIC_DB_TABLE_NAME)) {
            //包含db
            groupBys.add("dbType");
            groupBys.add("sqlDatabase");
            groupBys.add("sqlOperation");
        }
        if (measurement.equals(TSDB_METRIC_HTTP_TABLE_NAME)) {
            //包含http
            groupBys.add("httpMethod");
        }
        if (measurement.equals(TSDB_METRIC_RPC_TABLE_NAME)) {
            //包含rpc
            groupBys.add("type");
        }
        if (measurement.equals(TSDB_METRIC_ES_TABLE_NAME)) {
            //包含es
            groupBys.add("indices");
            groupBys.add("method");
        }
        return groupBys;
    }

    public QueryBuilder getGraphStatsGroupByBuilder(QueryBuilder queryBuilder, ServiceSearch search) {
        if (search == null) {
            return queryBuilder;
        }
        int interval = search.getInterval() == null ? 60 : search.getInterval();
        queryBuilder.setInterval(interval);
        if (StringUtils.isNotBlank(search.getServiceId())) {
            queryBuilder.addGroupBy("serviceId");
        }
        if (StringUtils.isNotBlank(search.getSrcServiceId())) {
            queryBuilder.addGroupBy("srcServiceId");
        }
        if (StringUtils.isNotBlank(search.getServiceInstance())) {
            queryBuilder.addGroupBy("serviceInstance");
        }
        if (StringUtils.isNotBlank(search.getSrcServiceInstance())) {
            queryBuilder.addGroupBy("srcServiceInstance");
        }
        if (StringUtils.isNotBlank(search.getResource())) {
            queryBuilder.addGroupBy("resource");
        }
        return queryBuilder;
    }
}
