package com.databuff.webapp.apm.util;

import java.util.Map;

public class TsDbExtractor {

    /**
     * 安全地从值映射中提取长整型值，尝试多个可能的键
     */
    public static Long extractLongValue(Map<String, Object> valueMap, String... possibleKeys) {
        for (String key : possibleKeys) {
            if (valueMap.containsKey(key) && valueMap.get(key) instanceof Number) {
                return ((Number) valueMap.get(key)).longValue();
            }
        }
        return null;
    }

    /**
     * 安全地从值映射中提取double值，尝试多个可能的键
     */
    public static Double extractDoubleValue(Map<String, Object> valueMap, String... possibleKeys) {
        for (String key : possibleKeys) {
            if (valueMap.containsKey(key) && valueMap.get(key) instanceof Number) {
                return ((Number) valueMap.get(key)).doubleValue();
            }
        }
        return null;
    }
}
