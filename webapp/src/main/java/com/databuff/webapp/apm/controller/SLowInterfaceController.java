package com.databuff.webapp.apm.controller;

import com.databuff.webapp.apm.model.GetServiceResourceRelationsRequest;
import com.databuff.webapp.apm.model.ServiceResourceRelationResponse;
import com.databuff.webapp.apm.model.UpdateResourceAlias;
import com.databuff.webapp.apm.service.SlowInterfaceService;
import com.databuff.webapp.config.common.CommonResponse;
import com.databuff.webapp.config.interceptor.DomainManagerRequired;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Api(value = "慢接口")
@RestController
@RequestMapping("/slowInterface")
public class SLowInterfaceController {

    @Autowired
    private SlowInterfaceService slowInterfaceService;

    /**
     * 获取服务实例统计信息
     **/
    @ApiOperation(value = "获取服务实例关联其他服务实例信息")
    @RequestMapping(value = "/getResourceRelations", method = RequestMethod.POST)
    @ResponseBody
    @DomainManagerRequired(DomainManagerRequired.Type.CURRENT)
    public CommonResponse getServiceResourceRelations(HttpServletRequest request, @RequestBody GetServiceResourceRelationsRequest re) {
        CommonResponse commonResponse = new CommonResponse();
        String apiKey = request.getAttribute("apiKey").toString();
        ServiceResourceRelationResponse response = slowInterfaceService.getServiceResourceRelations(re.getServiceId(), re.getResource(), re.getComponentType(), re.getStart(), re.getEnd(), apiKey);
        commonResponse.setData(response);
        return commonResponse;
    }

    /**
     * 修改接口别名
     **/
    @ApiOperation(value = "更新接口别名")
    @RequestMapping(value = "/updateResourceAlias", method = RequestMethod.POST)
    @ResponseBody
    @DomainManagerRequired(DomainManagerRequired.Type.CURRENT)
    public CommonResponse updateResourceAlias(@RequestBody UpdateResourceAlias request) {
        CommonResponse commonResponse = new CommonResponse();
        try {
            slowInterfaceService.updateResourceAlias(request.getServiceId(), request.getResource(), request.getAlias());
        } catch (Exception e) {
            commonResponse.setMessage(e.getMessage());
            return commonResponse;
        }
        return commonResponse;
    }

}
