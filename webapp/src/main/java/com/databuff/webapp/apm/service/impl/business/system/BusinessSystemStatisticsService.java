package com.databuff.webapp.apm.service.impl.business.system;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.threadLocal.TransmittableThreadLocalUtil;
import com.databuff.common.tsdb.TSDBOperateUtil;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.TSDBResultSet;
import com.databuff.common.tsdb.model.TSDBSeries;
import com.databuff.common.utils.DateUtils;
import com.databuff.dao.mysql.BusinessMapper;
import com.databuff.entity.BaseSearch;
import com.databuff.entity.dto.KeyValue;
import com.databuff.webapp.apm.model.BusinessServiceStatRequest;
import com.databuff.webapp.apm.model.BusinessSystemStatistics;
import com.databuff.webapp.infrastructure.service.InfrastructureService;
import com.databuff.webapp.util.ThreadPoolUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BusinessSystemStatisticsService {
    @Autowired
    private BusinessMapper businessMapper;

    @Autowired
    private BusinessSystemCommonService businessSystemCommonService;

    @Autowired
    private TSDBOperateUtil tsdbOperateUtil;

    @Autowired
    private InfrastructureService infrastructureService;

    @Autowired
    private BusinessSystemPermissionService businessSystemPermissionService;

    /**
     * 属性和标签模块的 构建对象业务系统的上下游依赖关系统计
     *
     * @param search
     * @return {@link BusinessSystemStatistics }
     * @throws Exception
     */
    public BusinessSystemStatistics getBusinessSystemStatistics(BusinessServiceStatRequest search) throws Exception {

        businessSystemPermissionService.validateBusinessSystemPermission(search.getBizId());

        BusinessSystemStatistics statistics = new BusinessSystemStatistics();

        try {
            Long startTime = DateUtils.tsdbTimeToMilliseconds(search.getFromTime());
            Long endTime = DateUtils.tsdbTimeToMilliseconds(search.getToTime());

            //查当前系统下层所有的服务id（包括子系统下的） ,有心跳的服务都要显示
            List<KeyValue> serviceIdAndTypes = businessMapper.getAllServiceIdsAndTypeByBusinessHierarchyWithTime(
                    Integer.valueOf(search.getBizId()), search.getFromTime());

            boolean isParentBizId = businessSystemCommonService.isParentBizId(search.getBizId(), search.getApiKey());

            Set<String> bizIds = new HashSet<>();
            bizIds.add(search.getBizId());
            if (isParentBizId) {
                bizIds.addAll(businessMapper.getSubSystemIdsByPId(Integer.valueOf(search.getBizId()))
                        .stream().map(String::valueOf).collect(Collectors.toSet()));
            }

            List<String> bizIdsList = new ArrayList<>(bizIds);
            boolean bizInternalCall = isParentBizId ? false : true; // 针对 sub_biz_internal_call 字段

            // 1.上游业务系统个数
            Future<Integer> upStreamBusinessSystemCountFuture = ThreadPoolUtil.EXECUTOR.submit(() -> {
                QueryBuilder queryBuilder = BusinessSystemQueryProvider.getUpstreamBusinessSystemQuery(
                        search.getApiKey(), false, bizIdsList, bizInternalCall, startTime, endTime);
                Set<String> upStreamBusinessSystemSet = queryAndGetUniqueBusinessSystem(queryBuilder);
                return upStreamBusinessSystemSet.size();
            });

            // 2.上游服务个数
            Future<Integer> upStreamServiceCountFuture = ThreadPoolUtil.EXECUTOR.submit(() -> {
                QueryBuilder queryBuilder = BusinessSystemQueryProvider.getUpstreamServiceQuery(
                        search.getApiKey(), false, bizIdsList, bizInternalCall, startTime, endTime);
                return queryAndGetUniqueIdCount(queryBuilder, "srcServiceId");
            });

            // 3.上游MQ个数
            Future<Integer> upStreamMQCountFuture = ThreadPoolUtil.EXECUTOR.submit(() -> {
                QueryBuilder queryBuilder = BusinessSystemQueryProvider.getUpstreamMQQuery(
                        search.getApiKey(), false, bizIdsList, bizInternalCall, startTime, endTime);
                return queryAndGetUniqueIdCount(queryBuilder, "srcServiceId");
            });

            // 4. 当前系统的所有服务(包含数据库缓存等) ,服务只要存活(例如上报jvm数据) 就算在统计指标个数内 ,查service表
            // -> 其实等于直接查当前系统下层所有的服务id（包括子系统下的） ,有心跳的服务都要显示 ,不需要再去查moredb等

            // 11.下游业务系统个数
            Future<Integer> downStreamBusinessSystemCountFuture = ThreadPoolUtil.EXECUTOR.submit(() -> {
                QueryBuilder queryBuilder = BusinessSystemQueryProvider.getDownstreamBusinessSystemQuery(
                        search.getApiKey(), false, bizIdsList, bizInternalCall, startTime, endTime);
                Set<String> downStreamBusinessSystemSet = queryAndGetUniqueBusinessSystem(queryBuilder);
                return downStreamBusinessSystemSet.size();
            });

            // 12.下游服务个数
            Future<Integer> downStreamServiceCountFuture = ThreadPoolUtil.EXECUTOR.submit(() -> {
                QueryBuilder queryBuilder = BusinessSystemQueryProvider.getDownstreamServiceQuery(
                        search.getApiKey(), false, bizIdsList, bizInternalCall, startTime, endTime);
                return queryAndGetUniqueIdCount(queryBuilder, "serviceId");
            });

            // 13.下游数据库个数
            Future<Integer> downStreamDatabaseCountFuture = ThreadPoolUtil.EXECUTOR.submit(() -> {
                QueryBuilder queryBuilder = BusinessSystemQueryProvider.getDownstreamDatabaseQuery(
                        search.getApiKey(), false, bizIdsList, bizInternalCall, startTime, endTime);
                return queryAndGetUniqueIdCount(queryBuilder, "serviceId");
            });

            // 14.下游MQ个数
            Future<Integer> downStreamMQCountFuture = ThreadPoolUtil.EXECUTOR.submit(() -> {
                QueryBuilder queryBuilder = BusinessSystemQueryProvider.getDownstreamMQQuery(
                        search.getApiKey(), false, bizIdsList, bizInternalCall, startTime, endTime);
                return queryAndGetUniqueIdCount(queryBuilder, "serviceId");
            });

            // 15.下游缓存个数
            Future<Integer> downStreamCacheCountFuture = ThreadPoolUtil.EXECUTOR.submit(() -> {
                QueryBuilder queryBuilder = BusinessSystemQueryProvider.getDownstreamCacheQuery(
                        search.getApiKey(), false, bizIdsList, bizInternalCall, startTime, endTime);
                return queryAndGetUniqueIdCount(queryBuilder, "serviceId");
            });

            if (!serviceIdAndTypes.isEmpty()) {
                //9.当前业务系统关联的容器个数 包括容器
                BaseSearch baseSearch = new BaseSearch();
                baseSearch.setFromTime(search.getFromTime());
                baseSearch.setToTime(search.getToTime());
                baseSearch.setApiKey(search.getApiKey());
                baseSearch.setOffset(0);
                baseSearch.setSize(Integer.MAX_VALUE);
                baseSearch.setSysId(Integer.valueOf(search.getBizId()));
                JSONObject containerJsonObject = infrastructureService.containerList(baseSearch);
                // 设置当前容器数量
                Integer containerSize = containerJsonObject.getInteger("size");
                statistics.setCurrentContainerCount(containerSize == null ? 0 : containerSize);

                //10.当前业务系统关联的主机个数
                JSONObject hostJsonObject = infrastructureService.hostListV2(baseSearch);
                Integer hostSize = hostJsonObject.getInteger("size");
                statistics.setCurrentHostCount(hostSize == null ? 0 : hostSize);

            } else {
                statistics.setCurrentSubsystemCount(0);
                statistics.setCurrentServiceCount(0);
                statistics.setCurrentContainerCount(0);
                statistics.setCurrentHostCount(0);
            }

            statistics.setUpStreamBusinessSystemCount(upStreamBusinessSystemCountFuture.get());
            statistics.setUpStreamServiceCount(upStreamServiceCountFuture.get());
            statistics.setUpStreamMQCount(upStreamMQCountFuture.get());

            // 计算当前系统的服务个数
            long currentServiceCount = 0;

            // 计算当前系统的数据库个数
            long currentDatabaseCount = 0;

            // 计算当前系统的MQ个数
            long currentMQCount = 0;

            // 计算当前系统的缓存个数
            long currentCacheCount = 0;

            List<String> aliveServiceIds = new ArrayList<>();

            for (KeyValue serviceIdAndType : serviceIdAndTypes) {
                aliveServiceIds.add(serviceIdAndType.getKey());
                if ("web".equals(serviceIdAndType.getValue()) || "custom".equals(serviceIdAndType.getValue())) {
                    currentServiceCount++;
                } else if ("db".equals(serviceIdAndType.getValue())) {
                    currentDatabaseCount++;
                } else if ("mq".equals(serviceIdAndType.getValue())) {
                    currentMQCount++;
                } else if ("cache".equals(serviceIdAndType.getValue())) {
                    currentCacheCount++;
                }
            }

            statistics.setCurrentServiceCount((int) currentServiceCount);
            statistics.setCurrentDatabaseCount((int) currentDatabaseCount);
            statistics.setCurrentMQCount((int) currentMQCount);
            statistics.setCurrentCacheCount((int) currentCacheCount);

            if (!aliveServiceIds.isEmpty()) {
                //使用存活的服务反找业务系统 ,但一个服务可以属于多个业务系统 ,所以查子系统个数还需要根据实际传入的父系统来过滤
                Set<Integer> subSystemIdsByServiceIds = businessMapper.getSubSystemIdsByServiceIds(aliveServiceIds);
                Set<Integer> subSystemIdsByPId = businessMapper.getSubSystemIdsByPId(Integer.valueOf(search.getBizId()));

                // 找出两个集合的交集，即既与服务相关又属于指定父系统的子系统ID
                Set<Integer> intersectionSubSystemIds = new HashSet<>(subSystemIdsByServiceIds);
                intersectionSubSystemIds.retainAll(subSystemIdsByPId);

                // 设置当前子系统数量为交集的大小
                statistics.setCurrentSubsystemCount(intersectionSubSystemIds.size());
            } else {
                statistics.setCurrentSubsystemCount(0);
            }

            statistics.setDownStreamBusinessSystemCount(downStreamBusinessSystemCountFuture.get());
            statistics.setDownStreamServiceCount(downStreamServiceCountFuture.get());
            statistics.setDownStreamDatabaseCount(downStreamDatabaseCountFuture.get());
            statistics.setDownStreamMQCount(downStreamMQCountFuture.get());
            statistics.setDownStreamCacheCount(downStreamCacheCountFuture.get());

            return statistics;
        } finally {
            TransmittableThreadLocalUtil.removeThreadLocal();
        }
    }

    /**
     * 查询并获取唯一的业务系统集合
     */
    private Set<String> queryAndGetUniqueBusinessSystem(QueryBuilder queryBuilder) throws Exception {
        Set<String> businessSystemSet = new HashSet<>();
        log.info(String.valueOf(queryBuilder));

        TSDBResultSet resultSet = tsdbOperateUtil.executeQuery(queryBuilder);

        if (resultSet == null || resultSet.getResults() == null || resultSet.getResults().isEmpty() ||
                resultSet.getResults().get(0) == null || resultSet.getResults().get(0).getSeries() == null ||
                resultSet.getResults().get(0).getSeries().isEmpty()) {
            return businessSystemSet;
        }

        for (TSDBSeries series : resultSet.getResults().get(0).getSeries()) {
            Map<String, String> tags = series.getTags();
            // 根据需要，只取 src_biz_id（如果不为空）否则取 dst_biz_id
            String bizId = (tags.get("src_biz_id") != null) ? tags.get("src_biz_id") : tags.get("dst_biz_id");
            // 如果 bizId 为空或空字符串，则跳过
            if (bizId == null || bizId.trim().isEmpty()) {
                continue;
            }
            // 直接添加，不再区分父子关系
            businessSystemSet.add(bizId);
        }

        return businessSystemSet;
    }

    /**
     * 查询并获取唯一的服务ID数量
     * 针对服务查询，按照指定的 serviceIdColumn（例如上游时为 srcServiceId，下游时为 dstServiceId）
     * 同时利用返回结果中的 srcServiceId 与 dstServiceId 过滤掉自调用（即两者相同）的记录
     */
    private int queryAndGetUniqueIdCount(QueryBuilder queryBuilder, String serviceIdColumn) throws Exception {
        TSDBResultSet resultSet = tsdbOperateUtil.executeQuery(queryBuilder);

        if (resultSet == null || resultSet.getResults() == null || resultSet.getResults().isEmpty() ||
                resultSet.getResults().get(0) == null || resultSet.getResults().get(0).getSeries() == null ||
                resultSet.getResults().get(0).getSeries().isEmpty()) {
            return 0;
        }

        // 用于存放过滤后的唯一 serviceId
        Set<String> uniqueServiceIds = new HashSet<>();

        for (TSDBSeries series : resultSet.getResults().get(0).getSeries()) {
            Map<String, String> tags = series.getTags();
            // 假定 tags 中同时包含 "srcServiceId" 和 "dstServiceId"
            String src = tags.get("srcServiceId");
            String dst = tags.get("serviceId");
            // 如果两者相等，则认为是自调用，过滤掉
            if (src != null && dst != null && src.equals(dst)) {
                continue;
            }
            // 否则取 serviceIdColumn 指定的字段
            String serviceId = tags.get(serviceIdColumn);
            if (serviceId != null && !serviceId.trim().isEmpty()) {
                uniqueServiceIds.add(serviceId);
            }
        }

        return uniqueServiceIds.size();
    }
}