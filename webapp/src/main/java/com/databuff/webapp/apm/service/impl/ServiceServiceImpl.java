package com.databuff.webapp.apm.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.databuff.common.constants.TSDBIndex;
import com.databuff.common.tsdb.TSDBOperateUtil;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.*;
import com.databuff.common.utils.DateUtils;
import com.databuff.dao.mysql.TraceServiceMapper;
import com.databuff.entity.TraceServiceEntity;
import com.databuff.entity.extend.ServiceSearch;
import com.databuff.service.ServiceSyncService;
import com.databuff.webapp.admin.service.DomainManagerPermissionService;
import com.databuff.webapp.apm.service.ServiceService;
import com.databuff.webapp.apm.util.ApmTraceSearchUtil;
import com.databuff.webapp.util.ThreadPoolUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.databuff.common.constants.Constant.Trace.SERVICE_ID;
import static com.databuff.common.constants.Constant.Trace.SRC_SERVICE_ID;
import static com.databuff.common.constants.TSDBIndex.TSDB_METRIC_EXCEPTION_TABLE_NAME;
import static com.databuff.common.constants.TSDBIndex.RESOURCE_METRIC_TABLES;
import static com.databuff.common.constants.MetricConstant.*;

/**
 * @author:TianMing
 * @date: 2021/10/11
 * @time: 19:00
 */
@Service
@Slf4j
public class ServiceServiceImpl implements ServiceService {

    @Autowired
    private ApmTraceSearchUtil apmUtil;
    @Autowired
    private TSDBOperateUtil tsdbOperateUtil;
    @Autowired
    @Qualifier("traceServiceMapper")
    private TraceServiceMapper traceServiceMapper;
    @Autowired
    private ServiceSyncService serviceSyncService;
    @Autowired
    private DomainManagerPermissionService permissionService;
    // 数据保留时长,默认7天
    @Value("${data.expireDays:7}")
    private Integer expireDays;
    @Override
    public Object getServiceTags(JSONObject search) {
        String apiKey = search.getString("apiKey");
        String fromTime = search.getString("startTime");
        String toTime = search.getString("endTime");
        if (StringUtils.isBlank(fromTime)) {
            toTime = DateUtils.dateToString("yyyy-MM-dd HH:mm:ss", new Date(System.currentTimeMillis() -  (expireDays * 24 * 60 * 60 * 1000L)));
        }
        String allTagStrs = traceServiceMapper.getServiceTags(apiKey, fromTime, toTime);
        Set<String> tagSets = new HashSet<>();
        if (StringUtils.isNotBlank(allTagStrs)) {
            tagSets.addAll(Arrays.asList(allTagStrs.split(",")));
        }
        Map<String, List<String>> tagGroupMap = new HashMap<>();
        tagSets.stream().forEach(t -> {
            if (t.contains(":")) {
                String t1 = t.split(":")[0];
                List<String> tags = tagGroupMap.getOrDefault(t1, new ArrayList<>());
                if (!tags.contains(t)) {
                    tags.add(t);
                }
                tagGroupMap.put(t1, tags);
            }
        });

        return tagGroupMap;
    }


    @Override
    public Object reqContributorService(ServiceSearch search) throws Exception {
        JSONObject data = new JSONObject();
        data.put("data", new JSONArray());
        data.put("status", 200);
        data.put("message", "SUCCESS");

        String measurement = search.getComponentType();
        if (StringUtils.isBlank(measurement)) {
            return data;
        }
        search.setIgnoreTime(true);
        //管理域服务实体权限校验
        if (!permissionService.domainManagerServicePermission(search)){
            return data;
        }
        List<TraceServiceEntity> allServices = traceServiceMapper.listService(search);
        List<String> serviceIds = allServices.stream().map(TraceServiceEntity::getId).collect(Collectors.toList());

        QueryBuilder queryBuilder = apmUtil.getReqContributorBuilder(serviceIds, search, search.getFromTimeVul(), search.getToTimeVul(), measurement);

        List<JSONObject> allSrcServices = new Vector<>();
        Future<?> future = ThreadPoolUtil.ENDPOINT_EXECUTOR.submit(() -> {
            try {
                List<TSDBSeries> seriesList = tsdbOperateUtil.executeQueryForAllGroups(queryBuilder);
                seriesList.forEach(s -> s.getValues().forEach(v -> {
                    JSONObject moreDBRet = new JSONObject();
                    for (int i = 0; i < s.getColumns().size(); i++) {
                        moreDBRet.put(s.getColumns().get(i), v.get(i));
                    }
                    moreDBRet.putAll(s.getTags());
                    allSrcServices.add(moreDBRet);
                }));
            } catch (Throwable e) {
                log.error("query request contributor service error ", e);
            }
        });
        QueryBuilder countBuilder = apmUtil.getReqContributorCountBuilder(serviceIds, search, search.getFromTimeVul(), search.getToTimeVul(), measurement);
        long total = 0;
        try {
            List<TSDBSeries> seriesList = tsdbOperateUtil.executeQueryForAllGroups(countBuilder);
            total = seriesList.size();
            if (total == 0) {
                return data;
            }
        } catch (Exception e) {
            log.error("service request contributor service list ret search error:{}", e);
        }
        try {
            future.get();
        } catch (Throwable e) {
            log.error("query request contributor service Future<?> error ", e);
        }

        List<Object> res = new ArrayList<>(16);

        for (JSONObject entitys : allSrcServices) {
            String srcSvcId = entitys.getString(SRC_SERVICE_ID);
            Long allCnt = entitys.getLong("callCnt");
            TraceServiceEntity serverEntity = serviceSyncService.getTraceServiceEntityByServiceId(srcSvcId);
            if (serverEntity == null) {
                continue;
            }
            Map<String, Object> resMap = new HashMap<>(3);
            resMap.put("serverName", serverEntity.getName());
            resMap.put("serviceId", serverEntity.getId());
            resMap.put("serviceType", serverEntity.getService_type());
            resMap.put("callCnt", allCnt);
            res.add(resMap);
        }
        data.put("total", total);
        data.put("size", res.size());
        data.put("offset", search.getOffset() + res.size());
        data.put("data", res);
        return data;
    }

    @Override
    public Object reqTop(ServiceSearch search) throws Exception {
        String apiKey = search.getApiKey();
        Integer size = search.getSize();
        if (size == null || size == 0) {
            size = 10;
        }
        String measurement = search.getComponentType();
        Set<String> tables = new HashSet<>();
        if (StringUtils.isBlank(measurement)) {
            tables.addAll(RESOURCE_METRIC_TABLES);
        } else {
            tables.add(measurement);
        }
        // fromTime为yyyy-MM-DD HH:mm:ss
        long fromTimeMs = search.getFromTimeVul();
        long toTimeMs = search.getToTimeVul();
        //管理域服务实体权限校验
        if (!permissionService.domainManagerServicePermission(search)){
            return new ArrayList<>();
        }
        List<Where> resourceWhereBuilder = apmUtil.getResourceWhereBuilder(search, fromTimeMs, toTimeMs);

        List<JSONObject> resultList = Collections.synchronizedList(new ArrayList<>());
        Integer finalSize = size;
        tables.parallelStream().forEach(m -> {
            String alis = "resCall";
            QueryBuilder queryBuilder = new QueryBuilder()
                    .setDatabaseName(apiKey,TSDBIndex.TSDB_APM_METRIC_DATABASE_NAME)
                    .setMeasurement(m)
                    .addAgg(Aggregation.of(AggFun.SUM,COMPONENT_CALLS, alis))
                    .addAgg(Aggregation.of(AggFun.SUM,COMPONENT_ERR_CNT, "errCnt"))
                    .addAgg(Aggregation.of("sum(" + COMPONENT_ERR_CNT + ") / sum(cnt)", "errRate"))
                    .addAgg(Aggregation.of("sum(" + COMPONENT_SUM_DURATION + ") / sum(" + COMPONENT_CALLS + ")", "avgLatency"))
                    .addAllWhere(resourceWhereBuilder)
                    .addAllGroupBy(Arrays.asList("resource", SERVICE_ID))
                    .addOrderBy(OrderBy.desc(alis))
                    .setLimit(finalSize);
            try {
                TSDBResultSet resultSet = tsdbOperateUtil.executeQuery(queryBuilder);
                List<TSDBResult> rets = resultSet.getResults();
                List<TSDBSeries> seriesList = rets.get(0).getSeries();
                if (seriesList != null) {
                    for (TSDBSeries series : seriesList) {
                        for (List<Object> values : series.getValues()) {
                            Double callCnt = getValueAsDouble(series, values, "callCnt");
                            Double errCnt = getValueAsDouble(series, values, "errCnt");
                            Double errRate = getValueAsDouble(series, values, "errRate");
                            Double avgLatency = getValueAsDouble(series, values, "avgLatency");
                            String resource = series.getTags().get("resource");
                            String serviceId = series.getTags().get(SERVICE_ID);

                            JSONObject result = new JSONObject();
                            result.put("avgLatency", avgLatency);
                            result.put("errRate", errRate);
                            result.put("errCnt", errCnt);
                            result.put("callCnt", callCnt);
                            result.put("resource", resource);
                            result.put("componentType", m);
                            result.put(SERVICE_ID, serviceId);

                            resultList.add(result);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("spanRatSecond search error{}", e.getMessage());
            }
        });
        // 使用 Comparator 对 callCnt 字段进行倒序排序
        Comparator<JSONObject> comparator = Comparator.comparingLong(o -> o.getLong("callCnt"));
        Collections.sort(resultList, comparator.reversed());

        // 取前 top10 的元素
        int top10 = Math.min(size, resultList.size());
        List<JSONObject> topList = resultList.subList(0, top10);
        return topList;
    }

    private Double getValueAsDouble(TSDBSeries series, List<Object> values, String columnName) {
        Integer index = series.getColumnIndex(columnName);
        return index == null ? 0 : ((Number) values.get(index)).doubleValue();
    }

    @Override
    public Object exceptionDistMap(ServiceSearch search) {
        JSONObject data = new JSONObject();
        data.put("data", new JSONArray());
        data.put("status", 200);
        data.put("message", "SUCCESS");

        long fromTimeMs = search.getFromTimeVul();
        long toTimeMs = search.getToTimeVul();

        //管理域服务实体权限校验
        if (!permissionService.domainManagerServicePermission(search)){
            return data;
        }

        List<Where> resourceWhereBuilder = apmUtil.getResourceWhereBuilder(search, fromTimeMs, toTimeMs);
        if (StringUtils.isBlank(search.getGroupBy())) {
            search.setGroupBy("exceptionName");
        }
        if (StringUtils.isNotBlank(search.getNotEmptyFields())) {
            for (String field : search.getNotEmptyFields().split(",")){
                resourceWhereBuilder.add(Where.neq(field, ""));
            }
        }
        if (StringUtils.isBlank(search.getSortField())) {
            search.setSortField("errCnt");
            search.setSortOrder("desc");
        }
        List<String> groupBys = Arrays.asList(search.getGroupBy().split(","));
        QueryBuilder queryBuilder = new QueryBuilder()
                .setDatabaseName(search.getApiKey(),TSDBIndex.TSDB_APM_METRIC_DATABASE_NAME)
                .setMeasurement(TSDB_METRIC_EXCEPTION_TABLE_NAME)
                .addAgg(Aggregation.of(AggFun.SUM,COMPONENT_ERR_CNT, "errCnt"))
                .addAllWhere(resourceWhereBuilder)
                .addAllGroupBy(groupBys);

        queryBuilder = apmUtil.getSortOrderBuilder(queryBuilder, search);
        List<JSONObject> datas = new ArrayList<>();
        QueryBuilder finalQueryBuilder = queryBuilder;
        Future<?> future = ThreadPoolUtil.ENDPOINT_EXECUTOR.submit(() -> {
            try {
                TSDBResultSet resultSet = tsdbOperateUtil.executeQuery(finalQueryBuilder);
                List<TSDBResult> rets = resultSet.getResults();
                List<TSDBSeries> seriesList = rets.get(0).getSeries();
                if (seriesList != null) {
                    seriesList.forEach(s -> s.getValues().forEach(v -> {
                        Long errCnt = v.get(1) == null ? 0 : ((Number) v.get(1)).longValue();
                        datas.add(new JSONObject() {{
                            for (String groupBy : groupBys) {
                                if (groupBy.equals("serviceId")) {
                                    TraceServiceEntity serverEntity = serviceSyncService.getTraceServiceEntityByServiceId(s.getTags().get(groupBy));
                                    if (serverEntity != null) {
                                        put("service", serverEntity.getName());
                                    }
                                }
                                put(groupBy, s.getTags().get(groupBy));
                            }
                            put("errCnt", errCnt);
                        }});
                    }));
                }
            } catch (Exception e) {
                log.error("exceptionDistMap search error{}", e.getMessage());
            }
        });

        long total = 0;
        long sumErrorCnt = 0;
        QueryBuilder countBuilder = new QueryBuilder()
                .setDatabaseName(search.getApiKey(),TSDBIndex.TSDB_APM_METRIC_DATABASE_NAME)
                .setMeasurement(TSDB_METRIC_EXCEPTION_TABLE_NAME)
                .addAgg(Aggregation.of(AggFun.SUM,COMPONENT_ERR_CNT, "errCnt"))
                .addAllWhere(resourceWhereBuilder)
                .addAllGroupBy(groupBys);
        try {
            TSDBResultSet resultSet = tsdbOperateUtil.executeQuery(countBuilder);
            List<TSDBResult> countRets = resultSet.getResults();
            if (countRets != null && !countRets.isEmpty()) {
                final List<TSDBSeries> seriesList = countRets.get(0).getSeries();
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(seriesList)) {
                    total = seriesList.size();
                    //求错误总数，相加求和
                    sumErrorCnt = seriesList.stream().mapToLong(s -> s.getValues().stream().mapToLong(v ->v.get(1) == null ? 0 : ((Number) v.get(1)).longValue()).sum()).sum();
                }
            }
            if (total == 0) {
                return data;
            }

        } catch (Exception e) {
            log.error("exceptionDistMap count search error:{}", e);
        }
        try {
            future.get();
        } catch (Exception e) {
            log.error("exceptionDistMap Future<?> get error:{}", e);
        }

        long finalErrCnt = sumErrorCnt;
        datas.parallelStream().map(d -> {
            d.put("percentage", d.getLong("errCnt") * 100.0 / finalErrCnt);
            return d;
        }).collect(Collectors.toList());
        data.put("total", total);
        data.put("totalError", sumErrorCnt);
        data.put("size", datas.size());
        data.put("offset", search.getOffset() + datas.size());
        data.put("data", datas);
        return data;
    }

    @Override
    public List<String> getSvcIds(String apiKey, String fromTime) {
        ServiceSearch search = new ServiceSearch();
        search.setApiKey(apiKey);
        search.setFromTime(fromTime);
        return traceServiceMapper.listServiceIds(search);
    }

}
