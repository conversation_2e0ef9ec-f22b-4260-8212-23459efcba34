package com.databuff.webapp.apm.model;

import lombok.Data;

import java.util.List;

/**
 * @author:TianMing
 * @date: 2022/4/14
 * @time: 17:37
 */
@Data
public class ServiceFlowEntity {
    private String apiKey ;
    private String hostName ;
    private Long startTime ;
    private Long endTime ;
    private List<ServiceTreeNode> serviceNode ;


    public ServiceFlowEntity(String apiKey, String hostName, Long startTime, Long endTime, List<ServiceTreeNode> serviceNode) {
        this.apiKey = apiKey;
        this.hostName = hostName;
        this.startTime = startTime;
        this.endTime = endTime;
        this.serviceNode = serviceNode;
    }
    public ServiceFlowEntity(){

    }
}
