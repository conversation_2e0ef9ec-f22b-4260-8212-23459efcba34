package com.databuff.webapp.apm.service.impl.business.system;

import com.databuff.common.constants.TSDBIndex;
import com.databuff.common.tsdb.TSDBOperateUtil;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.Aggregation;
import com.databuff.common.tsdb.model.TSDBResultSet;
import com.databuff.common.tsdb.model.TSDBSeries;
import com.databuff.common.tsdb.model.Where;
import com.databuff.common.utils.DateUtils;
import com.databuff.dao.mysql.BusinessMapper;
import com.databuff.entity.AlarmSearchParams;
import com.databuff.entity.Business;
import com.databuff.entity.dto.KeyValue;
import com.databuff.util.AlarmOlapEngine;
import com.databuff.webapp.apm.model.RelationDetailRequest;
import com.databuff.webapp.apm.model.RelationDetailResponse;
import com.databuff.webapp.apm.util.TsDbExtractor;
import com.databuff.webapp.util.ThreadPoolUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CurrentSystemRelationService implements RelationHandler {

    @Autowired
    private BusinessMapper businessMapper;

    @Autowired
    private BusinessSystemCommonService businessSystemCommonService;

    @Autowired
    private TSDBOperateUtil tsdbOperateUtil;  // Changed from MetricMoreDBOperateUtil to TSDBOperateUtil

    @Autowired
    private AlarmOlapEngine alarmOlapEngine;

    public List<RelationDetailResponse> handle(RelationDetailRequest search) throws Exception {
        switch (search.getTargetType()) {
            case BUSINESS_SYSTEM:
                return handleCurrentBusinessSystem(search);
            case SUBSYSTEM:
                return handleCurrentSubsystem(search);
            default:
                throw new IllegalArgumentException("不支持的目标类型: " + search.getTargetType());
        }
    }

    private List<RelationDetailResponse> handleCurrentBusinessSystem(RelationDetailRequest search) throws Exception {
        Future<List<Map<String, Object>>> alarmsFuture = queryAlarms(search.getApiKey(), search.getFromTime(), search.getToTime());

        Long startTime = DateUtils.tsdbTimeToMilliseconds(search.getFromTime());
        Long endTime = DateUtils.tsdbTimeToMilliseconds(search.getToTime());

        Set<String> bizIds = getBizIds(search);
        List<String> bizIdsList = new ArrayList<>(bizIds);

        // Build queries using QueryBuilder
        QueryBuilder inQueryBuilder = buildCurrentSystemQueryBuilder(search.getApiKey(), bizIdsList, startTime, endTime, true);
        QueryBuilder outQueryBuilder = buildCurrentSystemQueryBuilder(search.getApiKey(), bizIdsList, startTime, endTime, false);

        // Execute queries using TSDBOperateUtil
        TSDBResultSet inRets = tsdbOperateUtil.executeQuery(inQueryBuilder);
        TSDBResultSet outRets = tsdbOperateUtil.executeQuery(outQueryBuilder);

        RelationDetailResponse response = new RelationDetailResponse();
        response.setRequestVolume(0L);
        response.setErrorCnt(0L);
        response.setSumDuration(0L);
        response.setReqRate(0.0);
        response.setMaxResponseTime(0L);
        response.setErrorRate(0.0);
        response.setId(search.getBizId());

        // Use millisecond time directly
        long fromTimeMs = startTime;
        long toTimeMs = endTime;
        double timeRangeFactor = (toTimeMs - fromTimeMs) / 60000.0;

        processMetricResults(inRets, response, timeRangeFactor);
        processMetricResults(outRets, response, timeRangeFactor);

        Business business = businessMapper.getBusinessById(Integer.valueOf(search.getBizId()), search.getApiKey());
        if (business != null) {
            response.setName(business.getName());
        }

        setAlarmStatus(response, alarmsFuture.get());

        return Collections.singletonList(response);
    }

    private List<RelationDetailResponse> handleCurrentSubsystem(RelationDetailRequest search) throws Exception {
        Future<List<Map<String, Object>>> alarmsFuture = queryAlarms(search.getApiKey(), search.getFromTime(), search.getToTime());

        Long startTime = DateUtils.tsdbTimeToMilliseconds(search.getFromTime());
        Long endTime = DateUtils.tsdbTimeToMilliseconds(search.getToTime());

        // Build queries using QueryBuilder
        QueryBuilder inQueryBuilder = buildCurrentSubsystemQueryBuilder(search.getApiKey(), search.getBizId(), startTime, endTime, true);
        QueryBuilder outQueryBuilder = buildCurrentSubsystemQueryBuilder(search.getApiKey(), search.getBizId(), startTime, endTime, false);

        // Execute queries using TSDBOperateUtil
        TSDBResultSet inRets = tsdbOperateUtil.executeQuery(inQueryBuilder);
        TSDBResultSet outRets = tsdbOperateUtil.executeQuery(outQueryBuilder);

        Map<String, RelationDetailResponse> subsystemMetrics = new HashMap<>();

        // Use millisecond time directly
        long fromTimeMs = startTime;
        long toTimeMs = endTime;
        double timeRangeFactor = (toTimeMs - fromTimeMs) / 60000.0;

        processSubsystemTraffic(inRets, subsystemMetrics, true, timeRangeFactor);
        processSubsystemTraffic(outRets, subsystemMetrics, false, timeRangeFactor);

        List<Business> businesses = businessMapper.findBusiness(search.getApiKey());
        Map<Integer, String> businessMap = businesses.stream()
                .collect(Collectors.toMap(Business::getId, Business::getName));

        // 计算"存活"子系统
        // ① 根据当前业务系统获取存活的服务列表（有心跳数据的服务）
        List<KeyValue> serviceIdAndTypes = businessMapper.getAllServiceIdsAndTypeByBusinessHierarchyWithTime(
                Integer.valueOf(search.getBizId()), search.getFromTime());
        List<String> aliveServiceIds = serviceIdAndTypes.stream()
                .map(KeyValue::getKey)
                .collect(Collectors.toList());

        // ② 根据存活的服务反查对应的子系统ID
        Set<Integer> subSystemIdsByServiceIds = new HashSet<>();
        if (!aliveServiceIds.isEmpty()) {
            subSystemIdsByServiceIds = businessMapper.getSubSystemIdsByServiceIds(aliveServiceIds);
        }
        // ③ 当前业务系统下所有子系统
        Set<Integer> subSystemIdsByPId = businessMapper.getSubSystemIdsByPId(Integer.valueOf(search.getBizId()));
        // ④ 取交集得到真正"存活"的子系统ID集合
        subSystemIdsByServiceIds.retainAll(subSystemIdsByPId);
        Set<Integer> validSubsystemIds = new HashSet<>(subSystemIdsByServiceIds);

        List<Map<String, Object>> alarms = alarmsFuture.get();
        Map<String, Long> busNameToCnt = getAlarmCountsByBusName(alarms);

        // 传入 validSubsystemIds 到 createSubsystemResponses 中
        return createSubsystemResponses(search.getBizId(), businesses, businessMap, subsystemMetrics, busNameToCnt, validSubsystemIds);
    }

    private Set<String> getBizIds(RelationDetailRequest search) {
        Set<String> bizIds = new HashSet<>();
        bizIds.add(search.getBizId());

        if (businessSystemCommonService.isParentBizId(search.getBizId(), search.getApiKey())) {
            bizIds.addAll(businessMapper.getSubSystemIdsByPId(Integer.valueOf(search.getBizId()))
                    .stream()
                    .map(String::valueOf)
                    .collect(Collectors.toSet()));
        }
        return bizIds;
    }

    private Future<List<Map<String, Object>>> queryAlarms(String apiKey, String fromTime, String toTime) {
        AlarmSearchParams params = new AlarmSearchParams();
        params.setApiKey(apiKey);
        params.setFromTime(fromTime);
        params.setToTime(toTime);
        return ThreadPoolUtil.EXECUTOR.submit(() ->
                alarmOlapEngine.getAlarmStatusAggByField(params, "busName", "level"));
    }

    // Replaced string-based query with QueryBuilder
    private QueryBuilder buildCurrentSystemQueryBuilder(String apiKey, List<String> bizIds, Long startTime, Long endTime, boolean isIn) {
        QueryBuilder queryBuilder = new QueryBuilder()
                .setDatabaseName(apiKey + "_" + TSDBIndex.TSDB_APM_METRIC_DATABASE_NAME)
                .setMeasurement(TSDBIndex.TSDB_METRIC_BUSINESS_SERVICE)
                .addWhere(Where.gte("time", startTime))
                .addWhere(Where.lt("time", endTime))
                .addWhere(Where.eq("biz_internal_call", "0"));

        // Add aggregations
        queryBuilder.addAgg(Aggregation.of("sum(cnt)","cnt"))
                .addAgg(Aggregation.of("sum(sumDuration)","sumDuration"))
                .addAgg(Aggregation.of("sum(maxDuration)","maxDuration"))
                .addAgg(Aggregation.of("sum(errorCnt)","errorCnt"));

        // Add conditions based on direction (in/out)
        if (isIn) {
            queryBuilder.addWhere(Where.eq("isIn", "1"))
                    .addWhere(Where.in("dst_biz_id", bizIds))
                    .addWhere(Where.notIn("src_biz_id", bizIds));
        } else {
            queryBuilder.addWhere(Where.eq("isOut", "1"))
                    .addWhere(Where.in("src_biz_id", bizIds))
                    .addWhere(Where.notIn("dst_biz_id", bizIds));
        }

        return queryBuilder;
    }

    // Replaced string-based query with QueryBuilder
    private QueryBuilder buildCurrentSubsystemQueryBuilder(String apiKey, String parentBizId, Long startTime, Long endTime, boolean isIn) {
        QueryBuilder queryBuilder = new QueryBuilder()
                .setDatabaseName(apiKey + "_" + TSDBIndex.TSDB_APM_METRIC_DATABASE_NAME)
                .setMeasurement(TSDBIndex.TSDB_METRIC_BUSINESS_SERVICE)
                .addWhere(Where.gte("time", startTime))
                .addWhere(Where.lt("time", endTime))
                .addWhere(Where.eq("sub_biz_internal_call", "0"));

        // Add aggregations

        queryBuilder.addAgg(Aggregation.of("sum(cnt)","cnt"))
                .addAgg(Aggregation.of("sum(sumDuration)","sumDuration"))
                .addAgg(Aggregation.of("sum(maxDuration)","maxDuration"))
                .addAgg(Aggregation.of("sum(errorCnt)","errorCnt"));

        // Add conditions based on direction (in/out)
        if (isIn) {
            queryBuilder.addWhere(Where.eq("isIn", "1"))
                    .addWhere(Where.eq("dst_biz_pid", parentBizId))
                    .addGroupBy("dst_biz_id");
        } else {
            queryBuilder.addWhere(Where.eq("isOut", "1"))
                    .addWhere(Where.eq("src_biz_pid", parentBizId))
                    .addGroupBy("src_biz_id");
        }

        return queryBuilder;
    }

    // Updated to work with TSDBResultSet instead of List<Result>
    private void processMetricResults(TSDBResultSet results, RelationDetailResponse response, double timeRangeFactor) {
        if (results != null && !results.getResults().isEmpty() && results.getResults().get(0) != null &&
                results.getResults().get(0).getSeries() != null && !results.getResults().get(0).getSeries().isEmpty()) {

            TSDBSeries series = results.getResults().get(0).getSeries().get(0);
            if (!series.getValues().isEmpty()) {
                Map<String, Object> valueMap = new HashMap<>();
                List<Object> values = series.getValues().get(0);

                for (int i = 0; i < series.getColumns().size(); i++) {
                    valueMap.put(series.getColumns().get(i), values.get(i));
                }

                // Extract values, handling potential variations in column names
                Long callCnt = TsDbExtractor.extractLongValue(valueMap, "sum_cnt", "allCnt", "callCnt", "cnt");
                Long errorCnt = TsDbExtractor.extractLongValue(valueMap, "sum_errorCnt", "errorCnt", "errCnt");
                Long sumDuration = TsDbExtractor.extractLongValue(valueMap, "sum_sumDuration", "sumDuration");
                Long maxDuration = TsDbExtractor.extractLongValue(valueMap, "sum_maxDuration", "maxDuration", "maxLatency");

                if (callCnt != null) {
                    response.setRequestVolume(response.getRequestVolume() + callCnt);
                }

                if (errorCnt != null) {
                    response.setErrorCnt(response.getErrorCnt() + errorCnt);
                }

                if (sumDuration != null) {
                    response.setSumDuration(response.getSumDuration() + sumDuration);
                }

                if (callCnt != null) {
                    // Calculate reqRate using the time factor
                    double reqRate = callCnt / timeRangeFactor;
                    response.setReqRate(response.getReqRate() + reqRate);
                }

                if (maxDuration != null) {
                    response.setMaxResponseTime(Math.max(response.getMaxResponseTime(), maxDuration));
                }

                if (response.getRequestVolume() > 0) {
                    response.setAverageResponseTime((double) response.getSumDuration() / response.getRequestVolume());
                    response.setErrorRate((double) response.getErrorCnt() / response.getRequestVolume());
                }
            }
        }
    }

    // Updated to work with TSDBResultSet instead of List<Result>
    private void processSubsystemTraffic(TSDBResultSet results, Map<String, RelationDetailResponse> subsystemMetrics, boolean isIn, double timeRangeFactor) {
        if (results != null && !results.getResults().isEmpty() && results.getResults().get(0) != null &&
                results.getResults().get(0).getSeries() != null && !results.getResults().get(0).getSeries().isEmpty()) {

            for (TSDBSeries series : results.getResults().get(0).getSeries()) {
                if (!series.getTags().isEmpty() && !series.getValues().isEmpty()) {
                    String subsystemId = isIn ? series.getTags().get("dst_biz_id") : series.getTags().get("src_biz_id");
                    RelationDetailResponse response = subsystemMetrics.computeIfAbsent(subsystemId, k -> {
                        RelationDetailResponse newResponse = new RelationDetailResponse();
                        newResponse.setRequestVolume(0L);
                        newResponse.setErrorCnt(0L);
                        newResponse.setSumDuration(0L);
                        newResponse.setReqRate(0.0);
                        newResponse.setMaxResponseTime(0L);
                        newResponse.setErrorRate(0.0);
                        return newResponse;
                    });

                    Map<String, Object> valueMap = new HashMap<>();
                    List<Object> values = series.getValues().get(0);

                    for (int i = 0; i < series.getColumns().size(); i++) {
                        valueMap.put(series.getColumns().get(i), values.get(i));
                    }

                    // Extract values, handling potential variations in column names
                    Long callCnt = TsDbExtractor.extractLongValue(valueMap, "sum_cnt", "allCnt", "callCnt", "cnt");
                    Long errorCnt = TsDbExtractor.extractLongValue(valueMap, "sum_errorCnt", "errorCnt", "errCnt");
                    Long sumDuration = TsDbExtractor.extractLongValue(valueMap, "sum_sumDuration", "sumDuration");
                    Long maxDuration = TsDbExtractor.extractLongValue(valueMap, "sum_maxDuration", "maxDuration", "maxLatency");

                    if (callCnt != null) {
                        response.setRequestVolume(response.getRequestVolume() + callCnt);
                    }

                    if (errorCnt != null) {
                        response.setErrorCnt(response.getErrorCnt() + errorCnt);
                    }

                    if (sumDuration != null) {
                        response.setSumDuration(response.getSumDuration() + sumDuration);
                    }

                    if (callCnt != null) {
                        // Calculate reqRate using the time factor
                        double reqRate = callCnt / timeRangeFactor;
                        response.setReqRate(response.getReqRate() + reqRate);
                    }

                    if (maxDuration != null) {
                        response.setMaxResponseTime(Math.max(response.getMaxResponseTime(), maxDuration));
                    }

                    if (response.getRequestVolume() > 0) {
                        response.setAverageResponseTime((double) response.getSumDuration() / response.getRequestVolume());
                        response.setErrorRate((double) response.getErrorCnt() / response.getRequestVolume());
                    }
                }
            }
        }
    }

    private Map<String, Long> getAlarmCountsByBusName(List<Map<String, Object>> alarms) {
        return alarms.stream()
                .filter(map -> map.get("busName") != null) // 避免空指针 ,后续需要确认原因
                .collect(Collectors.groupingBy(
                map -> (String) map.get("busName"),
                Collectors.summingLong(map -> (Long) map.getOrDefault("cnt", 0L))
        ));
    }

    private void setAlarmStatus(RelationDetailResponse response, List<Map<String, Object>> alarms) {
        long totalAlarms = alarms.stream()
                .mapToLong(map -> (Long) map.get("cnt"))
                .sum();
        response.setErrorType(totalAlarms > 0 ? 1 : 0);
    }

    private List<RelationDetailResponse> createSubsystemResponses(String parentBizId,
                                                                  List<Business> businesses,
                                                                  Map<Integer, String> businessMap,
                                                                  Map<String, RelationDetailResponse> subsystemMetrics,
                                                                  Map<String, Long> busNameToCnt,
                                                                  Set<Integer> validSubsystemIds) {
        List<RelationDetailResponse> responses = new ArrayList<>();
        String parentName = businessMap.get(Integer.valueOf(parentBizId));

        // 仅遍历那些既属于当前业务系统（b.getPid() == parentBizId），又在 validSubsystemIds 集合中的子系统
        for (Business b : businesses) {
            if (b.getPid().equals(Integer.valueOf(parentBizId)) && validSubsystemIds.contains(b.getId())) {
                RelationDetailResponse response = subsystemMetrics.getOrDefault(b.getId().toString(), new RelationDetailResponse());
                response.setId(b.getId().toString());
                response.setName(b.getName());

                String childName = b.getName();
                Long alarmCnt = busNameToCnt.get(parentName + "-" + childName);
                //只要有告警就是不健康
                // 如果 alarmCnt 为 null 或者为 0，则认为没有告警，是健康的状态
                response.setErrorType(alarmCnt != null && alarmCnt > 0 ? 1 : 0);

                responses.add(response);
            }
        }

        return responses;
    }
}