package com.databuff.webapp.apm.model;

import com.databuff.common.utils.DateUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import static com.databuff.common.utils.TimeUtil.OUT_DAY_MS_LONG;

/**
 * @author:wz
 * @date: 2022/8/24
 * @desc: 业务topo查询参数
 */
@Data
public class BusinessTopoSearch {

    @ApiModelProperty(value = "开始时间",example = "2021-07-15 00:00:00")
    private String fromTime;
    @ApiModelProperty(value = "结束时间",example = "2021-07-15 23:59:59")
    private String toTime;
    @ApiModelProperty("apiKey")
    private String apiKey;
    @ApiModelProperty("topoType")
    private Integer topoType;
    @ApiModelProperty("系统id（顶层为0，子系统/父系统id）")
    private Integer id;
    @ApiModelProperty("账号")
    private String account;

    public Long getFromTimeVul(){
        if (StringUtils.isBlank(this.fromTime)){
            return DateUtils.strToDate("yyyy-MM-dd HH:mm:ss", this.fromTime).getTime() - OUT_DAY_MS_LONG;
        }
        return DateUtils.strToDate("yyyy-MM-dd HH:mm:ss",this.fromTime).getTime();
    }
    public Long getToTimeVul(){
        return DateUtils.strToDate("yyyy-MM-dd HH:mm:ss",this.toTime).getTime();
    }
}
