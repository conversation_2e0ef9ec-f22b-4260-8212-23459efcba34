package com.databuff.webapp.apm.service.impl.business.system;

import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.utils.DateUtils;
import com.databuff.webapp.apm.model.BusinessTargetType;
import com.databuff.webapp.apm.model.RelationDetailRequest;
import com.databuff.webapp.apm.model.RelationDetailResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DownstreamRelationService extends AbstractRelationHandler {

    @Override
    protected List<RelationDetailResponse> handleWithFilter(RelationDetailRequest search, String bizInternalCallFilter, boolean isParentBizId) throws Exception {
        Set<String> bizIds = new HashSet<>();
        bizIds.add(search.getBizId());
        if (isParentBizId) {
            bizIds.addAll(businessMapper.getSubSystemIdsByPId(Integer.valueOf(search.getBizId()))
                    .stream()
                    .map(String::valueOf)
                    .collect(Collectors.toSet()));
        }

        List<String> bizIdsList = new ArrayList<>(bizIds);
        Long startTime = DateUtils.tsdbTimeToMilliseconds(search.getFromTime());
        Long endTime = DateUtils.tsdbTimeToMilliseconds(search.getToTime());
        boolean bizInternalCall = !bizInternalCallFilter.contains("'0'"); // Convert filter string to boolean

        BusinessTargetType targetType = search.getTargetType();
        QueryBuilder queryBuilder;

        if (BusinessTargetType.BUSINESS_SYSTEM == targetType) {
            queryBuilder = BusinessSystemQueryProvider.getDownstreamBusinessSystemQuery(
                    search.getApiKey(), true, bizIdsList, bizInternalCall, startTime, endTime);
            return handleBusinessSystemQuery(search, queryBuilder, "dst_biz_id", "dst_biz_pid");
        } else if (targetType == BusinessTargetType.DB) {
            queryBuilder = BusinessSystemQueryProvider.getDownstreamDatabaseQuery(
                    search.getApiKey(), true, bizIdsList, bizInternalCall, startTime, endTime);
            return handleServiceQuery(search, queryBuilder, "serviceId");
        } else if (targetType == BusinessTargetType.MQ) {
            queryBuilder = BusinessSystemQueryProvider.getDownstreamMQQuery(
                    search.getApiKey(), true, bizIdsList, bizInternalCall, startTime, endTime);
            return handleServiceQuery(search, queryBuilder, "serviceId");
        } else if (targetType == BusinessTargetType.CACHE) {
            queryBuilder = BusinessSystemQueryProvider.getDownstreamCacheQuery(
                    search.getApiKey(), true, bizIdsList, bizInternalCall, startTime, endTime);
            return handleServiceQuery(search, queryBuilder, "serviceId");
        } else if (targetType == BusinessTargetType.SERVICE) {
            queryBuilder = BusinessSystemQueryProvider.getDownstreamServiceQuery(
                    search.getApiKey(), true, bizIdsList, bizInternalCall, startTime, endTime);
            return handleServiceQuery(search, queryBuilder, "serviceId");
        } else {
            throw new IllegalArgumentException("不支持的目标类型: " + targetType);
        }
    }
}