package com.databuff.webapp.apm.model;

import com.databuff.metric.dto.MetricByGroupGraph;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 三大指标趋势图
 * <AUTHOR>
 * @date 2024/05/21
 */
@Data
public class BusinessMetricsResponse {

    @ApiModelProperty("请求数-成功请求数")
    List<MetricByGroupGraph> succReqCounts;
    @ApiModelProperty("请求数-失败请求数")
    List<MetricByGroupGraph> errReqCounts;
    @ApiModelProperty("错误率-不同错误类型的失败请求数量")
    List<MetricByGroupGraph> typeErrCounts;
    @ApiModelProperty("错误率-总的错误率")
    List<MetricByGroupGraph> errRates;
    @ApiModelProperty("响应时间-响应时间")
    List<MetricByGroupGraph> avgTimes;
    @ApiModelProperty("响应时间-总的请求数量")
    List<MetricByGroupGraph> reqCounts;

}
