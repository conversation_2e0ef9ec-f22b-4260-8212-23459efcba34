package com.databuff.webapp.root.controller;

import com.databuff.entity.DcDatabuffIssueDetail;
import com.databuff.service.DcDatabuffIssueDetailService;
import com.databuff.webapp.config.interceptor.DomainManagerRequired;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/issues")
public class DcDatabuffIssueDetailController {

    @Autowired
    private DcDatabuffIssueDetailService service;

    @PostMapping
    @DomainManagerRequired
    public ResponseEntity<DcDatabuffIssueDetail> createIssueDetail(@RequestBody DcDatabuffIssueDetail detail) {
        DcDatabuffIssueDetail createdDetail = service.save(detail, null, DcDatabuffIssueDetail.Source.手动触发);
        return ResponseEntity.ok(createdDetail);
    }

    @GetMapping("/{id}")
    @DomainManagerRequired
    public ResponseEntity<DcDatabuffIssueDetail> getIssueDetailById(@PathVariable String id) {
        DcDatabuffIssueDetail detail = service.findById(id);
        if (detail != null) {
            return ResponseEntity.ok(detail);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @PutMapping("/{id}")
    @DomainManagerRequired
    public ResponseEntity<DcDatabuffIssueDetail> updateIssueDetail(@PathVariable String id, @RequestBody DcDatabuffIssueDetail detail) {
        if (service.findById(id) != null) {
            detail.setId(id);
            return ResponseEntity.ok(service.save(detail, null, DcDatabuffIssueDetail.Source.手动触发));
        } else {
            return ResponseEntity.notFound().build();
        }
    }
}