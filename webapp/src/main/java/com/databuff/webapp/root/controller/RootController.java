package com.databuff.webapp.root.controller;

import com.databuff.common.utils.StringUtil;
import com.databuff.entity.DcDatabuffIssueDetail;
import com.databuff.entity.DcDatabuffProblem;
import com.databuff.service.DatabuffProblemService;
import com.databuff.service.RootService;
import com.databuff.webapp.config.interceptor.DomainManagerRequired;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Api(value = "根因定位")
@RestController
@RequestMapping("/root")
public class RootController {

    @Resource
    private RootService rootService;
    @Resource
    private DatabuffProblemService databuffProblemService;

    /**
     * 触发定位分析
     **/
    @ApiOperation(value = "触发分析")
    @RequestMapping(value = "/analyse", method = RequestMethod.POST)
    @Transactional
    @DomainManagerRequired
    public DcDatabuffIssueDetail analyse(HttpServletRequest request, @RequestBody RootRequest rootRequest) {
        String apiKey = request.getAttribute("apiKey").toString();
        return rootService.analyseByClick(apiKey, rootRequest.getService(), rootRequest.getFromTime(), rootRequest.getToTime(), rootRequest.getDisableExpand(), DcDatabuffIssueDetail.Source.手动触发, false, false);
    }

    /**
     * 手动触发定位和影响面分析，以及返回RootDetail关联信息
     **/
    @ApiOperation(value = "触发分析")
    @RequestMapping(value = "/syncAnalyse", method = RequestMethod.POST)
    @Transactional
    @DomainManagerRequired
    public String syncAnalyse(HttpServletRequest request, @RequestBody RootRequest rootRequest) {
        String apiKey = request.getAttribute("apiKey").toString();
        DcDatabuffIssueDetail dcDatabuffIssueDetail = rootService.analyseByClick(apiKey, rootRequest.getService(), rootRequest.getFromTime(), rootRequest.getToTime(), rootRequest.getDisableExpand(), DcDatabuffIssueDetail.Source.手动触发, true, true);
        if (dcDatabuffIssueDetail != null) {
            String problemId = dcDatabuffIssueDetail.getProblemId();
            if (StringUtil.isNotEmpty(problemId)) {
                DcDatabuffProblem dcDatabuffProblem = databuffProblemService.findById(problemId);
                if (dcDatabuffProblem != null && dcDatabuffProblem.getInfluence() != null) {
                    return "success";
                }
            }
        }
        return "当前时间点服务无异常";
    }
}
