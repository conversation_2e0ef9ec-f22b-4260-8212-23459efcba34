package com.databuff.webapp.report.model;

import java.util.Date;
import java.util.List;
import javax.persistence.Column;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * @description 报告模版
 * <AUTHOR>
 * @date 2023-07-05
 */
@Data
public class ReportTemplateEntity {

    /**
     * 报告模版名称
     */
    private String name;

    /**
     * 报告模版id
     */
    private Integer id;

    /**
     * 报告类型：1-日报，2-周报，3-自定义
     */
    private Integer type;

    /**
     * 报告模版是否启用：0-停用，1-启用
     */
    private Integer status;

    /**
     * 报告模版创建时间
     */
    @Column(name="create_time")
    private Date createTime;

    /**
     * 报告模版更新时间
     */
    @Column(name="update_time")
    private Date updateTime;

    /**
     * 报告模版待生成的周期时间
     * { type: 'week'|'day', day: 1-7, time: 'HH:mm:ss', range: 1|-1|7|-7 }
     * type: 周期类型，week-周，day-日
     * day: 周期类型为周时，表示周几，1-7分别表示周一到周日；周期类型为日时，表示每日
     * time: 周期时间，格式为hh:mm:ss
     * range: 周期范围，1表示当天，-1表示前一天，7表示本周，-7表示前一周
     */
    @Column(name="cycle_time")
    private String cycleTime;

    /**
     * 报告模版待生成的自定义时间
     * { execute: 'YYYY-MM-DD HH:mm:ss', fromTime: 'YYYY-MM-DD HH:mm:ss', toTime: 'YYYY-MM-DD HH:mm:ss' }
     */
    @Column(name="custom_time")
    private String customTime;

    /**
     * 报告模版内容
     */
    private String content;

    /**
     * 报告接收人
     */
    private String receivers;

    /**
     * 是否启用邮件通知
     */
    private Integer emailable;

    /**
     * 是否启用钉钉机器人
     */
    private Integer dingable;

    /**
     * 报告模版创建者
     */
    private String creator;
    /**
     * 报告模版创建者
     */
    @Column(name="api_key")
    private String apiKey;
}
