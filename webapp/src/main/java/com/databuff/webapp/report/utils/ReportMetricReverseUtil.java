package com.databuff.webapp.report.utils;


import com.alibaba.fastjson.JSONObject;

import java.text.CharacterIterator;
import java.text.StringCharacterIterator;
import java.util.HashMap;


public class ReportMetricReverseUtil {
    private static HashMap<String,String> siFormat;
    private static HashMap<String,Double> timeFormat;
    private static HashMap<String,Double> byteFormat;
    static {
        siFormat = new HashMap<>(8);
        siFormat.put("garbage collection","gc");
        siFormat.put("garbage collections","gc");
        siFormat.put("thread","");
        siFormat.put("threads","");
        siFormat.put("packet","pkt");
        siFormat.put("packets","pkt");
        siFormat.put("request","req");
        siFormat.put("requests","req");
        timeFormat = new HashMap<>(8);
        timeFormat.put("nanosecond",1e-9);
        timeFormat.put("nanoseconds",1e-9);
        timeFormat.put("ns",1e-9);
        timeFormat.put("microsecond",0.000001);
        timeFormat.put("microseconds",0.000001);
        timeFormat.put("μs",0.000001);
        timeFormat.put("millisecond",0.001);
        timeFormat.put("milliseconds",0.001);
        timeFormat.put("ms",0.001);
        timeFormat.put("second",1.0);
        timeFormat.put("seconds",1.0);
        timeFormat.put("s",1.0);
        byteFormat = new HashMap<>(8);
        byteFormat.put("bit",0.125);
        byteFormat.put("bits",0.125);
        byteFormat.put("TiB",1099511627776.0);
        byteFormat.put("GiB",1073741824.0);
        byteFormat.put("mebibyte",1048576.0);
        byteFormat.put("mebibytes",1048576.0);
        byteFormat.put("MiB",1048576.0);
        byteFormat.put("kibibyte",1024.0);
        byteFormat.put("kibibytes",1024.0);
        byteFormat.put("KiB",1024.0);
        byteFormat.put("B",1.0);
        byteFormat.put("byte",1.0);
        byteFormat.put("bytes",1.0);
    }

    private static JSONObject result = new JSONObject();

    /**
     * 推断数值的缩放倍率
     * @param value
     * @return
     */
    public static JSONObject humanReadableFormat(Double value, String format) {
        result.put("scale", 1L);
        result.put("unit", "");
        result.put("extraScale", 1.0);
        Long val = humanReadableFormat(value, format,  null);
        if (val != null) {
            result.put("scale",val);
        }
        return result;
    }

    /**
     * 推断数值的缩放倍率
     * @param value
     * @return
     */
    public static Long humanReadableFormat(Double value, String unit, String format) {
        // 校验seconds
        if (value == Double.POSITIVE_INFINITY || value == Double.NEGATIVE_INFINITY){
            return 1L;
        }
        if ("".equals(unit) || unit == null){
            // 默认以1000进制处理
            return  humanReadableByteCountSI(value,"");
        }
        if ("%".equals(unit) || "percent".equals(unit)){
            // %
            result.put("unit", "%");
            return  humanReadableByteCountSI(value,"");
        }
        if ("fractions".equals(unit) || "fraction".equals(unit)){
            // fractions(%)
            return  humanReadableByteCountSI(value*100,"");
        }
        if (siFormat.containsKey(unit)){
            // 其他缩写单位
            return  humanReadableByteCountSI(value,siFormat.get(unit));
        }
        if (timeFormat.containsKey(unit)){
            // 时间,统一格式化到秒
            value = value * timeFormat.get(unit);
            result.put("extraScale", timeFormat.get(unit));
            return  humanReadableSeconds(value);
        }
        if (byteFormat.containsKey(unit)){
            // Bytes,统一格式化到B
            value = value * byteFormat.get(unit);
            result.put("extraScale", byteFormat.get(unit));
            return  humanReadableByteCountBin(value,format);
        }
        if ("°C".equals(unit)){
            // 摄氏度
            return  humanReadableByteCountSI(value,"°C");
        }
        if (unit.startsWith("!")){
            // 带!默认以1000进制处理，且去掉!
            return  humanReadableByteCountSI(value,unit.replace("!",""));
        }
        return  humanReadableByteCountSI(value,unit);
    }

    /**
     * 时间格式化
     * @param value 秒级
     * @return
     */
    private static Long humanReadableSeconds(Double value) {
        if (value < 0.000001){
            // return String.format("%.2f"+ "ns",value/1e-9);
            result.put("unit", "ns");
            return -(Math.round(Math.pow(10, 9)));
        }
        if (value < 0.001){
            // return String.format("%.2f"+ "us",value/0.000001);
            result.put("unit", "us");
            return -(Math.round(Math.pow(10,6)));
        }
        if (value < 1){
            // return String.format("%.2f"+ "ms",value/0.001);
            result.put("unit", "ms");
            return -1000L;
        }
        if (value < 60){
            // return String.format("%.2f"+ "s",value);
            result.put("unit", "s");
            return 1L;
        }
        if (value < 3600){
            // return String.format("%.2f"+ "min",value/60);
            result.put("unit", "min");
            return 60L;
        }
        if (value < 86400){
            // return String.format("%.2f"+ "h",value/3600);
            result.put("unit", "h");
            return 3600L;
        }
        if (value < 2592000){
            // return String.format("%.2f"+ "d",value/86400);
            result.put("unit", "d");
            return 86400L;
        }
        if (value < 31536000){
            // return String.format("%.2f"+ "M",value/2592000);
            result.put("unit", "M");
            return 2592000L;
        }
        // return String.format("%.2f"+ "y",value/31536000);
        result.put("unit", "y");
        return 31536000L;
    }

    public static Boolean isTimeFormat (String unit) {
        return timeFormat.containsKey(unit);
    }

    public static Boolean isByteFormat (String unit) {
        return byteFormat.containsKey(unit);
    }


    /**
     * bits进制转换倍率或十进制转换倍率
     * @param value
     * @param format
     * @return
     */
    private static Long humanReadableByteCountSI(double value, String format) {
        long SI = 1000;
        if (-SI < value && value < SI) {
            return 1L;
        }
        CharacterIterator ci = new StringCharacterIterator("kMGTPE");
        while (value <= -999_950 || value >= 999_950) {
            value /= 1000;
            ci.next();
        }
        result.put("unit", ci.current());
        int index = "kMGTPE".indexOf(ci.current());
        return Math.round(Math.pow(1000, index + 1));
    }

    /**
     * bytes可读
     * @param bytes
     * @param format
     * @return
     */
    private static Long humanReadableByteCountBin(Double bytes, String format) {
        Double absB = bytes == Double.MIN_VALUE ? Double.MAX_VALUE : Math.abs(bytes);
        double BIN = 1024d;
        if (absB < BIN) {
            return 1L;
        }
        // 保留两位小数
        long value = absB.longValue();
        CharacterIterator ci = new StringCharacterIterator("KMGTPE");
        for (int i = 40; i >= 0 && absB > 0xfffccccccccccccL >> i; i -= 10) {
            value >>= 10;
            ci.next();
        }
        result.put("unit", ci.current());
        int index = "kMGTPE".indexOf(ci.current());
        return Math.round(Math.pow(1000, index + 1));
    }
}
