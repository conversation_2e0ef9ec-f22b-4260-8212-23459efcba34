package com.databuff.webapp.report.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.databuff.common.exception.CustomException;
import com.databuff.common.utils.StringUtil;
import com.databuff.common.utils.email.MailConfig;
import com.databuff.common.utils.email.MailUtil;
import com.databuff.entity.NotifyConfig;
import com.databuff.webapp.config.common.CommonResponse;
import com.databuff.webapp.report.mapper.ReportMapper;
import com.databuff.webapp.report.model.ReportEntity;
import com.databuff.webapp.report.model.ReportSearch;
import com.databuff.webapp.report.model.ReportTemplateEntity;
import com.databuff.webapp.report.service.ReportService;
import com.databuff.webapp.report.utils.ReportWordUtil;
import com.databuff.webapp.util.FileUtil;
import com.databuff.webapp.util.ListPageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.databuff.dao.mysql.NotifyMapper;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.List;

@Service
@Slf4j
public class ReportImpl implements ReportService {

    @Autowired
    @Qualifier("webappReportMapper")
    private ReportMapper reportMapper;
    @Autowired
    private ReportWordUtil reportWordUtil;
    @Resource
    private NotifyMapper notifyMapper;

    private final static String outPutDirectory = "/var/dacheng/staticDir/report";

    /**
     * 获取报告列表
     */
    @Override
    public CommonResponse getReportList(ReportSearch queryJson) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        Integer pageNum = queryJson.getPageNum();
        Integer pageSize = queryJson.getPageSize();


        List<ReportEntity> reportList = reportMapper.getReportList(queryJson);
        JSONObject jsonObject = new JSONObject();
        if (reportList != null) {
            // 分页
            List<ReportEntity> listPageUtil = ListPageUtil.startPage(reportList, pageNum, pageSize);
            jsonObject.put("list", listPageUtil);
            jsonObject.put("total", reportList.size());
            commonResponse.setData(jsonObject);
        } else {
            commonResponse.setMessage("获取报告列表失败");
        }
        return commonResponse;
    }

    /**
     * 获取报告模版列表
     */
    @Override
    public CommonResponse getReportTemplateList(ReportSearch queryJson) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String name = queryJson.getName();
        Integer type = queryJson.getType();
        Integer pageNum = queryJson.getPageNum();
        Integer pageSize = queryJson.getPageSize();

        List<ReportTemplateEntity> reportList = reportMapper.getReportTemplateList(name, type);
        JSONObject jsonObject = new JSONObject();
        if (reportList != null) {
            // 分页
            List<ReportEntity> listPageUtil = ListPageUtil.startPage(reportList, pageNum, pageSize);
            jsonObject.put("list", listPageUtil);
            jsonObject.put("total", reportList.size());
            commonResponse.setData(jsonObject);
        } else {
            commonResponse.setMessage("获取报告列表失败");
        }
        return commonResponse;
    }

    /**
     * 获取报告模版详情
     */
    @Override
    public CommonResponse getTemplateById(Integer id) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        if (id != null) {
            ReportTemplateEntity templateById = reportMapper.getTemplateById(id);
            commonResponse.setData(templateById);
        } else {
            commonResponse.setMessage("获取报告模版详情失败");
        }
        return commonResponse;
    }


    /**
     * 添加报告模版
     */
    @Override
    public CommonResponse addTemplate(ReportTemplateEntity queryJson) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String name = queryJson.getName();
        Integer type = queryJson.getType();
        Integer id = queryJson.getId();
        String cycleTime = queryJson.getCycleTime();
        String customTime = queryJson.getCustomTime();
        String content = queryJson.getContent();
        if (StringUtil.isBlank(name)) {
            commonResponse.setMessage("报告模版名称不能为空");
            return commonResponse;
        }
        // 判断是否重名
        ReportTemplateEntity sameNameTemp = reportMapper.getTemplateByName(name);
        if (id == null && sameNameTemp != null) {
            commonResponse.setMessage("报告模版名称不能重复");
            return commonResponse;
        }
        if (type != 3 && StringUtil.isBlank(cycleTime)) {
            commonResponse.setMessage("报告模版周期不能为空");
            return commonResponse;
        }
        if (type == 3 && StringUtil.isBlank(customTime)) {
            commonResponse.setMessage("报告模版自定义时间不能为空");
            return commonResponse;
        }
        if (StringUtil.isBlank(content)) {
            commonResponse.setMessage("报告模版内容不能为空");
            return commonResponse;
        }

        if (id == null) {
            Integer rst = reportMapper.addTemplate(queryJson);
            if (rst == 0) {
                commonResponse.setMessage("添加失败");
                return commonResponse;
            }
        } else {
            reportMapper.editTemplate(queryJson);
        }
        commonResponse.setData(queryJson);
        return commonResponse;
    }

    @Override
    public CommonResponse editTemplate(ReportTemplateEntity queryJson) throws Exception {
        return addTemplate(queryJson);
    }

    @Override
    public CommonResponse deleteTemplate(Integer id) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        if (id != null) {
            Integer rst = reportMapper.deleteTemplate(id);
            if (rst == 1) {
                commonResponse.setData("删除成功");
            } else {
                commonResponse.setMessage("删除失败");
            }
        }
        return commonResponse;
    }

    @Override
    public CommonResponse toggleStatus(ReportTemplateEntity queryJson) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        Integer id = queryJson.getId();
        Integer status = queryJson.getStatus();
        if (id != null && status != null) {
            Integer rst = reportMapper.toggleStatus(id, status);
            if (rst == 1) {
                commonResponse.setData("修改成功");
            } else {
                commonResponse.setMessage("修改失败");
            }
        }
        return commonResponse;
    }


    @Override
    public void download(Integer id, HttpServletRequest req, HttpServletResponse response) throws Exception {
        // 从服务器中下载报告
        // docker内部容器文件路径：/var/dacheng/staticDir
        ReportEntity reportEntity = reportMapper.getReportById(id);
        if (reportEntity != null) {
            String fileName = reportEntity.getName() + ".docx";
            String filePath = "/var/dacheng/staticDir/report/" + fileName;
//            log.info("fileName : {}", fileName);
//            log.info("filePath : {}", filePath);
            File file = new File(filePath);

            try (FileInputStream fileInputStream = new FileInputStream(file)) {
                if (!file.exists()) {
                    // 更新报告记录状态为不存在
                    reportMapper.updateReportStatus(id, 3);
                    throw new CustomException("报告文件不存在！");
                }
                String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
                response.reset();
                // 设置response的Header
                response.setHeader("Content-Disposition", "attachment; filename=\""+ encodedFileName + "\"");
                response.setContentType("application/octet-stream");
                FileUtil.writeIntoOut(fileInputStream, response.getOutputStream(), 1024, false);
            } catch (FileNotFoundException e) {
                log.error("下载失败 ", e);
                // 更新报告记录状态为不存在
                reportMapper.updateReportStatus(id, 3);
                throw new CustomException("下载失败，文件未找到！");
            } catch (IOException e) {
                log.error("下载失败，IO异常 ",e);
                // 更新报告记录状态为不存在
                reportMapper.updateReportStatus(id, 3);
                throw new CustomException("下载失败，IO异常！");
            }
        } else {
            throw new CustomException("报告记录不存在");
        }

    }


    @Override
    public void preview(Integer id, HttpServletRequest req, HttpServletResponse response) throws Exception {

    }


    @Override
    public void generateRecord(Integer templateId) throws Exception {
        // 获取模版详情
        ReportTemplateEntity template = reportMapper.getTemplateById(templateId);
        if (template != null) {
            // 生成一条记录在dc_report表中
            ReportEntity reportEntity = new ReportEntity();
            reportEntity.setName(template.getName());
            reportEntity.setType(template.getType());
            reportEntity.setTemplate(JSON.toJSONString(template));
            reportEntity.setApiKey(template.getApiKey());

            Integer rst = reportMapper.addReport(reportEntity);
            if (rst == 1) {
                log.info("生成一条记录");
                log.info("记录ID: {}", reportEntity.getId());
            }
        }
    }

    @Override
    public void taskDownload(ReportTemplateEntity template, String outputName) throws Exception {
        if (template.getId() != null) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("template", template);
            try {
                reportWordUtil.initDoc(jsonObject);
                XWPFDocument doc = reportWordUtil.generateReport();
                String fileName = outputName + ".docx";
                // 判断目录是否存在
                File reportDir = new File(outPutDirectory);
                if (!reportDir.exists()) {
                    reportDir.mkdirs();
                }
                // 将文档保存到服务器指定目录下
                File file = new File(outPutDirectory + "/" + fileName);
                FileOutputStream outputStream = new FileOutputStream(file);
                doc.write(outputStream);
                outputStream.close();
                doc.close();
                log.info("保存成功！");
            } catch (FileNotFoundException e) {
                // 更新报告状态为生成失败
//                reportMapper.updateReportStatus(id, 2);
                throw new RuntimeException("保存失败！", e);
            } catch (IOException e) {
                log.info(e.getMessage());
//                reportMapper.updateReportStatus(id, 2);
                throw new RuntimeException("保存失败，IO异常: {}", e);
            }
        } else {
            throw new RuntimeException("报告模版不存在");
        }
    }

    @Override
    public void sendMail(ReportTemplateEntity template, String name) throws Exception {
        if (template.getEmailable()==1&& StringUtils.isNotBlank(template.getReceivers())){
            NotifyConfig notifyConfig = notifyMapper.getNotifyConfig(template.getApiKey(), "mail",null);
            if (notifyConfig!=null && notifyConfig.getEnable()==1){
                MailConfig mailConfig = new MailConfig(notifyConfig.getMailHost(),notifyConfig.getMailPort(),notifyConfig.getMailSsl()==1,notifyConfig.getMailSender(),notifyConfig.getMailNick(),notifyConfig.getMailSenderPwd());
                mailConfig.setCode(notifyConfig.getMailSecretCode());
                MailUtil.send(template.getReceivers(),name,name,outPutDirectory + "/" + name+".docx",mailConfig);
            }else{
                log.warn("报告未发送，请启动邮件发送开关");
            }
        }
    }

}
