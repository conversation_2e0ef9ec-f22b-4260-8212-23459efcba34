package com.databuff.webapp.metrics.controller;

import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.metric.MetricInstanceOperator;
import com.databuff.webapp.config.common.CommonResponse;
import com.databuff.webapp.config.interceptor.DomainManagerRequired;
import com.databuff.webapp.metrics.service.MetricsService;
import com.google.common.collect.Sets;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

import static com.databuff.metric.moredb.SQLParser.API_KEY;

/**
 * @package com.databuff.webapp.metrics.controller
 * @company: dacheng
 * @author: zlh
 * @createDate: 2021/7/22
 * @see com.databuff.webapp.metrics.controller.MetricsControllerV2
 * @deprecated
 */
@RestController
@RequestMapping("/metrics")
@Api(value = "指标")
@Deprecated
public class MetricsController {

    @Autowired
    private MetricsService metricsService;

    @Autowired
    private MetricInstanceOperator metricInstanceOperator;

    @ApiOperation(value = "查询指标详情", notes = "参数：metric,startTime,endTime")
    @PostMapping(value = "/findMetricDetail")
    @DomainManagerRequired
    public CommonResponse<Object> findMetricDetail(HttpServletRequest request, @RequestBody QueryRequest queryRequest) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        queryRequest.setApiKey(apiKey);
        commonResponse.setData(metricInstanceOperator.findMetricDetail(queryRequest, true, true));
        return commonResponse;
    }

    @ApiOperation(value = "根据指标查询标签", notes = "参数：metricList:[],tagList[],startTime,endTime")
    @PostMapping(value = "/findTagsByMetricsAndTags")
    @Deprecated
    @DomainManagerRequired
    public CommonResponse<Object> findTagsByMetricsAndTags(HttpServletRequest request, @RequestBody QueryRequest queryRequest) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        queryRequest.setApiKey(apiKey);
        commonResponse.setData(metricsService.findTagValues(queryRequest, Sets.newHashSet(), false));
        return commonResponse;
    }

}
