package com.databuff.webapp.metrics.controller;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.tsdb.TSDBOperateUtil;
import com.databuff.common.tsdb.dto.CompositeCondition;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.common.tsdb.model.LogicalOperator;
import com.databuff.common.tsdb.model.TSDBSeries;
import com.databuff.common.tsdb.model.WhereOp;
import com.databuff.entity.MetricMetaData;
import com.databuff.entity.MetricsQuery;
import com.databuff.metric.MetricAggregator;
import com.databuff.metric.dto.MetricDTO;
import com.databuff.service.MetricsQueryService;
import com.databuff.webapp.config.common.CommonResponse;
import com.databuff.webapp.config.interceptor.DomainManagerRequired;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

import static com.databuff.common.constants.Constant.Npm.*;
import static com.databuff.common.constants.TSDBIndex.TSDB_NPM_METRIC_DATABASE_NAME;
import static com.databuff.common.constants.TSDBIndex.TSDB_NPM_METRIC_TABLE_NAME;
import static com.databuff.entity.MetricMetaData.*;
import static com.databuff.metric.moredb.SQLParser.API_KEY;

/**
 * @package com.databuff.webapp.metrics.controller
 * @company: dacheng
 * @author: zlh
 * @createDate: 2021/7/22
 */
@RestController
@RequestMapping("/metrics/npm")
@Api(value = "网络指标")
public class NPMController {

    // 定义客户端和服务器标签的预定义键集合
    final Set<String> clientKeys = Sets.newHashSet("laddr_cid", "laddr_cluster_id", "laddr_cname", "laddr_deployment", "laddr_hostname", "laddr_ip", "laddr_pod_name", "laddr_port", "laddr_svc_id", "laddr_svc_instance", "laddr_svc_name", "direction", "dns", "intra_host", "ip_type", "network_family", "protocols");
    final Set<String> serverKeys = Sets.newHashSet("raddr_cid", "raddr_cluster_id", "raddr_cname", "raddr_deployment", "raddr_hostname", "raddr_ip", "raddr_pod_name", "raddr_port", "raddr_svc_id", "raddr_svc_instance", "raddr_svc_name", "direction", "dns", "intra_host", "ip_type", "network_family", "protocols");

    @Autowired
    protected MetricsQueryService metricsQueryService;

    @Autowired
    private MetricAggregator metricAggregator;

    @Autowired
    protected TSDBOperateUtil tsdbOperateUtil;


    private Collection<String> buildNPMTopoMetrics() {
        MetricMetaData[] metricData = {
                VOLUME_SENT,
                VOLUME_RCVD,
                MetricMetaData.TCP_RETRANSMIT,
                TCP_LATENCY,
                MetricMetaData.TCP_JITTER,
                MetricMetaData.TCP_CONNS_CLOSED,
                MetricMetaData.TCP_CONNS_ESTABLISHED,
                MetricMetaData.NPM_DNS_RESPONSES_FAILED,
                MetricMetaData.NPM_DNS_RESPONSES_SUCCEEDED,
                MetricMetaData.NPM_DNS_TIMEOUTS,
                MetricMetaData.NPM_DNS_FAILURES,
                MetricMetaData.NPM_DNS_REQUESTS,
                MetricMetaData.NPM_DNS_ERRORS,
                MetricMetaData.NPM_DNS_ERRORS_SERVFAIL
        };
        final List<String> collect = Arrays.stream(metricData).map(MetricMetaData::getCode).collect(Collectors.toList());
//        final Map<String, MetricsQuery> queryMap = metricsQueryService.load(collect);

        return collect;
    }

    private Collection<String> createMetricsArray() {
        MetricMetaData[] metricData = {
                MetricMetaData.THROUGHPUT_SENT,
                MetricMetaData.THROUGHPUT_RCVD,
                VOLUME_SENT,
                VOLUME_RCVD,
                MetricMetaData.TCP_RETRANSMIT,
                MetricMetaData.TCP_RETRANSMIT_PCT,
                TCP_LATENCY,
                MetricMetaData.TCP_JITTER,
                MetricMetaData.RTT,
                MetricMetaData.TCP_CONNS_CLOSED,
                MetricMetaData.TCP_CONNS_CLOSED_RATE,
                MetricMetaData.TCP_CONNS_ESTABLISHED,
                MetricMetaData.TCP_CONNS_ESTABLISHED_RATE
        };
        final List<String> collect = Arrays.stream(metricData).map(MetricMetaData::getCode).collect(Collectors.toList());
        return collect;
    }

    @ApiOperation(value = "npm性能指标列表", notes = "参数：无")
    @GetMapping(value = "/performance/metrics")
    @DomainManagerRequired
    public CommonResponse<Object> npmPerformanceMetricList(HttpServletRequest request) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        Collection<MetricMetaData> metric = Lists.newArrayList(
                TCP_LATENCY,
                PACKETS_SENT,
                TCP_CONNS_CLOSED,
                TCP_CONNS_CLOSED_RATE,
                TCP_CONNS_ESTABLISHED,
                TCP_CONNS_ESTABLISHED_RATE,
                TCP_RETRANSMIT,
                TCP_RETRANSMIT_PCT,
                RTT,
                TCP_JITTER,
                THROUGHPUT,
                THROUGHPUT_SENT,
                THROUGHPUT_RCVD,
                VOLUME,
                VOLUME_SENT,
                VOLUME_RCVD
        );
        final List<String> metrics = metric.stream().map(MetricMetaData::getCode).collect(Collectors.toList());
        commonResponse.setData(metricsQueryService.queryIn(metrics));
        return commonResponse;
    }

    /**
     * {
     * "db": "npm",
     * "tb": "npm",
     * "select": "sum(packets_sent)/${interval} AS \"volume_sent\", mean(retransmits) AS \"TCP_Retransmit\", mean(rtt) AS \"RTT\"",
     * "period": 30000,
     * "interval": 60,
     * "by": ["laddr_hostname"],
     * "from": [{
     *     "left": "laddr_ip",
     *     "right": "127",
     *     "operator": "like"
     * }]
     * }
     *
     * @param queryRequest 查询参数
     * @return 性能指标响应
     */
    @ApiOperation(value = "查询npm性能指标（支持分组）", notes = "需要提供查询参数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "queryRequest", value = "查询参数", required = true, dataType = "QueryRequest", paramType = "body")
    })
    @PostMapping(value = "/performance")
    public CommonResponse<Collection<TSDBSeries>> npmPerformanceMetrics(HttpServletRequest request, @RequestBody QueryRequest queryRequest) {
        CommonResponse<Collection<TSDBSeries>> commonResponse = new CommonResponse<>();
        
        try {
            // 1. 参数校验
            if (queryRequest == null) {
                throw new IllegalArgumentException("查询参数不能为空");
            }
            
            // 2. 获取API密钥并校验
            String apiKey = getValidApiKey(request);
            queryRequest.setApiKey(apiKey);
            
            // 3. 设置默认指标（若为空）
            if (CollectionUtils.isEmpty(queryRequest.getMetrics())) {
                List<String> defaultMetrics = getDefaultMetrics();
                queryRequest.setMetrics(defaultMetrics);
            }
            
            // 4. 强制设置数据库和表名（业务要求）
            queryRequest.setDb(request.getAttribute("apiKey").toString() + TSDB_NPM_METRIC_DATABASE_NAME);
            queryRequest.setTb(TSDB_NPM_METRIC_TABLE_NAME);
            
            // 5. 执行查询并封装结果
            Map<Map<String, String>, TSDBSeries> seriesMap = metricAggregator.seriesResult(queryRequest);
            commonResponse.setData(new ArrayList<>(seriesMap.values()));
            
        } catch (IllegalArgumentException e) {
            commonResponse.setMessage(e.getMessage());
        } catch (Exception e) {
            commonResponse.setMessage("系统异常：" + e.getMessage());
        }
        
        return commonResponse;
    }
    
    /**
     * 获取有效的API密钥
     *
     * @param request 用于从中获取API密钥的HTTP请求对象
     * @return 有效的API密钥字符串
     * @throws IllegalArgumentException 如果API密钥未找到时抛出
     */
    private String getValidApiKey(HttpServletRequest request) {
        String apiKey = (String) request.getAttribute(API_KEY);
        
        // 检查API密钥是否为空，若为空则抛出异常
        if (StringUtils.isEmpty(apiKey)) {
            throw new IllegalArgumentException("API密钥未找到");
        }
        
        return apiKey;
    }

    
    /**
     * 获取默认的npm指标标识符列表。
     * 
     * @return 默认npm指标的标识符列表
     * @throws RuntimeException 如果未找到任何符合条件的指标时抛出
     */
    private List<String> getDefaultMetrics() {
        // 过滤符合条件的MetricsQuery对象：非空、标识符包含"npm."、数据库和表名匹配指定名称
        List<MetricsQuery> npmMetrics = metricsQueryService.filterMetrics(i ->
            i != null && 
            i.getIdentifier().contains("npm.") &&
            i.getDatabase().equals(TSDB_NPM_METRIC_DATABASE_NAME) &&
            i.getMeasurement().equals(TSDB_NPM_METRIC_TABLE_NAME)
        );
        
        // 处理过滤结果，提取标识符并转换为列表，若结果为空则抛出异常
        return Optional.ofNullable(npmMetrics)
            .map(list -> list.stream().map(MetricsQuery::getIdentifier).collect(Collectors.toList()))
            .orElseThrow(() -> new RuntimeException("默认指标未找到"));
    }

    /**
     * 查询npm性能指标的性能标签（支持分组）
     * 
     * 该方法根据提供的查询参数检索TSDB_NPM_METRIC_DATABASE_NAME指标的性能标签，分为客户端标签和服务器标签。
     * 通过请求中的apiKey生成数据库名称，并使用预定义的clientKeys和serverKeys分别获取客户端和服务器的标签值。
     * 查询参数中的时间范围（FROM和TO）被转换为Timestamp对象以构建查询条件。
     * 
     * @param request        包含请求详细信息的HttpServletRequest对象，其中apiKey用于生成数据库名称。
     * @param queryRequest   包含查询参数的QueryRequest对象，包括时间范围和指标条件。
     * @return CommonResponse对象，其data字段包含client和server标签的Map结构，格式为{"client": clientMap, "server": serverMap}
     * @throws Exception     当检索性能标签过程中发生错误时抛出异常。
     */
    @ApiOperation(value = "查询npm性能指标的性能标签（支持分组）", notes = "需要提供查询参数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "queryRequest", value = "查询参数", required = true, dataType = "QueryRequest", paramType = "body")
    })
    @PostMapping(value = "/performance/tags")
    @DomainManagerRequired
    public CommonResponse<Object> npmPerformanceTags(HttpServletRequest request, @RequestBody QueryRequest queryRequest) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();

        // 设置apiKey到queryRequest并生成数据库名称
        String apiKey = (String) request.getAttribute(API_KEY);
        final String database = apiKey + "_" + TSDB_NPM_METRIC_DATABASE_NAME;

        // 获取客户端标签值
        queryRequest.setMetrics(clientKeys);
        final Map<String, Set<String>> client = tsdbOperateUtil.showTagValues("bytes_received", database, TSDB_NPM_METRIC_TABLE_NAME, null, clientKeys, queryRequest.getStart(), queryRequest.getEnd());

        // 获取服务器标签值
        queryRequest.setMetrics(serverKeys);
        final Map<String, Set<String>> server = tsdbOperateUtil.showTagValues("bytes_received", database, TSDB_NPM_METRIC_TABLE_NAME, null, serverKeys, queryRequest.getStart(), queryRequest.getEnd());

        // 组合结果到响应对象
        commonResponse.setData(new JSONObject().fluentPut("client", client).fluentPut("server", server));
        return commonResponse;
    }


    @ApiOperation(value = "npm性能指标查询(支持分组)", notes = "参数")
    @PostMapping(value = "/performance/list")
    @DomainManagerRequired
    public CommonResponse<Object> npmPerformanceMetricsList(HttpServletRequest request,
                                                            @RequestBody QueryRequest query) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);

        query.setApiKey(apiKey);
        query.setMetrics(createMetricsArray());
        query.setDb(request.getAttribute("apiKey").toString() + TSDB_NPM_METRIC_DATABASE_NAME);
        query.setTb(TSDB_NPM_METRIC_TABLE_NAME);
        query.setInterval(null);

        Map<Map<String, String>, Collection<MetricDTO>> data = metricAggregator.listResult(query);
        if (data == null) {
            commonResponse.setData(new ArrayList<>());
            return commonResponse;
        }

        Collection<JSONObject> result = data.entrySet().stream()
                .filter(entry -> entry != null && entry.getKey() != null && entry.getValue() != null && !entry.getValue().isEmpty())
                .map(entry -> createResultJsonObject(entry))
                .collect(Collectors.toList());

        commonResponse.setData(result);
        return commonResponse;
    }

    private JSONObject createResultJsonObject(Map.Entry<Map<String, String>, Collection<MetricDTO>> entry) {
        Map<String, String> key = entry.getKey();
        JSONObject jsonObject = new JSONObject(entry.getValue().size() + 1, true).fluentPut("tags", key);
        for (MetricDTO metricDTO : entry.getValue()) {
            if (metricDTO != null) {
                jsonObject.put(metricDTO.getMetric(), metricDTO.getMetricsVal());
            }
        }
        return jsonObject;
    }

    /**
     * 获取npm性能流量详情列表
     * 
     * @param request          HttpServletRequest对象，用于获取API密钥
     * @param queryRequest     请求体参数对象，包含查询条件（指标、时间范围等）
     * @return CommonResponse<TSDBSeries> 包含TSDB查询结果的通用响应对象
     */
    @ApiOperation(value = "npm性能流量详情列表", notes = "参数")
    @PostMapping(value = "/volume/details")
    @DomainManagerRequired
    public CommonResponse<TSDBSeries> npmVolumeDetails(HttpServletRequest request, @RequestBody QueryRequest queryRequest) {
        CommonResponse<TSDBSeries> commonResponse = new CommonResponse<>();

        // 获取请求中的API密钥
        String apiKey = (String) request.getAttribute(API_KEY);

        // 自动填充缺失的指标参数
        final Collection<String> queryRequestMetrics = queryRequest.getMetrics();
        if (queryRequestMetrics == null || queryRequestMetrics.isEmpty()) {
            Collection<String> metrics = new ArrayList<>();
            for (MetricMetaData value : values()) {
                metrics.add(value.getCode());
            }
            queryRequest.setMetrics(metrics);
        }

        // 设置必要的查询参数
        queryRequest.setApiKey(apiKey);
        queryRequest.setDb(request.getAttribute("apiKey").toString() + TSDB_NPM_METRIC_DATABASE_NAME);
        queryRequest.setTb(TSDB_NPM_METRIC_TABLE_NAME);
        queryRequest.setInterval(null);
        queryRequest.setBy(null);

        // 执行指标聚合查询
        final Map<Map<String, String>, TSDBSeries> data = metricAggregator.seriesResult(queryRequest);

        // 提取首个有效查询结果
        data.values().stream().filter(Objects::nonNull).findFirst().ifPresent(i -> {
            commonResponse.setData(i);
        });
        return commonResponse;
    }


    @ApiOperation(value = "npm拓扑指标查询(支持分组)", notes = "参数")
    @PostMapping(value = "/topo/nodes")
    @DomainManagerRequired
    public CommonResponse<Object> npmTopoNodes(HttpServletRequest request, @RequestBody QueryRequest queryRequest) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        final Collection<String> metrics = queryRequest.getMetrics();
        if (metrics == null || metrics.isEmpty()) {
            queryRequest.setMetrics(buildNPMTopoMetrics());
        }

        final String name = queryRequest.getName();

        queryRequest.setApiKey(apiKey);
        queryRequest.setDb(request.getAttribute("apiKey").toString() + TSDB_NPM_METRIC_DATABASE_NAME);
        queryRequest.setTb(TSDB_NPM_METRIC_TABLE_NAME);
        queryRequest.setInterval(null);

        // bugfix for java.lang.UnsupportedOperationException: null
        Collection<CompositeCondition> from = queryRequest.getFrom();
        Collection<CompositeCondition> newFrom = new ArrayList<>();
        if (from != null) {
            newFrom.addAll(from);
        }
        newFrom.add(new CompositeCondition("composite", LogicalOperator.AND, FIELD_DIRECTION, Lists.newArrayList(LOCAL, OUTGOING), false, WhereOp.IN.getSymbol()));
        queryRequest.setFrom(newFrom);

        Map<Map<String, String>, Collection<MetricDTO>> data = metricAggregator.listResult(queryRequest);
        if (data == null) {
            commonResponse.setData(new ArrayList<>());
            return commonResponse;
        }
        final Collection<JSONObject> result = new ArrayList<>();
        for (Map.Entry<Map<String, String>, Collection<MetricDTO>> entry : data.entrySet()) {
            final Map<String, String> key = entry.getKey();
            final Collection<MetricDTO> value = entry.getValue();
            final JSONObject edgeDto = new JSONObject()
                    .fluentPut("name", key.get(name));
            for (MetricDTO metricDTO : value) {
                if (metricDTO == null) {
                    continue;
                }
                edgeDto.put(metricDTO.getMetric(), metricDTO.getMetricsVal());
            }
            result.add(edgeDto);
        }
        commonResponse.setData(result);
        return commonResponse;
    }

    @ApiOperation(value = "npm拓扑指标查询(支持分组)", notes = "参数")
    @PostMapping(value = "/topo/edges")
    @DomainManagerRequired
    public CommonResponse<Object> npmTopoEdges(HttpServletRequest request, @RequestBody QueryRequest queryRequest) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        // 参数校验
        if (queryRequest == null) throw new IllegalArgumentException("查询参数不能为空");

        // 获取API密钥
        String apiKey = getValidApiKey(request);
        queryRequest.setApiKey(apiKey);

        // 设置默认指标
        if (CollectionUtils.isEmpty(queryRequest.getMetrics())) {
            List<String> defaultMetrics = getDefaultMetrics();
            queryRequest.setMetrics(defaultMetrics);
        }

        // 强制设置数据库和表名
        queryRequest.setDb(request.getAttribute("apiKey").toString() + TSDB_NPM_METRIC_DATABASE_NAME);
        queryRequest.setTb(TSDB_NPM_METRIC_TABLE_NAME);
        queryRequest.setInterval(null);

        Map<Map<String, String>, Collection<MetricDTO>> data = metricAggregator.listResult(queryRequest);
        if (data == null) {
            commonResponse.setData(new ArrayList<>());
            return commonResponse;
        }
        final String source = queryRequest.getSource();
        final String target = queryRequest.getTarget();

        final Collection<JSONObject> result = new ArrayList<>();

        for (Map.Entry<Map<String, String>, Collection<MetricDTO>> entry : data.entrySet()) {
            final Map<String, String> key = entry.getKey();
            final Collection<MetricDTO> value = entry.getValue();
            final JSONObject edgeDto = new JSONObject()
                    .fluentPut("source", key.get(source))
                    .fluentPut("target", key.get(target));
            for (MetricDTO metricDTO : value) {
                if (metricDTO == null) {
                    continue;
                }
                edgeDto.put(metricDTO.getMetric(), metricDTO.getMetricsVal());
            }
            result.add(edgeDto);
        }
        commonResponse.setData(result);
        return commonResponse;
    }

}
