package com.databuff.webapp.biz.service.impl;

import com.databuff.common.tsdb.model.TSDBResultSet;
import com.databuff.entity.BizScenario;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class ValidateService {

    /**
     * 校验获取到的场景对象和其 KPI 配置。
     */
    public void validateScenarioAndConfig(BizScenario scenario, int scenarioId, String apiKey) {
        if (scenario == null) {
            log.error("无法找到业务场景，ID: {}, ApiKey: {}", scenarioId, apiKey);
            throw new RuntimeException("指定的业务场景不存在");
        }
        if (scenario.getVersion() == null || scenario.getVersion() != 2) {
            log.error("业务场景 (ID: {}) 不是有效的 V2 版本", scenarioId);
            throw new RuntimeException("请求的业务场景版本无效");
        }
    }

    /**
     * 检查 TSDB 查询结果是否有效（非空，有 Series，有 Values）。
     */
    public boolean isResultValid(TSDBResultSet resultSet) {
        return resultSet != null &&
                !CollectionUtils.isEmpty(resultSet.getResults()) &&
                resultSet.getResults().get(0).getSeries() != null &&
                !CollectionUtils.isEmpty(resultSet.getResults().get(0).getSeries()) &&
                !CollectionUtils.isEmpty(resultSet.getResults().get(0).getSeries().get(0).getValues());
    }

    /**
     * 检查用于 TopN 查询的 TSDB 结果是否有效。
     * TopN 查询通常每个 Series 代表一个分组（如一个 resource），其 values 列表可能包含聚合值。
     */
    public boolean isResultValidForTopN(TSDBResultSet resultSet) {
        return resultSet != null &&
                !CollectionUtils.isEmpty(resultSet.getResults()) &&
                resultSet.getResults().get(0).getSeries() != null; // 只需要检查 Series 列表存在即可
        // TopN 查询可能 series 的 values 为空（如果只 select tag）或者只包含聚合值
    }

}
