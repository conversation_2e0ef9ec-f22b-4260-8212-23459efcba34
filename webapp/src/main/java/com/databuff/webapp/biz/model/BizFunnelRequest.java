package com.databuff.webapp.biz.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 漏斗分析详细路径接口 (POST /biz/funnel/detailed-path) 的请求体 DTO。
 */
@Data
@NoArgsConstructor // Lombok: 生成无参构造函数
@AllArgsConstructor // Lombok: 生成全参构造函数
@ApiModel(description = "漏斗分析详细路径接口的请求参数")
public class BizFunnelRequest {

    @ApiModelProperty(value = "业务场景ID 。如果不提供，Service 层应使用默认场景。", example = "1")
    private Integer bizScenarioId; // 注意: Service层需要处理String到Integer的转换（如果DB需要Integer）或按String查询

    @ApiModelProperty(value = "路径叶子/代表节点ID (可选, String 类型)。用于分支切换后指定新路径的起始部分。如果不提供，Service 层应计算默认路径（如最深路径）。", example = "node-email-verified")
    private String leafNodeId;

    @ApiModelProperty(value = "查询开始时间 (必需, 字符串格式如 'yyyy-MM-dd HH:mm:ss')", example = "2025-04-27 10:00:00", required = true)
    private String fromTime;

    @ApiModelProperty(value = "查询结束时间 (必需, 字符串格式如 'yyyy-MM-dd HH:mm:ss')", example = "2025-04-27 11:00:00", required = true)
    private String toTime;
}