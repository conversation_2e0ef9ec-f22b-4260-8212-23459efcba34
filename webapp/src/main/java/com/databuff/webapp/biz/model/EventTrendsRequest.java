package com.databuff.webapp.biz.model; // 假设 DTO 在此包下

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 获取业务事件性能趋势请求的 DTO。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("获取业务事件趋势请求参数")
public class EventTrendsRequest {

    /**
     * (必需) 需要查询趋势的业务事件 ID。
     */
    @NotNull(message = "业务事件ID不能为空")
    @ApiModelProperty(value = "业务事件ID", required = true, example = "5")
    private Integer eventId;

    /**
     * (必需) 查询时间范围 - 开始。
     * 格式: "yyyy-MM-dd HH:mm:ss"
     */
    @NotBlank(message = "开始时间不能为空")
    @ApiModelProperty(value = "查询开始时间", required = true, example = "2025-04-28 14:00:00")
    private String fromTime;

    /**
     * (必需) 查询时间范围 - 结束。
     * 格式: "yyyy-MM-dd HH:mm:ss"
     */
    @NotBlank(message = "结束时间不能为空")
    @ApiModelProperty(value = "查询结束时间", required = true, example = "2025-04-28 15:00:00")
    private String toTime;

    /**
     * (可选) 维度: "event" (事件整体) 或 "resource" (按接口/资源)。
     * 默认为 "event"。
     */
    @Pattern(regexp = "^(event|resource)$", message = "维度必须是 'event' 或 'resource'") // 正则校验
    @ApiModelProperty(value = "查询维度", example = "resource", allowableValues = "event, resource", required = false)
    private String dimension = "event"; // 提供默认值

    /**
     * (可选) TopN 值, 仅当 dimension="resource" 时有效。
     * 默认值为 5。
     */
    @Min(value = 1, message = "TopN值必须大于等于1") // 最小值校验
    @ApiModelProperty(value = "TopN数量 (仅当维度为resource时有效)", example = "10", required = false)
    private Integer topN = 5; // 提供默认值

    /**
     * (可选) 时间聚合间隔，单位：秒。
     * 默认值为 60 秒 (1分钟)。
     */
    @Min(value = 1, message = "时间间隔必须大于等于1秒")
    @ApiModelProperty(value = "时间聚合间隔(秒)", example = "300", required = false)
    private Integer interval = 60; // 提供默认值
}