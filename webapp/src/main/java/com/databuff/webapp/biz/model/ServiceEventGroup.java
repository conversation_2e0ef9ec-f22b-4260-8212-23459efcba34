package com.databuff.webapp.biz.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 按服务分组的业务事件列表响应项 DTO。
 * 代表一个服务及其关联到特定节点的事件列表和状态。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("按服务分组的事件列表项")
public class ServiceEventGroup {

    /**
     * 服务名称。
     */
    @ApiModelProperty(value = "服务名称", example = "交易服务")
    private String serviceName;

    /**
     * 服务 ID。
     */
    @ApiModelProperty(value = "服务ID", example = "trade-svc-abc")
    private String serviceId;

    /**
     * 服务状态 ("red" 或 "green")。
     * 如果其下关联的任何事件状态为 "red"，则此状态为 "red"。
     */
    @ApiModelProperty(value = "服务状态", example = "red", allowableValues = "red, green")
    private String status;

    /**
     * 关联到此服务和特定节点的业务事件列表。
     */
    @ApiModelProperty(value = "关联的业务事件列表")
    private List<EventStatus> events;
}