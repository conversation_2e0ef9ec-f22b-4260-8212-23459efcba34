package com.databuff.webapp.biz.service.impl;

import com.alibaba.fastjson.JSON;
import com.databuff.common.utils.DateUtils;
import com.databuff.dao.mysql.BizObservabilityMapper;
import com.databuff.entity.BizKpiConfig;
import com.databuff.entity.BizScenario;
import com.databuff.entity.enums.BusinessFlowType;
import com.databuff.webapp.biz.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Service
@Slf4j
public class ScenarioKpiService {
    @Autowired
    private BizObservabilityMapper bizMapper;
    @Autowired
    private TsdbQueryService tsdbQueryService;
    @Autowired
    private ValidateService validateService;

    private static final long COMPARISON_INTERVAL_MS = 24 * 60 * 60 * 1000L; // 对比周期：24小时


    /**
     * 获取指定业务场景在特定时间范围内的核心 KPI 指标。
     * <p>
     * 查询 `biz_event_kpi` 指标库，计算用户定义、转化/履约、错误数三个核心 KPI 的当前值，
     * 并与上一周期（默认24小时前）进行对比，返回包含详细结果的响应对象。
     * </p>
     *
     * @param apiKey  当前用户的 API Key。
     * @param request 包含 `bizScenarioId`, `fromTime`, `toTime` 的请求对象。
     * @return {@link ScenarioKpiResponse} 包含三个核心 KPI 结果的对象。
     * @throws IllegalArgumentException 如果请求参数无效。
     * @throws RuntimeException         如果场景配置缺失、解析失败或查询 TSDB 时发生错误。
     */
    public ScenarioKpiResponse getScenarioKpis(String apiKey, ScenarioKpiRequest request) {
        ScenarioKpiResponse response = new ScenarioKpiResponse();

        long startTime = System.currentTimeMillis();
        log.info("开始处理场景 KPI 请求，ApiKey: {}, 请求: {}", apiKey, request);

        // 1. 参数校验
        validateKpiRequest(apiKey, request);
        int scenarioId = request.getBizScenarioId();

        // 2. 加载场景配置
        BizScenario scenario = bizMapper.getBizScenarioById(scenarioId, apiKey);
        validateService.validateScenarioAndConfig(scenario, scenarioId, apiKey);

        if (!StringUtils.isNotBlank(scenario.getKpiConfig())) {
            log.info("业务场景 (ID: {}) 未配置 KPI (kpi_config 为空)", scenarioId);
            return response;
        }
        BizKpiConfig kpiConfig = parseKpiConfig(scenario.getKpiConfig(), scenarioId, apiKey); // 内部处理解析错误

        // 3. 定义时间范围
        long endN = DateUtils.stringToLong("yyyy-MM-dd HH:mm:ss", request.getToTime());
        long startN = DateUtils.stringToLong("yyyy-MM-dd HH:mm:ss", request.getFromTime());
        long startM = startN - COMPARISON_INTERVAL_MS;
        long endM = endN - COMPARISON_INTERVAL_MS;

        // 4. & 5. 查询指标 (当前周期 N 和上一周期 M)
        KpiRawMetrics metricsN = tsdbQueryService.queryKpisForPeriod(apiKey, scenarioId, kpiConfig, startN, endN);
        KpiRawMetrics metricsM = tsdbQueryService.queryKpisForPeriod(apiKey, scenarioId, kpiConfig, startM, endM);
        log.debug("查询指标完成: metricsN={}, metricsM={}", metricsN, metricsM);

        // 6. 计算 KPI 值 (原始数值)
        double userDefinedValueN = metricsN.getKpiValue();
        long conversionValueN = metricsN.getTotalCnt();
        long errorCountValueN = metricsN.getTotalError();
        double userDefinedValueM = metricsM.getKpiValue();
        long conversionValueM = metricsM.getTotalCnt();
        long errorCountValueM = metricsM.getTotalError();

        // 7. 计算对比结果
        ComparisonResult userDefinedComparison = calculateComparison(userDefinedValueN, userDefinedValueM, true);
        ComparisonResult conversionComparison = calculateComparison(conversionValueN, conversionValueM, true);
        ComparisonResult errorCountComparison = calculateComparison(errorCountValueN, errorCountValueM, false);

        // 8. 确定名称和单位
        String userDefinedName = kpiConfig.getKpiName(); // 直接从配置取
        String userDefinedUnit = kpiConfig.getKpiUnit();
        String conversionName = determineConversionName(kpiConfig); // 根据类型确定名称
        String conversionUnit = kpiConfig.getBusinessUnit(); // 从配置取单位
        String errorCountName = "错误数";
        String errorCountUnit = ""; // 或 "次"

        // 9. 组装 Response DTO (注意：value 字段传入原始数值)

        response.setUserDefined(new KpiResultItem(userDefinedName, userDefinedValueN, userDefinedUnit, userDefinedComparison));
        response.setConversion(new KpiResultItem(conversionName, conversionValueN, conversionUnit, conversionComparison));
        response.setErrorCount(new KpiResultItem(errorCountName, errorCountValueN, errorCountUnit, errorCountComparison));

        long endTime = System.currentTimeMillis();
        log.info("场景 KPI 请求处理完成，场景 ID: {}, 耗时: {} ms", scenarioId, (endTime - startTime));
        return response;
    }

    /**
     * 校验 KPI 请求参数。
     */
    private void validateKpiRequest(String apiKey, ScenarioKpiRequest request) {
        if (!StringUtils.isNotBlank(apiKey)) {
            throw new IllegalArgumentException("ApiKey 不能为空");
        }
        if (request == null) {
            throw new IllegalArgumentException("请求体不能为空");
        }
        if (request.getBizScenarioId() == null) {
            throw new IllegalArgumentException("业务场景ID (bizScenarioId) 不能为空");
        }
        if (!StringUtils.isNotBlank(request.getFromTime())) {
            throw new IllegalArgumentException("开始时间 (fromTime) 不能为空");
        }
        if (!StringUtils.isNotBlank(request.getToTime())) {
            throw new IllegalArgumentException("结束时间 (toTime) 不能为空");
        }
        // 可以添加更严格的时间格式校验
    }



    /**
     * 解析 KPI 配置 JSON 字符串。
     */
    private BizKpiConfig parseKpiConfig(String kpiJson, int scenarioId, String apiKey) {
        BizKpiConfig kpiConfig = null;
        try {
            kpiConfig = JSON.parseObject(kpiJson, BizKpiConfig.class);
        } catch (Exception e) { // **修改点 2: 捕获所有解析期间的异常，并归为格式错误**
            log.error("解析业务场景 (ID: {}) 的 KPI 配置 JSON 失败 (可能格式错误或类型不匹配): {}", scenarioId, e.getMessage(), e);
            throw new RuntimeException("KPI 配置解析失败或格式错误", e);
        }

        // 校验解析结果是否为 null 或缺少关键字段
        if (kpiConfig == null || !StringUtils.isNotBlank(kpiConfig.getKpiName()) ||
                kpiConfig.getCalculationMethod() == null || !StringUtils.isNotBlank(kpiConfig.getSourceEventId()) ||
                !StringUtils.isNotBlank(kpiConfig.getSourceAttributeKey())) {
            log.error("业务场景 (ID: {}) 的 KPI 配置解析后为 null 或缺少关键字段。JSON: {}", scenarioId, kpiJson);
            // **直接抛出业务校验异常，不再被下面的 catch 捕获**
            throw new RuntimeException("KPI 配置无效或不完整");
        }

        // 校验通过
        return kpiConfig;
    }





    /**
     * 计算两个数值之间的对比结果。
     *
     * @param valueN         当前周期的值 (Number 类型)
     * @param valueM         上一周期的值 (Number 类型)
     * @param isHigherBetter 是否值越高越好 (true for KpiValue/Conversion, false for ErrorCount)
     * @return 对比结果对象
     */
    private ComparisonResult calculateComparison(Number valueN, Number valueM, boolean isHigherBetter) {
        ComparisonResult comparison = new ComparisonResult(); // 初始化，所有字段为 null

        // 处理 null 值，视为 0 进行比较
        double valN = (valueN != null) ? valueN.doubleValue() : 0.0;
        double valM = (valueM != null) ? valueM.doubleValue() : 0.0;

        // 检查 M 是否为 0，无法计算比率
        if (Math.abs(valM) < 1E-9) { // 使用小 epsilon 比较浮点数 0
            log.trace("无法计算对比率，上一周期值为 0 或接近 0 (N={}, M={})", valN, valM);
            // 可选：如果 N > 0，可以显示箭头和颜色，但不显示百分比
            if (valN > valM) { // N > 0, M = 0 -> 增长
                comparison.setArrow("up");
                comparison.setColor(isHigherBetter ? "green" : "red");
            } else if (valN < valM) { // N < 0, M = 0 -> 下降 (理论上值应非负，但以防万一)
                comparison.setArrow("down");
                comparison.setColor(isHigherBetter ? "red" : "green");
            }
            // rate 保持 null
            return comparison;
        }

        // 计算变化率
        double rate = ((valN - valM) / valM) * 100.0;
        // 使用 BigDecimal 控制精度，例如保留一位小数
        comparison.setRate(BigDecimal.valueOf(rate).setScale(1, RoundingMode.HALF_UP).doubleValue());

        // 判断箭头和颜色
        if (Math.abs(rate) < 1E-9) { // 无变化
            // arrow 和 color 保持 null
        } else if (rate > 0) { // 增长
            comparison.setArrow("up");
            comparison.setColor(isHigherBetter ? "green" : "red");
        } else { // rate < 0，下降
            comparison.setArrow("down");
            comparison.setColor(isHigherBetter ? "red" : "green");
        }

        return comparison;
    }

    /**
     * 根据业务 KPI 配置确定转化/履约 KPI 的名称。
     * <p>
     * 如果类型是 FULFILLMENT 或 CONVERSION，返回固定名称。
     * 如果类型是 OTHER，则尝试使用配置中的 otherFlowTypeName，如果为空则返回默认名称。
     * 其他情况返回默认名称 "总次数"。
     * </p>
     *
     * @param kpiConfig 解析后的 KPI 配置对象，包含 businessFlowType 和 otherFlowTypeName。
     * @return 合适的 KPI 显示名称。
     */
    private String determineConversionName(BizKpiConfig kpiConfig) {
        if (kpiConfig == null || kpiConfig.getBusinessFlowType() == null) {
            log.warn("无法确定转化 KPI 名称，因为 kpiConfig 或 businessFlowType 为 null。使用默认名称。");
            return "总次数"; // 默认名称
        }

        BusinessFlowType flowType = kpiConfig.getBusinessFlowType();

        switch (flowType) {
            case FULFILLMENT:
                return "订单履约数"; // 固定名称
            case CONVERSION:
                return "业务转化数"; // 固定名称
            case OTHER:
                // 如果是 OTHER 类型，检查是否有自定义名称
                String customName = kpiConfig.getOtherFlowTypeName();
                if (StringUtils.isNotBlank(customName)) {
                    return customName; // 使用自定义名称
                } else {
                    log.trace("业务流类型为 OTHER，但未提供 otherFlowTypeName，使用默认名称。");
                    return "总次数"; // 自定义名称为空，使用默认
                }
            default:
                // 处理未知的枚举值（理论上不应发生）
                log.warn("遇到未知的 BusinessFlowType: {}，使用默认名称。", flowType);
                return "总次数"; // 未知类型，使用默认
        }
    }


}
