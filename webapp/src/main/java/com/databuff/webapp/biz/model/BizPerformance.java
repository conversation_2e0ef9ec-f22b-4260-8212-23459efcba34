package com.databuff.webapp.biz.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 用于 API 响应中表示漏斗步骤的整体业务性能指标 (用于左侧面板)。
 * 使用 Lombok 简化代码。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "漏斗步骤的整体业务性能指标 (用于左侧面板)。")
public class BizPerformance {

    @ApiModelProperty(value = "进入该步骤的总请求数。", example = "10000")
    private long totalRequests;

    @ApiModelProperty(value = "该步骤的整体状态指示器 (基于错误或告警)。", example = "green", allowableValues = "green, red")
    private String status;
}