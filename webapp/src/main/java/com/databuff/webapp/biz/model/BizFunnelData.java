package com.databuff.webapp.biz.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("漏斗分析接口数据")
public class BizFunnelData {
    @ApiModelProperty(value = "当前漏斗路径上每个步骤的详细数据列表。", required = true)
    private List<BizFunnelStepData> pathData;
}
