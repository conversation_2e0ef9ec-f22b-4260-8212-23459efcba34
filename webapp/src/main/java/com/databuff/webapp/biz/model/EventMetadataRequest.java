package com.databuff.webapp.biz.model; // 假设 DTO 在此包下

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 获取业务事件元数据请求的 DTO。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("获取业务事件元数据请求参数")
public class EventMetadataRequest {

    /**
     * (必需) 需要查询元数据的业务事件 ID。
     */
    @NotNull(message = "业务事件ID不能为空")
    @ApiModelProperty(value = "业务事件ID", required = true, example = "5")
    private Integer bizEventId;

    /**
     * (必需) 查询时间范围 - 开始。
     * **注意:** 当前此接口仅获取静态元数据，此字段暂时未在核心逻辑中使用。
     * 格式: "yyyy-MM-dd HH:mm:ss"
     */
    @NotBlank(message = "开始时间不能为空")
    @ApiModelProperty(value = "查询开始时间 (当前仅用于传递，逻辑中未使用)", required = true, example = "2025-04-28 14:00:00")
    private String fromTime;

    /**
     * (必需) 查询时间范围 - 结束。
     * **注意:** 当前此接口仅获取静态元数据，此字段暂时未在核心逻辑中使用。
     * 格式: "yyyy-MM-dd HH:mm:ss"
     */
    @NotBlank(message = "结束时间不能为空")
    @ApiModelProperty(value = "查询结束时间 (当前仅用于传递，逻辑中未使用)", required = true, example = "2025-04-28 15:00:00")
    private String toTime;
}