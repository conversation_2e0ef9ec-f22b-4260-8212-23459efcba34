package com.databuff.webapp.biz.model; // 假设 DTO 在此包下

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 获取业务场景核心 KPI 的请求体 DTO。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("获取场景KPI请求参数")
public class ScenarioKpiRequest {

    /**
     * 必需的业务场景 ID。
     */
    @NotNull(message = "业务场景ID不能为空") // 添加校验
    @ApiModelProperty(value = "业务场景ID", required = true, example = "2")
    private Integer bizScenarioId;

    /**
     * 必需的查询开始时间。
     * 格式: "yyyy-MM-dd HH:mm:ss"
     */
    @NotNull(message = "开始时间不能为空") // 添加校验
    @ApiModelProperty(value = "查询开始时间", required = true, example = "2025-04-28 09:27:00")
    private String fromTime;

    /**
     * 必需的查询结束时间。
     * 格式: "yyyy-MM-dd HH:mm:ss"
     */
    @NotNull(message = "结束时间不能为空") // 添加校验
    @ApiModelProperty(value = "查询结束时间", required = true, example = "2025-04-28 10:27:00")
    private String toTime;
}