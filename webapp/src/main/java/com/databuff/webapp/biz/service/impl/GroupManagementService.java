package com.databuff.webapp.biz.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.Aggregation;
import com.databuff.common.tsdb.model.Where;
import com.databuff.common.utils.DateUtils;
import com.databuff.dao.mysql.BizObservabilityMapper;
import com.databuff.entity.*;
import com.databuff.util.MetricsUtil;
import com.databuff.webapp.config.common.CommonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.databuff.common.constants.Constant.Metric.DATABASE_DATABUFF;
import static com.databuff.common.constants.TSDBIndex.METRIC_DATABUFF_ALARM;

@Service
@Slf4j
public class GroupManagementService {

    @Autowired
    private BizObservabilityMapper bizMapper;

    @Autowired
    private BizAlarmService bizAlarmService;


    @Transactional(rollbackFor = Exception.class)
    public CommonResponse addBizGroup(List<BizGroup> bizGroups, String apiKey) {
        CommonResponse commonResponse = new CommonResponse();
        for (BizGroup bizGroup : bizGroups) {
            if (bizGroup.getOrder() == null) {
                commonResponse.setMessage("排序号不能为空");
                return commonResponse;
            }
            if (StringUtils.isBlank(bizGroup.getGroupName())) {
                commonResponse.setMessage("业务事件名称不能为空");
                return commonResponse;
            }
            bizGroup.setApiKey(apiKey);
        }
        try {
            bizMapper.delGroup(apiKey);
            if (bizGroups.size() == 0) {
                return commonResponse;
            }
            int ret = 0;
            for (BizGroup bizGroup : bizGroups) {
                ret += bizMapper.insertGroup(bizGroup);
            }
            commonResponse.setData(ret);
        } catch (DuplicateKeyException e) {
            log.error("添加业务分组失败: {}", e.getMessage(), e);
            commonResponse.setMessage("添加失败: 分组名不可重复");
            return commonResponse;
        } catch (Exception e) {
            log.error("添加业务分组失败: {}", e.getMessage(), e);
            commonResponse.setMessage("添加失败: " + e.getMessage());
            return commonResponse;
        }
        return commonResponse;
    }


    public List<BizGroup> bizGroupList(String apiKey) {
        return bizMapper.getBizGroupList(apiKey);
    }

    public List<BizGroupScenario> bizGroupScenarios(BizSearch search) {

        final List<BizScenario> bizScenarioList = bizMapper.getBizScenarioList(search);

        final List<BizGroup> bizGroups = bizGroupList(search.getApiKey());

        List<BizGroupScenario> bizGroupScenarios = new ArrayList<>();
        Map<Integer, List<BizScenario>> map = new HashMap<>();
        bizScenarioList.forEach(bizScenario -> {
            if (map.containsKey(bizScenario.getBizGroupId())) {
                map.get(bizScenario.getBizGroupId()).add(bizScenario);
            } else {
                List<BizScenario> list = new ArrayList<>();
                list.add(bizScenario);
                map.put(bizScenario.getBizGroupId(), list);
            }
        });
        for (BizGroup bizGroup : bizGroups) {
            BizGroupScenario bizGroupScenario = new BizGroupScenario();
            bizGroupScenario.setId(bizGroup.getId());
            bizGroupScenario.setGroupName(bizGroup.getGroupName());
            bizGroupScenario.setOrder(bizGroup.getOrder());
            List<BizScenario> scenarios = map.get(bizGroup.getId());
            if (scenarios != null) {
                //计算告警数
                fillScenarioAlarm(scenarios, search);
                bizGroupScenario.setScenarios(scenarios);
                map.remove(bizGroup.getId());
            }
            bizGroupScenarios.add(bizGroupScenario);
        }
        BizGroupScenario noGroupScenario = new BizGroupScenario();
        noGroupScenario.setId(null);
        noGroupScenario.setGroupName("未分组");
        noGroupScenario.setOrder(99999);
        if (!CollectionUtils.isEmpty(map)) {
            List<BizScenario> scenarios = map.values().stream().flatMap(List::stream).collect(Collectors.toList());
            //计算告警数
            fillScenarioAlarm(scenarios, search);
            noGroupScenario.setScenarios(scenarios);
        }
        bizGroupScenarios.add(noGroupScenario);

        //在返回之前，统一处理所有 BizScenario 对象的 JSON 到对象转换
        processBizGroupScenarioList(bizGroupScenarios);

        return bizGroupScenarios;
    }

    /**
     * 辅助方法：遍历 BizGroupScenario 列表，并对其包含的 BizScenario 列表进行JSON到对象的转换。
     *
     * @param groupScenarios 业务分组场景列表
     */
    private void processBizGroupScenarioList(List<BizGroupScenario> groupScenarios) {
        if (CollectionUtils.isEmpty(groupScenarios)) {
            return;
        }
        for (BizGroupScenario groupScenario : groupScenarios) {
            if (groupScenario != null && !CollectionUtils.isEmpty(groupScenario.getScenarios())) {
                processBizScenarioObjects(groupScenario.getScenarios());
            }
        }
    }

    /**
     * 辅助方法：处理 BizScenario 列表，将 JSON 字符串字段解析为对象字段，并清空原字符串字段。
     *
     * @param scenarios 业务场景列表
     */
    private void processBizScenarioObjects(List<BizScenario> scenarios) {
        if (CollectionUtils.isEmpty(scenarios)) {
            return;
        }
        for (BizScenario scenario : scenarios) {
            if (scenario == null) {
                continue;
            }

            // 解析 scenarioGraph
            if (StringUtils.isNotBlank(scenario.getScenarioGraph())) {
                try {
                    BizScenarioGraph graphObject = JSON.parseObject(scenario.getScenarioGraph(), BizScenarioGraph.class);
                    scenario.setScenarioGraphObject(graphObject);
                } catch (JSONException e) {
                    log.warn("GroupManagementService.processBizScenarioObjects: 解析场景ID {} 的 scenarioGraph JSON失败: '{}', Error: {}",
                            scenario.getId(), scenario.getScenarioGraph(), e.getMessage());
                    scenario.setScenarioGraphObject(null); // 解析失败则设为null
                } finally {
                    scenario.setScenarioGraph(null); // 总是清空原字符串
                }
            } else {
                scenario.setScenarioGraphObject(null); // 如果原字符串为空，对象也设为null
            }

            // 解析 kpiConfig
            if (StringUtils.isNotBlank(scenario.getKpiConfig())) {
                try {
                    BizKpiConfig kpiConfigObject = JSON.parseObject(scenario.getKpiConfig(), BizKpiConfig.class);
                    scenario.setKpiConfigObject(kpiConfigObject);
                } catch (JSONException e) {
                    log.warn("GroupManagementService.processBizScenarioObjects: 解析场景ID {} 的 kpiConfig JSON失败: '{}', Error: {}",
                            scenario.getId(), scenario.getKpiConfig(), e.getMessage());
                    scenario.setKpiConfigObject(null); // 解析失败则设为null
                } finally {
                    scenario.setKpiConfig(null); // 总是清空原字符串
                }
            } else {
                scenario.setKpiConfigObject(null); // 如果原字符串为空，对象也设为null
            }
        }
    }

    private void fillScenarioAlarm(List<BizScenario> scenarios, BizSearch search) {
        long fromTimeVul = DateUtils.stringToLong("yyyy-MM-dd HH:mm:ss", search.getFromTime());
        long toTimeVul = DateUtils.stringToLong("yyyy-MM-dd HH:mm:ss", search.getToTime());
        Map<Integer, Integer> scenarioEventMap = new HashMap<>();
        Map<Integer, BizScenario> scenarioMap = new HashMap<>();
        List<Object> bizEventIds = new ArrayList<>();
        for (BizScenario scenario : scenarios) {
            if (scenario == null) continue;
            // 初始化告警计数
            scenario.setAlarmCnt(0L);
            Integer scenarioId = scenario.getId();
            Integer version = scenario.getVersion();

            // 根据版本号决定从哪里提取事件ID
            if (version != null && version == 1 && StringUtils.isNotBlank(scenario.getBizEvents())) {
                // --- 处理 V1 版本 ---
                try {
                    List<BizScenarioEvent> bizEventReqs = JSON.parseArray(scenario.getBizEvents(), BizScenarioEvent.class);
                    if (!CollectionUtils.isEmpty(bizEventReqs)) {
                        for (BizScenarioEvent req : bizEventReqs) {
                            if (req != null && req.getBizEventId() != null) {
                                bizEventIds.add(String.valueOf(req.getBizEventId()));
                                scenarioEventMap.put(req.getBizEventId(), scenarioId);
                                scenarioMap.put(scenarioId, scenario);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("GroupManagementService.fillScenarioAlarm (V1): 解析场景ID {} 的BizEvents JSON失败: '{}', Error: {}",
                            scenarioId, scenario.getBizEvents(), e.getMessage());
                }
            } else if (version != null && version == 2 && StringUtils.isNotBlank(scenario.getScenarioGraph())) {
                // --- 处理 V2 版本 ---
                try {
                    // 先解析 V2 图结构
                    BizScenarioGraph graph = JSON.parseObject(scenario.getScenarioGraph(), BizScenarioGraph.class);
                    if (graph != null && !CollectionUtils.isEmpty(graph.getNodes())) {
                        for (BizScenarioNodeV2 node : graph.getNodes()) {
                            if (node != null && node.getEvents() != null && !CollectionUtils.isEmpty(node.getEvents().getIncluded())) {
                                for (BizScenarioIncludedEventV2 eventRef : node.getEvents().getIncluded()) {
                                    if (eventRef != null && StringUtils.isNotBlank(eventRef.getBizEventId())) {
                                        try {
                                            int eventId = Integer.parseInt(eventRef.getBizEventId());
                                            bizEventIds.add(eventRef.getBizEventId()); // 添加 String 类型的 ID
                                            scenarioEventMap.put(eventId, scenarioId);
                                            scenarioMap.put(scenarioId, scenario);
                                        } catch (NumberFormatException nfe) {
                                            log.warn("GroupManagementService.fillScenarioAlarm (V2): 场景ID {}, 节点ID {}, 发现无效的bizEventId格式: {}",
                                                    scenarioId, node.getId(), eventRef.getBizEventId());
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("GroupManagementService.fillScenarioAlarm (V2): 解析场景ID {} 的ScenarioGraph JSON失败: '{}', Error: {}",
                            scenarioId, scenario.getScenarioGraph(), e.getMessage());
                }
            } else {
                // 版本号不明确或对应数据为空
                log.trace("GroupManagementService.fillScenarioAlarm: 场景ID {} 版本号为 {} 或对应数据字段为空，无法提取事件ID。",
                        scenarioId, version);
            }
        }

        List<Where> wheres = new ArrayList<>();
        wheres.add(Where.gte("time", fromTimeVul));
        wheres.add(Where.lt("time", toTimeVul));
        wheres.add(Where.in("bizEventId", bizEventIds));
        QueryBuilder alarmBuilder = new QueryBuilder()
                .setDatabaseName(MetricsUtil.generateFullDatabase(search.getApiKey(), DATABASE_DATABUFF))
                //只有来做显示是否有告警使用，不能当做告警数用
                .setMeasurement(METRIC_DATABUFF_ALARM)
                .addAgg(Aggregation.of("trigger.cnt"))
                .addAllWhere(wheres)
                .addGroupBy("bizEventId");

        Map<String, Long> alarmCntInfo = bizAlarmService.getEventIdAlarmCntMap(alarmBuilder);
        scenarioEventMap.forEach((k, v) -> {
            if (alarmCntInfo.containsKey(k.toString())) {
                BizScenario scenario = scenarioMap.get(v);
                if (scenario != null) {
                    Long alarmCnt = scenario.getAlarmCnt() == null ? 0 : scenario.getAlarmCnt();
                    alarmCnt += alarmCntInfo.get(k.toString());
                    scenario.setAlarmCnt(alarmCnt);
                }
            }
        });
    }

}
