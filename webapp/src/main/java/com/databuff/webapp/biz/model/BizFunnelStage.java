package com.databuff.webapp.biz.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 用于 API 响应中表示漏斗图中间区域单个层级的原始请求数细分。
 * 前端将使用这些数据计算百分比和总数，并绘制颜色分段。
 * 使用 Lombok 简化代码。
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "漏斗图中间区域单个层级的原始请求数细分。")
public class BizFunnelStage {

    @ApiModelProperty(value = "正常请求计数。", example = "9500")
    private long normalRequests;

    @ApiModelProperty(value = "慢请求计数。", example = "300")
    private long slowRequests;

    @ApiModelProperty(value = "错误请求计数。", example = "200")
    private long errorRequests;
}