package com.databuff.webapp.biz.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.databuff.common.constants.TSDBIndex;
import com.databuff.common.tsdb.TSDBOperateUtil;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.*;
import com.databuff.common.utils.DateUtils;
import com.databuff.dao.mysql.BizObservabilityMapper;
import com.databuff.entity.*;
import com.databuff.util.MetricsUtil;
import com.databuff.webapp.config.common.CommonResponse;
import com.databuff.webapp.util.ThreadPoolUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.databuff.common.constants.Constant.Metric.DATABASE_DATABUFF;
import static com.databuff.common.constants.Constant.Trace.BIZ_EVENT_ID;
import static com.databuff.common.constants.TSDBIndex.METRIC_DATABUFF_ALARM;

@Service
@Slf4j
public class ScenarioV1Service {

    @Autowired
    private BizObservabilityMapper bizMapper;
    @Autowired
    private TSDBOperateUtil tsdbOperateUtil;

    @Autowired
    private BizAlarmService bizAlarmService;

    public CommonResponse addBizScenario(BizScenario bizScenario) {
        CommonResponse commonResponse = new CommonResponse();

        if (!validateBasicParams(bizScenario, commonResponse)) {
            return commonResponse;
        }

        if (bizScenario.getVersion() == 1) {
            if (!processV1Scenario(bizScenario, commonResponse)) {
                return commonResponse;
            }
        } else if (bizScenario.getVersion() == 2) {
            if (!processV2Scenario(bizScenario, commonResponse)) {
                return commonResponse;
            }
        } else {
            commonResponse.setMessage("不支持的业务场景版本号：" + bizScenario.getVersion());
            return commonResponse;
        }

        return saveScenario(bizScenario, commonResponse);
    }

    private boolean validateBasicParams(BizScenario bizScenario, CommonResponse commonResponse) {
        if (bizScenario.getBizGroupId() == null) {
            commonResponse.setMessage("业务分组不能为空");
            return false;
        }
        if (StringUtils.isBlank(bizScenario.getBizScenarioName())) {
            commonResponse.setMessage("业务场景名称不能为空");
            return false;
        }
        return !isScenarioNameExists(bizScenario, commonResponse);
    }

    private boolean isScenarioNameExists(BizScenario bizScenario, CommonResponse commonResponse) {
        BizSearch search = new BizSearch();
        search.setBizScenarioName(bizScenario.getBizScenarioName());
        search.setApiKey(bizScenario.getApiKey());
        List<BizScenario> existingNames = bizMapper.getBizScenarioList(search);
        if (!CollectionUtils.isEmpty(existingNames)) {
            commonResponse.setMessage("该名称已存在");
            return true;
        }
        return false;
    }

    private boolean processV1Scenario(BizScenario bizScenario, CommonResponse commonResponse) {
        if (CollectionUtils.isEmpty(bizScenario.getBizScenarioEvents())) {
            commonResponse.setMessage("关联业务事件不能为空");
            return false;
        }
        bizScenario.setBizEvents(JSON.toJSONString(bizScenario.getBizScenarioEvents()));
        bizScenario.setScenarioGraph(null);
        bizScenario.setKpiConfig(null);
        return true;
    }

    private boolean processV2Scenario(BizScenario bizScenario, CommonResponse commonResponse) {
        BizScenarioGraph graph = bizScenario.getScenarioGraphObject();
        BizKpiConfig kpiConfig = bizScenario.getKpiConfigObject();

        if (!validateV2GraphIsPresent(graph, commonResponse)) {
            return false;
        }
        if (!validateKpiEventInGraph(kpiConfig, graph, commonResponse)) {
            return false;
        }
        serializeV2Objects(bizScenario, graph, kpiConfig);
        return true;
    }

    private boolean validateV2GraphIsPresent(BizScenarioGraph graph, CommonResponse commonResponse) {
        if (graph == null || CollectionUtils.isEmpty(graph.getNodes())) {
            commonResponse.setMessage("V2版本业务场景的流程图结构不能为空且必须包含节点");
            return false;
        }
        return true;
    }

    private boolean validateKpiEventInGraph(BizKpiConfig kpiConfig, BizScenarioGraph graph, CommonResponse commonResponse) {
        if (kpiConfig == null || StringUtils.isBlank(kpiConfig.getSourceEventId())) {
            return true; // KPI未配置或未指定源事件，无需校验
        }
        String kpiSourceEventId = kpiConfig.getSourceEventId();
        boolean kpiEventFound = false;
        // graph 和 graph.getNodes() 在调用此方法前已由 validateV2GraphIsPresent 确认非空
        nodeLoop:
        for (BizScenarioNodeV2 node : graph.getNodes()) {
            if (node.getEvents() != null && !CollectionUtils.isEmpty(node.getEvents().getIncluded())) {
                for (BizScenarioIncludedEventV2 event : node.getEvents().getIncluded()) {
                    if (event != null && kpiSourceEventId.equals(event.getBizEventId())) {
                        kpiEventFound = true;
                        break nodeLoop;
                    }
                }
            }
        }
        if (!kpiEventFound) {
            commonResponse.setMessage("KPI指标所对应的业务事件 (" + kpiSourceEventId + ") 必须配置在此业务场景的节点中");
        }
        return kpiEventFound;
    }

    private void serializeV2Objects(BizScenario bizScenario, BizScenarioGraph graph, BizKpiConfig kpiConfig) {
        // graph 在调用此方法前已确认非空
        bizScenario.setScenarioGraph(JSON.toJSONString(graph));
        if (kpiConfig != null) {
            bizScenario.setKpiConfig(JSON.toJSONString(kpiConfig));
        } else {
            bizScenario.setKpiConfig(null);
        }
        bizScenario.setBizEvents(null);
    }

    private CommonResponse saveScenario(BizScenario bizScenario, CommonResponse commonResponse) {

        if (StringUtils.isNotBlank(commonResponse.getMessage()) && !"SUCCESS".equals(commonResponse.getMessage())) {
            // Double check, 理论上不应执行到这里如果前面校验失败已返回
            return commonResponse;
        }
        bizMapper.insertBizScenario(bizScenario);
        bizScenario.setApiKey(null); // 清除敏感信息
        commonResponse.setData(bizScenario);
        return commonResponse;
    }


    public CommonResponse updateBizScenario(BizScenario bizScenario) {
        CommonResponse commonResponse = new CommonResponse();

        if (!validateIdPresentForUpdate(bizScenario, commonResponse)) {
            return commonResponse;
        }
        if (!validateNameNotConflictingForUpdate(bizScenario, commonResponse)) {
            return commonResponse;
        }

        // 假设 bizScenario.getVersion() 在更新请求中是有效的，代表了要更新的场景的当前版本
        // 如果版本可能在更新时改变，或者需要从数据库获取，此逻辑需要调整
        Integer version = bizScenario.getVersion();
        if (version == null) {
            // 为简化，若版本未知，则报错。实际可能需要加载旧数据确定版本。
            commonResponse.setMessage("更新操作必须明确场景版本。");
            return commonResponse;
        }

        if (version == 1) {
            if (!processV1DataForUpdate(bizScenario, commonResponse)) {
                return commonResponse;
            }
        } else if (version == 2) {
            if (!processV2DataForUpdate(bizScenario, commonResponse)) {
                return commonResponse;
            }
        } else {
            commonResponse.setMessage("不支持的业务场景版本号：" + version);
            return commonResponse;
        }

        return performUpdateDbOperation(bizScenario, commonResponse);
    }

    private boolean validateIdPresentForUpdate(BizScenario bizScenario, CommonResponse commonResponse) {
        if (bizScenario.getId() == null) {
            commonResponse.setMessage("业务场景ID不能为空");
            return false;
        }
        // 还可以增加查询数据库确认ID是否真实存在的逻辑，但通常更新操作会返回影响行数
        return true;
    }

    private boolean validateNameNotConflictingForUpdate(BizScenario bizScenario, CommonResponse commonResponse) {
        if (StringUtils.isBlank(bizScenario.getBizScenarioName())) {
            return true; // 名称为空字符串或null，不进行重名校验 (MyBatis动态更新不会设置它)
        }
        BizSearch search = new BizSearch();
        search.setBizScenarioName(bizScenario.getBizScenarioName());
        search.setApiKey(bizScenario.getApiKey());
        List<BizScenario> existingNames = bizMapper.getBizScenarioList(search);

        if (!CollectionUtils.isEmpty(existingNames)) {
            for (BizScenario existing : existingNames) {
                // 如果找到同名场景，且不是当前正在更新的场景ID，则冲突
                if (!existing.getId().equals(bizScenario.getId())) {
                    commonResponse.setMessage("该名称已存在");
                    return false;
                }
            }
        }
        return true;
    }

    private boolean processV1DataForUpdate(BizScenario bizScenario, CommonResponse commonResponse) {
        // 如果用户在更新请求中显式地传递了 bizScenarioEvents (即使是空列表)
        if (bizScenario.getBizScenarioEvents() != null) {
            if (CollectionUtils.isEmpty(bizScenario.getBizScenarioEvents())) {
                // 如果用户想将事件列表清空，而业务要求V1必须有关联事件
                commonResponse.setMessage("V1场景的关联业务事件列表在更新时不能为空");
                return false; // 或者根据业务，允许清空则移除此校验
            }
            bizScenario.setBizEvents(JSON.toJSONString(bizScenario.getBizScenarioEvents()));
        }
        // 如果 bizScenario.getBizScenarioEvents() 为 null，则 bizEvents 字段不会被 Mybatis 更新
        // 确保V1更新时，V2特定字段不被错误设置（如果它们是null，MyBatis的if test会跳过）
        // bizScenario.setScenarioGraph(null); // 如果DTO中这些是null，MyBatis的if不会更新
        // bizScenario.setKpiConfig(null);
        return true;
    }

    private boolean processV2DataForUpdate(BizScenario bizScenario, CommonResponse commonResponse) {
        BizScenarioGraph graphObject = bizScenario.getScenarioGraphObject();
        BizKpiConfig kpiConfigObject = bizScenario.getKpiConfigObject();

        // 规则1: 如果提供了KpiConfigObject且其有sourceEventId，则GraphObject也必须提供且有效
        if (kpiConfigObject != null && StringUtils.isNotBlank(kpiConfigObject.getSourceEventId())) {
            if (!validateV2GraphIsPresent(graphObject, commonResponse)) { // 复用add时的校验
                commonResponse.setMessage("当更新KPI并指定源事件时，V2版本业务场景的流程图结构不能为空且必须包含节点");
                return false;
            }
            // 此时 graphObject 已确认有效，可以用于后续KPI事件校验
            if (!validateKpiEventInGraph(kpiConfigObject, graphObject, commonResponse)) { // 复用add时的校验
                return false;
            }
        }

        // 规则2: 如果单独提供了GraphObject (即用户想更新图)，则图必须有效
        // (此情况已包含在规则1的graphObject校验中，如果kpiConfigObject也提供了的话)
        // 但如果kpiConfigObject未提供或其sourceEventId为空，仍需校验提供的graphObject
        if (graphObject != null && !validateV2GraphIsPresent(graphObject, commonResponse)) {
            return false;
        }

        // 序列化用户提供的对象。如果对象为null，对应JSON字符串也应为null或不设置，
        // 以便MyBatis的动态UPDATE正确工作。
        if (graphObject != null) {
            bizScenario.setScenarioGraph(JSON.toJSONString(graphObject));
        }
        if (kpiConfigObject != null) {
            bizScenario.setKpiConfig(JSON.toJSONString(kpiConfigObject));
        }
        return true;
    }

    private CommonResponse performUpdateDbOperation(BizScenario bizScenario, CommonResponse commonResponse) {

        if (StringUtils.isNotBlank(commonResponse.getMessage()) && !"SUCCESS".equals(commonResponse.getMessage())) {
            return commonResponse; // 如果前面有校验失败，直接返回
        }
        int updatedCount = bizMapper.updateBizScenario(bizScenario);
        if (updatedCount == 0) {
            // 如果ID有效但没有行被更新，可能数据未变，或记录不存在 (应由validateIdPresentForUpdate早期捕获)
            // 此处可以进一步检查记录是否存在，以给出更准确的反馈
            BizSearch search = new BizSearch();
            search.setApiKey(bizScenario.getApiKey());
            search.setBizScenarioId(bizScenario.getId());
            List<BizScenario> existing = bizMapper.getBizScenarioList(search);
            if (CollectionUtils.isEmpty(existing)) {
                commonResponse.setMessage("更新失败：未找到ID为 " + bizScenario.getId() + " 的业务场景。");
            } else {
                commonResponse.setMessage("更新失败或数据未发生变化。");
            }
        }
        return commonResponse;
    }


    @Transactional(rollbackFor = Exception.class)
    public Integer delBizScenario(Integer id, String apiKey) {
        return bizMapper.delBizScenario(id, apiKey);
    }


    public List<BizScenario> bizScenarios(BizSearch search) {
        final List<BizScenario> bizScenarioList = bizMapper.getBizScenarioList(search);
        for (BizScenario bizScenario : bizScenarioList) {
            //前端交互都使用对象 避免前端二次转换
            BizScenarioGraph bizScenarioGraph = JSON.parseObject(bizScenario.getScenarioGraph(), BizScenarioGraph.class);
            bizScenario.setScenarioGraphObject(bizScenarioGraph);
            bizScenario.setScenarioGraph(null);

            BizKpiConfig bizKpiConfig = JSON.parseObject(bizScenario.getKpiConfig(), BizKpiConfig.class);
            bizScenario.setKpiConfigObject(bizKpiConfig);
            bizScenario.setKpiConfig(null);
        }

        if (search.getBizEventId() != null) {
            final Integer targetEventId = search.getBizEventId();
            final String targetEventIdStr = String.valueOf(targetEventId);
            List<BizScenario> rets = new ArrayList<>();
            //如果事件id不为空。根据事件id筛选
            for (BizScenario scenario : bizScenarioList) {
                if (scenario == null) continue;

                boolean found = false;
                Integer version = scenario.getVersion();

                if (version != null && version == 1) {
                    // --- 处理 V1 ---
                    found = findEventInEventList(scenario, targetEventId, found);
                } else if (version != null && version == 2) {
                    // --- 处理 V2 ---
                    found = findEventInGraphNodes(scenario, targetEventId, found);

                    found = findEventInKpiConfig(scenario, found, targetEventIdStr);
                }
                // else: 版本号未知或场景数据不完整，忽略

                if (found) {
                    rets.add(scenario);
                }
            }
            return rets;
        } else {
            return bizScenarioList;
        }
    }

    private static boolean findEventInKpiConfig(BizScenario scenario, boolean found, String targetEventIdStr) {
        // 新增：检查 KPI 配置
        if (!found) { // 仅当在节点中未找到时才检查 KPI
            BizKpiConfig kpiConfig = scenario.getKpiConfigObject();
            // 如果对象为空但字符串存在（可能process时解析失败），尝试再次解析
            // 如果解析失败，kpiConfig会是null，后续的 kpiConfig != null 会处理
            if (kpiConfig == null && StringUtils.isNotBlank(scenario.getKpiConfig())) {
                try {
                    kpiConfig = JSON.parseObject(scenario.getKpiConfig(), BizKpiConfig.class);
                } catch (Exception e) {
                    log.warn("bizScenarios: 再次解析场景ID {} 的 V2 kpiConfig JSON 失败: {}. 此KPI配置的检查将被跳过。", scenario.getId(), e.getMessage());
                }
            }

            if (kpiConfig != null && StringUtils.isNotBlank(kpiConfig.getSourceEventId())) {
                if (targetEventIdStr.equals(kpiConfig.getSourceEventId())) {
                    found = true;
                }
            }
        }
        return found;
    }

    private static boolean findEventInEventList(BizScenario scenario, Integer targetEventId, boolean found) {
        List<BizScenarioEvent> bizEventReqs = scenario.getBizScenarioEvents(); // 优先使用对象
        if (CollectionUtils.isEmpty(bizEventReqs) && StringUtils.isNotBlank(scenario.getBizEvents())) {
            // 如果对象为空但字符串存在（可能process时解析失败），尝试再次解析
            try {
                bizEventReqs = JSON.parseArray(scenario.getBizEvents(), BizScenarioEvent.class);
            } catch (Exception e) {
                log.warn("bizScenarios: 再次解析场景ID {} 的 V1 bizEvents JSON 失败: {}", scenario.getId(), e.getMessage());
            }
        }
        if (!CollectionUtils.isEmpty(bizEventReqs)) {
            for (BizScenarioEvent req : bizEventReqs) {
                if (req != null && targetEventId.equals(req.getBizEventId())) {
                    found = true;
                    break;
                }
            }
        }
        return found;
    }

    private static boolean findEventInGraphNodes(BizScenario scenario, Integer targetEventId, boolean found) {
        BizScenarioGraph graph = scenario.getScenarioGraphObject();
        if (graph == null && StringUtils.isNotBlank(scenario.getScenarioGraph())) {
            // 如果对象为空但字符串存在，尝试再次解析
            try {
                graph = JSON.parseObject(scenario.getScenarioGraph(), BizScenarioGraph.class);
            } catch (Exception e) {
                log.warn("bizScenarios: 再次解析场景ID {} 的 V2 scenarioGraph JSON 失败: {}", scenario.getId(), e.getMessage());
            }
        }

        if (graph != null && !CollectionUtils.isEmpty(graph.getNodes())) {
            nodeLoop:
            // 添加标签以便跳出外层循环
            for (BizScenarioNodeV2 node : graph.getNodes()) {
                if (node != null && node.getEvents() != null && !CollectionUtils.isEmpty(node.getEvents().getIncluded())) {
                    for (BizScenarioIncludedEventV2 eventRef : node.getEvents().getIncluded()) {
                        if (eventRef != null && StringUtils.isNotBlank(eventRef.getBizEventId())) {
                            try {
                                int eventIdFromRef = Integer.parseInt(eventRef.getBizEventId());
                                if (targetEventId.equals(eventIdFromRef)) {
                                    found = true;
                                    break nodeLoop; // 找到匹配，跳出所有节点和事件循环
                                }
                            } catch (NumberFormatException nfe) {
                                // 忽略格式错误的 eventId
                            }
                        }
                    }
                }
            }
        }
        return found;
    }

    public List<BizScenarioMap> bizScenarioMap(BizSearch search) {
        if (search.getBizScenarioId() == null) {
            return new ArrayList<>();
        }
        BizScenario bizScenario = bizMapper.getBizScenarioById(search.getBizScenarioId(), search.getApiKey());
        if (bizScenario == null) {
            return new ArrayList<>();
        }
        List<BizScenarioMap> bizScenarioMaps = new ArrayList<>();
        List<BizScenarioEvent> bizEventReqs = JSON.parseArray(bizScenario.getBizEvents(), BizScenarioEvent.class);
        long fromTimeVul = DateUtils.stringToLong("yyyy-MM-dd HH:mm:ss", search.getFromTime());
        long toTimeVul = DateUtils.stringToLong("yyyy-MM-dd HH:mm:ss", search.getToTime());
        Set<Integer> bizEventIds = new HashSet<>();
        bizEventReqs.stream().filter(Objects::nonNull).forEach(reqs -> {
            bizEventIds.add(reqs.getBizEventId());
            BizScenarioMap bizScenarioMap = new BizScenarioMap();
            bizScenarioMap.setBizEventId(reqs.getBizEventId());
            bizScenarioMap.setBizEventName(reqs.getBizEventName());
            bizScenarioMap.setOrder(reqs.getOrder());
            //先全部设置为删除，后面会修改状态
            bizScenarioMap.setStatus(2);
            bizScenarioMaps.add(bizScenarioMap);
        });

        BizSearch eventSearch = new BizSearch();
        eventSearch.setBizEventIds(new ArrayList<>(bizEventIds));
        eventSearch.setApiKey(search.getApiKey());
        List<BizEvent> bizEvents = bizMapper.getBizEventList(eventSearch);
        Map<Integer, BizEvent> bizEventMap = bizEvents.stream().collect(Collectors.toMap(BizEvent::getId, b -> b));

        List<Where> wheres = new ArrayList<>();
        wheres.add(Where.gte("time", fromTimeVul));
        wheres.add(Where.lt("time", toTimeVul));
        wheres.add(Where.in("bizEventId", new ArrayList<>(bizEventIds)));

        Map<Integer, JSONObject> moreMap = new ConcurrentHashMap<>();
        Future bizEventInfoFuture = ThreadPoolUtil.EXECUTOR.submit(() -> {
            try {
                QueryBuilder queryBuilder = new QueryBuilder()
                        .setDatabaseName(search.getApiKey() + "_" + TSDBIndex.TSDB_APM_METRIC_DATABASE_NAME)
                        .setMeasurement(TSDBIndex.TSDB_METRIC_NAME_BIZ_EVENT)
                        .addAgg(Aggregation.of(AggFun.SUM, "cnt", "cnt"))
                        .addAgg(Aggregation.of(AggFun.SUM, "error", "error"))
                        .addAgg(Aggregation.of(AggFun.SUM, "sumDuration", "sumDuration"))
                        .addAllWhere(wheres)
                        .addGroupBy("bizEventId");
                TSDBResultSet resultSet = tsdbOperateUtil.executeQuery(queryBuilder);
                if (resultSet != null && resultSet.getResults() != null && !resultSet.getResults().isEmpty()) {
                    List<TSDBSeries> seriesList = resultSet.getResults().get(0).getSeries();
                    if (seriesList != null) {
                        seriesList.forEach(s -> { // TSDBSeries s
                            // 确保 s.getValues() 和 s.getTags() 不为 null，并且 BIZ_EVENT_ID 标签存在
                            if (s.getValues() == null || s.getTags() == null || s.getTags().get(BIZ_EVENT_ID) == null) {
                                return; // 或者根据实际情况记录日志并跳过
                            }

                            Integer bizEventIdFromTag = null;
                            try {
                                bizEventIdFromTag = Integer.parseInt(s.getTags().get(BIZ_EVENT_ID));
                            } catch (NumberFormatException e) {
                                log.error("Failed to parse bizEventId from tags: " + s.getTags().get(BIZ_EVENT_ID) + " for series: " + s.getTags(), e);
                                return; // 如果 bizEventId 无效，则跳过此 series
                            }

                            // 定义获取值的辅助方法
                            // 为了简洁，直接在循环内实现逻辑
                            Integer finalBizEventIdFromTag = bizEventIdFromTag;
                            s.getValues().forEach(v -> { // List<Object> values = v
                                JSONObject info = new JSONObject();

                                Integer cntIndex = s.getColumnIndex("cnt");
                                Long cntValue = (cntIndex == null || cntIndex >= v.size() || v.get(cntIndex) == null)
                                        ? 0L
                                        : ((Number) v.get(cntIndex)).longValue();
                                info.put("cnt", cntValue);

                                Integer errorIndex = s.getColumnIndex("error");
                                Long errorValue = (errorIndex == null || errorIndex >= v.size() || v.get(errorIndex) == null)
                                        ? 0L
                                        : ((Number) v.get(errorIndex)).longValue();
                                info.put("error", errorValue);

                                Integer sumDurationIndex = s.getColumnIndex("sumDuration");
                                Long sumDurationValue = (sumDurationIndex == null || sumDurationIndex >= v.size() || v.get(sumDurationIndex) == null)
                                        ? 0L
                                        : ((Number) v.get(sumDurationIndex)).longValue();
                                info.put("sumDuration", sumDurationValue);

                                moreMap.put(finalBizEventIdFromTag, info);
                            });
                        });
                    }
                }
            } catch (Throwable e) {
                log.error("query biz event error ", e);
            }
        });
        Map<String, Long> alarmCntInfo = new ConcurrentHashMap<>();


        QueryBuilder alarmBuilder = new QueryBuilder()
                .setDatabaseName(MetricsUtil.generateFullDatabase(search.getApiKey(), DATABASE_DATABUFF))
                .setMeasurement(METRIC_DATABUFF_ALARM)
                //只有来做显示是否有告警使用，不能当做告警数用
                .addAgg(Aggregation.of("trigger.cnt"))
                .addAllWhere(wheres)
                .addGroupBy("bizEventId");

        Future bizEventAlarmFuture = ThreadPoolUtil.EXECUTOR.submit(() -> {
            alarmCntInfo.putAll(bizAlarmService.getEventIdAlarmCntMap(alarmBuilder));
        });

        try {
            bizEventInfoFuture.get();
            bizEventAlarmFuture.get();
        } catch (Throwable e) {
            log.error("query biz event future error ", e);
        }

        for (BizScenarioMap bizS : bizScenarioMaps) {
            JSONObject info = moreMap.get(bizS.getBizEventId());
            if (info != null) {
                bizS.setCnt(info.getLong("cnt"));
                bizS.setError(info.getLong("error"));
                bizS.setSumDuration(info.getLong("sumDuration"));
            }
            BizEvent bizEvent = bizEventMap.get(bizS.getBizEventId());
            if (bizEvent != null) {
                bizS.setBizEventName(bizEvent.getBizName());
                bizS.setStatus(bizEvent.getEnabled() ? 0 : 1);
            }
            if (alarmCntInfo.containsKey(bizS.getBizEventId().toString())) {
                bizS.setAlarmCnt(alarmCntInfo.get(bizS.getBizEventId().toString()));
            } else {
                bizS.setAlarmCnt(0L);
            }
        }

        return bizScenarioMaps;
    }


}
