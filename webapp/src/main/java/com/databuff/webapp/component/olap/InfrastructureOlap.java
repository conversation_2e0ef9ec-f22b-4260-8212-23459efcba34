package com.databuff.webapp.component.olap;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.utils.StringUtils;
import com.databuff.common.constants.Constant;
import com.databuff.dao.mysql.HostInfoEntityMapper;
import com.databuff.dao.starrocks.InfrastructureSrMapper;
import com.databuff.entity.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class InfrastructureOlap {

    @Autowired
    private InfrastructureSrMapper infrastructureSrMapper;
    @Autowired
    private HostInfoEntityMapper hostInfoEntityMapper;

    @SneakyThrows
    public JSONObject listProcessGroupV2(BaseSearch search) {
        JSONObject data = new JSONObject();
        data.put("status", 200);
        data.put("message", "SUCCESS");
        data.put("data", new ArrayList<>());
        data.put("size", search.getSize());
        data.put("total", 0);
        Long total = infrastructureSrMapper.processCount(search);
        if (total==0){
            return data;
        }
        List<ProcessEntity> processEntities = infrastructureSrMapper.processList(search);
        data.put("data", processEntities);
        data.put("size", search.getSize());
        data.put("total", total);
        return data;
    }

    public List<JSONObject> listProcessNames(BaseSearch search) {
        List<ProcessEntity> processEntities = infrastructureSrMapper.processNames(search);
        List<JSONObject> rets = new ArrayList<>();
        Set<String> processSet = new HashSet<>();
        for (ProcessEntity processEntity : processEntities) {
            String pName = processEntity.getPname();
            if (processSet.contains(pName)) {
                continue;
            }
            processSet.add(pName);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", pName);
            rets.add(jsonObject);
        }
        return rets;
    }


    public Map<String, Set<String>> listContainerOrProcessTags(BaseSearch search, String table, boolean isContainer) {
        List<String> tags = new ArrayList<>();
        if (isContainer) {
            List<String> containerList = infrastructureSrMapper.containersDistinctTags(search);
            containerList.forEach(tag -> {
                if (StringUtils.isNotEmpty(tag)) {
                    tags.add(tag);
                }
            });
        } else {
            List<String> processEntities = infrastructureSrMapper.processDistinctTags(search);
            processEntities.forEach(tag -> {
                if (StringUtils.isNotEmpty(tag)) {
                    tags.add(tag);
                }
            });
        }
        Map<String, Set<String>> ret = new HashMap<>();
        for (String tag : tags) {
            String[] parts = tag.split(",");
            for (String part : parts) {
                String[] items = part.split(":");
                if (items.length>1){
                    Set<String> values = ret.computeIfAbsent(items[0], k -> new HashSet<>());
                    values.add(items[1]);                }
            }
        }
        return ret;
    }

    public List<JSONObject> listContainerNames(BaseSearch search) {
        List<Container> containerList = infrastructureSrMapper.containerNames(search);
        List<JSONObject> rets = new ArrayList<>();
        Set<String> containerNameSet = new HashSet<>();
        for (Container container : containerList) {
            String containerName = container.getName();
            if (containerNameSet.contains(containerName)) {
                continue;
            }
            containerNameSet.add(containerName);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", containerName);
            rets.add(jsonObject);
        }
        return rets;
    }
    public List<JSONObject> listContainer(BaseSearch search) {
        List<Container> containerList = infrastructureSrMapper.containerList(search);
        List<JSONObject> rets = new ArrayList<>();
        for (Container container : containerList) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("spuid", container.getSpuid());
            jsonObject.put("name", container.getName());
            jsonObject.put("hostName", container.getHostName());
            jsonObject.put("totalPct", container.getTotalPct());
            jsonObject.put("memRss", container.getMemRss());
            jsonObject.put("netSentBps", container.getNetSentBps());
            jsonObject.put("netRcvdBps", container.getNetRcvdBps());
            jsonObject.put("state", container.getState());
            jsonObject.put("started", container.getStarted());
            jsonObject.put(Constant.API_KEY2, container.getDfApiKey());
            jsonObject.put("timestamp", container.getTimestamp());
            JSONObject containerData = JSON.parseObject(container.getData());
            jsonObject.putAll(containerData);
            rets.add(jsonObject);
            //填充主机操作系统
            fillHostOs(rets, rets.stream().map(j -> j.getString("hostName")).collect(Collectors.toList()), search.getApiKey());
        }
        return rets;
    }


    public void fillHostOs(List<JSONObject> rets, List<String> hostNames, String apiKey) {
        if (CollectionUtils.isEmpty(hostNames)) {
            return;
        }
        Map<String, Map<String, String>>  hostGOOSs = hostInfoEntityMapper.hostGOOS(hostNames,apiKey);
        rets.stream().forEach(ret -> {
            String hostName = ret.getString("hostName");
            Map<String, String> hostOs = hostGOOSs.get(hostName);
            if (hostOs != null) {
                ret.put("hostOs", hostOs.getOrDefault("goos", ""));
            }else{
                ret.put("hostOs","");
            }
        });
    }

    public void fillHostOs(ProcessEntity ret, String apiKey) {
        List<String> hostNames = new ArrayList<>();
        hostNames.add(ret.getHostName());
        Map<String, Map<String, String>>  hostGOOSs = hostInfoEntityMapper.hostGOOS(hostNames,apiKey);
        String hostName = ret.getHostName();

        Map<String, String> hostOs = hostGOOSs.get(hostName);
        if (hostOs != null) {
            ret.setHostOs(hostOs.getOrDefault("goos", ""));
        }else{
            ret.setHostOs("");
        }
    }
}
