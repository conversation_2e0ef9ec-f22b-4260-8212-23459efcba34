package com.databuff.webapp.component.olap;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.constants.Constant;
import com.databuff.dao.starrocks.K8sSrMapper;
import com.databuff.entity.K8sEntity;
import com.databuff.entity.K8sParam;
import com.databuff.entity.K8sPodEntity;
import com.databuff.entity.dto.TimeValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class K8sOlap {


    @Autowired
    private K8sSrMapper k8sSrMapper;

    public List<JSONObject> listCluster(K8sParam k8sParam) {
        List<K8sEntity> k8sEntities = k8sSrMapper.K8sClusterList(k8sParam);
        List<JSONObject> list = new ArrayList<>();
        for (K8sEntity k8sEntity : k8sEntities) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("clusterId", k8sEntity.getClusterId());
            jsonObject.put("clusterName", k8sEntity.getClusterName());
            jsonObject.put(Constant.API_KEY2, k8sEntity.getApiKey());
            jsonObject.put("timestamp", k8sEntity.getTimestamp());
            String data = k8sEntity.getData();
            jsonObject.put("data", JSONObject.parse(data));
            list.add(jsonObject);
        }
        return list;
    }

    public List<JSONObject> listService(K8sParam k8sParam) {
        List<K8sEntity> k8sEntities = k8sSrMapper.K8sServiceList(k8sParam);
        List<JSONObject> list = new ArrayList<>();
        for (K8sEntity k8sEntity : k8sEntities) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", k8sEntity.getName());
            jsonObject.put("clusterId", k8sEntity.getClusterId());
            jsonObject.put("clusterName", k8sEntity.getClusterName());
            jsonObject.put("namespace", k8sEntity.getNamespace());
            jsonObject.put(Constant.API_KEY2, k8sEntity.getApiKey());
            jsonObject.put("timestamp", k8sEntity.getTimestamp());
            String data = k8sEntity.getData();
            jsonObject.put("data", JSONObject.parse(data));
            list.add(jsonObject);
        }
        return list;
    }

    public List<JSONObject> listNode(K8sParam k8sParam) {
        List<K8sEntity> k8sEntities = k8sSrMapper.K8sNodeList(k8sParam);
        List<JSONObject> list = new ArrayList<>();
        for (K8sEntity k8sEntity : k8sEntities) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", StringUtils.isBlank(k8sEntity.getNodeName())?k8sEntity.getName():k8sEntity.getNodeName());
            jsonObject.put("clusterId", k8sEntity.getClusterId());
            jsonObject.put("clusterName", k8sEntity.getClusterName());
            jsonObject.put(Constant.API_KEY2, k8sEntity.getApiKey());
            jsonObject.put("timestamp", k8sEntity.getTimestamp());
            String data = k8sEntity.getData();
            jsonObject.put("data", JSONObject.parse(data));
            list.add(jsonObject);
        }
        return list;
    }

    public List<JSONObject> listPod(K8sParam k8sParam) {
        List<K8sPodEntity> k8sPodEntities = k8sSrMapper.K8sPodList(k8sParam);
        List<JSONObject> list = new ArrayList<>();
        for (K8sPodEntity k8sPodEntity : k8sPodEntities) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", k8sPodEntity.getName());
            jsonObject.put("type", k8sPodEntity.getType());
            jsonObject.put("clusterId", k8sPodEntity.getClusterId());
            jsonObject.put("clusterName", k8sPodEntity.getClusterName());
            jsonObject.put("namespace", k8sPodEntity.getNamespace());
            jsonObject.put(Constant.API_KEY2, k8sPodEntity.getApiKey());
            jsonObject.put("timestamp", k8sPodEntity.getTimestamp());
            jsonObject.put("wlName", k8sPodEntity.getWlName());
            jsonObject.put("wlKind", k8sPodEntity.getWlKind());
            jsonObject.put("nodeName", k8sPodEntity.getNodeName());
            String data = k8sPodEntity.getData();
            jsonObject.put("data", JSONObject.parse(data));
            list.add(jsonObject);
        }
        return list;
    }

    public Long countPod(K8sParam k8sParam, List<String> notStatusList) {
        k8sParam.setNotStatus(notStatusList);
        return k8sSrMapper.podCount(k8sParam);
    }

    public List<JSONObject> listNamespace(K8sParam k8sParam) {
        List<K8sEntity> k8sEntities = k8sSrMapper.K8sNamespaceList(k8sParam);
        List<JSONObject> list = new ArrayList<>();
        for (K8sEntity k8sEntity : k8sEntities) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", k8sEntity.getName());
            jsonObject.put("clusterId", k8sEntity.getClusterId());
            jsonObject.put("clusterName", k8sEntity.getClusterName());
            jsonObject.put(Constant.API_KEY2, k8sEntity.getApiKey());
            jsonObject.put("timestamp", k8sEntity.getTimestamp());
            String data = k8sEntity.getData();
            jsonObject.put("data", JSONObject.parse(data));
            list.add(jsonObject);
        }
        return list;
    }

    public List<JSONObject> listWorkload(K8sParam k8sParam) {
        List<K8sEntity> k8sEntities = k8sSrMapper.K8sWorkloadList(k8sParam);
        List<JSONObject> list = new ArrayList<>();
        for (K8sEntity k8sEntity : k8sEntities) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", k8sEntity.getName());
            jsonObject.put("clusterId", k8sEntity.getClusterId());
            jsonObject.put("clusterName", k8sEntity.getClusterName());
            jsonObject.put(Constant.API_KEY2, k8sEntity.getApiKey());
            jsonObject.put("timestamp", k8sEntity.getTimestamp());
            jsonObject.put("namespace", k8sEntity.getNamespace());
            jsonObject.put("type", k8sEntity.getType());
            String data = k8sEntity.getData();
            jsonObject.put("data", JSONObject.parse(data));
            list.add(jsonObject);
        }
        return list;
    }

    public Map<String, Long> queryClusterResCount(List<String> clusterIds, K8sParam search, int type) {
        search.setClusterIds(clusterIds);
        List<TimeValue> rets = new ArrayList<>();
        switch (type) {
            case 1:
                rets = k8sSrMapper.countNsByCluster(search);
                break;
            case 2:
                rets = k8sSrMapper.countWlByCluster(search);
                break;
            case 3:
                rets = k8sSrMapper.countWlByNsCluster(search);
                break;
            case 4:
                rets = k8sSrMapper.countSvcByNsCluster(search);
                break;
            case 5:
                rets = k8sSrMapper.countPodByCluster(search);
                break;
            case 6:
                rets = k8sSrMapper.countSvcByCluster(search);
                break;
            case 7:
                rets = k8sSrMapper.countPodByNsClusterStatus(search);
                break;
            case 8:
                rets = k8sSrMapper.countPodByNsCluster(search);
                break;
            case 9:
                rets = k8sSrMapper.countPodByWLClusterStatus(search);
                break;
            case 10:
                rets = k8sSrMapper.countPodByNodeClusterStatus(search);
                break;
        }
        Map<String, Long> ret = new HashMap<>();
        for (TimeValue timeValue : rets) {
            Long cnt = timeValue.getValue().longValue();
            String clusterId = timeValue.getTag1().toString();
            if (timeValue.getTag3() != null) {
                ret.put(clusterId + "-" + timeValue.getTag2() + "-" + timeValue.getTag3(), cnt);
            } else if (timeValue.getTag2() != null) {
                ret.put(clusterId + "-" + timeValue.getTag2(), cnt);
            } else {
                ret.put(clusterId, cnt);
            }
        }
        return ret;
    }

}
