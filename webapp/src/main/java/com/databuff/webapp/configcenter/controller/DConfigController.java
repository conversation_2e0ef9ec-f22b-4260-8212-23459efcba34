package com.databuff.webapp.configcenter.controller;

import com.alibaba.fastjson.JSON;
import com.databuff.client.JavaAgentClient;
import com.databuff.common.audit.AuditEntity;
import com.databuff.util.IDConfigManager;
import com.databuff.util.IDConfigRegister;
import dto.DConf;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.recipes.cache.CuratorCacheListener;
import org.audit4j.core.annotation.DatabuffAudit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(value = "databuff分布式配置管理")
@Slf4j
@RestController
@RequestMapping("/dConfigManage")
public class DConfigController {

    @Autowired
    protected IDConfigManager<DConf> dCuratorManager;

    @Autowired
    protected JavaAgentClient javaAgentClient;

    @Autowired
    protected IDConfigRegister idConfigRegister;

    @DatabuffAudit(action = "删除", entityType = "动态配置", hasLink = false)
    @ApiOperation(value = "删除节点")
    @DeleteMapping("/deleteNode")
    public void deleteNode(@ApiParam(name = "path", value = "节点路径", required = true) @RequestParam String path) throws Exception {
        final List<DConf> confs = dCuratorManager.getConfs(path);
        if (confs != null && !confs.isEmpty()) {
            for (DConf conf : confs) {
                AuditEntity.builder()
                        .id(conf.getKey())
                        .name(conf.getPath())
                        .desc(conf.getValue().toString())
                        .add();
            }
        }
        dCuratorManager.deleteNode(path);
    }

    @ApiOperation(value = "获取子路径及其数据")
    @GetMapping("/getPathData")
    public List<DConf> getConfs(@ApiParam(name = "path", value = "节点路径", required = true) @RequestParam String path) throws Exception {
        return dCuratorManager.getConfs(path);
    }

    @ApiOperation(value = "获取子节点路径")
    @GetMapping("/getChildrenPaths")
    public List<String> getChildrenPaths(@ApiParam(name = "path", value = "节点路径", required = false) @RequestParam(required = false) String path) throws Exception {
        return dCuratorManager.getChildrenPaths(path);
    }

    @DatabuffAudit(action = "保存", entityType = "动态配置", hasLink = false)
    @ApiOperation(value = "保存节点")
    @PostMapping("/saveNode")
    public void saveNode(@RequestBody DConf dConf) throws Exception {
        validateDConf(dConf);
        String path = dConf.getPath();
        if (path != null && path.startsWith("/javaagent")) {
            validateJavaAgentConfig(dConf);
        }
        dCuratorManager.saveNode(path, dConf, true);
        AuditEntity.builder()
                .id(dConf.getKey())
                .name(dConf.getPath())
                .desc(dConf.getValue().toString())
                .add();
    }

    protected void validateDConf(DConf dConf) throws Exception {
        if (dConf == null || dConf.getValue() == null) {
            throw new Exception("配置数据不能为空");
        }
    }

    protected void validateJavaAgentConfig(DConf dConf) throws Exception {
        final Object value = dConf.getValue();
        if (value instanceof Map) {
            return;
        }
        if (value instanceof String) {
            try {
                JSON.parseObject((String) value, Map.class);
            } catch (Exception e) {
                throw new Exception("配置数据格式错误，javaagent 模块要求配置为json格式", e);
            }
        } else {
            throw new Exception("配置数据格式错误，javaagent 模块要求配置为json格式");
        }
    }

    @ApiOperation(value = "订阅节点变化")
    @PostMapping("/subscribeNode")
    public void subscribeNodeChange(@ApiParam(name = "path", value = "节点路径", required = true) @RequestParam String path) {
        idConfigRegister.subscribeNodeChange(path, CuratorCacheListener.builder()
                .forCreates(childData -> log.info("Node added: Path={}, Version={}, Data={}", childData.getPath(), childData.getStat().getVersion(), new String(childData.getData())))
                .forChanges((oldNode, node) -> log.info("Node updated: OldNode Path={}, OldNode Version={}, OldNode Data={}, NewNode Path={}, NewNode Version={}, NewNode Data={}", oldNode.getPath(), oldNode.getStat().getVersion(), new String(oldNode.getData()), node.getPath(), node.getStat().getVersion(), new String(node.getData())))
                .forDeletes(oldNode -> log.info("Node removed: Path={}, Version={}, Data={}", oldNode.getPath(), oldNode.getStat().getVersion(), new String(oldNode.getData())))
        );
    }

    @ApiOperation(value = "初始化javaagent配置")
    @GetMapping("/javaagent/init")
    public DConf javaAgentInit() throws Exception {
        return javaAgentClient.init();
    }
}