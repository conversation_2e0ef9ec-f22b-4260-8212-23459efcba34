package com.databuff.webapp.configcenter.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Setter
@Getter
@ToString
@TableName( value ="config_info" )
public class ConfigInfo {


    @TableId(value = "id", type = IdType.AUTO )
    private Long id;

    @TableField(value = "data_id" )
    private String dataId;

    @TableField(value = "group_id" )
    private String groupId;

    @TableField(value = "content" )
    private String content;

    @TableField(value = "md5")
    private String md5;

    @TableField(value = "gmt_create")
    private Date gmtCreate;

    @TableField(value = "gmt_modified")
    private Date gmtModified;

    @TableField(value = "src_user")
    private String srcUser;

    @TableField(value = "src_ip")
    private String srcIp;

    @TableField(value = "app_name")
    private String appName;

    @TableField(value = "tenant_id")
    private String tenantId;

    @TableField(value = "c_desc")
    private String cDesc;

    @TableField(value = "c_use")
    private String cUse;

    @TableField(value = "effect")
    private String effect;

    @TableField(value = "type")
    private String type;

    @TableField(value = "c_schema")
    private String cSchema;


    public ConfigInfo(){}

    public ConfigInfo(ConfigInfoView configInfoView) {
        this.id = configInfoView.getId();
        this.dataId = configInfoView.getDataId();
        this.groupId = configInfoView.getGroupId();
        this.content = configInfoView.getContent();
        this.md5 = configInfoView.getMd5();
        this.gmtCreate = configInfoView.getGmtCreate();
        this.gmtModified = configInfoView.getGmtModified();
        this.srcUser = configInfoView.getSrcUser();
        this.srcIp = configInfoView.getSrcIp();
        this.appName = configInfoView.getAppName();
        this.tenantId = configInfoView.getTenantId();
        this.cDesc = configInfoView.getCDesc();
        this.cUse = configInfoView.getCUse();
        this.effect = configInfoView.getEffect();
        this.type = configInfoView.getType();
        this.cSchema = configInfoView.getCSchema();
    }

}
