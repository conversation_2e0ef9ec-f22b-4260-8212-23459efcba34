package com.databuff.webapp.kafka.stream;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.databuff.common.audit.AuditEntity;
import com.databuff.common.constants.KafkaTopicConstant;
import com.databuff.common.threadLocal.ThreadLocalUtil;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.entity.*;
import com.databuff.service.ConvergencePolicyService;
import com.databuff.service.DomainManagerObjService;
import com.databuff.webapp.admin.service.UserService;
import com.databuff.webapp.monitor.service.MonitorService;
import com.databuff.webapp.resp.action.service.RespPolicyService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import static com.databuff.common.constants.KafkaTopicConstant.*;

/**
 * @package com.databuff.webapp.kafka.stream
 * @className: DCGroupConsumer
 * @description: 管理域状态处理对象
 * 负责对管理域的状态变化进行处理，包括添加、删除、更新操作，通过监听Kafka消息触发对应逻辑。
 * 当组被创建时复制全局配置策略到新组，当组被删除时清理相关资源。
 * @company: dacheng
 * @author: yuzhili
 * @createDate: 2025/01/13
 */
@Slf4j
@Component
public class DCGroupConsumer {

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private ConvergencePolicyService convergencePolicyService;

    @Autowired
    private RespPolicyService respPolicyService;

    @Autowired
    private UserService userService;

    @Autowired
    private DomainManagerObjService domainManagerObjService;

    /**
     * 监听组操作事件，处理组添加、删除、更新请求。
     *
     * @param records Kafka消费者记录列表，包含审计事件信息
     */
    @KafkaListener(containerFactory = "kafkaListenerContainerFactory", groupId = KafkaTopicConstant.DC_DATABUFF_ACTION_GROUP + "_added", topics = KafkaTopicConstant.DC_DATABUFF_ACTION_GROUP)
    public void onGroupAdded(List<ConsumerRecord<String, String>> records) {
        log.info("onGroupAdded: {}", records);
        for (ConsumerRecord<String, String> record : records) {
            final String value = record.value();
            final AuditEntity auditEntity = JSONObject.parseObject(value, AuditEntity.class);
            if (auditEntity == null) {
                continue;
            }
            final String id = auditEntity.getId();
            if (id == null) {
                continue;
            }
            final String name = auditEntity.getName();
            final String action = auditEntity.getAction();
            final String outcome = auditEntity.getOutcome();
            final String actor = auditEntity.getActor();
            log.info("onGroupAdded: id={}, name={}, action={}, outcome={}, actor={}", id, name, action, outcome, actor);
            if (actor == null) {
                continue;
            }

            ThreadLocalUtil.doWithThreadLocal(actor, Lists.newArrayList(id),
                    () -> {
                        switch (action) {
                            case DC_DATABUFF_ACTION_GROUP_ADD:
                                processOnAdded(id, actor);
                                break;
                            case DC_DATABUFF_ACTION_GROUP_DELETE:
                                processOnDeleted(id);
                                break;
                            case DC_DATABUFF_ACTION_GROUP_UPDATE:
                                processOnUpdated(id, actor);
                                break;
                            default:
                                break;
                        }
                        return null;
                    });
        }
    }

    /**
     * 处理组更新事件（当前未实现具体逻辑）
     *
     * @param id    更新的组标识
     * @param actor 执行更新操作的用户标识
     */
    private void processOnUpdated(String id, String actor) {
    }

    /**
     * 处理组删除事件，清理与组关联的所有资源。
     *
     * @param id 被删除组的唯一标识
     */
    private void processOnDeleted(String id) {
        monitorService.deleteByGid(id);
        convergencePolicyService.deleteByGid(id);
        respPolicyService.deleteBatchByGid(id);
        monitorService.delSilencePlanBatchByGid(id);
    }

    /**
     * 初始化新组配置，将全局策略复制到目标组。
     *
     * @param id    新增组的唯一标识
     * @param actor 执行组添加操作的用户标识
     */
    private void processOnAdded(String id, String actor) {
        final User userInfo = userService.getUserInfo(actor);
        if (userInfo == null) {
            return;
        }
        final Long userId = userInfo.getId();
        if (userId == null) {
            return;
        }

        // 1. 批量复制检测规则
        copyMonitors(id, actor);

        // 2. 批量复制收敛策略
        copyConvergencePolicies(id, actor);

        // 3. 批量复制响应策略
        copyRespPolicys(id, actor, userId);

        // 4. 批量复制静默计划
        copyMonitorSilences(id, actor, userId);
    }

    /**
     * 复制管理员创建的静默计划到目标组。
     *
     * @param id     目标组的唯一标识
     * @param actor  执行复制操作的用户标识
     * @param userId 用户的唯一标识（用于关联静默计划创建者）
     */
    private void copyMonitorSilences(String id, String actor, Long userId) {
        final List<DatabuffMonitorSilence> monitorSilenceByAdminCreator = monitorService.findMonitorSilenceByAdminCreator();
        if (monitorSilenceByAdminCreator == null) {
            return;
        }
        // 遍历所有符合条件的静默计划并复制到新组
        for (DatabuffMonitorSilence silence : monitorSilenceByAdminCreator) {
            if (silence == null) {
                continue;
            }
            try {
                monitorService.copySilencePlan(silence, actor, userId, id);
            } catch (Exception e) {
                log.error("onGroupAdded: addMonitorSilence error, silence={}", silence, e);
                OtelMetricUtil.logException("onGroupAdded", e);
            }
        }
    }

    /**
     * 复制管理员创建的响应策略到目标组，并设置用户关联信息。
     *
     * @param id     目标组的唯一标识
     * @param actor  执行复制操作的用户标识
     * @param userId 用户的唯一标识（用于关联策略创建者）
     */
    private void copyRespPolicys(String id, String actor, Long userId) {
        final List<RespPolicyRet> byCreatorAdmin = respPolicyService.findByCreatorAdmin();
        if (byCreatorAdmin == null) {
            return;
        }
        // 遍历所有由管理员创建的响应策略并复制到新组
        for (RespPolicyRet respPolicyRet : byCreatorAdmin) {
            if (respPolicyRet == null) {
                continue;
            }
            respPolicyRet.setId(null);
            respPolicyRet.setGid(id);
            respPolicyRet.setCreator(actor);
            respPolicyRet.setEditor(actor);
            respPolicyRet.setCreatorId(userId.toString());
            respPolicyRet.setEditorId(userId.toString());
            try {
                respPolicyService.save(respPolicyRet);
            } catch (Exception e) {
                log.error("onGroupAdded: addRespPolicy error, respPolicyRet={}", respPolicyRet, e);
                OtelMetricUtil.logException("onGroupAdded", e);
            }
        }
    }

    /**
     * 复制非默认且由管理员创建的收敛策略到目标组。
     *
     * @param id    目标组的唯一标识
     * @param actor 执行复制操作的用户标识
     */
    private void copyConvergencePolicies(String id, String actor) {
        final Timestamp nowTime = new Timestamp(System.currentTimeMillis());

        // 查询非默认的收敛策略，且创建者为Admin, 且gid为空
        LambdaQueryWrapper<ConvergencePolicy> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .and(wrapper -> wrapper.eq(ConvergencePolicy::getDef, false)
                        .or()
                        .isNull(ConvergencePolicy::getDef))
                .isNull(ConvergencePolicy::getGid);

        final List<ConvergencePolicy> convergencePolicies = convergencePolicyService.selectList(queryWrapper);
        if (convergencePolicies == null) {
            return;
        }
        for (ConvergencePolicy convergencePolicy : convergencePolicies) {
            if (convergencePolicy == null) {
                continue;
            }
            final String creator = convergencePolicy.getCreator();
            if (!domainManagerObjService.isAdministratorRole(creator)) {
                continue;
            }
            convergencePolicy.setId(null);
            convergencePolicy.setGid(id);
            convergencePolicy.setCreator(actor);
            convergencePolicy.setEditor(actor);
            convergencePolicy.setCreatedTime(nowTime);
            convergencePolicy.setUpdatedTime(nowTime);
            try {
                convergencePolicyService.insertPolicy(convergencePolicy);
            } catch (Exception e) {
                log.error("onGroupAdded: insert convergencePolicy error, convergencePolicy={}", convergencePolicy, e);
                OtelMetricUtil.logException("onGroupAdded", e);
            }
        }
    }

    /**
     * 复制管理员创建的监控规则到目标组，并设置创建时间。
     *
     * @param id    目标组的唯一标识
     * @param actor 执行复制操作的用户标识
     */
    private void copyMonitors(String id, String actor) {
        final List<DatabuffMonitor> monitors = monitorService.findMonitorsByAdminCreator();
        if (monitors == null) {
            return;
        }
        final Date nowDate = new Date();
        // 遍历所有管理员创建的监控规则并复制到新组
        for (DatabuffMonitor monitor : monitors) {
            if (monitor == null) {
                continue;
            }
            monitor.setId(null);
            monitor.setGid(id);
            monitor.setCreator(actor);
            monitor.setEditor(actor);
            monitor.setCreateTime(nowDate);
            monitor.setUpdateTime(nowDate);
            try {
                monitorService.addMonitorGid(monitor);
            } catch (Exception e) {
                log.error("onGroupAdded: addMonitor error, monitor={}", monitor, e);
                OtelMetricUtil.logException("onGroupAdded", e);
            }
        }
    }
}
