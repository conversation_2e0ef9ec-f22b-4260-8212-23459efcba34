package com.databuff.webapp.monitor.controller;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.audit.AuditEntity;
import com.databuff.common.constants.CommonConstants;
import com.databuff.common.constants.Constant;
import com.databuff.common.tsdb.dto.detect.DetectQueryRequest;
import com.databuff.common.tsdb.dto.detect.MultiDetectQueryRequest;
import com.databuff.common.tsdb.dto.preview.PreviewQueryRequest;
import com.databuff.common.utils.DateUtils;
import com.databuff.common.utils.TimeUtil;
import com.databuff.entity.*;
import com.databuff.entity.dto.AutoRecoverPolicyVO;
import com.databuff.entity.dto.DatabuffMonitorView;
import com.databuff.entity.dto.MonitorSearchParams;
import com.databuff.entity.extend.ServiceSearch;
import com.databuff.service.DomainManagerObjService;
import com.databuff.webapp.admin.service.UserService;
import com.databuff.webapp.apm.service.ServiceV2Service;
import com.databuff.webapp.config.common.CommonResponse;
import com.databuff.webapp.config.common.ErrorCode;
import com.databuff.webapp.config.interceptor.DomainManagerRequired;
import com.databuff.webapp.dto.*;
import com.databuff.webapp.monitor.model.AddSysWithRulesRequest;
import com.databuff.webapp.monitor.model.UpdateBusinessSystemRulesRequest;
import com.databuff.webapp.monitor.service.AlarmEventService;
import com.databuff.webapp.monitor.service.BusinessSystemService;
import com.databuff.webapp.monitor.service.MonitorService;
import com.databuff.webapp.util.JwtUtil;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.audit4j.core.annotation.DatabuffAudit;
import org.audit4j.core.annotation.SelectionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

import static com.databuff.common.constants.Constant.Trace.SERVICE_ID;
import static com.databuff.metric.moredb.SQLParser.API_KEY;

/**
 * @package com.databuff.webapp.monitor.service.impl
 * @company: dacheng
 * @author: zlh
 * @createDate: 2021/8/26
 */
@RestController
@RequestMapping("/monitor")
@Api("监控")
@Slf4j
public class MonitorController {
    @Autowired
    private MonitorService monitorService;

    @Autowired
    private UserService userService;

    @Autowired
    private DomainManagerObjService domainManagerObjService;

    @Autowired
    private AlarmEventService alarmEventService;
    @Autowired
    private ServiceV2Service serviceV2Service;

    @Autowired
    private BusinessSystemService businessSystemService;

    /**
     * @param request
     * @param jsonObject
     * @return
     * @see MonitorController#search(String, String, Boolean, String, Sort.Direction, Integer, Integer)
     * @deprecated
     */
    @ApiOperation(value = "分页查询监控管理", notes = "参数:pageNum,pageSize,name,classification[],priority[],status[],silence(0,1),silenceElapsed,silenceLeft,触发中特殊字段trigger:0,tag")
    @PostMapping(value = "/monitorManagerList")
    @Deprecated
    @DomainManagerRequired
    public CommonResponse<Object> monitorManagerList(HttpServletRequest request, @RequestBody JSONObject jsonObject) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        jsonObject.put(API_KEY, apiKey);
        String token = request.getHeader("Authorization");
        String account = JwtUtil.getClaim(token, Constant.ACCOUNT);
        jsonObject.put("account", account);
        commonResponse.setData(monitorService.monitorManagerList(jsonObject));
        return commonResponse;
    }

    @ApiOperation(value = "搜索监控规则")
    @PostMapping("/search")
    @DomainManagerRequired
    public PageInfo<DatabuffMonitor> search(HttpServletRequest request, @ApiParam(value = "搜索参数", required = true) @RequestBody MonitorSearchParams params) {
        String apiKey = (String) request.getAttribute(API_KEY);
        params.setApiKey(apiKey);
        params.setSystem(false);
        return monitorService.search(params);
    }

    @DatabuffAudit(
            action = "添加",
            selection = SelectionType.ALL,
            entityType = "检测规则",
            repository = "default"
    )
    @ApiOperation(value = "添加监控")
    @PostMapping(value = "/addMonitor")
    @DomainManagerRequired
    public CommonResponse<Object> addMonitor(HttpServletRequest request, @RequestBody DatabuffMonitor databuffMonitor) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        String token = request.getHeader("Authorization");
        String account = JwtUtil.getClaim(token, Constant.ACCOUNT);
        databuffMonitor.setApiKey(apiKey);
        monitorService.addMonitor(databuffMonitor, account);
        databuffMonitor.setApiKey(null);
        commonResponse.setData(databuffMonitor);

        AuditEntity.builder()
                .id(databuffMonitor.getId().toString())
                .name(databuffMonitor.getRuleName())
                .desc(databuffMonitor.getMessage())
                .build();
        return commonResponse;
    }

    @DatabuffAudit(
            action = "添加",
            selection = SelectionType.ALL,
            entityType = "静默计划",
            repository = "default"
    )
    @ApiOperation(value = "添加静默计划")
    @PostMapping(value = "/addSilencePlan")
    @DomainManagerRequired
    public CommonResponse<Object> addSilencePlan(HttpServletRequest request, @RequestBody DatabuffMonitorSilence monitorSilence) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        monitorSilence.setApiKey(apiKey);
        String token = request.getHeader("Authorization");
        String account = JwtUtil.getClaim(token, Constant.ACCOUNT);
        monitorService.addSilencePlan(monitorSilence, account);
        monitorSilence.setApiKey(null);
        commonResponse.setData(monitorSilence);

        final Long id = monitorSilence.getId();
        if (id == null) {
            return commonResponse;
        }
        AuditEntity.builder()
                .id(id.toString())
                .name(monitorSilence.getSilenceName())
                .desc(monitorSilence.getMessage())
                .build();
        return commonResponse;
    }

    @DatabuffAudit(
            action = "批量添加",
            selection = SelectionType.ALL,
            entityType = "静默计划",
            repository = "default"
    )
    @ApiOperation(value = "批量静默")
    @PostMapping(value = "/batchAddSilencePlan")
    @DomainManagerRequired
    public CommonResponse<Object> batchAddSilencePlan(HttpServletRequest request, @RequestBody List<DatabuffMonitorSilence> silenceList) {
        String apiKey = (String) request.getAttribute(API_KEY);
        for (DatabuffMonitorSilence monitorSilence : silenceList) {
            monitorSilence.setApiKey(apiKey);
            String token = request.getHeader("Authorization");
            String account = JwtUtil.getClaim(token, Constant.ACCOUNT);
            monitorService.addSilencePlan(monitorSilence, account);

            AuditEntity.builder()
                    .id(monitorSilence.getId().toString())
                    .name(monitorSilence.getSilenceName())
                    .desc(monitorSilence.getMessage())
                    .add();
        }
        return new CommonResponse<>();
    }


    @DatabuffAudit(
            action = "编辑",
            selection = SelectionType.ALL,
            entityType = "检测规则",
            repository = "default"
    )
    @ApiOperation(value = "编辑检测规则")
    @PostMapping(value = "/editMonitor")
    @DomainManagerRequired
    public CommonResponse<Object> editMonitor(@RequestHeader HttpHeaders headers, HttpServletRequest request, @RequestBody DatabuffMonitor databuffMonitor) throws Exception {
        final User userInfo = userService.getUserInfo(headers);
        if (userInfo == null) {
            return new CommonResponse<>(ErrorCode.COMMON_ERROR_STATUS, "用户不存在", null);
        }
        String apiKey = (String) request.getAttribute(API_KEY);
        databuffMonitor.setApiKey(apiKey);
        databuffMonitor.setEditorId(userInfo.getId());
        databuffMonitor.setEditor(userInfo.getAccount());

        final MultiDetectQueryRequest multiDetectQueryRequest = databuffMonitor.getQuery();
        databuffMonitor.setType(StringUtils.join(multiDetectQueryRequest.findWay(), ","));
        monitorService.editMonitor(databuffMonitor);

        AuditEntity.builder()
                .id(databuffMonitor.getId().toString())
                .name(databuffMonitor.getRuleName())
                .desc(databuffMonitor.getMessage())
                .build();

        return new CommonResponse<>();
    }

    @DatabuffAudit(
            action = "删除",
            entityType = "检测规则",
            hasLink = false
    )
    @ApiOperation(value = "删除监控", notes = "id")
    @PostMapping(value = "/delMonitor")
    @DomainManagerRequired
    public CommonResponse<Object> delMonitor(@RequestBody DatabuffMonitor databuffMonitor) throws Exception {
        monitorService.delMonitor(databuffMonitor);
        return new CommonResponse<>();
    }

    @DatabuffAudit(
            action = "删除",
            entityType = "检测规则",
            hasLink = false
    )
    @ApiOperation(value = "批量删除监控", notes = "MonitorList:[{id:1},{id:2}]")
    @PostMapping(value = "/batchDelMonitor")
    @DomainManagerRequired
    public CommonResponse<Object> batchDelMonitor(@RequestBody List<DatabuffMonitor> monitorList) throws Exception {
        for (DatabuffMonitor databuffMonitor : monitorList) {
            monitorService.delMonitor(databuffMonitor);
        }
        return new CommonResponse<>();
    }

    @DatabuffAudit(
            action = "删除",
            entityType = "检测规则",
            hasLink = false
    )
    @ApiOperation(value = "批量删除监控For推荐监控", notes = "MonitorList:[{id:1},{id:2}]")
    @PostMapping(value = "/batchDelMonitorForRecommand")
    @DomainManagerRequired
    public CommonResponse<Object> batchDelMonitorForRecommand(@RequestBody List<DatabuffMonitor> monitorList) throws Exception {
        for (DatabuffMonitor databuffMonitor : monitorList) {
            monitorService.delMonitorForRecommend(databuffMonitor);
        }
        return new CommonResponse<>();
    }

    @ApiOperation(value = "删除静默", notes = "ids")
    @PostMapping(value = "/delSilencePlan")
    @DomainManagerRequired
    public CommonResponse<Object> delSilencePlan(HttpServletRequest request, @RequestBody JSONObject info) {
        String apiKey = (String) request.getAttribute(API_KEY);
        info.put("apiKey", apiKey);
        String token = request.getHeader("Authorization");
        String account = JwtUtil.getClaim(token, Constant.ACCOUNT);
        monitorService.delSilencePlan(info, account);
        return new CommonResponse<>();
    }

    @DatabuffAudit(
            action = "取消",
            selection = SelectionType.ALL,
            entityType = "静默计划",
            repository = "default"
    )
    @ApiOperation(value = "取消静默", notes = "silenceIds[],host,monitorId,serviceId")
    @PostMapping(value = "/cancelSilencePlan")
    @DomainManagerRequired
    public CommonResponse<Object> cancelSilencePlan(HttpServletRequest request, @RequestBody JSONObject info) {
        String apiKey = (String) request.getAttribute(API_KEY);
        info.put("apiKey", apiKey);
        String token = request.getHeader("Authorization");
        String account = JwtUtil.getClaim(token, Constant.ACCOUNT);
        monitorService.cancelSilencePlan(info, account);
        return new CommonResponse<>();
    }

    @ApiOperation(value = "查询监控tags")
    @GetMapping(value = "/findMonitorTags")
    @DomainManagerRequired
    public CommonResponse<Object> findMonitorTags(HttpServletRequest request) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        String token = request.getHeader("Authorization");
        String account = JwtUtil.getClaim(token, Constant.ACCOUNT);
        commonResponse.setData(monitorService.findMonitorTags(apiKey, account));
        return commonResponse;
    }

    @ApiOperation(value = "解决", notes = "statusId(触发中解决),monitorId(监控管理解决)")
    @PostMapping(value = "/updateMonitorStatusToNormal")
    @Deprecated
    @DomainManagerRequired
    public CommonResponse<Object> updateMonitorStatusToNormal(@RequestBody JSONObject json) {
        monitorService.updateMonitorStatusToNormal(json);
        return new CommonResponse<>();
    }

    @ApiOperation(value = "批量解决", notes = "statusIds[](触发中解决),monitorIds[](监控管理解决)")
    @PostMapping(value = "/batchUpdateMonitorStatusToNormal")
    @Deprecated
    @DomainManagerRequired
    public CommonResponse<Object> batchUpdateMonitorStatusToNormal(@RequestBody Map<String, List<Long>> idMap) {
        monitorService.batchUpdateMonitorStatusToNormal(idMap);
        return new CommonResponse<>();
    }

    @DatabuffAudit(
            action = "批量添加",
            selection = SelectionType.ALL,
            entityType = "标签",
            repository = "default"
    )
    @ApiOperation(value = "批量添加标签", notes = "参数:tags:[],monitorList:[{id:1},{id:2}]")
    @PostMapping(value = "/batchAddMonitorTags")
    @DomainManagerRequired
    public CommonResponse<Object> batchAddMonitorTags(@RequestBody JSONObject jsonObject) {
        monitorService.batchAddMonitorTags(jsonObject);
        return new CommonResponse<>();
    }

    @ApiOperation(value = "静默计划详情", notes = "参数:id")
    @PostMapping(value = "/silenceInfo")
    @DomainManagerRequired
    public CommonResponse<Object> silenceInfo(HttpServletRequest request, @RequestBody JSONObject jsonObject) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        jsonObject.put(API_KEY, apiKey);
        String token = request.getHeader("Authorization");
        String account = JwtUtil.getClaim(token, Constant.ACCOUNT);
        jsonObject.put("account", account);
        commonResponse.setData(monitorService.silenceInfo(jsonObject));
        return commonResponse;
    }

    @ApiOperation(value = "分页查询静默计划", notes = "参数:sortField(CREATE_TIME,UPDATED_TIME),sortOrder(asc desc),pageNum,pageSize,silenceName,enabled,type(single,cycle)")
    @PostMapping(value = "/monitorSilenceList")
    @DomainManagerRequired
    public CommonResponse<Object> monitorSilenceList(HttpServletRequest request, @RequestBody JSONObject jsonObject) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        jsonObject.put(API_KEY, apiKey);
        String token = request.getHeader("Authorization");
        String account = JwtUtil.getClaim(token, Constant.ACCOUNT);
        jsonObject.put("account", account);
        commonResponse.setData(monitorService.monitorSilenceList(jsonObject));
        return commonResponse;
    }


    @DatabuffAudit(
            action = "导出",
            selection = SelectionType.ALL,
            entityType = "静默计划"
    )
    /**
     * 导出数据
     */
    @PostMapping("/exportSilence")
    @DomainManagerRequired
    public void exportSilence(HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject jsonObject) {
        String apiKey = (String) request.getAttribute(API_KEY);
        jsonObject.put(API_KEY, apiKey);
        String token = request.getHeader("Authorization");
        String account = JwtUtil.getClaim(token, Constant.ACCOUNT);
        jsonObject.put("account", account);
        jsonObject.remove("pageNum");
        jsonObject.remove("pageSize");
        monitorService.exportSilence(jsonObject, response);
    }

    @ApiOperation(value = "编辑静默计划")
    @PostMapping(value = "/editSilencePlan")
    @DomainManagerRequired
    public CommonResponse<Object> editSilencePlan(HttpServletRequest request, @RequestBody DatabuffMonitorSilence monitorSilence) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        monitorSilence.setApiKey(apiKey);
        User user = userService.getUserInfo(request);
        int status = monitorService.editSilencePlan(monitorSilence, user);
        if (status != ErrorCode.COMMON_SUCCESS_STATUS) {
            commonResponse.setStatus(status);
            commonResponse.setMessage("静默计划已过期,不能编辑");
            return commonResponse;
        }
        return new CommonResponse<>();
    }

    @ApiOperation(value = "启停静默计划")
    @PostMapping(value = "/publishSilencePlan")
    @DomainManagerRequired
    public CommonResponse<Object> publishSilencePlan(HttpServletRequest request, @RequestBody JSONObject info) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        User user = userService.getUserInfo(request);
        List<Integer> ids = info.getJSONArray("ids").toJavaList(Integer.class);
        StringBuilder sb = new StringBuilder();
        for (Integer id : ids) {
            DatabuffMonitorSilence monitorSilence = new DatabuffMonitorSilence();
            monitorSilence.setId(id.longValue());
            monitorSilence.setApiKey(apiKey);
            monitorSilence.setEnabled(info.getBoolean("enabled"));
            int status = monitorService.publishSilencePlan(monitorSilence, user);
            if (status != ErrorCode.COMMON_SUCCESS_STATUS) {
                sb.append(id).append(",");
            }
        }
        if (sb.length() > 0) {
            commonResponse.setMessage("id:" + sb.substring(0, sb.length() - 1) + "静默计划已过期,不能操作");
        }
        return commonResponse;
    }

    @ApiOperation(value = "查询监控详情", notes = "参数:monitorId")
    @PostMapping(value = "/getMonitorDetail")
    @DomainManagerRequired
    public CommonResponse<Object> getMonitorDetail(@RequestBody DatabuffMonitorStatus monitorStatus) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        commonResponse.setData(monitorService.getMonitorDetail(monitorStatus));
        return commonResponse;
    }


    @ApiOperation(value = "查询监控事件", notes = "参数:scrollId,size,group,status[],states[],monitorId,start,end(秒级),eventName,priority[],source[],classification[],service,taskNumber,sysId," + "service[]、host[]、process[]、plugin[]、containerName[]、instanceId[]、elasticCluster[]、busName[]、triggerObjType[],query")
    @PostMapping(value = "/findMonitorEvent")
    @DomainManagerRequired
    public CommonResponse<Object> findMonitorEvent(HttpServletRequest request, @RequestBody JSONObject jsonObject) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        String token = request.getHeader("Authorization");
        String account = JwtUtil.getClaim(token, Constant.ACCOUNT);
        jsonObject.put(API_KEY, apiKey);
        jsonObject.put("account", account);
        jsonObject.put("isDetail", false);
        return commonResponse;
    }


    @ApiOperation(value = "查询监控事件", notes = "参数:alarmId")
    @PostMapping(value = "/findMonitorEventV2")
    @DomainManagerRequired
    public CommonResponse<FindMonitorEventV2Response> findMonitorEventV2(HttpServletRequest request, @RequestBody FindMonitorEventV2Request requestVo) throws Exception {
        CommonResponse<FindMonitorEventV2Response> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        commonResponse.setData(monitorService.findMonitorEventV2(apiKey, requestVo.getAlarmId(), requestVo.getPageNum(), requestVo.getPageSize()));
        return commonResponse;
    }

    @ApiOperation(value = "查询事件趋势图", notes = "参数:alarmId, interval(秒级)")
    @PostMapping(value = "/findEventTrendV2")
    @DomainManagerRequired
    public CommonResponse<List<Map<String, Long>>> findEventTrendV2(@RequestBody FindMonitorEventTrendV2Request request) throws Exception {
        CommonResponse<List<Map<String, Long>>> commonResponse = new CommonResponse<>();
        commonResponse.setData(monitorService.findEventTrendV2(request.getAlarmId(), request.getInterval()));
        return commonResponse;
    }

    @ApiOperation(value = "查询事件趋势图", notes = "参数:alarmId, interval(秒级)")
    @PostMapping(value = "/findEventTrendV3")
    @DomainManagerRequired
    public Map<String, Integer> findEventTrendV3(HttpServletRequest r, @RequestBody FindMonitorEventTrendV2Request request) throws Exception {
        String apiKey = (String) r.getAttribute(API_KEY);
        final FindMonitorEventV2Response monitorEventV2 = monitorService.findMonitorEventV2(apiKey, request.getAlarmId(), 1, 9999);
        final List<FindMonitorEventV2Response.MonitorEvent> data = monitorEventV2.getData();
        Map<String, Integer> countMap = new HashMap<>();
        for (FindMonitorEventV2Response.MonitorEvent datum : data) {
            if (datum == null) {
                continue;
            }
            Long convergenceTime = datum.getConvergenceTime();
            if (countMap.containsKey(convergenceTime)) {
                countMap.put(convergenceTime.toString(), countMap.get(convergenceTime) + 1);
            } else {
                countMap.put(convergenceTime.toString(), 1);
            }
        }
        return countMap;
    }

    @ApiOperation(value = "查询事件详情", notes = "参数:eventId")
    @PostMapping(value = "/findEventDetailV2")
    @DomainManagerRequired
    public CommonResponse<FindMonitorEventDetailV2Response> findEventDetailV2(@RequestBody FindMonitorEventDetailV2Request request) throws Exception {
        CommonResponse<FindMonitorEventDetailV2Response> commonResponse = new CommonResponse<>();
        commonResponse.setData(monitorService.findEventDetailV2(request.getEventId()));
        return commonResponse;
    }

    @ApiOperation(value = "事件查询条件筛选项", notes = "参数:start,end(秒级)")
    @PostMapping(value = "/condition")
    @DomainManagerRequired
    public CommonResponse<Object> eventSearchCondition(HttpServletRequest request, @RequestBody JSONObject jsonObject) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        jsonObject.put(API_KEY, apiKey);
        return commonResponse;
    }

    @ApiOperation(value = "查询监控详情事件", notes = "参数:scrollId,size,group,status[],monitorId,start,end(秒级),eventName,priority[],source[]")
    @PostMapping(value = "/findMonitorDetailEvent")
    @DomainManagerRequired
    public CommonResponse<Object> findMonitorDetailEvent(HttpServletRequest request, @RequestBody JSONObject jsonObject) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        jsonObject.put(API_KEY, apiKey);
        jsonObject.put("isDetail", true);
        String token = request.getHeader("Authorization");
        String account = JwtUtil.getClaim(token, Constant.ACCOUNT);
        jsonObject.put("account", account);
        return commonResponse;
    }


    @ApiOperation(value = "获取原始事件的数据图", notes = "参数:group,monitorId,start,end(秒级)")
    @PostMapping(value = "/getEventChartMap")
    @DomainManagerRequired
    public CommonResponse<Object> getEventChartMap(HttpServletRequest request, @RequestBody MultiDetectQueryRequest multiDetectQueryRequest) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        multiDetectQueryRequest.setApiKey(apiKey);
        commonResponse.setData(monitorService.getEventChartMap(multiDetectQueryRequest));
        return commonResponse;

    }

    @ApiOperation(value = "预览静默时长,周的按国人习惯1-7排序，年月日重复的开始时间不返回预览，周返回本周开始时间", notes = "参数:size,start(秒级),recurrence")
    @PostMapping(value = "/previewSilenceTime")
    @DomainManagerRequired
    public CommonResponse<Object> previewSilenceTime(@RequestBody JSONObject json) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        commonResponse.setData(monitorService.previewSilenceTime(json));
        return commonResponse;
    }

    @ApiOperation(value = "预览指标图", notes = "参数:query,trigger,triggerObjType")
    @PostMapping(value = "/previewMonitorGraphV3")
    @DomainManagerRequired
    public CommonResponse<Object> previewMonitorGraphV3(HttpServletRequest request, @RequestBody PreviewQueryRequest previewQueryRequest) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        final DetectQueryRequest query = previewQueryRequest.getQuery();
        if (query == null) {
            return commonResponse;
        }

        final String apiKey = (String) request.getAttribute(API_KEY);

        query.setApiKey(apiKey);
        query.setAllPermission(domainManagerObjService.hasAllEntityPermission());
        query.setHasAnnotate(DomainManagerObjService.hasAnnotate());
        query.setDomainManagerStatusOpen(domainManagerObjService.getDomainManagerStatusOpen());
        query.setGids(domainManagerObjService.getGidFromThread());

        commonResponse.setData(monitorService.previewMonitorGraphV3(query, previewQueryRequest.getRule()));
        return commonResponse;
    }

    @ApiOperation(value = "查询所有监控")
    @GetMapping(value = "/findAllMonitor")
    @DomainManagerRequired
    public CommonResponse<Object> findAllMonitor(HttpServletRequest request) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        String token = request.getHeader("Authorization");
        String account = JwtUtil.getClaim(token, Constant.ACCOUNT);
        commonResponse.setData(monitorService.findAllMonitor(apiKey, account));
        return commonResponse;
    }

    @ApiOperation(value = "获取apm监控服务信息")
    @RequestMapping(value = "/service", method = RequestMethod.GET)
    @DomainManagerRequired
    public CommonResponse<Object> queryService(HttpServletRequest request) {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        JSONObject search = new JSONObject();
        search.put(API_KEY, request.getAttribute(API_KEY).toString());
        ServiceSearch serviceSearch = new ServiceSearch();
        serviceSearch.setApiKey(search.getString(API_KEY));
        serviceSearch.setFromTime(search.getString("startTime"));
        serviceSearch.setToTime(search.getString("endTime"));
        if (StringUtils.isBlank(serviceSearch.getFromTime())) {
            serviceSearch.setFromTime(DateUtils.dateToString("yyyy-MM-dd HH:mm:ss", new Date(System.currentTimeMillis() - TimeUtil.OUT_DAY_MS_LONG)));
        }
        List<TraceServiceEntity> traceServiceEntities = serviceV2Service.queryBasicServices(serviceSearch);
        //转换成List<JSONObject>格式，并将添加service_id字段为原来的id
        List<JSONObject> serviceList = new ArrayList<>();
        for (TraceServiceEntity traceServiceEntity : traceServiceEntities) {
            JSONObject service = JSONObject.parseObject(JSONObject.toJSONString(traceServiceEntity));
            service.put(SERVICE_ID, traceServiceEntity.getId());
            serviceList.add(service);
        }
        commonResponse.setData(serviceList);
        return commonResponse;
    }

    @ApiOperation(value = "根据监控名称或服务名称搜索监控对象", notes = "参数:name")
    @PostMapping(value = "/presetMonitorObject")
    @DomainManagerRequired
    public CommonResponse<Object> presetMonitorObject(HttpServletRequest request, @RequestBody DatabuffMonitor monitor) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        monitor.setApiKey(apiKey);
        commonResponse.setData(monitorService.presetMonitorObject(monitor));
        return commonResponse;
    }

    @ApiOperation(value = "推荐监控列表", notes = "参数:name,monitorObject")
    @PostMapping(value = "/presetMonitorList")
    @DomainManagerRequired
    public CommonResponse<Object> presetMonitorList(HttpServletRequest request, @RequestBody DatabuffMonitorView monitor) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        monitor.setApiKey(apiKey);
        commonResponse.setData(monitorService.presetMonitorList(monitor));
        return commonResponse;
    }

    @ApiOperation(value = "开启推荐监控", notes = "参数:id")
    @PostMapping(value = "/openPresetMonitor")
    @DomainManagerRequired
    public CommonResponse<Object> openPresetMonitor(HttpServletRequest request, @RequestBody DatabuffMonitorView monitor) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        monitor.setApiKey(apiKey);
        commonResponse.setData(monitorService.openPresetMonitor(monitor));
        return commonResponse;
    }

    @ApiOperation(value = "更新推荐监控")
    @PostMapping(value = "/updatePresetMonitor")
    @DomainManagerRequired
    public CommonResponse<Object> updatePresetMonitor(HttpServletRequest request, MultipartFile file) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        monitorService.updatePresetMonitor(file.getInputStream(), apiKey);
        return commonResponse;
    }

    @ApiOperation(value = "根据插件即监控对象查找推荐监控列表", notes = "参数:query,monitorObject")
    @PostMapping(value = "/findPresetMonitorByPluge")
    @DomainManagerRequired
    public CommonResponse<Object> findPresetMonitorByPluge(HttpServletRequest request, @RequestBody DatabuffMonitorView monitor) throws Exception {
        String apiKey = (String) request.getAttribute(API_KEY);
        monitor.setApiKey(apiKey);
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        commonResponse.setData(monitorService.presetMonitorList(monitor));
        return commonResponse;
    }

    @DatabuffAudit(
            action = "批量开启推荐监控",
            selection = SelectionType.ALL,
            entityType = "检测规则",
            repository = "default"
    )
    @ApiOperation(value = "批量开启推荐监控", notes = "参数:name,monitorObject,isEnable")
    @PostMapping(value = "/batchOpenPresetMonitor")
    @DomainManagerRequired
    public CommonResponse<Object> batchOpenPresetMonitor(HttpServletRequest request, @RequestBody DatabuffMonitorView monitor) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        monitor.setApiKey(apiKey);
        commonResponse.setData(monitorService.batchOpenPresetMonitor(monitor));
        return commonResponse;
    }

    @ApiOperation(value = "查询插件服务可监控列表")
    @PostMapping(value = "/findPluginServiceList")
    @DomainManagerRequired
    public CommonResponse<Object> findPluginServiceList(HttpServletRequest request) throws Exception {
        String apiKey = (String) request.getAttribute(API_KEY);
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        commonResponse.setData(monitorService.findPluginServiceList(apiKey));
        return commonResponse;
    }
    @ApiOperation(value = "更新事件自动恢复策略", notes = "参数: request, autoRecover")
    @PostMapping(value = "/updateAutoRecoverPolicy")
    @DomainManagerRequired
    public CommonResponse<Object> updateAutoRecoverPolicy(HttpServletRequest request, @RequestBody AutoRecoverPolicyVO autoRecover) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        monitorService.updateAutoRecoverConfig(autoRecover);
        return commonResponse;
    }

    @ApiOperation(value = "获取事件自动恢复策略", notes = "参数: request")
    @PostMapping(value = "/getAutoRecoverPolicy")
    @DomainManagerRequired
    public CommonResponse<Object> getAutoRecoverPolicy(HttpServletRequest request) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        commonResponse.setData(monitorService.getAutoRecoverConfig());
        return commonResponse;
    }


    @ApiOperation(value = "监控转为推荐监控，内部使用", notes = "参数:id")
    @GetMapping(value = "/monitorRevert/{monitorId}/{monitorObj}")
    @DomainManagerRequired
    public CommonResponse<Object> monitorRevert(HttpServletRequest request, @PathVariable Integer monitorId, @PathVariable String monitorObj) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        commonResponse.setData(monitorService.monitorRevert(monitorId, monitorObj));
        return commonResponse;
    }


    @ApiOperation(value = "更新告警事件状态", notes = "参数: type(1事件,2告警,3故障),esid,state[未处理：0(或者空) 处理异常：1  处理中：2 已修复：3 已屏蔽(已关闭): 4],remark")
    @PostMapping(value = "/updateEventAlarmState")
    @DomainManagerRequired
    public CommonResponse<Object> updateEventAlarmState(HttpServletRequest request, @RequestBody JSONObject param) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();

        String esid = param.getString("esid");
        if (StringUtils.isBlank(esid)) {
            commonResponse.setMessage("id 不能为空");
            return commonResponse;
        }
        Integer state = param.getInteger("state");
        if (state == null) {
            commonResponse.setMessage("state 不能为空");
            return commonResponse;
        }
        Integer type = param.getInteger("type");
        if (type == null) {
            commonResponse.setMessage("type 不能为空");
            return commonResponse;
        }
        String apiKey = (String) request.getAttribute(API_KEY);
        String user = (String) request.getAttribute("currentUser");
        param.put(API_KEY, apiKey);
        param.put("user", user);
        commonResponse.setData(alarmEventService.updateEventAlarmState(param));
        return commonResponse;
    }

    @ApiOperation(value = "处理日志查询", notes = "参数:esid")
    @PostMapping(value = "/processingLog")
    @DomainManagerRequired
    public CommonResponse<Object> eventAlarmProcessingLog(HttpServletRequest request, @RequestBody JSONObject param) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        param.put(API_KEY, apiKey);
        commonResponse.setData(alarmEventService.eventAlarmProcessingLogs(param));
        return commonResponse;
    }

    @DatabuffAudit(
            action = "启用/禁用",
            selection = SelectionType.ALL,
            entityType = "检测规则",
            repository = "default"
    )
    @ApiOperation(value = "启用/禁用指定监控规则")
    @PutMapping("/{id}/enable/{enabled}")
    @DomainManagerRequired
    public void enable(@ApiParam(value = "监控规则的ID", required = true) @PathVariable Integer id, @ApiParam(value = "启用或禁用监控规则", required = true) @PathVariable Boolean enabled) {
        DatabuffMonitor monitor = monitorService.findById(id);
        if (monitor == null) {
            throw new RuntimeException("监控不存在");
        }
        monitor.setEnabled(enabled == null || enabled);
        monitor.setUpdateTime(new Date());
        monitorService.update(monitor);

        AuditEntity.builder()
                .id(id.toString())
                .name(monitor.getRuleName())
                .action(() -> enabled ? CommonConstants.ACTION_ENABLE : CommonConstants.ACTION_DISABLE)
                .add();
    }

    @DatabuffAudit(
            action = "批量启用/禁用",
            entityType = "检测规则"
    )
    @ApiOperation(value = "批量启用/禁用指定监控规则")
    @PutMapping("/enable/{enabled}")
    @DomainManagerRequired
    public void enableBatch(@ApiParam(value = "需要启用或禁用的监控规则的ID列表", required = true) @RequestBody List<Integer> ids, @ApiParam(value = "启用或禁用监控规则", required = true) @PathVariable Boolean enabled) {
        for (Integer id : ids) {
            enable(id, enabled == null || enabled);
        }
    }

    @DatabuffAudit(
            action = "批量导出",
            selection = SelectionType.ALL,
            entityType = "检测规则",
            repository = "default"
    )
    @ApiOperation(value = "批量导出DatabuffMonitor为Excel(ID列表)")
    @PostMapping("/export/id")
    @DomainManagerRequired
    public void exportByIds(@RequestBody List<Long> ids, HttpServletResponse response) {
        List<DatabuffMonitor> monitors = monitorService.findByIds(ids);
        monitorService.exportToExcel(monitors, response);
        AuditEntity.builder()
                .desc(ids.toString())
                .build();
    }

    @DatabuffAudit(
            action = "批量导出",
            selection = SelectionType.ALL,
            entityType = "检测规则",
            repository = "default"
    )
    @ApiOperation(value = "批量导出DatabuffMonitor为Excel(关键字)")
    @PostMapping("/export/keyword")
    @DomainManagerRequired
    public void exportByKeyword(@RequestParam(required = false) String keyword, HttpServletResponse response) {
        List<DatabuffMonitor> monitors = monitorService.search(keyword);
        monitorService.exportToExcel(monitors, response);
        AuditEntity.builder()
                .desc(keyword)
                .build();
    }

    @ApiOperation(value = "根据搜索参数导出监控规则到CSV文件")
    @PostMapping("/export")
    @DomainManagerRequired
    public void export(@ApiParam(value = "搜索参数", required = true) @RequestBody MonitorSearchParams searchParams, HttpServletResponse response) {
        monitorService.export(searchParams, response);
    }


    @ApiOperation(value = "获取查询条件实体对象")
    @GetMapping(value = "/monitorObjs")
    @DomainManagerRequired
    public CommonResponse<Object> getMonitorObjs(HttpServletRequest request, @ApiParam(value = "类型(service,host,pname,businessName)", required = false) @RequestParam(required = false) String type) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        commonResponse.setData(monitorService.getMonitorObjs(apiKey, type));
        return commonResponse;
    }


    @ApiOperation(value = "新建业务系统与监控服务规则", notes = "业务系统监控服务规则")
    @PostMapping("/addSysWithRules")
    @DomainManagerRequired
    public CommonResponse addSysWithRules(HttpServletRequest request, @RequestBody AddSysWithRulesRequest rules) {
        CommonResponse commonResponse = new CommonResponse();
        rules.setApiKey(request.getAttribute("apiKey").toString());
        commonResponse.setData(businessSystemService.addSysWithRules(rules));
        return commonResponse;
    }


    @ApiOperation(value = "查询该业务系统下所有监控服务规则", notes = "业务系统监控服务规则")
    @GetMapping("/getBusinessSystemRules")
    @DomainManagerRequired
    public CommonResponse getBusinessSystemRules(HttpServletRequest request, @RequestParam Integer sysId) {
        CommonResponse commonResponse = new CommonResponse();
        String apiKey = request.getAttribute("apiKey").toString();
        commonResponse.setData(businessSystemService.getBusinessSystemRules(sysId, apiKey));
        return commonResponse;
    }


    @DatabuffAudit(
            action = "更新",
            selection = SelectionType.ALL,
            entityType = "业务系统监控",
            repository = "default"
    )
    @ApiOperation(value = "更新该业务系统的监控服务规则", notes = "业务系统监控服务规则")
    @PostMapping("/updateBusinessSystemRules")
    @DomainManagerRequired
    public CommonResponse updateBusinessSystemRules(HttpServletRequest request, @RequestBody UpdateBusinessSystemRulesRequest rules) {
        CommonResponse commonResponse = new CommonResponse();
        rules.setApiKey(request.getAttribute("apiKey").toString());
        commonResponse.setData(businessSystemService.updateBusinessSystemRules(rules));
        return commonResponse;
    }


    @ApiOperation(value = "删除该业务系统与其属于的监控服务规则", notes = "业务系统监控服务规则")
    @DeleteMapping("/deleteBusinessSystemAndRules")
    @DomainManagerRequired
    public CommonResponse deleteBusinessSystemAndRules(HttpServletRequest request, @RequestParam Integer sysId) {
        CommonResponse commonResponse = new CommonResponse();
        commonResponse.setData(businessSystemService.deleteBusinessSystemAndRules(sysId, request.getAttribute("apiKey").toString()));
        return commonResponse;
    }
    @ApiOperation(value = "获取所有规则（检测、收敛、响应、静默）名称")
    @GetMapping(value = "/ruleNames")
    @DomainManagerRequired
    public CommonResponse<Object> ruleNames(HttpServletRequest request, @ApiParam(value = "类型(monitor,convergence,action,silence)", required = false) @RequestParam(required = false) String type) throws Exception {
        CommonResponse<Object> commonResponse = new CommonResponse<>();
        String apiKey = (String) request.getAttribute(API_KEY);
        commonResponse.setData(monitorService.getRuleNames(apiKey, type));
        return commonResponse;
    }
}
