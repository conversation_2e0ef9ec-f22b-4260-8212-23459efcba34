package com.databuff.webapp.monitor.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.databuff.common.audit.AuditEntity;
import com.databuff.dao.mysql.BusinessMapper;
import com.databuff.dao.starrocks.DcAlertMapper;
import com.databuff.entity.AlarmSearchParams;
import com.databuff.entity.Business;
import com.databuff.entity.dto.DCAlarmDto;
import com.databuff.service.DcAlarmService;
import com.databuff.service.DcDatabuffIssueLogService;
import com.databuff.service.DomainManagerObjService;
import com.databuff.service.JedisService;
import com.databuff.sink.OlapTableWriter;
import com.databuff.util.AlarmOlapEngine;
import com.databuff.util.KafkaUtil;
import com.databuff.webapp.monitor.service.AlarmService;
import com.databuff.webapp.root.service.DatabuffProblemQueryService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.audit4j.core.annotation.DatabuffAudit;
import org.audit4j.core.annotation.SelectionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.databuff.common.constants.Constant.DF_API_KEY_VALUE;
import static com.databuff.common.constants.Constant.Kafka.METRIC_TOPIC;
import static com.databuff.common.constants.OlapDB.DC_ALARM;
import static com.databuff.common.constants.OlapDB.OLAP_DATABASE;
import static com.databuff.entity.dto.DCEventDto.BUS_NAME;

@Slf4j
@Service
public class AlarmServiceImpl implements AlarmService {

    @Autowired
    private DcAlarmService dcAlarmService;

    @Resource
    private AlarmOlapEngine alarmOlapEngine;

    @Autowired
    private KafkaUtil kafkaUtil;

    @Autowired
    private OlapTableWriter olapTableWriter;

    @Autowired
    private DcAlertMapper dcAlertMapper;

    @Autowired
    private DcDatabuffIssueLogService dcDatabuffIssueLogService;

    @Autowired
    private JedisService jedisService;

    @Autowired
    private DatabuffProblemQueryService databuffProblemQueryService;

    @Autowired
    private DomainManagerObjService domainManagerObjService;
    @Value("${ai.enable:false}")
    private boolean aiEnable;

    @Autowired
    private BusinessMapper businessMapper;

    @Override
    public PageInfo<DCAlarmDto> findAlarmListV2(AlarmSearchParams params) throws Exception {
        QueryWrapper<DCAlarmDto> wrapper = new QueryWrapper<>();


        List<DCAlarmDto> results = executeAlarmQuery(params, wrapper);
        if (CollectionUtils.isEmpty(results)) {
            return new PageInfo<>();
        }

        dcAlarmService.enrichAlarmData(results);
        for (DCAlarmDto dcAlarmDto : results) {
            dcAlarmDto.setAiEnable(aiEnable);
        }
        return new PageInfo<>(results);
    }

    private void buildQueryConditions(AlarmSearchParams params, QueryWrapper<DCAlarmDto> wrapper) {
        Optional.ofNullable(params.getProblemId())
                .map(dcDatabuffIssueLogService::selectAlarmIdsByProblemId)
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(ids -> wrapper.in("da.id", ids));

        wrapper.and(CollectionUtils.isNotEmpty(params.getLevel()),
                w -> w.in("da.level", params.getLevel()));
        wrapper.and(CollectionUtils.isNotEmpty(params.getStatus()),
                w -> w.in("da.status", params.getStatus()));
    }


    private void configurePagination(AlarmSearchParams params) {
        PageHelper.offsetPage(params.getOffset(), params.getSize(), params.isNeedCount())
                .setOrderBy(determineSortOrder(params));
    }

    private String determineSortOrder(AlarmSearchParams params) {
        return Optional.ofNullable(params.getSortField())
                .flatMap(field -> Optional.ofNullable(params.getSortOrder())
                        .map(order -> field + " " + order))
                .orElse("timestamp desc");
    }

    private static String[] keys = {"host", "service", "serviceId", "serviceInstance", "deviceName", BUS_NAME};

    private Collection<String> resolveGids(AlarmSearchParams params) {
        return Optional.ofNullable(params.getGids())
                .filter(CollectionUtils::isNotEmpty)
                .orElseGet(DomainManagerObjService::getGidFromThread);
    }


    @Override
    public Object getAlarmCntTrend(AlarmSearchParams searchParams) {
        JSONObject data = new JSONObject();
        alarmOlapEngine.alarmCntTrend(searchParams, data);
        return data;
    }

    @DatabuffAudit(
            action = "更新",
            selection = SelectionType.ALL,
            entityType = "告警状态",
            repository = "default"
    )
    @Override
    public Object updateAlarmStatus(AlarmSearchParams searchParams) {
        final Integer dealStatus = searchParams.getDealStatus();
        final DCAlarmDto alarm = getAlarmDetails(searchParams);
        final AuditEntity audit = createAuditEntity(alarm);

        if (dealStatus != 3 && dealStatus != 2) {
            return alarm;
        }

        updateAlarmStatusAndTime(alarm, dealStatus);
        updateAlarmRemark(alarm, searchParams);
        saveAlarmToOlap(alarm);

        if (dealStatus == 2) {
            updateProblemBeginToActionTime(alarm);
            countAlarmMetric(alarm, "onProcess.cnt");
        } else {
            countAlarmMetric(alarm, "processed.cnt");
        }
        audit.setAfterValue(alarm.getAlarmStatus());
        deleteAlarmCache(alarm);
        return alarm;
    }

    private DCAlarmDto getAlarmDetails(AlarmSearchParams searchParams) {
        final boolean allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        return dcAlertMapper.findAlertAllDetailsById(searchParams.getId(), searchParams.getApiKey(), allEntityPermission, DomainManagerObjService.getGidFromThread());
    }

    private AuditEntity createAuditEntity(DCAlarmDto alarm) {
        return AuditEntity.builder()
                .id(alarm.getId())
                .name(alarm.getDescription())
                .beforeValue(alarm.getAlarmStatus())
                .build();
    }

    private void updateAlarmRemark(DCAlarmDto alarm, AlarmSearchParams searchParams) {
        JSONArray remark = alarm.getRemark();
        if (remark == null) {
            remark = new JSONArray();
            alarm.setRemark(remark);
        }
        final Integer dealStatus = searchParams.getDealStatus();
        remark.add(new JSONObject()
                .fluentPut("account", searchParams.getAccount())
                .fluentPut("msg", searchParams.getRemark())
                .fluentPut("status", dealStatus)
                .fluentPut("statusCN", alarm.getAlarmStatus())
                .fluentPut("timestamp", System.currentTimeMillis()));
    }

    private void updateAlarmStatusAndTime(DCAlarmDto alarm, Integer dealStatus) {
        final long currentTimeMillis = System.currentTimeMillis();
        alarm.setUpdateTime(currentTimeMillis);
        alarm.setEndTriggerTime(currentTimeMillis);
        alarm.setStatus(dealStatus);
        alarm.setAlarmStatus(dealStatus == 2 ? "处理中" : "已处理");
    }

    private void saveAlarmToOlap(DCAlarmDto alarm) {
        final byte[] jsonBytes = JSONObject.toJSONBytes(alarm, SerializerFeature.DisableCircularReferenceDetect);
        olapTableWriter.write(jsonBytes, OLAP_DATABASE, DC_ALARM);
    }

    private void updateProblemBeginToActionTime(DCAlarmDto alarm) {
        String problemId = alarm.getProblemId();
        if (StringUtils.isNotEmpty(problemId)) {
            databuffProblemQueryService.updateBeginToActionTime(problemId);
        }
    }

    private void deleteAlarmCache(DCAlarmDto alarm) {
        JSONObject trigger = alarm.getTrigger();
        final String key = buildAlarmRedisKey(alarm.getPolicyId(), alarm.getGid(), trigger);
        jedisService.delKey(key);
    }

    public static String buildAlarmRedisKey(Integer policyId, String gid, JSONObject trigger) {
        return String.format("convergence:%d:%s:%s", policyId, gid, trigger.toJSONString());
    }

    private List<DCAlarmDto> executeAlarmQuery(AlarmSearchParams params, QueryWrapper<DCAlarmDto> wrapper) {
        JSONObject trigger = params.getTrigger();
        if (trigger == null) {
            trigger = new JSONObject();
            params.setTrigger(trigger);
        }
        // 通过父业务系统去查所有子业务系统。
        final Collection<String> parentBusNames = trigger.getObject(BUS_NAME, Collection.class);
        if (CollectionUtils.isNotEmpty(parentBusNames)) {
            final Collection<String> subBusNames = new HashSet<>();
            String apiKey = params.getApiKey();
            if (apiKey != null) {
                for (String parentBusName : parentBusNames) {
                    final List<Business> subBus = businessMapper.getBusinessListByParentName(apiKey, parentBusName);
                    if (CollectionUtils.isNotEmpty(subBus)) {
                        subBusNames.addAll(subBus.stream().map(Business::getName).collect(Collectors.toList()));
                    } else {
                        subBusNames.add(parentBusName);
                    }
                }
                trigger.put(BUS_NAME, subBusNames);
            } else {
                throw new IllegalArgumentException("API key cannot be null");
            }
        }

        buildQueryConditions(params, wrapper);
        configurePagination(params);
        return dcAlertMapper.searchAlarmsV2(
                params.getId(),
                params.isAllEntityPermission(),
                params.getIdLike(),
                params.getApiKey(),
                params.getFromTime(),
                params.getToTime(),
                params.getDescription(),
                trigger,
                wrapper,
                resolveGids(params)
        );
    }

    private void countAlarmMetric(DCAlarmDto alarmDto, String metric) {
        JSONObject trigger = alarmDto.getTrigger();
        Map<String, String> tag = new HashMap<>();
        for (String key : keys) {
            if (trigger.containsKey(key)) {
                Object value = trigger.get(key);
                if (value instanceof String) {
                    tag.put(key, (String) value);
                } else {
                    tag.put(key, "");
                }
            }
        }
        // 如果标签为空，则不发送
        if (tag.isEmpty()) {
            log.warn("告警标签为空，不发送告警到Kafka");
            return;
        }

        tag.put("level", String.valueOf(alarmDto.getLevel()));
        tag.put("policyId", String.valueOf(alarmDto.getPolicyId()));
        tag.put("gid", alarmDto.getGid());
        Map<String, Double> fields = new HashMap<>();
        fields.put(metric, 1D);

        JSONObject alarmMetric = new JSONObject();
        alarmMetric.put("databuff_database", DF_API_KEY_VALUE + "_databuff");
        alarmMetric.put("apiKey", DF_API_KEY_VALUE);
        alarmMetric.put("databuff_measurement", "databuff.alarm");
        alarmMetric.put("tag", tag);
        alarmMetric.put("fields", fields);
        alarmMetric.put("timestamp", alarmDto.getStartTriggerTime());

        try {
            kafkaUtil.producerSend(METRIC_TOPIC, alarmDto.getId(), alarmMetric);
        } catch (Exception e) {
            log.error("发送告警到Kafka时出错", e);
        }
    }

}
