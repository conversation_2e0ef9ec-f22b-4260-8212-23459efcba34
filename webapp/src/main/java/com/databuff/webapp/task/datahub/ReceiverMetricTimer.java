package com.databuff.webapp.task.datahub;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.databuff.common.tsdb.dto.CompositeCondition;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.common.tsdb.model.*;
import com.databuff.dao.mysql.datahub.DataHubProcessorMapper;
import com.databuff.entity.datahubv2.DataHubProcessor;
import com.databuff.metric.MetricAggregator;
import com.databuff.service.JedisService;
import com.databuff.webapp.datahub_v2.common.ProcessorParentTypeEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 有关于 ReceiverMetric 的定时任务
 */
@Component
@Slf4j
public class ReceiverMetricTimer {

    public static final String APIKEY = "NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4";

    @Autowired
    private MetricAggregator metricAggregator;

    @Autowired
    private JedisService jedisService;

    @Autowired
    private DataHubProcessorMapper dataHubProcessorMapper;

    private static final String REDIS_KEY_PREFIX = "receiver_metric:";
    private static final String[] METRICS = {
            "databuff.datahub_collector_otelcol_receiver_accepted_log_records",
            "databuff.datahub_collector_otelcol_receiver_accepted_metric_points",
            "databuff.datahub_collector_otelcol_receiver_accepted_spans"
    };

    @Scheduled(cron = "0 0 * * * ?")  // 每小时整点执行
    public void collectReceiverMetrics() {
        log.info("开始执行Receiver指标收集任务");
        try {
            // 从数据库获取所有receiver类型的processor
            List<DataHubProcessor> receiversFromDB = getReceiversFromDB();

            // 获取当前时间的整点小时时间
            long currentTime = System.currentTimeMillis();
            long endTime = (currentTime / 3600000) * 3600000;  // 当前小时的整点时间
            long startTime = endTime - 3600000;  // 上一个小时的整点时间

            log.info("统计时间范围: {} 到 {}", new Date(startTime), new Date(endTime));
            
            // 准备接收器列表
            List<String> receivers = new ArrayList<>();
            // 为每个接收器创建记录
            Map<String, Long> receiverTotalCounts = new HashMap<>();
            
            // 获取接收器标识
            receiversFromDB.forEach(processor -> {
                String processorType = processor.getType();
                Integer processorId = processor.getProcessorId();
                String processorName = processor.getName();
                String receiverTag = String.format("%s/%s/%s", processorType, processorId, processorName);
                receivers.add(receiverTag);
                receiverTotalCounts.put(receiverTag, 0L);
            });

            for (String metric : METRICS) {
                // 开始构建查询
                List<CompositeCondition> domainConditions = new ArrayList<>();
                CompositeCondition condition = CompositeCondition.builder()
                        .operator(WhereOp.IN.getSymbol())
                        .left("receiver")
                        .right(receivers)
                        .build();
                domainConditions.add(condition);

                Aggregation agg = Aggregation.of(AggFun.LAST, "\"value\"");
                ArrayList<Aggregation> aggregations = new ArrayList<>();
                aggregations.add(agg);

                HashSet<String> whiteKeys = Sets.newHashSet("receiver", "service_instance_id", "service_name", "service_version", "transport");
                QueryRequest queryRequest = QueryRequest.builder()
                        .db(APIKEY + "_databuff")
                        .start(startTime)
                        .end(endTime)
                        .by(Lists.newArrayList("service_instance_id", "receiver"))
                        .tb(metric)
                        .aggregations(aggregations)
                        .whiteKeys(whiteKeys)
                        .from(domainConditions)
                        .build();
                
                // 查询最新上报时间和数据量
                Map<Map<String, String>, TSDBSeries> mapTSDBSeriesMap = metricAggregator.seriesResult(queryRequest);
                
                // 记录最后更新时间
                Map<String, Long> lastUpdateTimes = new HashMap<>();
                
                // 循环处理每个receiver的数据
                for (Map.Entry<Map<String, String>, TSDBSeries> tsdbSeriesEntity : mapTSDBSeriesMap.entrySet()) {
                    Map<String, String> key = tsdbSeriesEntity.getKey();
                    String receiver = key.get("receiver");
                    // 检查该接收器是否在请求列表中
                    if (!receivers.contains(receiver)) {
                        continue;
                    }
                    
                    TSDBSeries series = tsdbSeriesEntity.getValue();
                    
                    if (series != null && !series.getValues().isEmpty()) {
                        List<List<Object>> values = series.getValues();
                        
                        // 按时间排序
                        values.sort(Comparator.comparingLong(a -> Long.parseLong(a.get(0).toString())));
                        
                        // 获取该实例的第一个和最后一个有效值
                        Optional<Double> firstValue = values.stream()
                                .filter(row -> row.get(1) != null)
                                .map(row -> Double.parseDouble(row.get(1).toString()))
                                .findFirst();
                        
                        Optional<Double> lastValue = values.stream()
                                .filter(row -> row.get(1) != null)
                                .map(row -> Double.parseDouble(row.get(1).toString()))
                                .reduce((first, second) -> second);
                        
                        Optional<Long> lastTimeOpt = values.stream()
                                .filter(row -> row.get(0) != null)
                                .map(row -> Long.parseLong(row.get(0).toString()))
                                .max(Long::compare);
                        
                        // 更新最后更新时间
                        if (lastTimeOpt.isPresent()) {
                            long currentLastUpdateTime = lastTimeOpt.get();
                            long existingLastUpdateTime = lastUpdateTimes.getOrDefault(receiver, 0L);
                            if (currentLastUpdateTime > existingLastUpdateTime) {
                                lastUpdateTimes.put(receiver, currentLastUpdateTime);
                            }
                        }
                        
                        // 计算增量并更新小时增量
                        if (firstValue.isPresent() && lastValue.isPresent()) {
                            double increment = lastValue.get() - firstValue.get();
                            if (increment > 0) {
                                long currentCount = receiverTotalCounts.getOrDefault(receiver, 0L);
                                receiverTotalCounts.put(receiver, currentCount + (long) increment);
                            }
                        }
                    }
                }
            }
            
            // 从Redis获取累计统计数据并更新
            long currentHour = System.currentTimeMillis() / 3600000 * 3600000; // 当前小时的开始时间戳
            
            for (String receiver : receivers) {
                String redisKey = REDIS_KEY_PREFIX + receiver;
                Map<String, String> redisMap = jedisService.getHashMap(redisKey);
                
                long totalCount = receiverTotalCounts.getOrDefault(receiver, 0L);
                long lastTimestamp = 0L;
                long redisTotal = 0L;
                
                if (redisMap != null) {
                    if (redisMap.get("value") != null) {
                        redisTotal = Long.parseLong(redisMap.get("value"));
                    }
                    if (redisMap.get("timestamp") != null) {
                        lastTimestamp = Long.parseLong(redisMap.get("timestamp"));
                    }
                }
                
                // 计算最终的总量
                long newValue = totalCount + redisTotal;
                
                // 确保不重复写入同一个小时
                if (lastTimestamp < currentHour) {
                    // 本小时未上报，累加并写入
                    Map<String, String> saveMap = new HashMap<>();
                    saveMap.put("value", String.valueOf(newValue));
                    saveMap.put("timestamp", String.valueOf(currentHour));
                    jedisService.setHashMap(redisKey, saveMap, 7200); // 2小时过期
                    log.info("Receiver {} 指标统计完成，当前累计值: {}, 本小时增量: {}", receiver, newValue, totalCount);
                } else {
                    log.info("Receiver {} 本小时数据已上报，跳过", receiver);
                }
            }
        } catch (Exception e) {
            log.error("执行Receiver指标收集任务失败", e);
        }
    }

    private List<DataHubProcessor> getReceiversFromDB() {
        // TODO: 实现从数据库获取receiver类型的processor的逻辑
        DataHubProcessor dataHubProcessor = new DataHubProcessor();
        dataHubProcessor.setType(ProcessorParentTypeEnum.RECEIVERS.name());
        return dataHubProcessorMapper.selectList(new QueryWrapper<>(dataHubProcessor));
    }
}