package com.databuff.webapp.task.cleanTasks;

import com.databuff.common.utils.DateUtils;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.common.utils.TimeUtil;
import com.databuff.dao.mysql.BusinessMapper;
import com.databuff.dao.mysql.MonitorMapper;
import com.databuff.dao.mysql.TraceServiceMapper;
import com.databuff.entity.AgentLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.Date;
import java.util.List;

/**
 * @author:TianMing
 * @date: 2021/8/6
 * @time: 15:10
 */
@Component
@Slf4j
@RefreshScope
public class ClearExpireIndexTimer {

    // 过期天数,默认7天
    @Value("${data.expireDays:7}")
    private Integer expireDays;
    @Resource
    private ClearMysqlDataMapper clearMysqlDataMapper;
    @Resource
    private MonitorMapper monitorMapper;
    @Autowired
    @Qualifier("traceServiceMapper")
    private TraceServiceMapper traceServiceMapper;

    @Autowired
    private BusinessMapper businessMapper;

    public void clearExpireMysqlData() {
        int problemCount = clearMysqlDataMapper.delProblemByLastTime(expireDays);
        log.info("delete problem {} 条，保留周期为{}天", problemCount, expireDays);

        int problemServiceCount = clearMysqlDataMapper.delProblemServiceByLastTime(expireDays);
        log.info("delete problem service {} 条，保留周期为{}天", problemServiceCount, expireDays);

        // stop时间超过expireDays 删除
        long expireSecond = System.currentTimeMillis() / 1000 - (expireDays * 24 * 60 * 60);
        monitorMapper.delMonitorSilenceByLastTime(expireSecond);

        clearMysqlDataMapper.delNotifyRecordByLastTime(expireDays);

        //删除过期agent日志，删除文件，并删除mysql记录
        try {
            List<AgentLog> agentLogs = clearMysqlDataMapper.getExpireDaysAgentLogs(expireDays);
            if (agentLogs != null && agentLogs.size() > 0) {
                for (AgentLog agentLog : agentLogs) {
                    try {
                        //删除文件
                        File file = new File(agentLog.getPath());
                        if (file.exists()) {
                            file.delete();
                        }
                        //删除mysql记录
                        clearMysqlDataMapper.delAgentLogById(agentLog.getId());
                    } catch (Exception e) {
                        log.error("cleanAgentLog failed, msg:", e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("cleanAgentLog failed, msg:", e);
        }

        //清理expireDays 天前服务
        try {
            String sevenDayTime = DateUtils.dateToString("yyyy-MM-dd HH:mm:ss", new Date(System.currentTimeMillis() - (expireDays * 24 * 60 * 60 * 1000L)));
            int count = traceServiceMapper.deleteByTime(sevenDayTime);
            OtelMetricUtil.logCounter("clear.service.cnt", count);
            //删除不在服务列表中得业务系统与服务关系数据
            int count2 = businessMapper.delRelationOffService();
            OtelMetricUtil.logCounter("clear.business.relation.cnt", count2);
            log.info("cleanOldServices started, deleted number is {},del business service relation number is {}", count, count2);
        } catch (Exception e) {
            log.error("cleanOldServices failed, msg:", e);
        }

        try {
            // 删除过期issue日志数据
            int count = clearMysqlDataMapper.delIssueLogByLastTime(expireDays);
            OtelMetricUtil.logCounter("clear.issue.log.cnt", count);
            count = clearMysqlDataMapper.delIssueServiceByLastTime(expireDays);
            OtelMetricUtil.logCounter("clear.issue.service.cnt", count);
            count = clearMysqlDataMapper.delIssueDetailByLastTime(expireDays);
            OtelMetricUtil.logCounter("clear.issue.detail.cnt", count);
        } catch (Exception e) {
            log.error("cleanIssue failed, msg:", e);
        }

    }


}
