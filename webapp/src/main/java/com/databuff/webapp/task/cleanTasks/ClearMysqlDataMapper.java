package com.databuff.webapp.task.cleanTasks;

import com.databuff.entity.AgentLog;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;


@Mapper
@Repository
public interface ClearMysqlDataMapper {

    /**
     * 删除过期issue日志数据
     *
     * @param expireDays 过期天数
     * @return
     */
    @Delete("DELETE FROM `dc_databuff_issue_log` WHERE create_time < NOW() - INTERVAL #{expireDays} DAY")
    int delIssueLogByLastTime(@Param("expireDays") int expireDays);

    /**
     * 删除过期issue服务数据
     *
     * @param expireDays 过期天数
     * @return
     */
    @Delete("DELETE FROM `dc_databuff_issue_service` where create_time < NOW() - INTERVAL #{expireDays} DAY")
    int delIssueServiceByLastTime(@Param("expireDays") int expireDays);

    /**
     * 删除过期issue详情数据
     *
     * @param expireDays 过期天数
     * @return
     */
    @Delete("DELETE FROM `dc_databuff_issue_detail` WHERE start_time < NOW() - INTERVAL #{expireDays} DAY")
    int delIssueDetailByLastTime(@Param("expireDays") int expireDays);


    @Delete("DELETE from  `df_notify_record` where DATE_FORMAT(notice_time,'%Y-%m-%d %T') < DATE_FORMAT(DATE_SUB(now(),INTERVAL #{expireDays} day),'%Y-%m-%d %T')")
    int delNotifyRecordByLastTime(@Param("expireDays") int expireDays);

    @Delete("DELETE from  `dc_databuff_problem` where DATE_FORMAT(analyseEndTime,'%Y-%m-%d %T') < DATE_FORMAT(DATE_SUB(now(),INTERVAL #{expireDays} day),'%Y-%m-%d %T')")
    int delProblemByLastTime(@Param("expireDays") int expireDays);

    @Delete("DELETE from `dc_databuff_problem_service` where DATE_FORMAT(analyseEndTime,'%Y-%m-%d %T') < DATE_FORMAT(DATE_SUB(now(),INTERVAL #{expireDays} day),'%Y-%m-%d %T')")
    int delProblemServiceByLastTime(@Param("expireDays") int expireDays);

    /**
     * 删除过期agentLog数据
     */
    @Select("select id,path from `dc_agent_log` where DATE_FORMAT(upload_time,'%Y-%m-%d %T') < DATE_FORMAT(DATE_SUB(now(),INTERVAL #{expireDays} day),'%Y-%m-%d %T')")
    List<AgentLog> getExpireDaysAgentLogs(@Param("expireDays") int expireDays);

    @Delete("DELETE from `dc_agent_log` where id = #{id}")
    int delAgentLogById(@Param("id") int id);
}
