package com.databuff.webapp.rumV2.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.databuff.entity.rum.starrocks.BaseRumIosSpan;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.databuff.common.utils.StringUtil.convertNumberToString;

@Data
public class RumIosSpanVO {
    @ApiModelProperty(value = "开始时间", example = "2023-10-01 12:00:00")
    @JSONField(name = "startTime", format="yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "traceId", example = "trace_001")
    @JSONField(name = "trace_id")
    private String traceId;

    @ApiModelProperty(value = "spanId", example = "span_001")
    @JSONField(name = "span_id")
    private String spanId;

    @ApiModelProperty(value = "父spanId", example = "parent_span_001")
    @JSONField(name = "parent_id")
    private String parentId;

    @ApiModelProperty(value = "应用id", example = "12345")
    @JSONField(name = "app_id")
    private String appId;

    @ApiModelProperty(value = "服务名", example = "service_name")
    @JSONField(name = "service")
    private String service;

    @ApiModelProperty(value = "请求URL", example = "http://example.com/request")
    @JSONField(name = "http_url")
    private String httpUrl;

    @ApiModelProperty(value = "开始时间", example = "1696156800000")
    @JSONField(name = "start")
    private Long start;

    @ApiModelProperty(value = "结束时间", example = "1696156801500")
    @JSONField(name = "end")
    private Long end;

    @ApiModelProperty(value = "耗时", example = "1500")
    @JSONField(name = "duration")
    private Long duration;

    @ApiModelProperty(value = "相对时间", example = "1500")
    private Long relativeTime;

    public static RumIosSpanVO from(BaseRumIosSpan span) {
        RumIosSpanVO vo = new RumIosSpanVO();
        if (span == null) {
            return vo;
        }
        vo.setStartTime(span.getStartTime());
        vo.setTraceId(convertNumberToString(span.getTraceId()));
        vo.setSpanId(convertNumberToString(span.getSpanId()));
        vo.setParentId(convertNumberToString(span.getParentId()));
        vo.setAppId(convertNumberToString(span.getAppId()));
        vo.setService(span.getService());
        vo.setHttpUrl(span.getHttpUrl());
        vo.setStart(span.getStart());
        vo.setEnd(span.getEnd());
        vo.setDuration(span.getDuration());
        return vo;
    }

    public static List<RumIosSpanVO> from(List<? extends BaseRumIosSpan> spans) {
        List<RumIosSpanVO> voList = new ArrayList<>();
        if (spans == null) {
            return voList;
        }
        for (BaseRumIosSpan span : spans) {
            voList.add(from(span));
        }
        return voList;
    }
}
