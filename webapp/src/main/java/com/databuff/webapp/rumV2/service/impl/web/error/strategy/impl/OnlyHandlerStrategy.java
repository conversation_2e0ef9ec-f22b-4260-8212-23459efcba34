package com.databuff.webapp.rumV2.service.impl.web.error.strategy.impl;

import com.databuff.dao.mysql.RumJsErrorMapper;
import com.databuff.dao.starrocks.RumErrorLogsMapper;
import com.databuff.entity.rum.mysql.RumJsError;
import com.databuff.entity.rum.web.JsErrorSearchCriteria;
import com.databuff.entity.rum.web.RumErrorLogsAggregateDto;
import com.databuff.webapp.rumV2.model.QueryResult;
import com.databuff.webapp.rumV2.service.impl.web.error.strategy.QueryStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class OnlyHandlerStrategy implements QueryStrategy {
    @Autowired
    private RumJsErrorMapper rumJsErrorMapper;
    @Autowired
    private RumErrorLogsMapper rumErrorLogsMapper;

    @Override
    public QueryResult execute(JsErrorSearchCriteria criteria) {
        if (criteria.getHandler() == null) {
            throw new IllegalArgumentException("Handler cannot be null");
        }

        List<RumJsError> mysqlErrors = rumJsErrorMapper.getJsErrorListByAppIdAndHandler(
                criteria.getAppId(),
                criteria.getHandler(),
                criteria.getErrorMessage(),
                criteria.getOffset(),
                criteria.getPageSize()
        );
        long totalCount = rumJsErrorMapper.countJsErrorsByAppIdAndHandler(
                criteria.getAppId(),
                criteria.getHandler(),
                criteria.getErrorMessage()
        );
        List<RumErrorLogsAggregateDto> starRocksErrors = !mysqlErrors.isEmpty() ?
                rumErrorLogsMapper.getErrorLogsListByErrorMessages(
                        criteria.getAppId(),
                        mysqlErrors.stream().map(RumJsError::getErrorMessage).collect(Collectors.toList()),
                        0,
                        Integer.MAX_VALUE,
                        criteria.getFromTime(),
                        criteria.getToTime(),
                        null,
                        null
                ) : new ArrayList<>();

        return new QueryResult(mysqlErrors, starRocksErrors, totalCount);
    }

    @Override
    public boolean isStarRocksPrimary() {
        return false;
    }
}