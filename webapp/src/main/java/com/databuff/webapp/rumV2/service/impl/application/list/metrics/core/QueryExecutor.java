package com.databuff.webapp.rumV2.service.impl.application.list.metrics.core;

import com.databuff.common.tsdb.TSDBOperateUtil;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.TSDBResultSet;
import com.databuff.webapp.util.ThreadPoolUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Component
public class QueryExecutor {

    @Autowired
    protected TSDBOperateUtil tsdbOperateUtil;

    public <T> Map<Integer, T> executeQueriesParallel(List<QueryBuilder> queries, String apiKey, ResultMapper<T> mapper) {
        Map<Integer, T> results = new HashMap<>();

        List<Future<TSDBResultSet>> futures = queries.stream()
                .map(query -> ThreadPoolUtil.METRIC_EXECUTOR.submit(() ->
                        tsdbOperateUtil.executeQuery(query)))
                .collect(Collectors.toList());

        for (Future<TSDBResultSet> future : futures) {
            try {
                TSDBResultSet result = future.get();
                mapper.mergeResult(results, result);
            } catch (Exception e) {
                throw new RuntimeException("Query execution failed", e);
            }
        }

        return results;
    }

    public TreeMap<String, Double> executePvTrendQuery(QueryBuilder query) {
        TSDBResultSet queryResult = tsdbOperateUtil.executeQuery(query);

        TreeMap<String, Double> result = new TreeMap<>();
        if (queryResult.getResults() != null && !queryResult.getResults().isEmpty()) {
            if (queryResult.getResults().get(0).getSeries() != null &&
                    !queryResult.getResults().get(0).getSeries().isEmpty()) {
                List<List<Object>> values = queryResult.getResults().get(0).getSeries().get(0).getValues();
                if (values != null) {
                    for (List<Object> value : values) {
                        result.put(
                                String.valueOf(((Number) value.get(0)).longValue()),
                                value.get(1) != null ? ((Number) value.get(1)).doubleValue() : 0.0
                        );
                    }
                }
            }
        }
        return result;
    }
}