package com.databuff.webapp.rumV2.service.impl.application.list.metrics.query;

import com.databuff.common.constants.TSDBIndex;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.Aggregation;
import com.databuff.common.tsdb.model.Where;
import com.databuff.webapp.rumV2.model.MobileType;
import com.databuff.webapp.rumV2.util.RumPercentileUtil;

import java.util.Date;
import java.util.List;

/**
 * RUM Mobile App 指标查询构建器。
 * <p>
 * 负责根据平台 (iOS/Android)、数据库类型 (MoreDB, OpenGemini等) 和查询需求，
 * 构建用于获取 Mobile App 性能指标的 TSDB 查询语句。
 * </p>
 */
public class MobileMetricsQueryBuilder {
    private final String apiKey;      // Databuff API Key
    private final long startTime;     // 查询开始时间戳 (毫秒)
    private final long endTime;       // 查询结束时间戳 (毫秒)
    private final MobileType platform;  // 移动平台 (ios, android)


    /**
     * 构造函数。
     *
     * @param apiKey    Databuff API Key。
     * @param fromTime  查询开始时间。
     * @param toTime    查询结束时间。
     * @param platform  移动平台 ({@link MobileType})。
     */
    public MobileMetricsQueryBuilder(String apiKey, Date fromTime, Date toTime, MobileType platform) {
        this.apiKey = apiKey;
        this.startTime = fromTime.getTime();
        this.endTime = toTime.getTime();
        this.platform = platform;
    }

    /**
     * 构建用于查询 App 统计数据（如ANR次数、崩溃次数、启动次数）的 QueryBuilder。
     *
     * @param appIds 应用ID列表。
     * @return 配置好的 QueryBuilder 实例。
     */
    public QueryBuilder buildStatsQuery(List<String> appIds) {
        QueryBuilder builder = new QueryBuilder()
                .setDatabaseName(apiKey, TSDBIndex.TSDB_RUM_METRIC_DATABASE_NAME) // 使用RUM数据库名
                .setMeasurement(String.format("rum.%s.app.stats", platform.getValue())) // 表名动态构建，例如 rum.ios.app.stats
                .addGroupBy("appId");

        builder.addWhere(Where.gte("time", startTime));
        builder.addWhere(Where.lte("time", endTime));

        if (appIds != null && !appIds.isEmpty()) {
            builder.addWhere(Where.in("appId", appIds));
        }

        builder.addAgg(Aggregation.of("sum(anrCount)", "anrCount"));
        builder.addAgg(Aggregation.of("sum(crashCount)", "crashCount"));
        builder.addAgg(Aggregation.of("sum(launchCount)", "launchCount"));

        return builder;
    }

    /**
     * 构建用于获取 Mobile App 百分位指标数据的 QueryBuilder。
     * <p>
     * 策略与 Web 端类似：MoreDB 尝试直接查询P75，OpenGemini 则查询所有直方图桶的SUM。
     * </p>
     *
     * @param appIds     应用ID列表。
     * @param metricType 指标类型，用于 'type' 标签过滤 (例如 "launchDuration", "pageDuration")。
     * 同时也用于生成目标别名 (例如 "launchDuration75")。
     * @return 配置好的 QueryBuilder 实例。
     */
    public QueryBuilder buildPercentileQuery(List<String> appIds, String metricType , RumPercentileUtil percentileUtil) {
        // 表名动态构建，例如 rum.ios.app.stats.percentile
        // **重要**: 此表需要包含 'histogramFieldX' 字段和 'type' 标签。
        String tableName = String.format("rum.%s.app.stats.percentile", platform.getValue());
        String targetAlias = metricType + "75"; // 目标别名，例如 "launchDuration75"

        QueryBuilder builder = new QueryBuilder()
                .setDatabaseName(apiKey, TSDBIndex.TSDB_RUM_METRIC_DATABASE_NAME) // 使用RUM数据库名
                .setMeasurement(tableName)
                .addGroupBy("appId");

        builder.addWhere(Where.gte("time", startTime));
        builder.addWhere(Where.lte("time", endTime));

        if (appIds != null && !appIds.isEmpty()) {
            builder.addWhere(Where.in("appId", appIds));
        }

        // 使用 'type' 标签过滤特定指标类型的数据
        builder.addWhere(Where.eq("type", metricType));

        // 调用Util方法来添加特定于数据库的百分位聚合
        percentileUtil.addPercentileAggregationsToQuery(
                builder,
                tableName,
                75,                  // 固定查询P75
                targetAlias          // 例如 "launchDuration75"
        );
        return builder;
    }
}