package com.databuff.webapp.rumV2.service.impl.web.request;

import com.databuff.dao.starrocks.RumWebRequestSpanMapper;
import com.databuff.entity.rum.web.RumWebRequestTraceDetailDto;
import com.databuff.entity.rum.web.RumWebRequestTraceDetailResponseDto;
import com.databuff.entity.rum.web.RumWebRequestTraceListDto;
import com.databuff.entity.rum.web.RumWebRequestTraceListSearchCriteria;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RumWebRequestTraceService {
    @Autowired
    private RumWebRequestSpanMapper rumWebRequestSpanMapper;

    public PageInfo<RumWebRequestTraceListDto> getRequestTraceList(RumWebRequestTraceListSearchCriteria criteria) {
        String orderBy = criteria.getSortField() + " " + criteria.getSortOrder();
        PageHelper.startPage(criteria.getPageNum(), criteria.getPageSize(), orderBy);
        List<RumWebRequestTraceListDto> list = rumWebRequestSpanMapper.getRequestTraceList(criteria);
        return new PageInfo<>(list);
    }

    public RumWebRequestTraceDetailResponseDto getRequestTraceDetail(RumWebRequestTraceDetailDto criteria) {
        return rumWebRequestSpanMapper.getRequestTraceDetail(criteria);
    }

}
