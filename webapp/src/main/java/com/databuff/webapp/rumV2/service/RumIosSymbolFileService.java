package com.databuff.webapp.rumV2.service;

import com.databuff.entity.rum.mysql.IosSymbolFile;
import com.databuff.entity.rum.web.IosSymbolSearchCriteria;
import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Optional;

public interface RumIosSymbolFileService {
    PageInfo<IosSymbolFile> getSymbolFiles(IosSymbolSearchCriteria search);

    String saveSymbolFile(Integer appId, MultipartFile file);

    int deleteSymbolFiles(List<Long> ids);

    void downloadSymbolFile(Long id, HttpServletResponse response);

    Optional<String> getSymbolFilePath(String uuid);

    Optional<String> getSymCacheFilePath(String uuid);

}