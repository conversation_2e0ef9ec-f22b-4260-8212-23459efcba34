package com.databuff.webapp.rumV2.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.databuff.entity.rum.starrocks.RumPageSpan;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.databuff.common.utils.StringUtil.convertNumberToString;

@Data
public class RumPageSpanVO {

    @ApiModelProperty(value = "开始时间", example = "2023-10-01 12:00:00")
    @JSONField(name = "startTime", format="yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "页面id", example = "1001")
    @JSONField(name = "page_id")
    private String pageId;

    @ApiModelProperty(value = "traceId", example = "trace_001")
    @JSONField(name = "trace_id")
    private String traceId;

    @ApiModelProperty(value = "spanId", example = "span_001")
    @JSONField(name = "span_id")
    private String spanId;

    @ApiModelProperty(value = "父spanId", example = "parent_span_001")
    @JSONField(name = "parent_id")
    private String parentId;

    @ApiModelProperty(value = "应用id", example = "12345")
    @JSONField(name = "app_id")
    private Integer appId;

    @ApiModelProperty(value = "会话id", example = "session_001")
    @JSONField(name = "session_id")
    private String sessionId;

    @ApiModelProperty(value = "服务名", example = "service_name")
    @JSONField(name = "service")
    private String service;

    @ApiModelProperty(value = "页面来源URL", example = "http://example.com")
    @JSONField(name = "location_href")
    private String locationHref;

    @ApiModelProperty(value = "处理过的页面来源URL", example = "http://example.com/processed")
    @JSONField(name = "processed_location_href")
    private String processedLocationHref;

    @ApiModelProperty(value = "请求URL", example = "http://example.com/request")
    @JSONField(name = "http_url")
    private String httpUrl;

    @ApiModelProperty(value = "租户 api key id", example = "1")
    @JSONField(name = "df_api_key_id")
    private Integer dfApiKeyId;

    @ApiModelProperty(value = "开始时间", example = "1696156800000")
    @JSONField(name = "start")
    private Long start;

    @ApiModelProperty(value = "结束时间", example = "1696156801500")
    @JSONField(name = "end")
    private Long end;

    @ApiModelProperty(value = "耗时", example = "1500")
    @JSONField(name = "duration")
    private Long duration;

    @ApiModelProperty(value = "相对时间", example = "1500")
    private Long relativeTime;

    public static RumPageSpanVO fromRumSlowPageSpan(RumPageSpan rumPageSpan) {
        RumPageSpanVO vo = new RumPageSpanVO();
        if (rumPageSpan == null) {
            return vo;
        }
        vo.setStartTime(rumPageSpan.getStartTime());
        vo.setPageId(convertNumberToString(rumPageSpan.getPageId()));
        vo.setTraceId(convertNumberToString(rumPageSpan.getTraceId()));
        vo.setSpanId(convertNumberToString(rumPageSpan.getSpanId()));
        vo.setParentId(convertNumberToString(rumPageSpan.getParentId()));
        vo.setAppId(rumPageSpan.getAppId());
        vo.setSessionId(convertNumberToString(rumPageSpan.getSessionId()));
        vo.setService(rumPageSpan.getService());
        vo.setLocationHref(rumPageSpan.getLocationHref());
        vo.setProcessedLocationHref(rumPageSpan.getProcessedLocationHref());
        vo.setHttpUrl(rumPageSpan.getHttpUrl());
        vo.setDfApiKeyId(rumPageSpan.getDfApiKeyId());
        vo.setStart(rumPageSpan.getStart());
        vo.setEnd(rumPageSpan.getEnd());
        vo.setDuration(rumPageSpan.getDuration());
        vo.setRelativeTime(rumPageSpan.getRelativeTime());
        return vo;
    }

    public static List<RumPageSpanVO> fromRumSlowPageSpan(List<RumPageSpan> rumPageSpans) {
        List<RumPageSpanVO> voList = new ArrayList<>();
        if (rumPageSpans == null) {
            return voList;
        }
        for (RumPageSpan rumPageSpan : rumPageSpans) {
            voList.add(fromRumSlowPageSpan(rumPageSpan));
        }
        return voList;
    }

    public static PageInfo<RumPageSpanVO> fromRumSlowPageSpanPageInfo(PageInfo<RumPageSpan> rumSlowPageSpanPageInfo) {
        List<RumPageSpanVO> voList = fromRumSlowPageSpan(rumSlowPageSpanPageInfo.getList());
        PageInfo<RumPageSpanVO> voPageInfo = new PageInfo<>(voList);
        voPageInfo.setPageNum(rumSlowPageSpanPageInfo.getPageNum());
        voPageInfo.setPageSize(rumSlowPageSpanPageInfo.getPageSize());
        voPageInfo.setSize(rumSlowPageSpanPageInfo.getSize());
        voPageInfo.setStartRow(rumSlowPageSpanPageInfo.getStartRow());
        voPageInfo.setEndRow(rumSlowPageSpanPageInfo.getEndRow());
        voPageInfo.setTotal(rumSlowPageSpanPageInfo.getTotal());
        voPageInfo.setPages(rumSlowPageSpanPageInfo.getPages());
        voPageInfo.setNavigateFirstPage(rumSlowPageSpanPageInfo.getNavigateFirstPage());
        voPageInfo.setNavigateLastPage(rumSlowPageSpanPageInfo.getNavigateLastPage());
        voPageInfo.setNavigatepageNums(rumSlowPageSpanPageInfo.getNavigatepageNums());
        voPageInfo.setNavigatePages(rumSlowPageSpanPageInfo.getNavigatePages());
        voPageInfo.setPrePage(rumSlowPageSpanPageInfo.getPrePage());
        voPageInfo.setNextPage(rumSlowPageSpanPageInfo.getNextPage());
        voPageInfo.setIsFirstPage(rumSlowPageSpanPageInfo.isIsFirstPage());
        voPageInfo.setIsLastPage(rumSlowPageSpanPageInfo.isIsLastPage());
        voPageInfo.setHasPreviousPage(rumSlowPageSpanPageInfo.isHasPreviousPage());
        voPageInfo.setHasNextPage(rumSlowPageSpanPageInfo.isHasNextPage());
        return voPageInfo;
    }
}