package com.databuff.webapp.rumV2.service.impl.web.action.helper;

import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.Aggregation;
import com.databuff.common.tsdb.model.OrderBy;
import com.databuff.common.tsdb.model.TSDBResultSet;
import com.databuff.common.tsdb.model.Where;
import com.databuff.common.utils.DateUtils;
import com.databuff.entity.dto.WebSearchCriteria;
import com.databuff.entity.rum.mysql.RumWebAlias;
import com.databuff.webapp.rumV2.util.RumPercentileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>封装与 RUM Web Action 查询构建相关的逻辑。</p>
 * <p>替代旧版 “SQLParser + JSON” 模式，直接用 QueryBuilder 构造 TSDB 查询。</p>
 */
@Slf4j
public class RumWebActionQueryBuilderHelper {

    private static final String ACTION_NAME = "actionName";
    private static final String APP_ID = "appId";

    /**
     * <p>构建 webActionSearch 用的 QueryBuilder。</p>
     * <p>会包含：sum(actionCount) as actionCount、sum(actionDuration)/sum(actionCount) as actionDuration 等字段。</p>
     *
     * @param search          用户输入的查询条件
     * @param matchedAliases  若传入别名，则查出的 alias 对应的 RumWebAlias 列表
     * @param validSortFields 可以在 TSDB 排序的字段
     * @return 构造好的 QueryBuilder
     */
    public static QueryBuilder buildWebActionSearchQuery(
            WebSearchCriteria search,
            List<RumWebAlias> matchedAliases,
            List<String> validSortFields
    ) {
        long fromMs = toMs(search.getFromTime(), true);
        long toMs = toMs(search.getToTime(), false);

        // 1. 基础 queryBuilder
        QueryBuilder qb = new QueryBuilder()
                .setDatabaseName(search.getApiKey() + "_rum")
                .setMeasurement("rum.web.action")
                .addWhere(Where.gte("time", fromMs))
                .addWhere(Where.lt("time", toMs))
                .addWhere(Where.eq(APP_ID, search.getAppId()))
                .addAgg(Aggregation.of(" sum(actionCount)", "actionCount"))
                .addAgg(Aggregation.of(" sum(actionDuration)/sum(actionCount)", "actionDuration"))
                .addAgg(Aggregation.of(" sum(actionRequestDuration)/sum(actionCount)", "actionRequestDuration"))
                .addAgg(Aggregation.of(" sum(actionServiceDuration)/sum(actionCount)", "actionServiceDuration"))
                .addAgg(Aggregation.of(" sum(ajaxErrorCount)", "ajaxErrorCount"))
                .addAgg(Aggregation.of(" sum(ajaxRequestCount)", "ajaxRequestCount"))
                .addAgg(Aggregation.of(" (100*sum(ajaxErrorCount))/sum(ajaxRequestCount)", "ajaxError"))
                .addAgg(Aggregation.of(" sum(successfulActionCount)", "successfulActionCount"))
                .addAgg(Aggregation.of(" sum(serverResponseCount)", "serverResponseCount"))
                .addAgg(Aggregation.of(" (100*sum(successfulActionCount))/sum(actionCount)", "actionAvailability"))
                .addGroupBy(ACTION_NAME);

        // 2. 如果用户输入了 actionName，需要加上 Where.like(...)；如果只想精确匹配，可用 eq
        if (StringUtils.isNotBlank(search.getActionName())) {
            qb.addWhere(Where.like(ACTION_NAME, search.getActionName()));
        }

        // 3. 如果 alias 匹配出了若干 RumWebAlias，需要在 TSDB 中 Where.in("actionName", those ids)
        if (CollectionUtils.isNotEmpty(matchedAliases)) {
            List<String> aliasIds = matchedAliases.stream()
                    .map(RumWebAlias::getId)
                    .collect(Collectors.toList());
            qb.addWhere(Where.in(ACTION_NAME, aliasIds));
        }

        // 4. 如果排序字段属于 validSortFields，则在 TSDB 进行分页与排序
        String sortField = search.getSortField();
        boolean sortByTSDB = validSortFields.contains(sortField);
        if (sortByTSDB) {
            Sort.Direction order = search.getSortOrder() == null
                    ? Sort.Direction.DESC : search.getSortOrder();
            int offset = (search.getPageNum() - 1) * search.getPageSize();
            offset = Math.max(offset, 0);

            qb.setLimit(search.getPageSize());
            qb.setOffset(offset);
            qb.addOrderBy(new OrderBy(sortField, order == Sort.Direction.ASC));
        }

        return qb;
    }

    /**
     * <p>构建“分组计数”查询，用于在 TSDB 排序时获取 total。</p>
     */
    public static QueryBuilder buildActionSearchCountQuery(
            WebSearchCriteria search,
            List<RumWebAlias> matchedAliases
    ) {
        long fromMs = toMs(search.getFromTime(), true);
        long toMs = toMs(search.getToTime(), false);

        QueryBuilder qb = new QueryBuilder()
                .setDatabaseName(search.getApiKey() + "_rum")
                .setMeasurement("rum.web.action")
                .addWhere(Where.gte("time", fromMs))
                .addWhere(Where.lt("time", toMs))
                .addWhere(Where.eq(APP_ID, search.getAppId()))
                // 只要做分组，不一定要加任何 agg
                .addAgg(Aggregation.of(" sum(actionCount)", "actionCount"))
                .addGroupBy(ACTION_NAME);

        // 若传入 actionName
        if (StringUtils.isNotBlank(search.getActionName())) {
            qb.addWhere(Where.like(ACTION_NAME, search.getActionName()));
        }
        // 若别名匹配
        if (CollectionUtils.isNotEmpty(matchedAliases)) {
            List<String> aliasIds = matchedAliases.stream()
                    .map(RumWebAlias::getId)
                    .collect(Collectors.toList());
            qb.addWhere(Where.in(ACTION_NAME, aliasIds));
        }

        return qb;
    }

    /**
     * <p>构造获取指定 actionCount 的 QueryBuilder。</p>
     * <p>对应 getActionCount(WebSearchCriteria)。</p>
     */
    public static QueryBuilder buildActionCountQuery(WebSearchCriteria search) {
        long fromMs = toMs(search.getFromTime(), true);
        long toMs = toMs(search.getToTime(), false);

        QueryBuilder qb = new QueryBuilder()
                .setDatabaseName(search.getApiKey() + "_rum")
                .setMeasurement("rum.web.action")
                .addWhere(Where.gte("time", fromMs))
                .addWhere(Where.lt("time", toMs))
                .addWhere(Where.eq(APP_ID, search.getAppId()))
                .addAgg(Aggregation.of(" sum(actionCount)", "actionCount"));

        // 如果 actionName 不为空
        if (StringUtils.isNotBlank(search.getActionName())) {
            qb.addWhere(Where.eq(ACTION_NAME, search.getActionName()));
        }

        return qb;
    }

    /**
     * <p>构造获取所有 actionName 的 QueryBuilder。</p>
     * <p>对应 getAllActionNames(WebSearchCriteria)。</p>
     */
    public static QueryBuilder buildAllActionNamesQuery(WebSearchCriteria search) {
        long fromMs = toMs(search.getFromTime(), true);
        long toMs = toMs(search.getToTime(), false);

        QueryBuilder qb = new QueryBuilder()
                .setDatabaseName(search.getApiKey() + "_rum")
                .setMeasurement("rum.web.action")
                .addWhere(Where.gte("time", fromMs))
                .addWhere(Where.lt("time", toMs))
                .addWhere(Where.eq(APP_ID, search.getAppId()))
                // 随意加一个agg字段，这里 sum(actionCount) 以便 groupBy 不报错
                .addAgg(Aggregation.of(" sum(actionCount)", "actionCount"))
                .addGroupBy(ACTION_NAME);

        // 如果传入 actionName 当作筛选(可能是 like)
        if (StringUtils.isNotBlank(search.getActionName())) {
            qb.addWhere(Where.like(ACTION_NAME, search.getActionName()));
        }
        return qb;
    }

    /**
     * <p>UETrend: 如果是upper，需要查询 percentile 表。</p>
     * <p>此方法已改造为使用 RumPercentileUtil 处理数据库差异化逻辑。</p>
     *
     * @param search          用户输入的查询条件。
     * @param tsdbOperateUtil TSDB 操作工具。
     * @param percentileUtil  RUM 百分位处理工具的实例。 <<<< 新增参数
     * @return Map<String ( 指标类型 ), MetricByGroupGraph>
     */
    public static Map<String, com.databuff.metric.dto.MetricByGroupGraph> queryUpperPercentileIfNeeded(
            WebSearchCriteria search,
            com.databuff.common.tsdb.TSDBOperateUtil tsdbOperateUtil,
            RumPercentileUtil percentileUtil) {
        if (!"upper".equalsIgnoreCase(search.getAggregation())) {
            return new HashMap<>(); // 非 upper 则直接返回空
        }
        Long fromMs = toMs(search.getFromTime(), true); // toMs 方法保持您原有的
        Long toMs = toMs(search.getToTime(), false);

        String percentileTable = "rum.web.action.percentile";
        String genericValueAlias = "upperVal"; // MoreDB直接使用此别名，OpenGemini用此作为桶别名前缀

        QueryBuilder qb = new QueryBuilder()
                .setDatabaseName(search.getApiKey() + "_rum")
                // .setMeasurement(percentileTable) // Measurement 将由 percentileUtil.addPercentileAggregationsToQuery 设置
                .addWhere(Where.gte("time", fromMs))
                .addWhere(Where.lt("time", toMs))
                .addWhere(Where.eq(APP_ID, search.getAppId()));

        // 处理 actionName 过滤 (保持原有逻辑)
        if (StringUtils.isNotBlank(search.getActionName())) {
            qb.addWhere(Where.eq(ACTION_NAME, search.getActionName()));
        }

        // **重要**: 处理 type 过滤。search.getFields() 包含需要查询百分位的指标类型列表。
        // RumPercentileUtil.addPercentileAggregationsToQuery 不处理 'type' 过滤。
        // 原查询是 WHERE type IN (...) GROUP BY type。
        // 如果 Util 的 addPercentileAggregationsToQuery 是为单个指标类型设计的，
        // 那么这里需要为 search.getFields() 中的每个 field 构建一次聚合，
        // 或者修改 Util 或此处的逻辑以适应一个查询获取多种type的百分位。
        //
        // 当前的 RumPercentileUtil.addPercentileAggregationsToQuery 是针对单个 finalAlias 的。
        // 而原始的SQL是 `SELECT upper(P) as upperVal ... WHERE type IN (...) GROUP BY type`
        // 这意味着对于MoreDB，一个聚合就能配合GROUP BY type出结果。
        // 对于OpenG，一个SUM(histX) as upperVal_bucketX 也需要配合GROUP BY type。
        // 所以，`addPercentileAggregationsToQuery` 的 `finalAlias` 应该是通用的 "upperVal"。
        // `Where.in("type", search.getFields())` 和 `addGroupBy("type")` 必须在这里设置。

        if (CollectionUtils.isNotEmpty(search.getFields())) {
            qb.addWhere(Where.in("type", search.getFields()));
        } else {
            // 如果 search.getFields() 为空，可能需要一个默认的 type 或不进行 type 过滤
            // 根据您的业务逻辑决定，这里假设 fields 不会为空，或者如果为空则不进行 type 过滤
            log.warn("queryUpperPercentileIfNeeded: search.getFields() 为空，可能导致查询范围过大或无 'type' 标签数据。");
        }

        qb.addGroupBy(ACTION_NAME); // 保持原有的 GROUP BY
        qb.addGroupBy("type");      // 保持原有的 GROUP BY type，这样每个 type 会产生一个 series

        if (search.getInterval() != null && search.getInterval() > 0) {
            qb.setInterval(search.getInterval());
        } else {
            qb.setInterval(60); // 默认间隔
        }

        // 调用 Util 添加聚合
        percentileUtil.addPercentileAggregationsToQuery(
                qb,
                percentileTable,
                search.getUpperNumber(),
                genericValueAlias // 例如 "upperVal"
        );

        TSDBResultSet rs = tsdbOperateUtil.executeQuery(qb);

        // 调用 Util 处理结果集
        // processPercentileTrendResultSetForAllTypes 会处理 MoreDB 和 OpenG 的情况
        // "type" 是在 tags 中区分不同指标的键
        // isMobile 对于 rum.web.action 通常是 false (即单位为毫秒)
        return percentileUtil.processMultiTypePercentileResultSet(
                rs,
                "type", // Tag key to distinguish different metrics in series
                search.getUpperNumber(),
                genericValueAlias, // The alias used in aggregation
                true
        );
    }


    /**
     * <p>UETrend: 处理 avg 或默认聚合。</p>
     */
    public static Map<String, com.databuff.metric.dto.MetricByGroupGraph> queryAvgStatsOverTime(
            WebSearchCriteria search,
            com.databuff.common.tsdb.TSDBOperateUtil tsdbOperateUtil
    ) {
        long fromMs = toMs(search.getFromTime(), true);
        long toMs = toMs(search.getToTime(), false);

        // 构造 aggregator 列表
        List<Aggregation> aggs = buildAvgAggregations(search.getFields());

        QueryBuilder qb = new QueryBuilder()
                .setDatabaseName(search.getApiKey() + "_rum")
                .setMeasurement("rum.web.action")
                .addWhere(Where.gte("time", fromMs))
                .addWhere(Where.lt("time", toMs))
                .addWhere(Where.eq(APP_ID, search.getAppId()))
                .addAllAgg(aggs)
                .addGroupBy(ACTION_NAME)
                .setInterval((search.getInterval() == null) ? 60 : search.getInterval());

        // 如果 actionName 不为空
        if (StringUtils.isNotBlank(search.getActionName())) {
            qb.addWhere(Where.eq(ACTION_NAME, search.getActionName()));
        }

        TSDBResultSet rs = tsdbOperateUtil.executeQuery(qb);
        return com.databuff.webapp.rumV2.service.impl.web.action.helper.RumWebActionResultParserHelper
                .parseAvgStatsOverTime(rs);
    }

    /**
     * <p>Time Distribution 查询。</p>
     */
    public static QueryBuilder buildTimeDistributionQuery(WebSearchCriteria search) {
        long fromMs = toMs(search.getFromTime(), true);
        long toMs = toMs(search.getToTime(), false);

        String field = search.getField();

        return new QueryBuilder()
                .setDatabaseName(search.getApiKey() + "_rum")
                .setMeasurement("rum.web.action")
                .addWhere(Where.gte("time", fromMs))
                .addWhere(Where.lt("time", toMs))
                .addWhere(Where.eq(APP_ID, search.getAppId()))
                .addWhere(Where.eq(ACTION_NAME, search.getActionName()))
                .addAgg(Aggregation.of(" sum(actionCount)", "actionCount"))
                .addGroupBy(ACTION_NAME)
                .addGroupBy(field + "Tier");
    }

    //================= 私有方法与工具方法 ========================

    /**
     * <p>将 fields 中的每个字段构造为 sum(field)/sum(actionCount) 或 sum(field)。</p>
     */
    private static List<Aggregation> buildAvgAggregations(Collection<String> fields) {
        List<Aggregation> result = new java.util.ArrayList<>();
        for (String f : fields) {
            if ("actionCount".equalsIgnoreCase(f)) {
                result.add(Aggregation.of(" sum(actionCount)", "actionCount"));
            } else {
                result.add(Aggregation.of(" sum(" + f + ")/sum(actionCount)", f));
            }
        }
        return result;
    }

    /**
     * 将日期转换成 long 毫秒
     */
    private static long toMs(Date date, boolean isFromTime) {
        if (date == null) {
            if (isFromTime) {
                return System.currentTimeMillis() - 24 * 3600_000L;
            } else {
                return System.currentTimeMillis();
            }
        }
        return DateUtils.tsdbTimeToMilliseconds(date);
    }

}
