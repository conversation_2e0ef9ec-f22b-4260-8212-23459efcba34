package com.databuff.webapp.rumV2.service.impl.android.symbol;

import com.databuff.common.utils.PathResolver;
import com.databuff.dao.mysql.RumAndroidSymbolFileMapper;
import com.databuff.entity.rum.mysql.AndroidSymbolFile;
import com.databuff.entity.rum.web.BaseRumAndroidErrorDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Optional;

/**
 * Android 堆栈符号化服务
 *
 * 本服务用于将混淆后的 Android 崩溃日志转换为可读的格式。处理流程如下：
 * 1. 根据应用 ID 获取 mapping 文件相对路径，并还原为绝对路径；
 * 2. 利用 RetraceUtil 对预处理后的堆栈进行反混淆；
 * 3. 返回反混淆后的堆栈字符串（若无 mapping 文件或反混淆出错，则返回原始堆栈）。
 */
@Slf4j
@Service
public class AndroidStackSymbolizationService {

    @Autowired
    private RumAndroidSymbolFileMapper symbolFileMapper;

    /**
     * 符号化 Android 堆栈信息
     *
     * @param errorDto 错误信息实体（ANR/Crash），包含 appId、appVersion、appVersionName 及 stackInfo（多线程堆栈）
     * @return 符号化后的堆栈字符串
     */
    public String symbolizeStack(BaseRumAndroidErrorDto errorDto) {
        // 1. 输入验证
        if (errorDto == null || errorDto.getAppId() == null || !StringUtils.hasText(errorDto.getStackInfo())) {
            log.warn("Invalid input: errorDto or its appId/stackInfo is null or empty.");
            return (errorDto != null) ? errorDto.getStackInfo() : null; // 尽量返回原始堆栈, 如果errorDto整个为null, 那就返回null
        }
        String rawStack = errorDto.getStackInfo();
        if (rawStack == null || rawStack.trim().isEmpty()) {
            return rawStack;
        }

        // 1. 获取符号表文件绝对路径
        String mappingPath = getMappingFilePath(errorDto);
        if (mappingPath == null) {
            log.warn("No mapping file found for appId:{}, version:{}, versionName:{}",
                    errorDto.getAppId(), errorDto.getAppVersion(), errorDto.getAppVersionName());
            return rawStack;
        }

        // 2. 调用 RetraceUtil 进行反混淆
        try {
            return RetraceUtil.symbolizeStackTrace(
                    mappingPath,
                    rawStack
            );
        } catch (Exception e) {
            log.error("Symbolize stack failed for appId:{} version:{}",
                    errorDto.getAppId(), errorDto.getAppVersion(), e);
            return rawStack;
        }
    }

    /**
     * 获取符号表文件绝对路径
     */
    private String getMappingFilePath(BaseRumAndroidErrorDto errorDto) {
        // 输入验证 (getMappingFilePath也做了验证, 保证代码的健壮性)
        if (errorDto.getAppId() == null || !StringUtils.hasText(errorDto.getAppVersion()) || !StringUtils.hasText(errorDto.getAppVersionName())) {
            log.warn("Cannot get mapping file path: appId, appVersion, or appVersionName is missing.");
            return null;
        }
        // 根据版本信息查询符号表记录
        AndroidSymbolFile symbolFile = symbolFileMapper.getByAppVersionAndBuild(
                errorDto.getAppId(),
                errorDto.getAppVersion(),
                errorDto.getAppVersionName()
        );
        return Optional.ofNullable(symbolFile)
                .map(AndroidSymbolFile::getFilePath)
                .map(PathResolver::resolveRelativePath) // 利用工具将相对路径还原为绝对路径
                .orElse(null);
    }


}
