package com.databuff.webapp.rumV2.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.databuff.entity.rum.starrocks.RumPage;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.databuff.common.utils.StringUtil.convertNumberToString;

@Data
public class RumPageVO {

    @ApiModelProperty(value = "事件发生时间", example = "2023-10-01 12:00:00")
    @JSONField(name = "startTime", format = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "页面id", example = "1001")
    @JSONField(name = "page_id")
    private String pageId;

    @ApiModelProperty(value = "页面来源URL", example = "http://example.com")
    @JSONField(name = "location_href")
    private String locationHref;

    @ApiModelProperty(value = "处理过的页面来源URL", example = "http://example.com/processed")
    @JSONField(name = "processed_location_href")
    private String processedLocationHref;

    @ApiModelProperty(value = "是否是慢页面 0:否 1:是", example = "0")
    @JSONField(name = "is_slow_page")
    private Integer isSlowPage;

    @ApiModelProperty(value = "是否是慢完全加载 0:否 1:是")
    @JSONField(name = "is_slow_full_load_time")
    private Integer isSlowFullLoadTime;

    @ApiModelProperty(value = "是否是慢lcp 0:否 1:是")
    @JSONField(name = "is_slow_lcp")
    private Integer isSlowLcp;

    @ApiModelProperty(value = "是否是慢fcp 0:否 1:是")
    @JSONField(name = "is_slow_fcp")
    private Integer isSlowFcp;

    @ApiModelProperty(value = "是否是慢dcl 0:否 1:是")
    @JSONField(name = "is_slow_dcl")
    private Integer isSlowDcl;

    @ApiModelProperty(value = "用户ID", example = "user_001")
    @JSONField(name = "user_id")
    private String userId;

    @ApiModelProperty(value = "应用id", example = "12345")
    @JSONField(name = "app_id")
    private String appId;

    @ApiModelProperty(value = "公网IP", example = "***********")
    @JSONField(name = "ip")
    private String ip;

    @ApiModelProperty(value = "会话ID", example = "2001")
    @JSONField(name = "session_id")
    private String sessionId;

    @ApiModelProperty(value = "运营商", example = "ISP Provider")
    @JSONField(name = "isp")
    private String isp;

    @ApiModelProperty(value = "浏览器", example = "Chrome")
    @JSONField(name = "browser")
    private String browser;

    @ApiModelProperty(value = "操作系统", example = "Windows 10")
    @JSONField(name = "operating_system")
    private String operatingSystem;

    @ApiModelProperty(value = "地域", example = "Beijing")
    @JSONField(name = "region")
    private String region;

    @ApiModelProperty(value = "探针版本", example = "1.0.0")
    @JSONField(name = "probe_version")
    private String probeVersion;

    @ApiModelProperty(value = "完全加载时间(ms)", example = "1500")
    @JSONField(name = "full_load_time")
    private Long fullLoadTime;

    @ApiModelProperty(value = "Largest Contentful Paint(ns)", example = "1200")
    @JSONField(name = "lcp")
    private Long lcp;

    @ApiModelProperty(value = "First Contentful Paint(ns)", example = "800")
    @JSONField(name = "fcp")
    private Long fcp;

    @ApiModelProperty(value = "DomContentLoaded(ns)", example = "600")
    @JSONField(name = "dcl")
    private Long dcl;

    @ApiModelProperty(value = "First Input Delay(ns)", example = "50")
    @JSONField(name = "fid")
    private Long fid;

    @ApiModelProperty(value = "Cumulative Layout Shift", example = "1")
    @JSONField(name = "cls")
    private Double cls;

    @ApiModelProperty(value = "Time to First Byte(ns)", example = "300")
    @JSONField(name = "ttfb")
    private Long ttfb;

    @ApiModelProperty(value = "Time to Interactive(ns)", example = "2000")
    @JSONField(name = "tti")
    private Long tti;

    @ApiModelProperty(value = "Total Blocking Time(ns)", example = "2000")
    @JSONField(name = "tbt")
    private Long tbt;

    @ApiModelProperty(value = "User Agent", example = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3")
    @JSONField(name = "user_agent")
    private String userAgent;

    @ApiModelProperty(value = "租户 api key id", example = "1")
    @JSONField(name = "df_api_key_id")
    private Integer dfApiKeyId;

    public static RumPageVO fromRumPage(RumPage rumPage) {
        RumPageVO vo = new RumPageVO();
        if (rumPage == null) {
            return vo;
        }
        vo.setStartTime(rumPage.getStartTime());
        vo.setPageId(convertNumberToString(rumPage.getPageId()));
        vo.setLocationHref(rumPage.getLocationHref());
        vo.setProcessedLocationHref(rumPage.getProcessedLocationHref());
        vo.setIsSlowPage(rumPage.getIsSlowPage());
        vo.setIsSlowFullLoadTime(rumPage.getIsSlowFullLoadTime());
        vo.setIsSlowLcp(rumPage.getIsSlowLcp());
        vo.setIsSlowFcp(rumPage.getIsSlowFcp());
        vo.setIsSlowDcl(rumPage.getIsSlowDcl());
        vo.setUserId(rumPage.getUserId());
        vo.setAppId(convertNumberToString(rumPage.getAppId()));
        vo.setIp(rumPage.getIp());
        vo.setSessionId(convertNumberToString(rumPage.getSessionId()));
        vo.setIsp(rumPage.getIsp());
        vo.setBrowser(rumPage.getBrowser());
        vo.setOperatingSystem(rumPage.getOperatingSystem());
        vo.setRegion(rumPage.getRegion());
        vo.setProbeVersion(rumPage.getProbeVersion());
        vo.setFullLoadTime(rumPage.getFullLoadTime());
        vo.setLcp(rumPage.getLcp());
        vo.setFcp(rumPage.getFcp());
        vo.setDcl(rumPage.getDcl());
        vo.setFid(rumPage.getFid());
        vo.setCls(rumPage.getCls());
        vo.setTtfb(rumPage.getTtfb());
        vo.setTti(rumPage.getTti());
        vo.setTbt(rumPage.getTbt());
        vo.setUserAgent(rumPage.getUserAgent());
        vo.setDfApiKeyId(rumPage.getDfApiKeyId());
        return vo;
    }

    public static List<RumPageVO> fromRumPage(List<RumPage> rumPages) {
        List<RumPageVO> voList = new ArrayList<>();
        if (rumPages == null) {
            return voList;
        }
        for (RumPage rumPage : rumPages) {
            voList.add(fromRumPage(rumPage));
        }
        return voList;
    }

    public static PageInfo<RumPageVO> fromRumPagePageInfo(PageInfo<RumPage> rumPagePageInfo) {
        List<RumPageVO> voList = fromRumPage(rumPagePageInfo.getList());
        PageInfo<RumPageVO> voPageInfo = new PageInfo<>(voList);
        voPageInfo.setPageNum(rumPagePageInfo.getPageNum());
        voPageInfo.setPageSize(rumPagePageInfo.getPageSize());
        voPageInfo.setSize(rumPagePageInfo.getSize());
        voPageInfo.setStartRow(rumPagePageInfo.getStartRow());
        voPageInfo.setEndRow(rumPagePageInfo.getEndRow());
        voPageInfo.setTotal(rumPagePageInfo.getTotal());
        voPageInfo.setPages(rumPagePageInfo.getPages());
        voPageInfo.setNavigateFirstPage(rumPagePageInfo.getNavigateFirstPage());
        voPageInfo.setNavigateLastPage(rumPagePageInfo.getNavigateLastPage());
        voPageInfo.setNavigatepageNums(rumPagePageInfo.getNavigatepageNums());
        voPageInfo.setNavigatePages(rumPagePageInfo.getNavigatePages());
        voPageInfo.setPrePage(rumPagePageInfo.getPrePage());
        voPageInfo.setNextPage(rumPagePageInfo.getNextPage());
        voPageInfo.setIsFirstPage(rumPagePageInfo.isIsFirstPage());
        voPageInfo.setIsLastPage(rumPagePageInfo.isIsLastPage());
        voPageInfo.setHasPreviousPage(rumPagePageInfo.isHasPreviousPage());
        voPageInfo.setHasNextPage(rumPagePageInfo.isHasNextPage());
        return voPageInfo;
    }
}