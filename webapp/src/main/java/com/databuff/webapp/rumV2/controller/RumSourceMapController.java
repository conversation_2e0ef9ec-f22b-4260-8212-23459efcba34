package com.databuff.webapp.rumV2.controller;

import com.databuff.entity.rum.mysql.RumSourceMap;
import com.databuff.entity.rum.web.SourceMapSearchCriteria;
import com.databuff.webapp.config.common.CommonResponse;
import com.databuff.webapp.rumV2.service.RumSourceMapService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/rum/v2/sourcemap")
public class RumSourceMapController {

    @Autowired
    private RumSourceMapService rumSourceMapService;

    @ApiOperation(value = "应用概览-应用设置-source map设置-列表查询", notes = "Source Map列表查询")
    @PostMapping("/list")
    public ResponseEntity<PageInfo<RumSourceMap>> listSourceMaps(@RequestBody SourceMapSearchCriteria search) {
        PageInfo<RumSourceMap> sourceMaps = rumSourceMapService.getSourceMaps(search);
        return ResponseEntity.ok(sourceMaps);
    }



    @ApiOperation(value = "应用概览-应用设置-source map设置-上传Source Map", notes = "上传应用的Source Map文件")
    @PostMapping("/upload")
    public CommonResponse<String> uploadSourceMap(
            @RequestParam("appId") Integer appId,
            @RequestParam("file") MultipartFile file) {
        String error = rumSourceMapService.saveOrUpdateSourceMap(appId, file);
        if (error != null) {
            return new CommonResponse<>(400, error, null);
        }
        return new CommonResponse<>(200, "SUCCESS", "Source Map 上传成功");
    }



    @ApiOperation(value = "应用概览-应用设置-source map设置-删除Source Map", notes = "根据ids 删除多个Source Map文件")
    @DeleteMapping("/delete")
    public CommonResponse<String> deleteSourceMaps(@RequestParam("ids") List<Integer> ids) {
        int deletedCount = rumSourceMapService.deleteSourceMaps(ids);
        return new CommonResponse<>(200, "SUCCESS", "Successfully deleted " + deletedCount + " source maps");
    }



}
