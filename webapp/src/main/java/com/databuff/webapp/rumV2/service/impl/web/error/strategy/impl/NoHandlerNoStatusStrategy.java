package com.databuff.webapp.rumV2.service.impl.web.error.strategy.impl;

import com.databuff.dao.mysql.RumJsErrorMapper;
import com.databuff.dao.starrocks.RumErrorLogsMapper;
import com.databuff.entity.rum.mysql.RumJsError;
import com.databuff.entity.rum.web.JsErrorSearchCriteria;
import com.databuff.entity.rum.web.RumErrorLogsAggregateDto;
import com.databuff.webapp.rumV2.model.QueryResult;
import com.databuff.webapp.rumV2.service.impl.web.error.strategy.QueryStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class NoHandlerNoStatusStrategy implements QueryStrategy {
    @Autowired
    private RumJsErrorMapper rumJsErrorMapper;
    @Autowired
    private RumErrorLogsMapper rumErrorLogsMapper;

    @Override
    public QueryResult execute(JsErrorSearchCriteria criteria) {
        List<RumJsError> mysqlErrors = rumJsErrorMapper.getJsErrorListByAppIdAndErrorMessage(criteria.getAppId(), criteria.getErrorMessage());
        List<RumErrorLogsAggregateDto> starRocksErrors;
        long totalCount;

        if (!mysqlErrors.isEmpty()) {
            starRocksErrors = rumErrorLogsMapper.getErrorLogsList(
                    criteria.getAppId(),
                    criteria.getErrorMessage(),
                    criteria.getOffset(),
                    criteria.getPageSize(),
                    criteria.getFromTime(),
                    criteria.getToTime(),
                    criteria.getSortField(),
                    criteria.getSortOrder()
            );
            totalCount = rumErrorLogsMapper.countErrorLogs(
                    criteria.getAppId(),
                    criteria.getFromTime(),
                    criteria.getToTime(),
                    criteria.getErrorMessage()
            );
        } else {
            starRocksErrors = rumErrorLogsMapper.getErrorLogsList(
                    criteria.getAppId(),
                    criteria.getErrorMessage(),
                    criteria.getOffset(),
                    criteria.getPageSize(),
                    criteria.getFromTime(),
                    criteria.getToTime(),
                    criteria.getSortField(),
                    criteria.getSortOrder()
            );
            totalCount = rumErrorLogsMapper.countErrorLogs(
                    criteria.getAppId(),
                    criteria.getFromTime(),
                    criteria.getToTime(),
                    criteria.getErrorMessage()
            );
        }

        return new QueryResult(mysqlErrors, starRocksErrors, totalCount);
    }

    @Override
    public boolean isStarRocksPrimary() {
        return true;
    }
}
