package com.databuff.webapp.rumV2.service;

import com.databuff.entity.rum.web.*;
import com.databuff.webapp.rumV2.model.LifecycleEventDto;
import com.databuff.webapp.rumV2.model.ThreadStackDto;

import java.util.List;

public interface RumAndroidDetailService {
    // 启动详情
    RumAndroidLaunchDto getLaunchBasic(AndroidLaunchDetailRequest request);
    List<LifecycleEventDto> getLaunchLifecycle(AndroidLaunchDetailRequest request);
    List<RumAndroidLaunchSpanDto> getLaunchNetwork(AndroidLaunchDetailRequest request);


    // 页面详情
    RumAndroidPageDto getPageBasic(AndroidPageDetailRequest request);
    List<LifecycleEventDto> getPageLifecycle(AndroidPageDetailRequest request);
    List<RumAndroidPageSpanDto> getPageNetwork(AndroidPageDetailRequest request);

    // 操作详情
    RumAndroidActionDto getActionBasic(AndroidActionDetailRequest request);
    List<LifecycleEventDto> getActionLifecycle(AndroidActionDetailRequest request);
    List<RumAndroidActionSpanDto> getActionNetwork(AndroidActionDetailRequest request);


    // 卡顿详情
    RumAndroidAnrDto getAnrBasic(AndroidAnrDetailRequest request);
    List<ThreadStackDto> getAnrStack(AndroidAnrDetailRequest request);
    List<AndroidExceptionTraceDto> getAnrTrace(AndroidAnrDetailRequest request);

    // 崩溃详情
    RumAndroidCrashDto getCrashBasic(AndroidCrashDetailRequest request);
    List<ThreadStackDto> getCrashStack(AndroidCrashDetailRequest request);
    List<AndroidExceptionTraceDto> getCrashTrace(AndroidCrashDetailRequest request);
}

