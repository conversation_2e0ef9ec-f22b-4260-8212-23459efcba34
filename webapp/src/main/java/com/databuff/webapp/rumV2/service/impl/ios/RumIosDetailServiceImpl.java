package com.databuff.webapp.rumV2.service.impl.ios;

import com.databuff.common.exception.BusinessException;
import com.databuff.dao.mysql.IosDeviceMappingMapper;
import com.databuff.dao.mysql.RumAppV2Mapper;
import com.databuff.dao.starrocks.RumIosDetailMapper;
import com.databuff.entity.rum.mysql.RumAppSettings;
import com.databuff.entity.rum.starrocks.BaseRumIosInfo;
import com.databuff.entity.rum.starrocks.RumIosLifecycleMethod;
import com.databuff.entity.rum.web.*;
import com.databuff.webapp.rumV2.model.LifecycleEventDto;
import com.databuff.webapp.rumV2.model.ThreadStackDto;
import com.databuff.webapp.rumV2.service.RumIosDetailService;
import com.databuff.webapp.rumV2.service.impl.ios.symbol.IosStackSymbolizationService;
import com.databuff.webapp.util.IosErrorStackParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RumIosDetailServiceImpl implements RumIosDetailService {

    @Autowired
    private RumIosDetailMapper rumIosDetailMapper;

    @Autowired
    private RumAppV2Mapper rumAppV2Mapper;

    @Autowired
    private IosStackSymbolizationService iosStackSymbolizationService;

    @Autowired
    private IosDeviceMappingMapper iosDeviceMappingMapper;

    // Launch detail methods
    @Override
    public RumIosLaunchDto getLaunchBasic(IosLaunchDetailRequest request) {
        RumIosLaunchDto launch = rumIosDetailMapper.selectLaunchById(request);
        enrichAppInfo(request.getAppId(), launch);
        return launch;
    }

    private void enrichAppInfo(Integer appId, BaseRumIosInfo baseInfo) {
        if (baseInfo == null) {
            throw new BusinessException("ios基础数据不存在");
        }
        RumAppSettings appSettings = rumAppV2Mapper.getAppSettingsById(appId);
        if (appSettings == null) {
            throw new BusinessException("ios应用信息不存在");
        }
        baseInfo.setAppName(appSettings.getAppName());
    }

    @Override
    public List<LifecycleEventDto> getLaunchLifecycle(IosLaunchDetailRequest request) {
        List<RumIosLifecycleMethod> methods = rumIosDetailMapper.selectLifecycleMethods(request.getLaunchId(), 1, request);
        Long launchStart = rumIosDetailMapper.selectLaunchStart(request);
        return convertToLifecycleDTO(methods, launchStart);
    }

    @Override
    public List<RumIosLaunchSpanDto> getLaunchNetwork(IosLaunchDetailRequest request) {
        List<RumIosLaunchSpanDto> spans = rumIosDetailMapper.selectLaunchSpans(request);
        Long launchStartTime = rumIosDetailMapper.selectLaunchStart(request);
        calculateRelativeTime(spans, launchStartTime);
        return spans;
    }

    // Page detail methods
    @Override
    public RumIosPageDto getPageBasic(IosPageDetailRequest request) {
        RumIosPageDto page = rumIosDetailMapper.selectPageById(request);
        enrichAppInfo(request.getAppId(), page);
        return page;
    }

    @Override
    public List<LifecycleEventDto> getPageLifecycle(IosPageDetailRequest request) {
        List<RumIosLifecycleMethod> methods = rumIosDetailMapper.selectLifecycleMethods(request.getPageId(), 4, request);
        Long pageStart = rumIosDetailMapper.selectPageStart(request);
        return convertToLifecycleDTO(methods, pageStart);
    }

    @Override
    public List<RumIosPageSpanDto> getPageNetwork(IosPageDetailRequest request) {
        List<RumIosPageSpanDto> spans = rumIosDetailMapper.selectPageSpans(request);
        Long pageStartTime = rumIosDetailMapper.selectPageStart(request);
        calculateRelativeTime(spans, pageStartTime);
        return spans;
    }

    // Action detail methods

    @Override
    public RumIosActionDto getActionBasic(IosActionDetailRequest request) {
        RumIosActionDto action = rumIosDetailMapper.selectActionById(request);
        enrichAppInfo(request.getAppId(), action);
        return action;
    }

    @Override
    public List<LifecycleEventDto> getActionLifecycle(IosActionDetailRequest request) {
        List<RumIosLifecycleMethod> methods = rumIosDetailMapper.selectLifecycleMethods(request.getActionId(), 2, request);
        Long actionStart = rumIosDetailMapper.selectActionStart(request);
        return convertToLifecycleDTO(methods, actionStart);
    }

    @Override
    public List<RumIosActionSpanDto> getActionNetwork(IosActionDetailRequest request) {
        List<RumIosActionSpanDto> spans = rumIosDetailMapper.selectActionSpans(request);
        Long actionStartTime = rumIosDetailMapper.selectActionStart(request);
        calculateRelativeTime(spans, actionStartTime);
        return spans;
    }

    // ANR detail methods
    @Override
    public RumIosAnrDto getAnrBasic(IosAnrDetailRequest request) {
        RumIosAnrDto anr = rumIosDetailMapper.selectAnrById(request);
        enrichAppInfo(request.getAppId(), anr);
        return anr;
    }

    @Override
    public List<ThreadStackDto> getAnrStack(IosAnrDetailRequest request) {
        RumIosAnrDto anr = rumIosDetailMapper.selectAnrStack(request);
        String probeValue = iosDeviceMappingMapper.getProbeValue(anr.getDeviceIdentifier());
        String symbolizedStack = iosStackSymbolizationService.symbolizeStack(anr.getStackInfo(),probeValue);
        return IosErrorStackParser.parseStackTrace(symbolizedStack);
    }

    @Override
    public List<IosExceptionTraceDto> getAnrTrace(IosAnrDetailRequest request) {
        RumIosAnrDto anr = getAnrBasic(request);
        List<RumIosActionDto> actions = rumIosDetailMapper.selectExceptionTrace(anr.getStartTime(), request.getAppId(), anr.getUserId());
        return convertToExceptionTrace(actions, anr.getStartTime(), "卡顿");
    }

    private List<IosExceptionTraceDto> convertToExceptionTrace(List<RumIosActionDto> actions, Date errorTime, String errorType) {
        List<IosExceptionTraceDto> result = new ArrayList<>();

        // Add the ANR/Crash event at the top
        IosExceptionTraceDto errorEvent = new IosExceptionTraceDto();
        errorEvent.setActionName(errorType); // "卡顿" or "崩溃"
        errorEvent.setPageName("");
        errorEvent.setStartTime(errorTime);
        errorEvent.setRelativeTime(0L);
        result.add(errorEvent);

        // Add previous actions
        if (!CollectionUtils.isEmpty(actions)) {
            result.addAll(actions.stream().map(action -> {
                IosExceptionTraceDto dto = new IosExceptionTraceDto();
                dto.setActionId(String.valueOf(action.getActionId()));
                dto.setAppId(action.getAppId());
                dto.setActionName(action.getActionName());
                dto.setPageName(action.getPageName());
                dto.setStartTime(action.getStartTime());
                dto.setRelativeTime((errorTime.getTime() - action.getStartTime().getTime()) / 1000);
                return dto;
            }).collect(Collectors.toList()));
        }

        return result;
    }


    // Crash detail methods
    @Override
    public RumIosCrashDto getCrashBasic(IosCrashDetailRequest request) {
        RumIosCrashDto crash = rumIosDetailMapper.selectCrashById(request);
        enrichAppInfo(request.getAppId(), crash);
        return crash;
    }

    @Override
    public List<ThreadStackDto> getCrashStack(IosCrashDetailRequest request) {
        RumIosCrashDto crash = rumIosDetailMapper.selectCrashStack(request);
        String probeValue = iosDeviceMappingMapper.getProbeValue(crash.getDeviceIdentifier());
        String symbolizedStack = iosStackSymbolizationService.symbolizeStack(crash.getStackInfo(),probeValue);
        return IosErrorStackParser.parseStackTrace(symbolizedStack);
    }

    @Override
    public List<IosExceptionTraceDto> getCrashTrace(IosCrashDetailRequest request) {
        RumIosCrashDto crash = getCrashBasic(request);
        List<RumIosActionDto> actions = rumIosDetailMapper.selectExceptionTrace(crash.getStartTime(), request.getAppId(), crash.getUserId());
        return convertToExceptionTrace(actions, crash.getStartTime(), "崩溃");
    }


    private void calculateRelativeTime(List<? extends BaseRumIosSpanDto> spans, Long baseStartTime) {
        if (CollectionUtils.isEmpty(spans)) {
            return;
        }

        spans.forEach(span -> {
            span.setRelativeTime(span.getStart() - baseStartTime);
            span.setIsError(span.isErrorStatusCode());
        });
    }

    private List<LifecycleEventDto> convertToLifecycleDTO(List<RumIosLifecycleMethod> methods, Long baseStartTime) {
        if (CollectionUtils.isEmpty(methods)) {
            return new ArrayList<>();
        }

        Map<String, List<RumIosLifecycleMethod>> pageGroups = methods.stream()
                .collect(Collectors.groupingBy(RumIosLifecycleMethod::getPageName));

        List<LifecycleEventDto> result = new ArrayList<>();
        int level = 1;

        for (Map.Entry<String, List<RumIosLifecycleMethod>> entry : pageGroups.entrySet()) {
            String pageName = entry.getKey();
            List<RumIosLifecycleMethod> pageMethods = entry.getValue();

            for (RumIosLifecycleMethod method : pageMethods) {
                LifecycleEventDto dto = new LifecycleEventDto();
                dto.setName(pageName + "." + method.getMethodName() + "()");
                dto.setStart(method.getStart());
                dto.setEnd(method.getEnd());
                dto.setDuration(method.getDuration());
                dto.setLevel(level);
                dto.setRelativeTime(method.getStart() - baseStartTime);
                result.add(dto);
            }

            // Increment level for next page group
            level++;
        }

        return result;
    }

}
