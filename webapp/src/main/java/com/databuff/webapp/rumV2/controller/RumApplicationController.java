package com.databuff.webapp.rumV2.controller;

import com.databuff.entity.rum.mysql.RumAppSettings;
import com.databuff.entity.rum.web.*;
import com.databuff.metric.dto.MetricByGroupGraph;
import com.databuff.webapp.config.common.CommonResponse;
import com.databuff.webapp.rumV2.model.*;
import com.databuff.webapp.rumV2.service.RumApplicationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.TreeMap;

@Api(value = "RUM v2 应用前端接口")
@RestController
@RequestMapping("/rum/v2/applications")
public class RumApplicationController {

    @Autowired
    private RumApplicationService rumApplicationService;

    @ApiOperation(value = "应用概览-设置应用状态", notes = "应用列表 根据应用ID设置应用的启用或禁用状态")
    @PostMapping("/{id}/set-status")
    public CommonResponse<String> setApplicationStatus(
            @ApiParam(value = "应用ID", required = true) @PathVariable int id,
            @ApiParam(value = "状态值", required = true) @RequestParam int status) {
        boolean isCurrentlyEnabled = rumApplicationService.setApplicationStatus(id, status);
        CommonResponse<String> commonResponse = new CommonResponse<>();
        commonResponse.setData(isCurrentlyEnabled ? "Application enabled" : "Application disabled");
        return commonResponse;
    }


    @ApiOperation(value = "应用概览-删除WEB应用", notes = "应用列表 删除WEB应用")
    @DeleteMapping("/{id}")
    public CommonResponse<String> deleteApplication(@PathVariable int id) {
        boolean deleted = rumApplicationService.deleteApplication(id);
        CommonResponse<String> commonResponse = new CommonResponse<>();
        if (deleted) {
            commonResponse.setData("Application deleted successfully");

        } else {
            commonResponse.setData("Application deleted successfully");
            commonResponse.setStatus(HttpStatus.NOT_FOUND.value());
        }
        return commonResponse;
    }

    @ApiOperation(value = "应用概览-WEB应用指标展示列表", notes = "应用列表 WEB应用指标展示列表")
    @PostMapping("/list")
    public CommonResponse<ExtendedPageInfo<ApplicationListDto>> getApplicationsPage(HttpServletRequest httpServletRequest, @RequestBody ApplicationSearchCriteria criteria) {
        criteria.setApiKey(httpServletRequest.getAttribute("apiKey").toString());
        ExtendedPageInfo<ApplicationListDto> applications = rumApplicationService.getApplicationsPage(criteria);
        CommonResponse<ExtendedPageInfo<ApplicationListDto>> response = new CommonResponse<>();
        response.setData(applications);
        return response;
    }

    @ApiOperation(value = "应用概览-WEB应用指标展示列表V2", notes = "应用列表展示会话、交互、异常等指标")
    @PostMapping("/list/v2")
    public CommonResponse<ExtendedPageInfo<ApplicationListDtoV2>> getApplicationsPageV2(
            HttpServletRequest httpServletRequest,
            @RequestBody ApplicationSearchCriteriaV2 criteria) {
        criteria.setApiKey(httpServletRequest.getAttribute("apiKey").toString());
        ExtendedPageInfo<ApplicationListDtoV2> applications = rumApplicationService.getApplicationsPageV2(criteria);
        CommonResponse<ExtendedPageInfo<ApplicationListDtoV2>> response = new CommonResponse<>();
        response.setData(applications);
        return response;
    }


    @ApiOperation(value = "应用概览-创建应用", notes = "应用列表 创建应用")
    @PostMapping("")
    public CommonResponse<CreateApplicationResponse> createApplication(HttpServletRequest httpServletRequest, @Valid @RequestBody CreateApplicationRequest request) {
        request.setApiKey(httpServletRequest.getAttribute("apiKey").toString());
        CreateApplicationResponse createdApp = rumApplicationService.createApplication(request);
        CommonResponse<CreateApplicationResponse> response = new CommonResponse<>();
        response.setData(createdApp);
        return response;
    }


    @ApiOperation(value = "应用概览-获取所有应用ID和名称和应用类型", notes = "返回所有应用的ID和名称列表")
    @GetMapping("/id-name")
    public CommonResponse<List<ApplicationIdNameDto>> getAllApplicationsIdAndName() {
        List<ApplicationIdNameDto> applications = rumApplicationService.getAllApplicationsIdAndName();
        CommonResponse<List<ApplicationIdNameDto>> response = new CommonResponse<>();
        response.setData(applications);
        return response;
    }


    @ApiOperation(value = "应用概览-修改应用名称", notes = "基本设置 根据应用ID修改应用名称")
    @PutMapping("/{id}/name")
    public CommonResponse<String> updateApplicationName(@PathVariable int id, @Valid @RequestBody ApplicationIdNameDto nameDto) {
        rumApplicationService.updateApplicationName(id, nameDto.getName());
        CommonResponse<String> response = new CommonResponse<>();
        response.setData("Application name updated successfully");
        return response;
    }

    @ApiOperation(value = "应用概览-保存自定义IP地域规则", notes = "基本设置 IP地域设置")
    @PutMapping("/{id}/ip-rules")
    public CommonResponse<String> updateCustomIpRules(
            @PathVariable int id,
            @Valid @RequestBody List<CustomIpRuleDto> customIpRules) {
        rumApplicationService.updateCustomIpRules(id, customIpRules);
        CommonResponse<String> response = new CommonResponse<>();
        response.setData("Custom IP rules updated successfully");
        return response;
    }

    @ApiOperation(value = "应用概览-切换自定义IP地域规则使用状态", notes = "启用或禁用应用的自定义IP地域规则")
    @PutMapping("/{id}/toggle-custom-ip-rules")
    public CommonResponse<String> toggleCustomIpRules(
            @ApiParam(value = "应用ID", required = true) @PathVariable int id,
            @ApiParam(value = "是否使用自定义IP地域规则", required = true) @RequestParam boolean useCustomIpRules) {
        rumApplicationService.toggleCustomIpRules(id, useCustomIpRules);
        CommonResponse<String> response = new CommonResponse<>();
        response.setData("Custom IP rules usage updated successfully");
        return response;
    }

    @ApiOperation(value = "应用概览-获取应用设置", notes = "根据应用ID返回应用设置")
    @GetMapping("/{id}/settings")
    public CommonResponse<RumAppSettings> getAppSettingsById(
            @ApiParam(value = "应用ID", required = true) @PathVariable int id) {
        RumAppSettings appSettings = rumApplicationService.getAppSettingsById(id);
        CommonResponse<RumAppSettings> response = new CommonResponse<>();
        response.setData(appSettings);
        return response;
    }

    @ApiOperation(value = "应用概览-更新应用评分设置", notes = "根据应用ID更新评分设置")
    @PutMapping("/{id}/score-settings")
    public CommonResponse<String> updateScoreSettings(
            @ApiParam(value = "应用ID", required = true) @PathVariable int id,
            @Valid @RequestBody Object scoreSettings) {
        rumApplicationService.updateScoreSettings(id, scoreSettings);
        CommonResponse<String> response = new CommonResponse<>();
        response.setData("Score settings updated successfully");
        return response;
    }

    @ApiOperation(value = "应用详情-用户体验评分", notes = "返回应用的用户体验评分详情")
    @PostMapping("/{id}/user-experience-score")
    public CommonResponse<UserExperienceScoreDto> getUserExperienceScore(
            HttpServletRequest httpServletRequest,
            @ApiParam(value = "应用ID", required = true) @PathVariable int id,
            @RequestBody UserExperienceScoreRequest request) {
        request.setApiKey(httpServletRequest.getAttribute("apiKey").toString());
        UserExperienceScoreDto scoreDto = rumApplicationService.getUserExperienceScore(request);
        CommonResponse<UserExperienceScoreDto> response = new CommonResponse<>();
        response.setData(scoreDto);
        return response;
    }

    @ApiOperation(value = "应用详情-iOS用户体验评分", notes = "返回iOS应用的用户体验评分详情")
    @PostMapping("/{id}/ios-user-experience-score")
    public CommonResponse<MobileUserExperienceScoreDto> getIosUserExperienceScore(
            HttpServletRequest httpServletRequest,
            @ApiParam(value = "应用ID", required = true) @PathVariable int id,
            @RequestBody UserExperienceScoreRequest request) {
        request.setApiKey(httpServletRequest.getAttribute("apiKey").toString());
        MobileUserExperienceScoreDto scoreDto = rumApplicationService.getMobileUserExperienceScore(request, MobileType.IOS);
        CommonResponse<MobileUserExperienceScoreDto> response = new CommonResponse<>();
        response.setData(scoreDto);
        return response;
    }

    @ApiOperation(value = "应用详情-android用户体验评分", notes = "返回android应用的用户体验评分详情")
    @PostMapping("/{id}/android-user-experience-score")
    public CommonResponse<MobileUserExperienceScoreDto> getAndroidUserExperienceScore(
            HttpServletRequest httpServletRequest,
            @ApiParam(value = "应用ID", required = true) @PathVariable int id,
            @RequestBody UserExperienceScoreRequest request) {
        request.setApiKey(httpServletRequest.getAttribute("apiKey").toString());
        MobileUserExperienceScoreDto scoreDto = rumApplicationService.getMobileUserExperienceScore(request, MobileType.ANDROID);
        CommonResponse<MobileUserExperienceScoreDto> response = new CommonResponse<>();
        response.setData(scoreDto);
        return response;
    }


    @ApiOperation(value = "应用详情-PV&UV柱状趋势图", notes = "返回应用的PV或UV趋势数据")
    @PostMapping("/pv-uv-trend")
    public CommonResponse<TreeMap<String, Double>> getPvUvTrend(
            HttpServletRequest httpServletRequest,
            @Valid @RequestBody PvUvTrendRequest request) {
        request.setApiKey(httpServletRequest.getAttribute("apiKey").toString());
        TreeMap<String, Double> trendData = rumApplicationService.getPvUvTrend(request);
        CommonResponse<TreeMap<String, Double>> response = new CommonResponse<>();
        response.setData(trendData);
        return response;
    }

    @ApiOperation(value = "应用详情-指标概览", notes = "返回应用的多项指标概览数据")
    @PostMapping("/metrics-overview")
    public CommonResponse<List<MetricByGroupGraph>> getMetricsOverview(
            HttpServletRequest httpServletRequest,
            @Valid @RequestBody MetricsOverviewRequest request) {
        request.setApiKey(httpServletRequest.getAttribute("apiKey").toString());
        List<MetricByGroupGraph> metricsData = rumApplicationService.getMetricsOverview(request);
        CommonResponse<List<MetricByGroupGraph>> response = new CommonResponse<>();
        response.setData(metricsData);
        return response;
    }

    @ApiOperation(value = "应用详情-iOS指标概览", notes = "返回iOS应用的多项指标概览数据，包括启动时间、页面加载时间、操作时间、请求响应时间、卡顿率、崩溃率、启动次数、设备数等趋势")
    @PostMapping("/ios-metrics-overview")
    public CommonResponse<List<MetricByGroupGraph>> getIosMetricsOverview(
            HttpServletRequest httpServletRequest,
            @Valid @RequestBody MobileMetricsOverviewRequest request) {
        request.setApiKey(httpServletRequest.getAttribute("apiKey").toString());
        List<MetricByGroupGraph> metricsData = rumApplicationService.getMobileMetricsOverview(request, MobileType.IOS);
        CommonResponse<List<MetricByGroupGraph>> response = new CommonResponse<>();
        response.setData(metricsData);
        return response;
    }

    @ApiOperation(value = "应用详情-android指标概览", notes = "返回android应用的多项指标概览数据，包括启动时间、页面加载时间、操作时间、请求响应时间、卡顿率、崩溃率、启动次数、设备数等趋势")
    @PostMapping("/android-metrics-overview")
    public CommonResponse<List<MetricByGroupGraph>> getAndroidMetricsOverview(
            HttpServletRequest httpServletRequest,
            @Valid @RequestBody MobileMetricsOverviewRequest request) {
        request.setApiKey(httpServletRequest.getAttribute("apiKey").toString());
        List<MetricByGroupGraph> metricsData = rumApplicationService.getMobileMetricsOverview(request, MobileType.ANDROID);
        CommonResponse<List<MetricByGroupGraph>> response = new CommonResponse<>();
        response.setData(metricsData);
        return response;
    }

    @ApiOperation(value = "应用详情-指标平均值", notes = "返回指定时间区间内的各项指标平均值")
    @PostMapping("/metrics-average")
    public CommonResponse<MetricsAverageDto> getMetricsAverage(
            HttpServletRequest httpServletRequest,
            @Valid @RequestBody MetricsAverageRequest request) {
        request.setApiKey(httpServletRequest.getAttribute("apiKey").toString());
        MetricsAverageDto metricsData = rumApplicationService.getApplicationMetricsAverage(request);
        CommonResponse<MetricsAverageDto> response = new CommonResponse<>();
        response.setData(metricsData);
        return response;
    }

    @ApiOperation(value = "应用详情-iOS指标平均值", notes = "返回指定时间区间内的iOS各项指标平均值")
    @PostMapping("/ios-metrics-average")
    public CommonResponse<MobileMetricsAverageDto> getIosMetricsAverage(
            HttpServletRequest httpServletRequest,
            @Valid @RequestBody MetricsAverageRequest request) {
        request.setApiKey(httpServletRequest.getAttribute("apiKey").toString());
        MobileMetricsAverageDto metricsData = rumApplicationService.getMobileMetricsAverage(request, MobileType.IOS);
        CommonResponse<MobileMetricsAverageDto> response = new CommonResponse<>();
        response.setData(metricsData);
        return response;
    }

    @ApiOperation(value = "应用详情-android指标平均值", notes = "返回指定时间区间内的android各项指标平均值")
    @PostMapping("/android-metrics-average")
    public CommonResponse<MobileMetricsAverageDto> getAndroidMetricsAverage(
            HttpServletRequest httpServletRequest,
            @Valid @RequestBody MetricsAverageRequest request) {
        request.setApiKey(httpServletRequest.getAttribute("apiKey").toString());
        MobileMetricsAverageDto metricsData = rumApplicationService.getMobileMetricsAverage(request, MobileType.ANDROID);
        CommonResponse<MobileMetricsAverageDto> response = new CommonResponse<>();
        response.setData(metricsData);
        return response;
    }

    @ApiOperation(value = "应用概览-更新安全设置", notes = "根据应用ID更新安全设置")
    @PutMapping("/{id}/security-settings")
    public CommonResponse<String> updateSecuritySettings(
            @ApiParam(value = "应用ID", required = true) @PathVariable int id,
            @Valid @RequestBody Object securitySettings) {
        rumApplicationService.updateSecuritySettings(id, securitySettings);
        CommonResponse<String> response = new CommonResponse<>();
        response.setData("Security settings updated successfully");
        return response;
    }

    @ApiOperation(value = "应用概览-更新阈值设置", notes = "根据应用ID更新阈值设置")
    @PutMapping("/{id}/threshold-settings")
    public CommonResponse<String> updateThresholdSettings(
            @ApiParam(value = "应用ID", required = true) @PathVariable int id,
            @Valid @RequestBody ThresholdSettings thresholdSettings) {
        rumApplicationService.updateThresholdSettings(id, thresholdSettings);
        CommonResponse<String> response = new CommonResponse<>();
        response.setData("Threshold settings updated successfully");
        return response;
    }

    @ApiOperation(value = "应用概览-更新URL聚合设置", notes = "根据应用ID更新URL聚合设置")
    @PutMapping("/{id}/url-aggregation-settings")
    public CommonResponse<String> updateUrlAggregationSettings(
            @ApiParam(value = "应用ID", required = true) @PathVariable int id,
            @Valid @RequestBody UrlAggregationSettings urlAggregationSettings) {
        rumApplicationService.updateUrlAggregationSettings(id, urlAggregationSettings);
        CommonResponse<String> response = new CommonResponse<>();
        response.setData("URL aggregation settings updated successfully");
        return response;
    }

    @ApiOperation(value = "获取所有应用名称", notes = "返回所有不重复的应用名称列表")
    @GetMapping("/all-app-names")
    public CommonResponse<List<String>> getAllDistinctAppNames() {
        List<String> appNames = rumApplicationService.getAllDistinctAppNames();
        CommonResponse<List<String>> response = new CommonResponse<>();
        response.setData(appNames);
        return response;
    }

    @ApiOperation(value = "创建应用-下载SDK", notes = "根据应用类型下载对应的SDK")
    @GetMapping("/sdk/download/{appType}")
    @ResponseBody
    public void downloadSdk(
            @ApiParam(value = "应用类型(web/ios)", required = true) @PathVariable String appType,
            @ApiParam(value = "SDK版本号") @RequestParam(required = false) String version,
            HttpServletRequest request,
            HttpServletResponse response) throws Exception {
        rumApplicationService.downloadSdk(appType, version, response);
    }


}
