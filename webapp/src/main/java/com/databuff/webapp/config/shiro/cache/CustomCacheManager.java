package com.databuff.webapp.config.shiro.cache;

import com.databuff.service.JedisService;
import org.apache.shiro.cache.Cache;
import org.apache.shiro.cache.CacheException;
import org.apache.shiro.cache.CacheManager;

/**
 * 重写Shiro缓存管理器
 *
 * <AUTHOR>
 * @date 2019/3/4 17:41
 */
public class CustomCacheManager implements CacheManager {

    private JedisService jedisService;

    public CustomCacheManager(JedisService jedisService) {
        this.jedisService = jedisService;
    }

    @Override
    public <K, V> Cache<K, V> getCache(String s) throws CacheException {
        return new CustomCache<K, V>(jedisService);
    }
}
