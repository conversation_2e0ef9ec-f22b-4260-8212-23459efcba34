package com.databuff.webapp.config;

import com.databuff.webapp.audit.DatabuffObjectToJsonSerializer;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.audit4j.core.AnnotationTransformer;
import org.audit4j.core.DefaultAnnotationTransformer;
import org.audit4j.core.ObjectSerializer;
import org.audit4j.core.annotation.AuditField;
import org.audit4j.core.annotation.DatabuffAudit;
import org.audit4j.core.annotation.DeIdentify;
import org.audit4j.core.annotation.IgnoreAudit;
import org.audit4j.core.dto.AnnotationAuditEvent;
import org.audit4j.core.dto.AuditEvent;
import org.audit4j.core.dto.DatabuffAnnotationAuditEvent;
import org.audit4j.core.dto.Field;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

public class CustomAnnotationTransformer extends DefaultAnnotationTransformer implements AnnotationTransformer<AuditEvent> {

    private final static String ACTION = "action";

    // Default Fields serializer
    private ObjectSerializer serializer;


    public CustomAnnotationTransformer() {
        super(new DatabuffObjectToJsonSerializer());
        serializer = new DatabuffObjectToJsonSerializer();
    }

    /**
     * Transform to event.
     *
     * @param annotationEvent input Annotation AuditEvent
     * @return transform audit event
     */
    @Override
    public AuditEvent transformToEvent(AnnotationAuditEvent annotationEvent) {
        AuditEvent event = null;

        final Method method = annotationEvent.getMethod();
        if (annotationEvent.getClazz().isAnnotationPresent(DatabuffAudit.class)
                && !method.isAnnotationPresent(IgnoreAudit.class)) {
            DatabuffAnnotationAuditEvent databuffAnnotationAuditEvent = new DatabuffAnnotationAuditEvent(annotationEvent.getClazz(), method, annotationEvent.getArgs());
            if (annotationEvent instanceof DatabuffAnnotationAuditEvent) {
                databuffAnnotationAuditEvent = (DatabuffAnnotationAuditEvent) annotationEvent;
            }
            event = databuffAnnotationAuditEvent;

            DatabuffAudit audit = annotationEvent.getClazz().getAnnotation(DatabuffAudit.class);

            // Extract fields
            databuffAnnotationAuditEvent.setFields(getFields(method, annotationEvent.getArgs()));

            // Extract Actor
            String annotationAction = audit.action();
            if (ACTION.equals(annotationAction)) {
                databuffAnnotationAuditEvent.setAction(method.getName());
            } else {
                databuffAnnotationAuditEvent.setAction(annotationAction);
            }
            databuffAnnotationAuditEvent.setEntityType(audit.entityType());
            databuffAnnotationAuditEvent.setHasLink(audit.hasLink());

            // Extract repository
            databuffAnnotationAuditEvent.setRepository(audit.repository());

            databuffAnnotationAuditEvent.setActor(annotationEvent.getActor());
            databuffAnnotationAuditEvent.setOrigin(annotationEvent.getOrigin());
        } else if (!annotationEvent.getClazz().isAnnotationPresent(DatabuffAudit.class)
                && method.isAnnotationPresent(DatabuffAudit.class)) {
            DatabuffAnnotationAuditEvent databuffAnnotationAuditEvent = new DatabuffAnnotationAuditEvent(annotationEvent.getClazz(), method, annotationEvent.getArgs());
            if (annotationEvent instanceof DatabuffAnnotationAuditEvent) {
                databuffAnnotationAuditEvent = (DatabuffAnnotationAuditEvent) annotationEvent;
            }
            event = databuffAnnotationAuditEvent;
            DatabuffAudit audit = method.getAnnotation(DatabuffAudit.class);

            // Extract fields
            databuffAnnotationAuditEvent.setFields(getFields(method, annotationEvent.getArgs()));

            // Extract Actor
            String annotationAction = audit.action();
            if (ACTION.equals(annotationAction)) {
                databuffAnnotationAuditEvent.setAction(method.getName());
            } else {
                databuffAnnotationAuditEvent.setAction(annotationAction);
            }

            databuffAnnotationAuditEvent.setEntityType(audit.entityType());
            databuffAnnotationAuditEvent.setHasLink(audit.hasLink());

            // Extract repository
            databuffAnnotationAuditEvent.setRepository(audit.repository());

            databuffAnnotationAuditEvent.setActor(annotationEvent.getActor());
            databuffAnnotationAuditEvent.setOrigin(annotationEvent.getOrigin());
        }

        // 从 swagger 注解中提取操作
        if (event != null && StringUtils.isEmpty(event.getAction())) {
            ApiOperation apiOperation = method.getAnnotation(ApiOperation.class);
            if (apiOperation != null) {
                event.setAction(apiOperation.value());
            }
        }

        return event;
    }

    /**
     * Extract fields based on annotations.
     *
     * @param method : Class method with annotations.
     * @param params : Method parameter values.
     * @return list of fields extracted from method.
     * @since 2.4.1
     */
    private List<Field> getFields(final Method method, final Object[] params) {
        final Annotation[][] parameterAnnotations = method.getParameterAnnotations();

        final List<Field> fields = new ArrayList<Field>();

        int i = 0;
        String paramName = null;
        for (final Annotation[] annotations : parameterAnnotations) {
            final Object object = params[i++];
            boolean ignoreFlag = false;
            DeIdentify deidentify = null;
            for (final Annotation annotation : annotations) {
                if (annotation instanceof IgnoreAudit) {
                    ignoreFlag = true;
                    break;
                }
                if (annotation instanceof AuditField) {
                    final AuditField field = (AuditField) annotation;
                    paramName = field.field();
                }
                if (annotation instanceof DeIdentify) {
                    deidentify = (DeIdentify) annotation;
                }
            }

            if (ignoreFlag) {

            } else {
                if (null == paramName) {
                    paramName = "arg" + i;
                }
                serializer.serialize(fields, object, paramName, deidentify);
            }

            paramName = null;
        }
        return fields;
    }
}
