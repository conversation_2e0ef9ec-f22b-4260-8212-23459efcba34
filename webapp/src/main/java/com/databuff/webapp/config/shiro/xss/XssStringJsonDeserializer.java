package com.databuff.webapp.config.shiro.xss;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * @author:TianMing
 * @date: 2022/3/28
 * @time: 11:18
 */
@Slf4j
public class XssStringJsonDeserializer extends JsonDeserializer<String> {
    //html过滤
    private final static HTMLFilter htmlFilter = new HTMLFilter(log.isDebugEnabled());

    @Override
    public Class<String> handledType() {
        return String.class;
    }

    @Override
    public String deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        return htmlFilter.filter(jsonParser.getValueAsString());
    }
}
