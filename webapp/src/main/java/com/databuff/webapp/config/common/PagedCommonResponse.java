package com.databuff.webapp.config.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

@ApiModel
@ToString
@Data
public class PagedCommonResponse<T> extends CommonResponse<T> {
    @ApiModelProperty(value = "总记录数")
    private long total;

    public PagedCommonResponse() {
        super();
    }

    public PagedCommonResponse(Integer status, String message, T data, long total) {
        super(status, message, data);
        this.total = total;
    }
}
