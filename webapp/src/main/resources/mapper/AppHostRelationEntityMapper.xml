<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.databuff.webapp.infrastructure.mapper.AppHostRelationEntityMapper">
  <resultMap id="BaseResultMap" type="com.databuff.webapp.infrastructure.model.AppHostRelationEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 01 15:35:06 CST 2022.
    -->
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="host_name" jdbcType="VARCHAR" property="hostName" />
    <result column="app" jdbcType="VARCHAR" property="app" />
    <result column="api_key" jdbcType="VARCHAR" property="apiKey" />
  </resultMap>
  <insert id="insert" parameterType="com.databuff.webapp.infrastructure.model.AppHostRelationEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 01 15:35:06 CST 2022.
    -->
    insert into dc_app_host_relation (id, host_name, app, 
      api_key)
    values (#{id,jdbcType=INTEGER}, #{hostName,jdbcType=VARCHAR}, #{app,jdbcType=VARCHAR}, 
      #{apiKey,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.databuff.webapp.infrastructure.model.AppHostRelationEntity">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Jul 01 15:35:06 CST 2022.
    -->
    insert into dc_app_host_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="hostName != null">
        host_name,
      </if>
      <if test="app != null">
        app,
      </if>
      <if test="apiKey != null">
        api_key,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="hostName != null">
        #{hostName,jdbcType=VARCHAR},
      </if>
      <if test="app != null">
        #{app,jdbcType=VARCHAR},
      </if>
      <if test="apiKey != null">
        #{apiKey,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getDistinctAppsByHostNames" resultType="java.lang.String">
    select distinct app from dc_app_host_relation where 1=1
    <if test="apiKey != null">
      and dc_app_host_relation.api_key = #{apiKey}
    </if>
    and dc_app_host_relation.host_name in
    <foreach collection="hostNames" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
    <select id="getHostNamesByApp" resultType="java.lang.String">
      select host_name from dc_app_host_relation where dc_app_host_relation.app = #{app}
      <if test="apiKey != null">
        and dc_app_host_relation.api_key = #{apiKey}
      </if>
    </select>
  <select id="listAppByHosts" resultType="com.databuff.webapp.infrastructure.model.AppHostRelationEntity">
    select app,host_name,api_key from dc_app_host_relation where 1=1
    <if test="apiKey != null">
      and dc_app_host_relation.api_key = #{apiKey}
    </if>
    and dc_app_host_relation.host_name in
    <foreach collection="hostNames" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
  <select id="getHostNames" resultType="java.lang.String">
    select host_name from dc_app_host_relation where 1 = 1
    <if test="apiKey != null">
      and dc_app_host_relation.api_key = #{apiKey}
    </if>
  </select>
  <select id="getHostNamesLikeHostName" resultType="java.lang.String">
    select host_name from dc_app_host_relation where 1 = 1
    <if test="apiKey != null">
      and dc_app_host_relation.api_key = #{apiKey}
    </if>
    <if test="hostName != null">
      and host_name like concat('%',#{hostName},'%')
    </if>
  </select>
</mapper>