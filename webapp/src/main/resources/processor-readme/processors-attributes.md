# Attributes Processor 产品说明文档

## 一、功能说明

Attributes Processor 是 用于对跟踪（Traces）、指标（Metrics）、日志（Logs）中的属性（Attributes）进行灵活管理。核心功能包括：

 - **属性操作**：支持插入、更新、删除、哈希、提取和类型转换等多种操作，满足数据清洗、敏感信息处理、元数据丰富等场景需求。

 - **数据过滤**：通过包含（Include）/ 排除（Exclude）规则，按服务名、属性键值、资源信息等条件筛选目标数据，实现精准处理。

## 二、处理的数据类型

**跟踪数据（Traces）**

    处理 Span 的属性，如服务名、端点、调用链 ID 等。

**指标数据（Metrics）**

    处理指标的数据点属性（如标签），支持计数器、仪表盘等类型。

**日志数据（Logs）**

    处理日志记录的属性，如日志级别、时间戳、自定义字段。

## 三、主要配置说明

### （一）核心操作配置

支持七种操作类型，需按顺序配置`actions`列表，每个操作包含以下关键参数：

| 操作类型      | 必选参数                                                     | 说明                           |
| --------- | -------------------------------------------------------- | ---------------------------- |
| `insert`  | `key`, `action`, `value`/`from_attribute`/`from_context` | 插入新属性（键不存在时生效）               |
| `update`  | `key`, `action`, `value`/`from_attribute`/`from_context` | 更新现有属性（键存在时生效）               |
| `upsert`  | `key`, `action`, `value`/`from_attribute`/`from_context` | 插入或更新属性（自动判断键是否存在）           |
| `delete`  | `key`/`pattern`, `action`                                | 删除属性（支持正则匹配键名）               |
| `hash`    | `key`/`pattern`, `action`                                | 对属性值进行 SHA1 哈希（用于敏感信息脱敏）     |
| `extract` | `key`, `pattern`, `action`                               | 使用正则表达式从属性值中提取子值，写入新键（原键值不变） |
| `convert` | `key`, `action`, `converted_type`（int/double/string）     | 转换属性类型（如将字符串转为整数）            |

### （二）过滤规则配置

通过`include`和`exclude`字段定义数据筛选条件，支持严格匹配（`strict`）或正则匹配（`regexp`）：

| 信号类型        | 可选匹配条件                                                             |
| ----------- | ------------------------------------------------------------------ |
| **Traces**  | `services`（服务名）、`span_names`、`span_kinds`、`attributes`、`resources` |
| **Logs**    | `log_bodies`、`log_severity_texts`、`attributes`、`resources`         |
| **Metrics** | `metric_names`、`resources`                                         |

#### 示例：仅处理服务名为`web-service`的跟踪数据

```
attributes: 
 include: 
   match_type: strict 
   services: ["web-service"] 
```

### （三）高级配置

**元数据匹配**：通过`from_context`从接收器元数据（如 gRPC Headers、HTTP Metadata）中提取值。

**正则缓存**：启用`regexp.cacheenabled`优化正则匹配性能，配置`cachemaxnumentries`限制缓存大小。

## 四、配置样例

### （一）综合操作示例

```
 attributes/example:
   # 过滤规则：仅处理指标名为`http.requests`的数据
   include:
     match_type: strict
     metric_names: ["http.requests"]
   # 操作列表（按顺序执行）
   actions:
     # 删除`db.table`属性
     - key: db.table
       action: delete
     # 插入或更新`redacted_span`属性为true
     - key: redacted_span
       value: true
       action: upsert
     # 将`key_original`属性值复制到`copy_key`
     - key: copy_key
       from_attribute: key_original
       action: update
     # 对`account_email`属性值进行SHA1哈希
     - key: account_email
       action: hash
     # 将`http.status_code`属性转换为整数类型
     - key: http.status_code
       action: convert
       converted_type: int
```

### （二）敏感信息处理示例

```
 attributes/security:
   actions:
     # 删除密码属性
     - key: account_password
       action: delete
     # 哈希用户邮箱
     - key: user.email
       action: hash
       pattern: "^user\\\\.email\$"  # 正则匹配键名（可选） 
```

## 五、处理内容说明

### （一）操作执行逻辑

**数据筛选**：根据`include`和`exclude`规则过滤数据，仅对符合条件的信号执行操作。

**顺序执行**：按`actions`列表顺序执行每个操作，前一个操作可能影响后续操作（如删除属性后，后续更新操作将失效）。

**类型安全**：属性值类型由配置推断（如`value: 123`为整数，`value: "string"`为字符串），转换操作需显式指定目标类型。

### （二）风险提示

**指标属性修改**：修改指标数据点的属性可能导致 “Identity Conflict”（标识冲突），因处理器不重新聚合数据点。建议仅新增属性，避免修改现有键值。

**性能影响**：大量正则匹配或复杂操作可能增加处理延迟，建议对高频数据简化规则。

### （三）与 Metric Transform Processor 对比


| 功能         | Attributes Processor     | Metric Transform Processor |
| ---------- | ------------------------ | -------------------------- |
| **属性操作**   | 支持插入 / 更新 / 删除 / 哈希 / 提取 | 支持属性增删改（基础功能）              |
| **指标结构修改** | 不支持                      | 支持重命名指标、删除数据点、类型转换         |
| **适用场景**   | 轻量级属性清洗、元数据丰富            | 复杂指标转换（如聚合、缩放）             |

通过这种方式，Attributes Processor 可以灵活地对遥测数据的属性进行定制化处理，提高数据的质量和可用性。