package com.databuff.util;

import dto.DConf;
import io.swagger.annotations.ApiOperation;

import java.util.List;
import java.util.Map;

/**
 * 分布式配置管理器接口
 */
public interface IDConfigManager<T> {
    /**
     * 保存节点
     *
     * @param dConf     节点数据
     * @param overWrite
     * @throws Exception 如果保存过程中发生错误
     */
    void saveNode(String path, T dConf, Boolean overWrite) throws Exception;

    void saveNodeByKey(String key, T dConf, Boolean overWrite) throws Exception;

    /**
     * 创建节点
     * @param path 节点路径
     * @param data 节点数据
     * @throws Exception 如果创建过程中发生错误
     */
    void createNode(String path, T data) throws Exception;

    /**
     * 删除节点
     * @param path 节点路径
     * @throws Exception 如果删除过程中发生错误
     */
    void deleteNode(String path) throws Exception;

    /**
     * 更新节点
     * @param path 节点路径
     * @param data 新的节点数据
     * @throws Exception 如果更新过程中发生错误
     */
    void updateNode(String path, T data) throws Exception;

    /**
     * 获取节点数据
     * @param path 节点路径
     * @return 节点数据
     * @throws Exception 如果获取过程中发生错误
     */
    T getData(String path) throws Exception;


    /**
     * 获取节点数据(递归)
     * @param path 节点路径
     * @return 节点数据
     * @throws Exception 如果获取过程中发生错误
     */
    List<T> getConfs(String path) throws Exception;


    @ApiOperation(value = "获取节点数据(递归)", notes = "获取指定路径的节点数据(递归)")
    Map<String, DConf> getConfMap(String path) throws Exception;

    /**
     * 获取子节点路径
     * @param path 父节点路径
     * @return 所有直接子节点的路径
     * @throws Exception 如果获取过程中发生错误
     */
    List<String> getChildrenPaths(String path) throws Exception;

    /**
     * 递归获取子节点路径
     * @param path 父节点路径
     * @return 所有子节点的路径
     * @throws Exception 如果获取过程中发生错误
     */
    List<String> getChildrenPathsRecursively(String path) throws Exception;

    /**
     * 递归获取所有路径
     * @param path 节点路径
     * @param allPaths 用于存储所有路径的列表
     * @return 所有子节点的路径
     * @throws Exception 如果获取过程中发生错误
     */
    List<String> getAllPathsRecursively(String path, List<String> allPaths) throws Exception;
}