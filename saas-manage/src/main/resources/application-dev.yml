server:
  port: 18112

spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  datasource:
    name: Databuff
    url: ******************************************************************************************************************************************************
    username: ENC(FcapGk77xxe9X57xlYPgcw==)
    password: ENC(SojPGmS12MqqSsfs8WQw3QWg2KRMoj0p)
    # 使用Druid数据源
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    druid:
      filters: stat
      maxActive: 20
      initialSize: 1
      maxWait: 60000
      minIdle: 1
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: select 'x'
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxOpenPreparedStatements: 20
      stat-view-servlet:
        # 是否启用StatViewServlet(监控页面),默认true-启动，false-不启动
        enabled: false
        url-pattern: '/druid/*'
        # 禁用HTML页面上的"Reset All"功能
        reset-enable: false
    # redis
  redis:
    host: **************
    port: 16379
    timeout: 10000
    password:
    jedis:
      pool:
        max-idle: 8
        min-idle: 0
        max-wait: -1
        max-active: 200
  resources:
    add-mappings: false
  mvc:
    throw-exception-if-no-handler-found: true

  # 使用aop操作日志
  aop:
    auto: true
    proxy-target-class: true

mybatis:
  # Mybatis配置Mapper路径
  mapper-locations: classpath:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
  # Mybatis配置Model类对应
  type-aliases-package: com.databuff.saas.model(.*)

pagehelper:
  params: count=countSql
  # 指定分页插件使用哪种方言
  helper-dialect: mysql
  # 分页合理化参数 pageNum<=0时会查询第一页 pageNum>pages(超过总数时) 会查询最后一页
  reasonable: 'true'
  support-methods-arguments: 'false'

mapper:
  # 通用Mapper的insertSelective和updateByPrimaryKeySelective中是否判断字符串类型!=''
  not-empty: true

#日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果设置为WARN，则低于WARN的信息都不会输出
logging:
  config: classpath:logback-spring.xml
  # Debug打印SQL
  level.com.databuff.dao.mysql: debug

swagger:
  enable: true
jasypt:
  encryptor:
    password: 123456   #开发环境密码

management:
  # Customizing the management endpoint paths
  context-path: /manage

source:
  url: http://elasticsearch:9200

access:
  url: https://www.databuff.com/login/
  phone: 400-0571-198

# AccessToken过期时间-5分钟-5*60(秒为单位)
accessTokenExpireTime: 30240000
# RefreshToken过期时间-30分钟-30*60(秒为单位)
refreshTokenExpireTime: 3600
# dczw缓存过期时间-5分钟-5*60(秒为单位)(一般设置与AccessToken过期时间一致)
shiroCacheExpireTime: 3600

# 云通信短信API
sms:
  accessKeyId:
  accessKeySecret:
  regionId:
  signName:
  templateCode:
  enable: false
# 邮件发件人API
mail:
  addr:
  user: DataBuff
  password:
  enable: false
  smtp:
    host: smtp.dacheng-tech.com
    port: 25
    ssl: false

# databuff申请试用地址及databuff登陆地址，创建Admin账户后展示跳转
databuff:
  apply: http://saas.databuff.com:19091
  client: https://saas.databuff.com