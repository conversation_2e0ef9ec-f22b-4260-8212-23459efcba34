package com.databuff.saas.controller;

import com.databuff.saas.config.common.CommonResponse;
import com.databuff.saas.model.saas.SaasApply;
import com.databuff.saas.service.ApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @package com.databuff.saas.controller
 * @company: dacheng
 * @author: zlh
 * @createDate: 2021/10/27
 */
@Api("saas申请")
@RestController
@RequestMapping("/saasApply")
public class ApplyController {
    @Autowired
    private ApplyService applyService;
    
    @ApiOperation("申请试用")
    @PostMapping("/trialApply")
    public CommonResponse trialApply(@RequestBody SaasApply saasApply) throws Exception {
        applyService.trialApply(saasApply);
        return new CommonResponse();
    }

    @ApiOperation("获取邮件验证码")
    @PostMapping("/getEmailCode")
    public CommonResponse getEmailCode(@RequestBody SaasApply saasApply) throws Exception {
        applyService.getEmailCode(saasApply);
        return new CommonResponse();
    }

    @ApiOperation("获取短信验证码")
    @PostMapping("/getPhoneCode")
    public CommonResponse getPhoneCode(@RequestBody SaasApply saasApply) throws Exception {
        applyService.getPhoneCode(saasApply);
        return new CommonResponse();
    }
    @ApiOperation("校验短信/邮件验证码")
    @PostMapping("/checkVerifyCode")
    public CommonResponse checkVerifyCode(@RequestBody SaasApply saasApply) throws Exception {
        applyService.checkVerifyCode(saasApply);
        return new CommonResponse();
    }
}
