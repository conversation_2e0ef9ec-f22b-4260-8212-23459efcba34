package com.databuff.saas.util.sms;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.QuerySendDetailsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.QuerySendDetailsResponse;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedList;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: zlh
 * @Date: 2019/4/28 上午10:42
 */
@Component
@Slf4j
public class NoticeSmsUtil {
    /** 产品名称:云通信短信API产品,开发者无需替换 */
    final static String product = "Dysmsapi";
    /** 产品域名,开发者无需替换 */
    final static String domain = "dysmsapi.aliyuncs.com";

    // 此处需要替换成开发者自己的AK(在阿里云访问控制台寻找)
    @Value("${sms.accessKeyId}")
    private String accessKeyId;
    @Value("${sms.accessKeySecret}")
    private String accessKeySecret;
    @Value("${sms.regionId}")
    private String regionId;
    @Value("${sms.signName}")
    private String signName;
    @Value("${sms.templateCode}")
    private String templateCode;

    /**
     * 匹配${...}
     */
    public static final String MATCH_REGEX = "(?<=\\$\\{).*?(?=\\})+";
    public static final String REPLACE_REGEX = "(?:(?:\\$)(?<dest>\\{[^\\}]*?\\}))";
    public static final Pattern MATCH_PATTERN = Pattern.compile(MATCH_REGEX);
    /**
     * 占位符标志
     */
    public static final String PLACEHOLDER = "%s";

    public void sendSms(String content,String phone) throws ClientException {
        try {
            //可自助调整超时时间
            System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
            System.setProperty("sun.net.client.defaultReadTimeout", "10000");

            //初始化acsClient,暂不支持region化
            IClientProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
            DefaultProfile.addEndpoint(regionId, regionId, product, domain);
            IAcsClient acsClient = new DefaultAcsClient(profile);

            //组装请求对象-具体描述见控制台-文档部分内容
            SendSmsRequest request = new SendSmsRequest();
            //必填:待发送手机号
            request.setPhoneNumbers(phone);
            //必填:短信签名-可在短信控制台中找到
            request.setSignName(signName);
            //必填:短信模板-可在短信控制台中找到
            request.setTemplateCode(templateCode);
            //短信内容
            request.setTemplateParam(content);
            //选填-上行短信扩展码(无特殊需求用户请忽略此字段)
            //request.setSmsUpExtendCode("90997");
            //可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
            request.setOutId("yourOutId");
            //hint 此处可能会抛出异常，注意catch
            acsClient.getAcsResponse(request);
        }catch (ClientException e){
            log.error("----------短信发送失败,phone: "+phone);
        }
    }

    /**
     * 使用AK&SK初始化账号Client
     * @param accessKeyId
     * @param accessKeySecret
     * @return Client
     * @throws Exception
     */
    public static Client createClient(String accessKeyId, String accessKeySecret) throws Exception {
        Config config = new Config()
                // 您的AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 您的AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // 访问的域名
        config.endpoint = "dysmsapi.aliyuncs.com";
        return new Client(config);
    }

    /**
     *
     * @param signName
     * @param templateCode
     * @param content
     * @param phones 号码逗号分隔
     * @param accessKeyId
     * @param accessKeySecret
     * @return
     * @throws Exception
     */
    public static SendSmsResponse sendSms(String signName, String templateCode, String content, String phones, String accessKeyId, String accessKeySecret) throws Exception {


        Client acsClient = createClient(accessKeyId, accessKeySecret);

        com.aliyun.dysmsapi20170525.models.SendSmsRequest sendSmsRequest = new com.aliyun.dysmsapi20170525.models.SendSmsRequest();

        //必填:待发送手机号
        sendSmsRequest.setPhoneNumbers(phones);
        //必填:短信签名-可在短信控制台中找到
        sendSmsRequest.setSignName(signName);
        //必填:短信模板-可在短信控制台中找到
        sendSmsRequest.setTemplateCode(templateCode);
        //短信内容
        sendSmsRequest.setTemplateParam(content);
        //选填-上行短信扩展码(无特殊需求用户请忽略此字段)
        //request.setSmsUpExtendCode("90997");
        //可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
        sendSmsRequest.setOutId("yourOutId");

        // 复制代码运行请自行打印 API 的返回值
        com.aliyun.dysmsapi20170525.models.SendSmsResponse sendSmsResponse = acsClient.sendSms(sendSmsRequest);
        return sendSmsResponse;
    }

    public QuerySendDetailsResponse querySendDetails(String bizId) throws ClientException {

        //可自助调整超时时间
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");

        //初始化acsClient,暂不支持region化
        IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", accessKeyId, accessKeySecret);
        DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", product, domain);
        IAcsClient acsClient = new DefaultAcsClient(profile);

        //组装请求对象
        QuerySendDetailsRequest request = new QuerySendDetailsRequest();
        //必填-号码
        request.setPhoneNumber("15000000000");
        //可选-流水号
        request.setBizId(bizId);
        //必填-发送日期 支持30天内记录查询，格式yyyyMMdd
        SimpleDateFormat ft = new SimpleDateFormat("yyyyMMdd");
        request.setSendDate(ft.format(new Date()));
        //必填-页大小
        request.setPageSize(10L);
        //必填-当前页码从1开始计数
        request.setCurrentPage(1L);

        //hint 此处可能会抛出异常，注意catch
        QuerySendDetailsResponse querySendDetailsResponse = acsClient.getAcsResponse(request);

        return querySendDetailsResponse;
    }


    /**
     * 提取短信模板中的字段
     * @param templateContent
     * @return
     */
    public static LinkedList<String> parseGrammar(String templateContent){
        if (StringUtils.isBlank(templateContent)){
            return null;
        }
        LinkedList<String> fields = new LinkedList<>();
        //匹配描述的语法规则
        Matcher matcher = MATCH_PATTERN.matcher(templateContent);
        //获取匹配到的字段组，其结构为#{...}或${...}
        while (matcher.find()){
            String field = matcher.group();
            if (StringUtils.isBlank(field)){
                return null;
            }
            fields.addLast(field);
        }
        return  fields ;
    }
}
