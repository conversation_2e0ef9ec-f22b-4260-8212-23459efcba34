package com.databuff.saas.model.saas;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("外发告警短信配置表")
public class NotifySmsConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Integer id;

    /**
     * api_key
     */
    @ApiModelProperty("api_key")
    private String apiKey;

    /**
     * 是否开启，默认1开启，0不开启
     */
    @ApiModelProperty("是否开启，默认1开启，0不开启")
    private Integer enable;

    /**
     * dingtalk，wechat，sms，mail
     */
    @ApiModelProperty("dingtalk，wechat，sms，mail")
    private String notifyType;

    /**
     * 短信访问秘钥id
     */
    @ApiModelProperty("短信访问秘钥id")
    private String smsKeyId;

    /**
     * 短信访问秘钥
     */
    @ApiModelProperty("短信访问秘钥")
    private String smsKeySecret;

    /**
     * 短信模板
     */
    @ApiModelProperty("短信模板")
    List<NotifySmsTemplate> templates ;
}
