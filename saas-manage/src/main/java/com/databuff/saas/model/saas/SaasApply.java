package com.databuff.saas.model.saas;

import com.databuff.saas.model.BaseQuery;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * @description  
 * <AUTHOR>
 * @date 2021-10-26 
 */
@Setter
@Getter
@ToString
@Entity
@Table ( name ="dc_saas_apply" )
public class SaasApply extends BaseQuery {

   	@Column(name = "id" )
	private Long id;

   	private String idView;

	public void setId(Long id) {
		this.id = id;
		String tmp = "S000000000000";
		String idStr = String.valueOf(id);
		String tmpSub = tmp.substring(0,tmp.length() - idStr.length());
		idView = tmpSub + idStr;
	}

	/**
	 * 租户id
	 */
	@Column(name = "tenant_id" )
	private Long tenantId;
	/**
	 * 邮件地址
	 */
   	@Column(name = "email_addr" )
	private String emailAddr;
	private String emailCode;

	/**
	 * 申请密码
	 */
   	@Column(name = "apply_pw" )
	private String applyPw;

	/**
	 * 申请人
	 */
   	@Column(name = "apply_name" )
	private String applyName;

	/**
	 * 电话
	 */
   	@Column(name = "apply_phone" )
	private String applyPhone;
	private String phoneCode;

	/**
	 * 公司名称
	 */
   	@Column(name = "company_name" )
	private String companyName;

	/**
	 * 规模
	 */
   	@Column(name = "company_scale" )
	private String companyScale;

	/**
	 * 统一社会信用代码
	 */
	@Column(name = "company_code" )
	private String companyCode;

	/**
	 * 营业执照
	 */
	@Column(name = "business_lic" )
	private String businessLic;

	/**
	 * 使用类型 试用1、正式2
	 */
   	@Column(name = "use_type" )
	private Integer useType = 1;

	/**
	 * 审核类型试用申请1、企业认证2、服务升级3
	 */
   	@Column(name = "verify_type" )
	private Integer verifyType = 1;

	/**
	 * 审核状态 授权待审核1 通过2 不通过3 信息待审核4 信息审核不通过5(通过为1)
	 */
   	@Column(name = "verify_status" )
	private Integer verifyStatus = 4;

	private Integer oldVerifyStatus = 4;

	/**
	 * 审核原因
	 */
   	@Column(name = "verify_reason" )
	private String verifyReason;

	/**
	 * 使用开始日期
	 */
   	@Column(name = "lic_startTime" )
	private Date licStartTime;

	/**
	 * 使用结束日期
	 */
   	@Column(name = "lic_endTime" )
	private Date licEndTime;

	/**
	 * 使用时长 day
	 */
	@Column(name = "use_time" )
	private Long useTime;

   	@Column(name = "create_time" )
	private Date createTime;

   	@Column(name = "update_time" )
	private Date updateTime;

	/**
	 * 授权信息，自定义功能及其他权限 license_json
	 */
	private String licenseJson;
}
