package com.databuff.saas.model.saas;

import java.io.Serializable;
import java.util.Date;

/**
 * @description 对应表dc_saas_other_confine 
 * <AUTHOR>
 * @date 2022-02-22 
 */
public class SaasOtherConfine implements Serializable {
	private static final long serialVersionUID =  8724873775110326899L;

	/**
	 * 平台授权apiKey为platform ；对应表字段 api_key
	 */
	private String apiKey;

	/**
	 * 租户id tenant_id
	 */
	private Long tenantId;
	/**
	 * 主机监测；对应表字段 confine_host
	 */
	private Long confineHost;

	/**
	 * 基础设施监测；对应表字段 confine_plugins
	 */
	private Long confinePlugins;

	/**
	 * 应用性能监测；对应表字段 confine_app
	 */
	private Long confineApp;

	/**
	 * 容器节点；对应表字段 confine_container
	 */
	private Long confineContainer;

	/**
	 * 0预扣限制 1授权限制；对应表字段 confine_status
	 */
	private Integer confineStatus;

	/**
	 * 限制结束时间同lic结束时间；对应表字段 confine_end_time
	 */
	private Date confineEndTime;

	/**
	 * 对应表字段 create_time
	 */
	private Date createTime;

	public SaasOtherConfine(String apiKey,Long tenantId,Long confineHost,Long confinePlugins,Long confineApp,
							Long confineContainer,Integer confineStatus,Date confineEndTime){
		this.apiKey = apiKey;
		this.tenantId = tenantId;
		this.confineHost = confineHost;
		this.confinePlugins = confinePlugins;
		this.confineApp = confineApp;
		this.confineContainer = confineContainer;
		this.confineStatus = confineStatus;
		this.confineEndTime = confineEndTime;
	}
	public SaasOtherConfine(){}

	public String getApiKey() {
		return this.apiKey;
	}

	public void setApiKey(String apiKey) {
		this.apiKey = apiKey;
	}

	public Long getTenantId() {
		return tenantId;
	}

	public void setTenantId(Long tenantId) {
		this.tenantId = tenantId;
	}

	public Long getConfineHost() {
		return this.confineHost;
	}

	public void setConfineHost(Long confineHost) {
		this.confineHost = confineHost;
	}

	public Long getConfinePlugins() {
		return this.confinePlugins;
	}

	public void setConfinePlugins(Long confinePlugins) {
		this.confinePlugins = confinePlugins;
	}

	public Long getConfineApp() {
		return this.confineApp;
	}

	public void setConfineApp(Long confineApp) {
		this.confineApp = confineApp;
	}

	public Long getConfineContainer() {
		return this.confineContainer;
	}

	public void setConfineContainer(Long confineContainer) {
		this.confineContainer = confineContainer;
	}

	public Integer getConfineStatus() {
		return this.confineStatus;
	}

	public void setConfineStatus(Integer confineStatus) {
		this.confineStatus = confineStatus;
	}

	public Date getConfineEndTime() {
		return this.confineEndTime;
	}

	public void setConfineEndTime(Date confineEndTime) {
		this.confineEndTime = confineEndTime;
	}

	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

}
