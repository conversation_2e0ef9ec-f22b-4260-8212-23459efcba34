package com.databuff.saas.model.saas;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.util.Date;

/**
 * @description  
 * <AUTHOR>
 * @date 2021-11-01 
 */
@Setter
@Getter
@ToString
@Entity
@Table ( name ="dc_user" )
public class User {

   	@Column(name = "id" )
	private Long id;

	/**
	 * 账户
	 */
   	@Column(name = "account" )
	private String account;

	/**
	 * 密码
	 */
   	@Column(name = "password" )
	private String password;

   	@Column(name = "c_id" )
	private String cId;

	/**
	 * 是否是租户 0F 1T
	 */
   	@Column(name = "member_tenant" )
	private Integer memberTenant;

	/**
	 * 允许通过邮箱直接添加我为企业成员0F1T
	 */
   	@Column(name = "allow_invitation" )
	private Integer allowInvitation;

	/**
	 * 邮件地址
	 */
   	@Column(name = "email_addr" )
	private String emailAddr;

	/**
	 * 手机号
	 */
   	@Column(name = "member_phone" )
	private String memberPhone;

	/**
	 * 昵称
	 */
   	@Column(name = "nick_name" )
	private String nickName;

	/**
	 * 电话
	 */
   	@Column(name = "mobile" )
	private String mobile;

	/**
	 * 公司名称
	 */
   	@Column(name = "company" )
	private String company;

	/**
	 * 责任人
	 */
   	@Column(name = "responsible" )
	private String responsible;

	/**
	 * 备注
	 */
   	@Column(name = "remark" )
	private String remark;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_time" )
	private Date createTime;

	/**
	 * 更新时间
	 */
   	@Column(name = "update_time" )
	private Date updateTime;

	/**
	 * 锁定终止时间
	 */
   	@Column(name = "lockexpire_time" )
	private Date lockexpireTime;

	/**
	 * 累计登录错误次数
	 */
   	@Column(name = "passwd_errors" )
	private Long passwdErrors;

	/**
	 * 密码连续错误策略
	 */
   	@Column(name = "passwd_tactics" )
	private Long passwdTactics;

	/**
	 * 锁定时长
	 */
   	@Column(name = "locked_duration" )
	private Long lockedDuration;

}
