package com.databuff.saas.config.kaptcha;

import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.google.code.kaptcha.util.Config;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

@Configuration
public class KaptchaConfig {

    @Bean(name="captchaProducer")
    public DefaultKaptcha getKaptchaBean(){
        DefaultKaptcha captchaProducer=new DefaultKaptcha();
        Properties properties=new Properties();
        //边框
        properties.setProperty("kaptcha.border", "no");
        //边框颜色
        properties.setProperty("kaptcha.border.color", "105,179,90");
        //验证码字体颜色
        properties.setProperty("kaptcha.textproducer.font.color", "255,86,94");
        //验证码噪点颜色
        properties.setProperty("kaptcha.noise.color", "255,86,94");
        //验证码样式引擎
        properties.setProperty("kaptcha.obscurificator.impl", "com.databuff.saas.config.kaptcha.DisKaptchaCssImpl");
        properties.setProperty("kaptcha.background.impl", "com.databuff.saas.config.kaptcha.NoKaptchaBackground");
        //验证码图片宽度
        properties.setProperty("kaptcha.image.width", "125");
        //文字距离
        properties.setProperty("kaptcha.textproducer.char.space","6");
        //验证码图片高度
        properties.setProperty("kaptcha.image.height", "45");
        properties.setProperty("kaptcha.session.key", "code");
        properties.setProperty("kaptcha.textproducer.char.length", "4");
        properties.setProperty("kaptcha.textproducer.font.names", "微软雅黑");
        properties.setProperty("kaptcha.textproducer.font.size ", "10");
        //设置渐变
        Config config=new Config(properties);
        captchaProducer.setConfig(config);
        return captchaProducer;
    }
}