package com.databuff.saas.config.shiro.xss;

import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @author:TianMing
 * @date: 2022/3/28
 * @time: 10:24
 */
@Component
public class XssFilter implements Filter {

    @Override
    public void init(FilterConfig config) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain)
            throws IOException, ServletException {
        XssHttpServletRequestWrapper xssRequest = new XssHttpServletRequestWrapper(
                (HttpServletRequest) req);

        HttpServletResponse response = (HttpServletResponse) res;
        //防止在IE9、chrome和safari中的MIME类型混淆攻击
        response.setHeader("x-content-type-options", "nosniff");
        //配置哪些网站可以通过frame来加载资源。它主要是用来防止UI redressing 补偿样式攻击
        /**
         * DENY – 禁止所有的资源（本地或远程）试图通过frame来加载其他也支持X-Frame-Options 的资源。
         * SAMEORIGIN – 只允许遵守同源策略的资源（和站点同源）通过frame加载那些受保护的资源。
         * ALLOW-FROM http://www.example.com – 允许指定的资源（必须带上协议http或者https）通过frame来加载受保护的资源。这个配置只在IE和firefox下面有效。其他浏览器则默认允许任何源的资源（在X-Frame-Options没设置的情况下）。
         */
        response.setHeader("x-frame-options", "SAMEORIGIN");
        //让请求，不被缓存，
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        //配置浏览器和服务器之间安全的通信。它主要是用来防止中间人攻击，因为它强制所有的通信都走TLS。目前IE还不支持 STS头。需要注意的是，在普通的http请求中配置STS是没有作用的，因为攻击者很容易就能更改这些值。为了防止这样的现象发生，很多浏览器内置了一 个配置了STS的站点list。
        /**
         * max-age=31536000 – 告诉浏览器将域名缓存到STS list里面，时间是一年。
         * max-age=31536000; includeSubDomains – 告诉浏览器将域名缓存到STS list里面并且包含所有的子域名，时间是一年。
         * max-age=0 – 告诉浏览器移除在STS缓存里的域名，或者不保存此域名。
         */
        response.setHeader("strict-transport-security", "max-age=15724800; includeSubDomains");
        //防止浏览器中的反射性xss
        response.setHeader("x-xss-protection", "1; mode=block");

        chain.doFilter(xssRequest, response);
    }
    @Override
    public void destroy() {
    }

}
