package com.databuff.saas.service;

import com.databuff.saas.model.saas.SaasApply;

/**
 * @package com.databuff.saas.service
 * @company: dacheng
 * @author: zlh
 * @createDate: 2021/10/27
 */
public interface ApplyService {
    /**
     * 申请使用
     * @param saasApply
     * @throws Exception
     */
    void trialApply(SaasApply saasApply) throws Exception;

    /**
     * 获取邮件验证码
     * @param saasApply
     * @throws Exception
     */
    void getEmailCode(SaasApply saasApply) throws Exception;

    /**
     * 获取短信验证码
     * @param saasApply
     * @throws Exception
     */
    void getPhoneCode(SaasApply saasApply) throws Exception;

    /**
     * 校验短信/邮件验证码
     * @param saasApply
     * @throws Exception
     */
    void checkVerifyCode(SaasApply saasApply) throws Exception;
}
