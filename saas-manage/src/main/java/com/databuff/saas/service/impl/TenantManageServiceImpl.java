package com.databuff.saas.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.databuff.saas.config.common.Constant;
import com.databuff.saas.config.exception.BusinessException;
import com.databuff.saas.mapper.TenantManageMapper;
import com.databuff.saas.model.saas.*;
import com.databuff.saas.service.TenantManageService;
import com.databuff.saas.util.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.databuff.common.utils.TimeUtil.ONE_DAY_MS;

/**
 * @package com.databuff.saas.service.impl
 * @company: dacheng
 * @author: zlh
 * @createDate: 2021/10/26
 */
@Service
@Slf4j
public class TenantManageServiceImpl implements TenantManageService {
    @Autowired
    private TenantManageMapper tenantManageMapper;
    @Autowired
    CommonUtil commonUtil;
    @Autowired
    private MailUtil mailUtil;

    @Value("${source.url}")
    private String sourceUrl;

    @Value("${access.url}")
    private String accessUrl;

    @Value("${access.phone}")
    private String accessPhone;

    @Override
    public Object listTenant(SaasTenant saasTenant) {
        if (saasTenant.getLicEndTime() != null){
            saasTenant.setLicEndTime(new Date());
        }
        PageHelper.startPage(saasTenant.getPageNum(),saasTenant.getPageSize());
        List<SaasTenant> tenantList = tenantManageMapper.listTenant(saasTenant);
        return new PageInfo<>(tenantList);
    }

    @Override
    public Object listApply(SaasApply apply) {
        PageHelper.startPage(apply.getPageNum(),apply.getPageSize());
        List<SaasApply> applyList = tenantManageMapper.findAllSaasApply(apply);
        return new PageInfo<>(applyList);
    }

    @Override
    public Object addTenant(SaasTenant saasTenant,String account) throws Exception {
        String emailAddr = saasTenant.getEmailAddr();
        // 邮箱地址不允许重复
        User user = tenantManageMapper.getMemberByEmailAddr(emailAddr);
        SaasApply apply = tenantManageMapper.getApplyByEmailAddr(emailAddr,1);
        if (user != null || apply != null){
            throw new Exception("此邮箱已申请，请联系商务购买服务");
        }

        Date date = new Date();
        saasTenant.setCreateTime(date);
        saasTenant.setUpdateTime(date);
        saasTenant.setApplyTime(date);
        long thisTime = System.currentTimeMillis();
        // 对密码脱敏加密
        saasTenant.setTenantPw(AesCipherUtil.enCrypto(saasTenant.getTenantPw()));
        // 生成api key
        String source = saasTenant.getEmailAddr() + thisTime;
        saasTenant.setApiKey(commonUtil.uuidV3(source));
        saasTenant.setApplyId(0L);
        JSONObject licJson = new JSONObject();
        licJson.put("funcScope", Lists.newArrayList("all"));
        saasTenant.setLicenseJson(licJson.toJSONString());
        saasTenant.setUseTime(15L);
        Long useTime = saasTenant.getUseTime() * ONE_DAY_MS;
        long startTime = System.currentTimeMillis();
        long endTime = startTime + useTime;
        saasTenant.setLicStartTime(new Date(startTime));
        saasTenant.setLicEndTime(new Date(endTime));
        saasTenant.setTenantContact(account);

        tenantManageMapper.addTenant(saasTenant);
        User saasMember = this.getSaasMember(date, saasTenant);
        tenantManageMapper.addMember(saasMember);
        // 初始化看板相关
        this.initDashboardAbort(saasTenant,date);
        tenantManageMapper.addVerifyLog(saasTenant.getApplyId(),saasTenant.getId(),"发起申请","添加租户",account,0);
        return saasTenant;
    }

    @Override
    public void updateTenant(SaasTenant saasTenant) {
        Date date = new Date();
        saasTenant.setUpdateTime(date);
        tenantManageMapper.updateTenant(saasTenant);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTenantLic(SaasTenant tenant,String account) throws Exception {
        Date date = new Date();
        SaasTenant saasTenant = tenantManageMapper.getTenantByid(tenant.getId());
        JSONObject licenseJson = JSON.parseObject(tenant.getLicenseJson());
        JSONObject otherPermission = licenseJson.getJSONObject("otherPermission");
        if (this.isThanConfine(otherPermission)) {
            // 超过限制
            throw new Exception("租户实例限制超过了平台授权实例总限制");
        }
        // 根据授权信息获取菜单id
        List<Long> resourceIds = getResourceIds(licenseJson);

        tenantManageMapper.delTenantResourceByApiKey(saasTenant.getApiKey());
        tenantManageMapper.addTenantResource(saasTenant.getApiKey(),resourceIds);
        // 保存租户授权信息
        Long useTime = tenant.getUseTime() * ONE_DAY_MS;
        long startTime = System.currentTimeMillis();
        long endTime = startTime + useTime;
        saasTenant.setLicStartTime(new Date(startTime));
        saasTenant.setLicEndTime(new Date(endTime));
        saasTenant.setUseType(tenant.getUseType());
        saasTenant.setUseTime(tenant.getUseTime());
        saasTenant.setUpdateTime(date);
        saasTenant.setLicenseJson(tenant.getLicenseJson());
        tenantManageMapper.updateTenantLic(saasTenant);

        // 其他限制
        SaasOtherConfine otherConfine = new SaasOtherConfine(saasTenant.getApiKey(),tenant.getId(),otherPermission.getLongValue("host"),otherPermission.getLongValue("plugins")
                ,otherPermission.getLongValue("app"),otherPermission.getLongValue("container"),1,saasTenant.getLicEndTime());
        tenantManageMapper.addSaasOtherConfine(otherConfine);

        JSONObject confineJson = new JSONObject(4);
        confineJson.put("host",otherConfine.getConfineHost());
        confineJson.put("plugins",otherConfine.getConfinePlugins());
        confineJson.put("app",otherConfine.getConfineApp());
        confineJson.put("container",otherConfine.getConfineContainer());
        //发送到Redis,key过期时间6min
        JedisUtil.setObject(Constant.SAAS_API_KEY_CATCH+saasTenant.getApiKey(), confineJson.toJSONString(), 360);
    }

    private User getSaasMember(Date date, SaasTenant saasTenant) {
        User user = new User();
        user.setAccount(saasTenant.getEmailAddr());
        user.setEmailAddr(saasTenant.getEmailAddr());
        user.setPassword(saasTenant.getTenantPw());
        user.setMemberPhone(saasTenant.getTenantPhone());
        user.setNickName(saasTenant.getTenantContact());
        user.setMemberTenant(1);
        user.setCId(saasTenant.getApiKey());
        user.setCreateTime(date);
        user.setUpdateTime(date);
        Date lockexpireTime = DateUtils.strToDate("yyyy-MM-dd HH:mm:ss", "1970-01-01 08:00:00");
        //默认设置5次输入错误锁定3分钟
        user.setPasswdTactics(5L);
        user.setLockedDuration(3L);
        user.setLockexpireTime(lockexpireTime);
        return user;
    }

    private DataSource getDataSource(String apiKey,long orgId,Date date) {
        String md5ApiKey = AesCipherUtil.getMD5Str(apiKey);
        DataSource dataSource = new DataSource();
        dataSource.setOrgId(orgId);
        dataSource.setUrl(sourceUrl);
        dataSource.setDatabase("dc_source_metrics_"+md5ApiKey+"_*");
        dataSource.setCreated(date);
        dataSource.setUpdated(date);
        dataSource.setUid(apiKey);
        dataSource.setJsonData("{\"esVersion\":\"7.0.0\",\"logLevelField\":\"\",\"logMessageField\":\"\",\"maxConcurrentShardRequests\":5,\"timeField\":\"timestamp\"}");
        return dataSource;
    }

    /**
     * 根据授权信息获取菜单id
     * @param licenseJson
     * @return
     */
    private List<Long> getResourceIds(JSONObject licenseJson) {
        List<Long> pathList = new ArrayList<>(16);
        JSONArray funcScope = licenseJson.getJSONArray("funcScope");
        if (funcScope != null){
            String all = funcScope.getString(0);
            if ("all".equals(all)){
                // 所有范围
                funcScope = null;
            }
            // 获取菜单
            List<SaasResource> resourceList = tenantManageMapper.findResourceByFunc(funcScope);
            for (SaasResource resource:resourceList){
                pathList.add(resource.getId());
            }
        }
        // 自定义选择菜单
        JSONArray custom = licenseJson.getJSONArray("custom");
        if (custom != null) {
            for (int i = 0; i < custom.size(); i++) {
                JSONObject modelJson = custom.getJSONObject(i);
                long id = modelJson.getLongValue("id");
                if (!pathList.contains(id)) {
                    pathList.add(id);
                }
            }
        }
        // 返回resource ids
        return pathList;
    }

    /**
     * 解析授权信息
     * @param licFile
     * @return
     * @throws Exception
     */
    private JSONObject parseAuthKey(MultipartFile licFile) throws Exception {
        if (licFile == null){
            throw new BusinessException("未上传授权文件");
        }
        // 文件名字
        BufferedReader bufferedReader = null;
        try {
            //校验通过则记录授权信息
            String filePath = licFile.getOriginalFilename();
            String suffix = ".lic";
            if (!filePath.endsWith(suffix)) {
                throw new BusinessException("授权文件格式错误");
            }
            //获取加密内容校验
            bufferedReader = new BufferedReader(new InputStreamReader(licFile.getInputStream()));
            String encryptStr = bufferedReader.readLine();
            String rsaPrivateDecrypt = DecryptAuthKey.decryAuthDataWithRSAPublicKey(encryptStr);
            String aesDecryptData = DecryptAuthKey.decryptAuthKeyWithAES(rsaPrivateDecrypt);

            JSONObject licenseJson = JSONObject.parseObject(aesDecryptData);
            if (licenseJson == null) {
                throw new Exception("授权失败，请重新申请授权！");
            }
            return licenseJson;
        } catch (Exception e) {
            throw new Exception(e);
        } finally {
            // 关闭输入流
            try {
                if (bufferedReader != null) {
                    bufferedReader.close();
                }
            } catch (IOException e) {
                log.error("-----关闭输入流error");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void verifyApply(SaasApply saasApply,String account) throws Exception {
        Date date = new Date();
        saasApply.setUpdateTime(date);
        Integer verifyStatus = saasApply.getVerifyStatus();
        if (verifyStatus == 4){
            // 更新授权直达授权待审核
            verifyStatus = 1;
            // 不允许重复申请
            SaasApply applyTmp = tenantManageMapper.getApplyByTenantId(saasApply.getTenantId(),verifyStatus);
            if (applyTmp != null){
                throw new Exception("请勿重复申请！该租户已申请授权，请耐心等待平台审核。");
            }
            // 申请待审核信息
            JSONObject licenseJson = JSON.parseObject(saasApply.getLicenseJson());
            JSONObject otherPermission = licenseJson.getJSONObject("otherPermission");
            if (this.isThanConfine(otherPermission)) {
                // 超过限制
                throw new Exception("租户实例限制超过了平台授权实例总限制");
            }
            saasApply.setVerifyStatus(1);
            saasApply.setCreateTime(date);
            saasApply.setUpdateTime(date);
            saasApply.setApplyPw("");
            saasApply.setApplyName(account);
            tenantManageMapper.addSaasApply(saasApply);
            // 实例限制
            SaasOtherConfine otherConfine = new SaasOtherConfine(saasApply.getTenantId().toString(),saasApply.getTenantId(),otherPermission.getLongValue("host"),otherPermission.getLongValue("plugins")
                    ,otherPermission.getLongValue("app"),otherPermission.getLongValue("container"),0,new Date(1889061071000L));
            tenantManageMapper.addSaasOtherConfine(otherConfine);
            // 修改成功记录审核日志
            tenantManageMapper.addVerifyLog(saasApply.getId(),saasApply.getTenantId(),"发起申请","服务升级 "+saasApply.getVerifyReason(),account,0);
            return;
        }
        SaasApply apply = tenantManageMapper.getApplyById(saasApply);
        Integer verifyType = apply.getVerifyType();
        // 信息待审核通过
        if (verifyStatus == 1){
            JSONObject licenseJson = JSON.parseObject(saasApply.getLicenseJson());
            JSONObject otherPermission = licenseJson.getJSONObject("otherPermission");
            if (this.isThanConfine(otherPermission)) {
                // 超过限制
                throw new Exception("租户实例限制超过了平台授权实例总限制");
            }
            // 审核信息通过
            boolean b = tenantManageMapper.updateApplyVerifyInfo(saasApply);
            if (b){
                // 实例限制
                SaasOtherConfine otherConfine = new SaasOtherConfine(apply.getId().toString(),apply.getId(),otherPermission.getLongValue("host"),otherPermission.getLongValue("plugins")
                        ,otherPermission.getLongValue("app"),otherPermission.getLongValue("container"),0,new Date(1889061071000L));
                tenantManageMapper.addSaasOtherConfine(otherConfine);
                // 修改成功记录审核日志
                tenantManageMapper.addVerifyLog(saasApply.getId(),apply.getTenantId(),"信息审核",saasApply.getVerifyReason(),account,2);
            }
            return;
        }
        if (verifyStatus == 3){
            saasApply.setOldVerifyStatus(1);
            // 不通过，更新原因
            boolean b = tenantManageMapper.updateApplyVerifyStatus(saasApply);
            if (b) {
                // 修改成功记录审核日志
                tenantManageMapper.addVerifyLog(saasApply.getId(),apply.getTenantId(),"授权审核",saasApply.getVerifyReason(),account,1);
                if (verifyType == 1) {
                    // 审核不通过，删除实例限制
                    tenantManageMapper.delOtherConfineByApiKey(saasApply.getId().toString());
                    // 发送不通过邮件
                    String title = "【乘云DataBuff】很抱歉地通知您，您的试用申请未能通过审核。";
                    String content = "尊敬的用户:<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您好！<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;很抱歉的通知您，由于【" + saasApply.getVerifyReason() + "】的原因，您提交的试用申请未能通过审核。请完善并提交正确的信息申请试用。<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;电询：" + accessPhone + "<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DataBuff平台访问地址：" + accessUrl;
                    mailUtil.send(apply.getEmailAddr(), title, content);
                }
                if (verifyType == 2) {
                    // 发送不通过邮件
                    String title = "【乘云DataBuff】很抱歉地通知您，您的企业认证申请未能通过审核，请完善并提交正确的企业信息。";
                    String content = "尊敬的用户:<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您好！<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;很抱歉的通知您，由于【" + saasApply.getVerifyReason() + "】的原因，您提交的企业认证申请未能通过审核，请完善并提交正确的企业信息进行企业认证。<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;电询：" + accessPhone + "<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DataBuff平台访问地址：" + accessUrl;
                    mailUtil.send(apply.getEmailAddr(), title, content);
                }
            }
            return;
        }
        if (verifyStatus == 5){
            saasApply.setOldVerifyStatus(4);
            // 不通过，更新原因
            boolean b = tenantManageMapper.updateApplyVerifyStatus(saasApply);
            if (b) {
                // 修改成功记录审核日志
                tenantManageMapper.addVerifyLog(saasApply.getId(),apply.getTenantId(),"信息审核", saasApply.getVerifyReason(), account,1);
                if (verifyType == 1) {
                    // 发送不通过邮件
                    String title = "【乘云DataBuff】很抱歉地通知您，您的试用申请信息未能通过审核。";
                    String content = "尊敬的用户:<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您好！<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;很抱歉的通知您，由于【" + saasApply.getVerifyReason() + "】的原因，您提交的试用申请未能通过审核。请完善并提交正确的信息申请试用。<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;电询：" + accessPhone + "<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DataBuff平台访问地址：" + accessUrl;
                    mailUtil.send(apply.getEmailAddr(), title, content);
                }
                if (verifyType == 2) {
                    // 发送不通过邮件
                    String title = "【乘云DataBuff】很抱歉地通知您，您的企业认证申请信息未能通过审核，请完善并提交正确的企业信息。";
                    String content = "尊敬的用户:<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您好！<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;很抱歉的通知您，由于【" + saasApply.getVerifyReason() + "】的原因，您提交的企业认证申请未能通过审核，请完善并提交正确的企业信息进行企业认证。<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;电询：" + accessPhone + "<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DataBuff平台访问地址：" + accessUrl;
                    mailUtil.send(apply.getEmailAddr(), title, content);
                }
            }
            return;
        }
        // 授权审核
        if (verifyStatus == 2) {
            saasApply.setOldVerifyStatus(1);
            // 更新审核状态
            boolean b = tenantManageMapper.updateApplyVerifyStatus(saasApply);
            if (b) {
                switch (verifyType) {
                    case 1: {
                        // 申请试用
                        JSONObject licenseJson = JSON.parseObject(saasApply.getLicenseJson());
                        // 根据授权信息获取菜单id
                        List<Long> resourceIds = getResourceIds(licenseJson);
                        if (resourceIds.isEmpty()) {
                            throw new Exception("授权功能为空，授权失败");
                        }

                        // 授权根据时长重新计算起止时间
                        long useTime = apply.getUseTime() * ONE_DAY_MS;
                        long startTime = System.currentTimeMillis();
                        long endTime = startTime + useTime;
                        // 增加租户
                        SaasTenant tenant = getSaasTenant(date, startTime, endTime, apply);
                        tenantManageMapper.addTenant(tenant);

                        // 初始化看板相关
                        this.initDashboardAbort(tenant, date);
                        // 增加成员
                        User user = getSaasMember(date, tenant);
                        tenantManageMapper.addMember(user);
                        // 授权
                        tenantManageMapper.delTenantResourceByApiKey(tenant.getApiKey());
                        tenantManageMapper.addTenantResource(tenant.getApiKey(), resourceIds);

                        // 实例限制
                        tenantManageMapper.updateOtherConfine(saasApply.getId().toString(),tenant.getApiKey(),tenant.getId(),1,tenant.getLicEndTime());
                        // 实例限制缓存
                        JSONObject otherPermission = licenseJson.getJSONObject("otherPermission");
                        JSONObject confineJson = new JSONObject(4);
                        confineJson.put("host",otherPermission.getLongValue("host"));
                        confineJson.put("plugins",otherPermission.getLongValue("plugins"));
                        confineJson.put("app",otherPermission.getLongValue("app"));
                        confineJson.put("container",otherPermission.getLongValue("container"));
                        //发送到Redis,key过期时间6min
                        JedisUtil.setObject(Constant.SAAS_API_KEY_CATCH + tenant.getApiKey(), confineJson.toJSONString(), 360);

                        // 发送邮件
                        String title = "【乘云DataBuff】恭喜！您的试用申请已审核通过！";
                        String content = "尊敬的用户:<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您好！恭喜！<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您提交的试用申请已于" + DateUtils.dateToString("yyyy年MM月dd日 HH:mm:ss", date) + "审核通过。<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;让您久等了，感谢您的信赖和支持~<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DataBuff平台访问地址：" + accessUrl;
                        mailUtil.send(apply.getEmailAddr(), title, content);
                        // 修改成功记录审核日志
                        tenantManageMapper.addVerifyLog(saasApply.getId(),tenant.getId(),"授权审核", saasApply.getVerifyReason(), account,2);
                        break;
                    }
                    case 2: {
                        // 企业认证，修改租户企业信息
                        SaasTenant tenant = new SaasTenant();
                        tenant.setEmailAddr(apply.getEmailAddr());
                        tenant.setCompanyCode(apply.getCompanyCode());
                        tenant.setBusinessLic(apply.getBusinessLic());
                        tenant.setUpdateTime(date);
                        tenant.setCerStatus(1);
                        tenant.setCerTime(date);
                        tenantManageMapper.updateTenantCerByEmailAddr(tenant);
                        // 发送邮件
                        String title = "【乘云DataBuff】恭喜！您的企业认证申请已审核通过！";
                        String content = "尊敬的用户:<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您好！恭喜！<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您提交的企业认证申请已于" + DateUtils.dateToString("yyyy年MM月dd日 HH:mm:ss", date) + "审核通过。<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;让您久等了，感谢您一直以来的信赖和支持~<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DataBuff平台访问地址：" + accessUrl;
                        mailUtil.send(apply.getEmailAddr(), title, content);
                        // 修改成功记录审核日志
                        tenantManageMapper.addVerifyLog(saasApply.getId(),apply.getTenantId(),"授权审核", saasApply.getVerifyReason(), account,2);
                        break;
                    }
                    case 3: {
                        SaasTenant tenant = tenantManageMapper.getTenantByid(apply.getTenantId());
                        // 服务升级
                        JSONObject licenseJson = JSON.parseObject(saasApply.getLicenseJson());
                        // 根据授权信息获取菜单id
                        List<Long> resourceIds = getResourceIds(licenseJson);
                        if (resourceIds.isEmpty()) {
                            throw new Exception("授权功能为空，授权失败");
                        }
                        // 授权根据时长重新计算起止时间
                        long useTime = apply.getUseTime() * ONE_DAY_MS;
                        long startTime = System.currentTimeMillis();
                        long endTime = startTime + useTime;
                        tenant.setLicenseJson(saasApply.getLicenseJson());
                        tenant.setUseType(apply.getUseType());
                        tenant.setUseTime(saasApply.getUseTime());
                        tenant.setLicStartTime(new Date(startTime));
                        tenant.setLicEndTime(new Date(endTime));
                        // 授权
                        tenantManageMapper.delTenantResourceByApiKey(tenant.getApiKey());
                        tenantManageMapper.addTenantResource(tenant.getApiKey(), resourceIds);
                        // 获取审核后的实例限制信息
                        SaasOtherConfine confine = tenantManageMapper.getOtherConfineByApiKey(apply.getTenantId().toString());
                        // 实例限制
                        confine.setApiKey(tenant.getApiKey());
                        tenantManageMapper.addSaasOtherConfine(confine);
                        tenantManageMapper.delOtherConfineByApiKey(apply.getTenantId().toString());
                        // 修改租户权限
                        tenant.setUpdateTime(date);
                        tenantManageMapper.updateTenantLic(tenant);
                        // 实例限制缓存
                        JSONObject otherPermission = licenseJson.getJSONObject("otherPermission");
                        JSONObject confineJson = new JSONObject(4);
                        confineJson.put("host",otherPermission.getLongValue("host"));
                        confineJson.put("plugins",otherPermission.getLongValue("plugins"));
                        confineJson.put("app",otherPermission.getLongValue("app"));
                        confineJson.put("container",otherPermission.getLongValue("container"));
                        //发送到Redis,key过期时间6min
                        JedisUtil.setObject(Constant.SAAS_API_KEY_CATCH + tenant.getApiKey(), confineJson.toJSONString(), 360);
                        // 发送邮件
                        String title = "【乘云DataBuff】恭喜！您的服务升级已审核通过！";
                        String content = "尊敬的用户:<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您好！恭喜！<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您提交的服务升级已于" + DateUtils.dateToString("yyyy年MM月dd日 HH:mm:ss", date) + "审核通过。<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;让您久等了，感谢您的信赖和支持~<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DataBuff平台访问地址：" + accessUrl;
                        mailUtil.send(apply.getEmailAddr(), title, content);
                        // 修改成功记录审核日志
                        tenantManageMapper.addVerifyLog(saasApply.getId(),tenant.getId(),"授权审核", saasApply.getVerifyReason(), account,2);
                        break;
                    }
                    default: {
                        break;
                    }
                }
            }
        }
    }

    /**
     * 判断是否超过总限制
     * @param otherPermission
     * @return
     */
    private boolean isThanConfine(JSONObject otherPermission) {
        // 判断实例限制是否超过总限制
        SaasOtherConfine totalConfine = this.getTotalConfine();
        if (totalConfine.getConfineHost() != -1 && totalConfine.getConfineHost() < otherPermission.getLongValue("host")){
            return true;
        }
        if (totalConfine.getConfinePlugins() != -1 && totalConfine.getConfinePlugins() < otherPermission.getLongValue("plugins")){
            return true;
        }
        if (totalConfine.getConfineApp() != -1 && totalConfine.getConfineApp() < otherPermission.getLongValue("app")){
            return true;
        }
        return totalConfine.getConfineContainer() != -1 && totalConfine.getConfineContainer() < otherPermission.getLongValue("container");
    }

    /**
     * 初始化看板相关
     * @param tenant
     * @param date
     */
    private void initDashboardAbort(SaasTenant tenant, Date date) {
        // 增加org id记录
        SaasOrg org = new SaasOrg();
        org.setName(tenant.getApiKey());
        tenantManageMapper.addOrg(org);
        // 增加数据源
        DataSource dataSource = this.getDataSource(tenant.getApiKey(), org.getId(),date);
        tenantManageMapper.addDataSource(dataSource);
        // 初始化首页、所有主机、所有集成目录
        String fristFolder = "{\"schemaVersion\":17,\"title\":\"首页\",\"uid\":\"djhCr_Znz\",\"version\":1}";
        SaasDashboard firstDashboard = this.getSaasDashboard(date, fristFolder, org.getId(), "首页", 0, 1, "djhCr_Znz");
        tenantManageMapper.addDashboard(firstDashboard);
        String jcFolder = "{\"schemaVersion\":17,\"title\":\"所有集成\",\"uid\":\"QG1yGXZ7k\",\"version\":1}";
        SaasDashboard jcDashboard = this.getSaasDashboard(date, jcFolder, org.getId(), "所有集成", 0, 1, "QG1yGXZ7k");
        tenantManageMapper.addDashboard(jcDashboard);
        String hostFolder = "{\"schemaVersion\":17,\"title\":\"所有主机\",\"uid\":\"nx2CulZ7k\",\"version\":1}";
        SaasDashboard hostDashboard = this.getSaasDashboard(date, hostFolder, org.getId(), "所有主机", 0, 1, "nx2CulZ7k");
        tenantManageMapper.addDashboard(hostDashboard);
    }

    private void addDataSourceForDashboard(JSONObject frontPageStencilJson,String source) {
        JSONArray panels = frontPageStencilJson.getJSONArray("panels");
        for (int i=0; i < panels.size();i++){
            JSONObject panelsJson = panels.getJSONObject(i);
            panelsJson.put("datasource",source);
        }
    }

    private SaasTenant getSaasTenant(Date date, long startTime, long endTime, SaasApply apply) {
        SaasTenant tenant = new SaasTenant();
        tenant.setTenantName(apply.getEmailAddr());
        tenant.setEmailAddr(apply.getEmailAddr());
        tenant.setTenantPw(apply.getApplyPw());
        tenant.setTenantPhone(apply.getApplyPhone());
        tenant.setTenantContact(apply.getApplyName());
        tenant.setUseType(apply.getUseType());
        tenant.setUseTime(apply.getUseTime());
        tenant.setTenantType(1);
        tenant.setCompanyName(apply.getCompanyName());
        tenant.setCompanyScale(apply.getCompanyScale());
        tenant.setLicStartTime(new Date(startTime));
        tenant.setLicEndTime(new Date(endTime));
        tenant.setCreateTime(date);
        tenant.setUpdateTime(date);
        tenant.setApplyTime(date);
        tenant.setApplyId(apply.getId());
        tenant.setLicenseJson(apply.getLicenseJson());
        // 生成api key
        String source = tenant.getEmailAddr() + date.getTime();
        tenant.setApiKey(commonUtil.uuidV3(source));
        return tenant;
    }

    private SaasDashboard getSaasDashboard(Date date, String tencilJson,long orgId,
                                           String title,long folderId,int isFolder,String uid) {
        SaasDashboard dashboard = new SaasDashboard();
        dashboard.setSlug(title);
        dashboard.setData(tencilJson);
        dashboard.setOrgId(orgId);
        dashboard.setTitle(title);
        dashboard.setCreated(date);
        dashboard.setUpdated(date);
        dashboard.setFolderId(folderId);
        dashboard.setIsFolder(isFolder);
        dashboard.setUid(uid);
        return dashboard;
    }

    @Override
    public void reTenantPw(SaasTenant tenant) {
        // 生成密码
        long l = System.currentTimeMillis();
        String uuid = commonUtil.uuidV3(String.valueOf(l));
        String pw = uuid.substring(0,8);
        // md5加密
        String pwMd5 = AesCipherUtil.getMD5Str(pw);
        // 加密脱敏
        String crypto = AesCipherUtil.enCrypto(pwMd5);
        // 修改租户密码
        tenantManageMapper.updateTenantPw(tenant.getEmailAddr(),crypto);
        // 修改成员密码
        tenantManageMapper.updateMemberPw(tenant.getEmailAddr(),crypto);
        // 发送邮件
        String title = "【乘云DataBuff】您的账户密码已由平台管理员重置";
        String content = "尊敬的用户您好:<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您的新密码是："+pw;
        mailUtil.send(tenant.getEmailAddr(),title,content);
    }

    @Override
    public Object findVerifyLog(SaasVerifyLog verifyLog) {
        return tenantManageMapper.findVerifyLog(verifyLog);
    }

    @Override
    public SaasOtherConfine getTotalConfine() {
        Date date = new Date();
        SaasOtherConfine totalConfine = tenantManageMapper.getTotalConfine();
        SaasOtherConfine apiKeyConfine = tenantManageMapper.getApiKeyConfineCount(date);
        if (apiKeyConfine == null){
            return totalConfine;
        }
        long host = totalConfine.getConfineHost() - apiKeyConfine.getConfineHost();
        Long confineHost = totalConfine.getConfineHost() == -1L ? -1L : host < 0 ? 0 : host;

        long plugins = totalConfine.getConfinePlugins() - apiKeyConfine.getConfinePlugins();
        Long confinePlugins = totalConfine.getConfinePlugins() == -1L ? -1L : plugins < 0 ? 0 : plugins;

        long app = totalConfine.getConfineApp() - apiKeyConfine.getConfineApp();
        Long confineApp = totalConfine.getConfineApp() == -1L ? -1L : app < 0 ? 0 : app;

        long container = totalConfine.getConfineContainer() - apiKeyConfine.getConfineContainer();
        Long confineContainer = totalConfine.getConfineContainer() == -1L ? -1L : container < 0 ? 0 : container;
        return new SaasOtherConfine(null,null,confineHost,confinePlugins,confineApp,confineContainer,1,null);
    }
}
