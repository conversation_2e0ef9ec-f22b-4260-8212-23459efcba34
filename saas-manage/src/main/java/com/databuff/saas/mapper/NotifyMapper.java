package com.databuff.saas.mapper;


import com.alibaba.fastjson.JSONObject;
import com.databuff.saas.model.saas.NotifyConfig;
import com.databuff.saas.model.saas.NotifySmsTemplate;
import com.databuff.saas.model.saas.WebhookConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author:TianMing
 * @date: 2021/12/23
 * @time: 14:24
 */
@Mapper
@Repository
public interface NotifyMapper {

    /**
     *设置告警通知配置
     * @param notifyConfig
     * @return
     */
    int setNotifyConfig(NotifyConfig notifyConfig);

    /**
     * 修改租户告警通知配置
     * @param notifyConfig
     * @return
     */
    int updateTenantNotifyConfig(NotifyConfig notifyConfig);

    /**
     *获取告警通知配置
     * @param apiKey
     * @param notifyType
     * @return
     */
    NotifyConfig getNotifyConfig(@Param("apiKey") String apiKey, @Param("notifyType") String notifyType, @Param("id") Integer id);

    /**
     * 获取短信模板
     * @param apiKey
     * @param notifyConfigId
     * @param smsNotifyType
     * @return
     */
    List<NotifySmsTemplate> getSmsTemplates(@Param("apiKey") String apiKey, @Param("notifyConfigId") Integer notifyConfigId, @Param("smsNotifyType") String smsNotifyType);
    /**
     * 插入短信模板
     * @param smsTemplate
     * @return
     */
    int insertSmsTemplate(NotifySmsTemplate smsTemplate);
    /**
     * 删除短信模板
     * @param smsTemplate
     * @return
     */
    int delSmsTemplate(NotifySmsTemplate smsTemplate);

    int setUpgradeConfig( @Param("upgradeConfig") String upgradeConfig);

    String getUpgradeConfig();


}
