

告警列表：

![](https://cdn.nlark.com/yuque/0/2025/png/40720541/1748159894590-c2a51726-15ec-4551-bc8d-a8675a61eafc.png)



告警详情：

![](https://cdn.nlark.com/yuque/0/2025/png/40720541/1748159935243-dc3ceb5a-cd2a-4134-90ad-2420dd8f0cd5.png)



事件详情-异常指标：

![](https://cdn.nlark.com/yuque/0/2025/png/40720541/1748159977207-94f66063-21b7-4724-ba8a-f8621ceb7b5a.png)

事件详情-链路追踪：

![](https://cdn.nlark.com/yuque/0/2025/png/40720541/1748160044267-a869ad8d-6c24-447d-a685-e8d3c9622050.png)



告警详情-根因分析

![](https://cdn.nlark.com/yuque/0/2025/png/40720541/1748160216456-8a689122-0154-48cc-9da5-2eb5ccabdf9a.png)





### 检测规则配置
告警生成流程请参考《告警生成详细设计》，本文略过

**数据来源**：消费databuff平台事件/第三方事件，根据时间窗口聚合生成告警。

**核心配置**：

![](https://cdn.nlark.com/yuque/0/2025/png/40720541/1748162103164-54e1b52d-6fd5-498b-8f6b-180b90d7fa62.png)

**检测方法**：<font style="color:#080808;background-color:#ffffff;">AlarmCheckOperatorV2</font>

```java
package com.databuff.metric;

/**
 * 告警检查操作接口（V2版本）
 * <p>
 * 此接口定义了一系列方法，用于处理监控告警的检查逻辑，包括指标结果检查、
 * 无数据情况处理以及标签补充等功能。主要应用于时序数据的告警判断过程中。
 * </p>
 */
public interface AlarmCheckOperatorV2 {

    /**
     * 检查结果后执行进一步的处理。
     * <p>
     * 它接收监控对象、聚合后的时序数据、查询请求、指标聚合器以及查询集合，
     * 并返回一个包含处理结果的映射。
     * </p>
     *
     * @param monitor           包含监控信息的对象。
     * @param map               聚合后的时序数据，键为时间序列，值为对应的指标值。
     * @param detectQueryRequest 包含检测查询信息的请求对象。
     * @param metricAggregator  用于执行指标聚合的工具类。
     * @param queries           查询集合。
     * @return 包含处理结果的映射。
     */
    Map<Object, Object> afterCheckResult(DatabuffMonitor monitor, Map<Map, Map<Object, Double>> map, DetectQueryRequest detectQueryRequest, MetricAggregator metricAggregator, Collection<QueryRequest> queries);

    /**
     * 无数据的情况下执行检查逻辑。
     * <p>
     * 它接收监控对象、键、无数据结果集合以及查询请求，
     * 并返回一个包含处理结果的映射。
     * </p>
     *
     * @param monitor           包含监控信息的对象。
     * @param key               唯一标识符，用于标记无数据的情况。
     * @param map               无数据结果集合。
     * @param detectQueryRequest 包含检测查询信息的请求对象。
     * @return 包含处理结果的映射。
     */
    Map<Object, Object> afterCheckNoDataResult(DatabuffMonitor monitor, String key, Set<Map<String, String>> map, DetectQueryRequest detectQueryRequest);
}

```

<font style="color:#080808;background-color:#ffffff;"></font>

```java
package com.databuff.metric.impl.alarmV2;
@Component
@Slf4j
public class ThresholdAlarmCheck extends BaseAlarmCheckV2 implements AlarmCheckOperatorV2 {

    private final static String SEPARATOR = ":";
    private final static String REDIS_PREFIX = "monitor:metric:";
    private static final int CRITICAL_STATUS = 3;
    private static final int WARNING_STATUS = 2;
    private static final int NORMAL_STATUS = 0;

    @Override
    public Map<Object, Object> afterCheckResult(DatabuffMonitor monitor, Map<Map, Map<Object, Double>> aggTimeSeries, DetectQueryRequest detectQueryRequest, MetricAggregator metricAggregator, Collection<QueryRequest> queries) {
        Map<Object, Object> ret = new HashMap<>(16);
        if (CollectionUtils.isEmpty(aggTimeSeries)) {
            return ret;
        }
        // 从查询参数中提取完整性检查标志和时间窗口周期
        final Boolean fullWindow = detectQueryRequest.getRequireFullWindow();
        final long period = detectQueryRequest.getPeriod();

        /** 数据过滤逻辑：
         * 1. 移除空数据条目
         * 2. 当需要完整时间窗口数据时，执行数据完整性校验
         * 3. 使用removeIf简化迭代器操作 */
        aggTimeSeries.entrySet().removeIf(entry ->
                                          entry == null || entry.getKey() == null || entry.getValue() == null
                                          || CollectionUtils.isEmpty(entry.getValue())
                                          || (fullWindow && !dataIntegrityCheck(period, entry))
                                         );

        final String timeAggregator = detectQueryRequest.getTimeAggregator();
        final String comparison = detectQueryRequest.getComparison();
        final Boolean continuous = detectQueryRequest.getContinuous();
        final Integer continuous_n = detectQueryRequest.getContinuousN();
        final ThresholdsDTO thresholds = detectQueryRequest.getThresholds();

        /** 核心处理：获取符合阈值条件的聚合值
         *  参数说明：
         *  - timeAggregator: 时间维度聚合方式
         *  - comparison: 比较运算符(>,<等)
         *  - continuous: 是否要求连续触发
         *  - continuous_n: 连续触发次数阈值 */
        Map<Map, Map.Entry> aggValue = this.getAggValue(timeAggregator, aggTimeSeries, comparison, continuous, continuous_n, thresholds);
        if (CollectionUtils.isEmpty(aggValue)) {
            return ret;
        }

        // 遍历处理符合条件的指标数据
        for (Map.Entry<Map, Map.Entry> entry : aggValue.entrySet()) {
            if (entry == null) {
                continue;
            }
            // 真实指标值格式：Entry<时间戳, 指标值>
            final Map.Entry<Long, Double> realAggValue = entry.getValue();
            EventEntity eventEntity = judgeThresholdStatus(thresholds, comparison, realAggValue);
            if (eventEntity == null) {
                continue;
            }
            ret.put(entry.getKey(), eventEntity);
        }
        return ret;
    }

    @Override
    public Map<Object, Object> afterCheckNoDataResult(DatabuffMonitor monitor, String mKey, Set<Map<String, String>> map, DetectQueryRequest detectQueryRequest) {
        Map<Object, Object> ret = new HashMap<>(16);

        Integer noDataTimeframe = detectQueryRequest.getNoDataTimeframe();
        Collection<String> byArr = detectQueryRequest.findBy();
        final int mid = monitor.getId().intValue();
        String noDataPrefix = REDIS_PREFIX + "nodata" + SEPARATOR + mid + SEPARATOR + mKey + SEPARATOR;
        Set<String> noDataKeys = jedisService.keysS(noDataPrefix);
        Map<String, Integer> noDataMap = new HashMap<>(noDataKeys.size());
        for (String k : noDataKeys) {
            int lastIndexOf = k.lastIndexOf("_");
            noDataMap.put(k.substring(0, lastIndexOf), Integer.valueOf(k.substring(lastIndexOf + 1)));
        }
        jedisService.delKeyStrs(noDataKeys);
        List<String> newKeys = new ArrayList<>(16);
        int num;
        int status = 1;
        for (Object retKey : map) {
            // 类型安全校验：防御性编程避免类型转换异常
            if (!(retKey instanceof Map)) {
                log.warn("非预期的retKey类型: {}", retKey.getClass());
                continue;
            }
            /** 分组键构建逻辑：
             * 1. 根据配置的维度字段(byArr)提取标签值
             * 2. 使用StringBuilder避免字符串拼接性能损耗
             * 3. 自动处理分隔符(避免首部出现多余分隔符) */
            Map<String, String> tagMap = (Map<String, String>) retKey;
            StringBuilder groupBuilder = new StringBuilder();
            for (String groupStr : byArr) {
                if (groupBuilder.length() > 0) {
                    groupBuilder.append(SEPARATOR);
                }
                groupBuilder.append(tagMap.get(groupStr));
            }
            String group = groupBuilder.toString();

            /** 无数据状态处理：
             * 1. 检查历史累积次数(noDataMap)
             * 2. 根据累积次数与阈值关系生成不同状态事件
             *  - num >= noDataTimeframe: 生成正式无数据事件
             *  - num < evaluation_delay: 延迟状态事件
             *  - 其他情况: 记录无数据但状态正常 */
            num = 1;
            String noKey = noDataPrefix + group;
            if (noDataMap.containsKey(noKey)) {
                num += noDataMap.get(noKey);
            }
            EventEntity eventEntity = EventEntity.builder().way(EventEntity.DetectionType.threshold).build();
            eventEntity.setValue(0);
            eventEntity.setGroup(group);

            if (num >= noDataTimeframe) {
                eventEntity.setNoData(true);
                eventEntity.setStatus(status);
                ret.put(retKey, eventEntity);
                newKeys.add(noKey + "_" + num);
            } else {
                eventEntity.setStatus(NORMAL_STATUS);
                eventEntity.setNoData(true);
                ret.put(retKey, eventEntity);
                newKeys.add(noKey + "_" + num);
            }
        }
        jedisService.setBatchStr(newKeys, "0");
        return ret;
    }

    /**
     * 阈值状态判断核心方法
     *
     * @param thresholds 包含critical/warning阈值的JSON对象
     * @param comparison 比较运算符(支持>/>=/</<=/==)
     * @param timeValue  指标数据Entry<时间戳, 值>
     * @return 包含三种状态的事件实体: - 3(重要)/2(次要)/0(正常)
     **/
    public EventEntity judgeThresholdStatus(ThresholdsDTO thresholds, String comparison, Map.Entry<Long, Double> timeValue) {
        final Double critical = thresholds.getCritical() == null ? null : thresholds.getCritical().doubleValue();
        final Double warning = thresholds.getWarning() == null ? null : thresholds.getWarning().doubleValue();
        if (critical == null && warning == null) {
            log.warn("阈值配置错误：critical和warning至少需要配置一个");
            return null;
        }

        EventEntity eventEntity = EventEntity.builder().way(EventEntity.DetectionType.threshold).build();
        Double value = timeValue.getValue();
        if (value == null) {
            return null;
        }

        /** 阈值判断逻辑：
         * 1. 根据比较运算符选择条件检查策略
         * 2. 优先检查critical阈值
         * 3. 当critical阈值未触发时检查warning阈值
         * */
        int status = NORMAL_STATUS;
        String threshold = "";
        boolean conditionMet = false;

        switch (comparison) {
            case ">":
                conditionMet = checkCondition(value, critical, warning, (v, t) -> v > t);
                break;
            case ">=":
                conditionMet = checkCondition(value, critical, warning, (v, t) -> v >= t);
                break;
            case "<":
                conditionMet = checkCondition(value, critical, warning, (v, t) -> v < t);
                break;
            case "<=":
                conditionMet = checkCondition(value, critical, warning, (v, t) -> v <= t);
                break;
            default: // "=="
                conditionMet = checkCondition(value, critical, warning, (v, t) -> v.equals(t));
                break;
        }

        /** 状态分级逻辑：
         * - 当critical阈值存在且满足时，标记为重要告警(3)
         * - 否则当warning阈值存在且满足时，标记为次要告警(2)
         * - 都不满足时保持正常状态(0)
         * */
        if (conditionMet) {
            status = (critical != null && checkCriticalCondition(value, critical, comparison)) ? CRITICAL_STATUS : WARNING_STATUS;
            threshold = (status == CRITICAL_STATUS) ? critical.toString() : warning.toString();
        }

        eventEntity.setValue(value);
        eventEntity.setAbnormalTime(timeValue.getKey());
        eventEntity.setStatus(status);
        eventEntity.setThreshold(threshold);
        return eventEntity;
    }

    /**
     * 检查给定的值是否满足临界条件或警告条件。
     *
     * @param value    要检查的值，不能为null。
     * @param critical 临界值，如果为null，则不检查临界条件。
     * @param warning  警告值，如果为null，则不检查警告条件。
     * @param checker  用于检查条件的接口实现，不能为null。
     * @return 如果值满足临界条件或警告条件，则返回true；否则返回false。
     */
    private boolean checkCondition(Double value, Double critical, Double warning, ConditionChecker checker) {
        // 检查是否满足临界条件
        boolean criticalCondition = critical != null && checker.check(value, critical);
        // 检查是否满足警告条件
        boolean warningCondition = warning != null && checker.check(value, warning);
        // 返回是否满足任一条件
        return criticalCondition || warningCondition;
    }

    /**
     * 检查给定值是否满足与临界值的比较条件。
     *
     * 该函数根据比较操作符判断给定值是否满足与临界值的比较条件。
     * 支持的操作符包括：">", ">=", "<", "<=", 以及默认的等于比较。
     *
     * @param value 要比较的数值，不能为null。
     * @param critical 临界值，不能为null。
     * @param comparison 比较操作符，支持的值为：">", ">=", "<", "<=", 其他值将执行等于比较。
     * @return 如果给定值满足与临界值的比较条件，则返回true；否则返回false。
     */
    private boolean checkCriticalCondition(Double value, Double critical, String comparison) {
        // 根据比较操作符进行相应的比较操作
        switch (comparison) {
            case ">":
                return value > critical;
            case ">=":
                return value >= critical;
            case "<":
                return value < critical;
            case "<=":
                return value <= critical;
            default:
                return value.equals(critical);
        }
    }

    /**
     * 条件检查策略接口
     * 用于抽象不同比较运算符的实现细节
     */
    private interface ConditionChecker {
        boolean check(Double value, Double threshold);
    }
}
```

```java
package com.databuff.metric.impl.alarmV2;

@Component
@Slf4j
public class DynamicBaselineCheck extends BaseAlarmCheckV2 implements AlarmCheckOperatorV2 {

    private final static String SEPARATOR = ":";
    private final static String REDIS_PREFIX = "monitor:metric:";

    @Autowired
    private CalculateBaseline calculateBaseline;

    private MetricAggregator metricAggregator;

    @Override
    public Map<Object, Object> afterCheckResult(DatabuffMonitor monitor, Map<Map, Map<Object, Double>> aggTimeSeries, DetectQueryRequest detectQueryRequest, MetricAggregator metricAggregator, Collection<QueryRequest> queries) {
        this.metricAggregator = metricAggregator;
        Map<Object, Object> ret = new HashMap<>(16);
        if (CollectionUtils.isEmpty(aggTimeSeries)) {
            return ret;
        }
        //要求周期内完整数据
        final boolean fullWindow = detectQueryRequest.getRequireFullWindow();
        final long period = detectQueryRequest.getPeriod();

        // 动态基线 分数
        final double baselineScope = detectQueryRequest.getBaselineScope();
        // 比较符号
        final String comparison = detectQueryRequest.getComparison();

        // 删除不满足条件的数据
        for (Iterator<Map.Entry<Map, Map<Object, Double>>> iterator = aggTimeSeries.entrySet().iterator(); iterator.hasNext(); ) {
            Map.Entry<Map, Map<Object, Double>> entry = iterator.next();
            if (entry == null || entry.getKey() == null || entry.getValue() == null) {
                iterator.remove();
                continue;
            }
            Map<Object, Double> retMap = entry.getValue();
            if (CollectionUtils.isEmpty(retMap)) {
                iterator.remove();
                continue;
            }
            if (fullWindow && !dataIntegrityCheck(period, entry)) {
                iterator.remove();
            }
        }

        final String timeAggregator = detectQueryRequest.getTimeAggregator();

        // 必须连续/无需连续
        final Boolean continuous = detectQueryRequest.getContinuous();
        // 连续次数（分钟）
        final Integer continuous_n = detectQueryRequest.getContinuousN();
        // 阈值
        final ThresholdsDTO thresholds = detectQueryRequest.getThresholds();

        // 根据时间聚合器和比较符获取分组的聚合值
        Map<Map, EventEntity> aggValue = this.getAggValue(aggTimeSeries, comparison, thresholds, baselineScope, queries);
        if (CollectionUtils.isEmpty(aggValue)) {
            return ret;
        }
        // 检测方法
        final String way = detectQueryRequest.getWay();
        // 将检索到的“way”字符串转换为`EventEntity.DetectionType`类型的枚举值
        final EventEntity.DetectionType detectionType = EventEntity.DetectionType.valueOf(way);
        // 对于每个 `EventEntity`，调用 `setWay` 方法，并以 `detectionType` 作为参数
        aggValue.values().forEach(eventEntity -> eventEntity.setWay(detectionType));
        ret.putAll(aggValue);
        return ret;
    }

    protected Map<Map, EventEntity> getAggValue(final Map<Map, Map<Object, Double>> aggTimeSeries,
                                                final String comparison, ThresholdsDTO thresholds,
                                                double baselineScope, Collection<QueryRequest> queries) {

        Map<Map, EventEntity> result = new HashMap<>();
        for (Map.Entry<Map, Map<Object, Double>> lineData : aggTimeSeries.entrySet()) {
            if (lineData == null || lineData.getValue() == null || CollectionUtils.isEmpty(lineData.getValue().values())) {
                continue;
            }
            final Map<Object, Double> lineDataValue = lineData.getValue();
            if (!(lineDataValue instanceof TreeMap)) {
                continue;
            }

            /**
             * lineDataKey 指的是时间序列对应的标签集合（触发对象）
             * @see {"host":"host193","source":"DataBuff","host_id":"fa9880f4582d8076a2c2a76290a8ed71"}
             */
            final Map<String, String> lineDataKey = lineData.getKey();
            final String group = lineDataKey.entrySet().stream()
                    .filter(Objects::nonNull)
                    .map(i -> i.getKey() + SEPARATOR + i.getValue())
                    .reduce((a, b) -> a + SEPARATOR + b)
                    .orElse(null);
            if (group == null) {
                log.warn("找不到触发对象，不计算动态基线");
                continue;
            }
            final String keyStr = REDIS_PREFIX + "baseline" + SEPARATOR + group + SEPARATOR + comparison + SEPARATOR + baselineScope;
            final String baselineJson = jedisService.getJson(keyStr);
            if (baselineJson == null) {
                calculateBaseline.processAsync(keyStr, queries, lineDataKey, metricAggregator, comparison, baselineScope);
                continue;
            }

            JSONObject baselineJSON = JSONObject.parseObject(baselineJson);
            final double num = baselineJSON.getDoubleValue("num");
            if (num < 2016) {
                log.debug("监控[{}]计算基线点数{}，低于所需数据量(分钟粒度下，至少需要满足 7d*20%*24h*60min=2016 的数据量)不满足要求", keyStr, num);
                continue;
            }
            final double baseline = baselineJSON.getDoubleValue("baseline");

            if (thresholds == null || !(lineDataValue instanceof TreeMap)) {
                log.warn("错误的参数配置：continuous_n或thresholds或lineDataValue");
                break;
            }

            final TreeMap<Long, Double> treeMap = new TreeMap<>();
            for (Map.Entry<Object, Double> entry : lineDataValue.entrySet()) {
                if (entry.getKey() instanceof String) {
                    try {
                        Long key = Long.parseLong((String) entry.getKey());
                        treeMap.put(key, entry.getValue());
                    } catch (NumberFormatException e) {
                        // 处理转换错误
                        log.error("Key conversion error: " + entry.getKey(), e);
                    }
                }
            }

            final Double critical = thresholds.getCritical() == null ? null : thresholds.getCritical().doubleValue();
            final Double warning = thresholds.getWarning() == null ? null : thresholds.getWarning().doubleValue();
            if (critical == null && warning == null) {
                log.warn("阈值配置错误：critical和warning至少需要配置一个");
                return null;
            }
            
            EventEntity eventEntity = EventEntity.builder().way(EventEntity.DetectionType.baseline).build();
            Map.Entry<Long, Double> realTimeValue = this.getEntryWithValue(treeMap, critical, baseline, false, comparison);
            if (realTimeValue != null) {
                eventEntity.setValue(realTimeValue.getValue());
                eventEntity.setAbnormalTime(realTimeValue.getKey());
                eventEntity.setStatus(3);
                eventEntity.setThreshold(String.valueOf(baseline));
                result.put(lineDataKey, eventEntity);
            } else {
                realTimeValue = this.getEntryWithValue(treeMap, warning, baseline, false, comparison);
                if (realTimeValue != null) {
                    eventEntity.setValue(realTimeValue.getValue());
                    eventEntity.setAbnormalTime(realTimeValue.getKey());
                    eventEntity.setStatus(2);
                    eventEntity.setThreshold(String.valueOf(baseline));
                    result.put(lineDataKey, eventEntity);
                }
            }
        }
        return result;
    }

    @Override
    public Map<Object, Object> afterCheckNoDataResult(DatabuffMonitor monitor, String mKey, Set<Map<String, String>> map, DetectQueryRequest detectQueryRequest) {
        Map<Object, Object> ret = new HashMap<>(16);

        Integer noDataTimeframe = detectQueryRequest.getNoDataTimeframe();
        // 分组
        Collection<String> byArr = detectQueryRequest.findBy();
        // 查询id下所有无数据
        final int mid = monitor.getId().intValue();
        String noDataPrefix = REDIS_PREFIX + "nodata" + SEPARATOR + mid + SEPARATOR + mKey + SEPARATOR;
        Set<String> noDataKeys = jedisService.keysS(noDataPrefix);
        Map<String, Integer> noDataMap = new HashMap<>(noDataKeys.size());
        for (String k : noDataKeys) {
            int lastIndexOf = k.lastIndexOf("_");
            noDataMap.put(k.substring(0, lastIndexOf), Integer.valueOf(k.substring(lastIndexOf + 1)));
        }
        // 置过期
        jedisService.delKeyStrs(noDataKeys);
        List<String> newKeys = new ArrayList<>(16);
        int num;
        int status = 1;
        for (Object retKey : map) {
            Map<String, String> tagMap = (Map<String, String>) retKey;
            String group = "";
            for (String groupStr : byArr) {
                if ("*".equals(group)) {
                    group = tagMap.get(groupStr);
                } else {
                    group = group + SEPARATOR + tagMap.get(groupStr);
                }
            }

            num = 1;
            // 设置无数据的key
            String noKey = noDataPrefix + group;
            if (noDataMap.containsKey(noKey)) {
                num += noDataMap.get(noKey);
            }
            EventEntity eventEntity = EventEntity.builder().way(EventEntity.DetectionType.baseline).build();
            // 超过检测周期数可生成事件
            if (num >= noDataTimeframe) {
                eventEntity.setValue(0);
                eventEntity.setNoData(true);
                eventEntity.setStatus(status);
                eventEntity.setGroup(group);
                ret.put(retKey, eventEntity);
                newKeys.add(noDataPrefix + group + "_" + num);
                continue;
            }
            eventEntity.setValue(0);
            //无数据但是没有到检测周期状态正常
            eventEntity.setStatus(0);
            eventEntity.setNoData(true);
            eventEntity.setGroup(group);
            ret.put(retKey, eventEntity);
            // 没超过检测周期数或没生成事件加入设置redis key,生成事件自动取消累积，即不加入
            newKeys.add(noDataPrefix + group + "_" + num);
        }
        jedisService.setBatchStr(newKeys, "0");

        return ret;
    }

    /**
     * 单值阈值判断
     *
     * @param thresholds
     * @param comparison
     * @param timeValue
     * @return
     */
    // status=3, 重要, status=2, 次要
    public EventEntity judgeThresholdStatus(JSONObject thresholds, String comparison, Map.Entry<Long, Double> timeValue) {
        //重要  critical 必填
        String critical = thresholds.getString("critical");
        Double criticalThreshold = null;
        if (StringUtils.isNotBlank(critical)) {
            criticalThreshold = Double.valueOf(critical);
        }
        //次要  warning %
        String warning = thresholds.getString("warning");

        EventEntity eventEntity = EventEntity.builder().way(EventEntity.DetectionType.baseline).build();
        // 根据阈值判断状态
        int status = 0;
        String threshold = "";
        final Double value = timeValue.getValue();
        switch (comparison) {
            case ">":
                if (criticalThreshold != null && value > criticalThreshold) {
                    //触发 重要
                    status = 3;
                    threshold = critical;
                } else if (StringUtils.isNotBlank(warning)) {
                    double warnThreshold = Double.parseDouble(warning);
                    //触发 次要
                    if (value > warnThreshold) {
                        status = 2;
                        threshold = warning;
                    }
                }
                break;
            case ">=":
                if (criticalThreshold != null && value >= criticalThreshold) {
                    //触发 重要
                    status = 3;
                    threshold = critical;
                } else if (StringUtils.isNotBlank(warning)) {
                    double warnThreshold = Double.parseDouble(warning);
                    //触发 次要
                    if (value >= warnThreshold) {
                        status = 2;
                        threshold = warning;
                    }
                }
                break;
            case "<":
                if (criticalThreshold != null && value < criticalThreshold) {
                    //触发 重要
                    status = 3;
                    threshold = critical;
                } else if (StringUtils.isNotBlank(warning)) {
                    double warnThreshold = Double.parseDouble(warning);
                    //触发 次要
                    if (value < warnThreshold) {
                        status = 2;
                        threshold = warning;
                    }
                }
                break;
            case "<=":
                if (criticalThreshold != null && value <= criticalThreshold) {
                    //触发 重要
                    status = 3;
                    threshold = critical;
                } else if (StringUtils.isNotBlank(warning)) {
                    double warnThreshold = Double.parseDouble(warning);
                    //触发 次要
                    if (value <= warnThreshold) {
                        status = 2;
                        threshold = warning;
                    }
                }
                break;
            default:
                if (criticalThreshold != null && value == criticalThreshold) {
                    //触发 重要
                    status = 3;
                    threshold = critical;
                } else if (StringUtils.isNotBlank(warning)) {
                    double warnThreshold = Double.parseDouble(warning);
                    //触发 次要
                    if (value == warnThreshold) {
                        status = 2;
                        threshold = warning;
                    }
                }
                break;
        }
        eventEntity.setValue(value);
        eventEntity.setAbnormalTime(timeValue.getKey());
        eventEntity.setStatus(status);
        eventEntity.setThreshold(threshold);
        return eventEntity;
    }
}
```

```java
package com.databuff.metric.impl.alarmV2;

@Component
@Slf4j
public class MutationCheck extends BaseAlarmCheckV2 implements AlarmCheckOperatorV2 {

    private final static String SEPARATOR = ":";
    private final static String REDIS_PREFIX = "monitor:metric:";


    private MetricAggregator metricAggregator;

    @Override
    public Map<Object, Object> afterCheckResult(DatabuffMonitor monitor, Map<Map, Map<Object, Double>> aggTimeSeries, DetectQueryRequest multiQueryDTO, MetricAggregator metricAggregator, Collection<QueryRequest> queries) {
        if (multiQueryDTO == null) {
            return new HashMap<>(16);
        }
    
        this.metricAggregator = metricAggregator;
        Map<Object, Object> ret = new HashMap<>(16);

        if (CollectionUtils.isEmpty(aggTimeSeries)) {
            return ret;
        }

        final Boolean lessDataTimeframe = multiQueryDTO.getLessDataTimeframe();
        if (lessDataTimeframe != null && lessDataTimeframe) {
            aggTimeSeries = this.filterAggTimeSeries(aggTimeSeries);
        }
    
        if (CollectionUtils.isEmpty(aggTimeSeries)) {
            return ret;
        }

        final String expr = multiQueryDTO.getExpr();
        final Integer comparePeriod = multiQueryDTO.getComparePeriod();
        final String fluctuate = multiQueryDTO.getFluctuate();
        for (QueryRequest query : multiQueryDTO.getNonNullQueries()) {
            query.setTimeOffset(query.getTimeOffset() + comparePeriod);
        }

        final Map<Map, Map<Object, Double>> beforeAggTimeSeries = metricAggregator.aggResult(expr, multiQueryDTO.getNonNullQueriesMap());
        if (CollectionUtils.isEmpty(beforeAggTimeSeries)) {
            return ret;
        }

        //要求周期内完整数据
        final boolean fullWindow = multiQueryDTO.getRequireFullWindow();
        final long period = multiQueryDTO.getPeriod();

        // 删除不满足条件的数据
        for (Iterator<Map.Entry<Map, Map<Object, Double>>> iterator = aggTimeSeries.entrySet().iterator(); iterator.hasNext(); ) {
            Map.Entry<Map, Map<Object, Double>> entry = iterator.next();
            if (entry == null || entry.getKey() == null || entry.getValue() == null) {
                iterator.remove();
                continue;
            }
            Map<Object, Double> retMap = entry.getValue();
            if (CollectionUtils.isEmpty(retMap)) {
                iterator.remove();
                continue;
            }
            if (fullWindow && !dataIntegrityCheck(period, entry)) {
                iterator.remove();
            }
        }

        final String timeAggregator = multiQueryDTO.getTimeAggregator();

        // 阈值
        final ThresholdsDTO thresholds = multiQueryDTO.getThresholds();

        // 根据时间聚合器和比较符获取分组的聚合值
        Map<Map, EventEntity> aggValue = this.getAggValue(timeAggregator, aggTimeSeries, beforeAggTimeSeries, fluctuate, thresholds);
        if (CollectionUtils.isEmpty(aggValue)) {
            return ret;
        }
        ret.putAll(aggValue);
        return ret;
    }

    protected Map<Map, EventEntity> getAggValue(final String timeAggregator,
                                                final Map<Map, Map<Object, Double>> aggTimeSeries,
                                                final Map<Map, Map<Object, Double>> beforeAggTimeSeries,
                                                final String fluctuate, ThresholdsDTO thresholds) {
        Map<Map, EventEntity> result = new HashMap<>();
        if (thresholds == null) {
            log.warn("错误的参数配置：continuous_n或thresholds或lineDataValue");
            return result;
        }
        final Double critical = thresholds.getCritical() == null ? null : thresholds.getCritical().doubleValue();
        final Double warning = thresholds.getWarning() == null ? null : thresholds.getWarning().doubleValue();
        if (critical == null && warning == null) {
            log.warn("阈值配置错误：critical和warning至少需要配置一个");
            return null;
        }

        for (Map.Entry<Map, Map<Object, Double>> lineData : aggTimeSeries.entrySet()) {
            if (lineData == null || lineData.getValue() == null || CollectionUtils.isEmpty(lineData.getValue().values())) {
                continue;
            }
            final Map<String, String> lineDataKey = lineData.getKey();

            final Map<Object, Double> yLineDataValue = beforeAggTimeSeries.get(lineDataKey);
            if (yLineDataValue == null || CollectionUtils.isEmpty(yLineDataValue.values()) || !(yLineDataValue instanceof TreeMap)) {
                continue;
            }

            final Map<Object, Double> xLineDataValue = lineData.getValue();
            if (!(xLineDataValue instanceof TreeMap)) {
                continue;
            }

            final TreeMap<String, Double> sortedTimeValues = (TreeMap) xLineDataValue;
            // 最近一个值非null的时间节点（秒）
            final Long lastTimestamp;
            final String lastNonNullValueKey = getLastNonNullValueKey(sortedTimeValues);
            if (lastNonNullValueKey == null) {
                lastTimestamp = System.currentTimeMillis();
            } else {
                lastTimestamp = Long.valueOf(lastNonNullValueKey);
            }

            /**
             * lineDataKey 指的是时间序列对应的标签集合（触发对象）
             * @see {"host":"host193","source":"DataBuff","host_id":"fa9880f4582d8076a2c2a76290a8ed71"}
             */
            final Collection<Double> xValues = xLineDataValue.values();
            final Collection<Double> yValues = yLineDataValue.values();

            EventEntity eventEntity = EventEntity.builder().way(mutation).build();
            Double xMean;
            Double yMean;
            Double threethreAggs = 0D;
            switch (timeAggregator) {
                case "avg":
                case "mean":
                    xMean = xValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).average().getAsDouble();
                    yMean = yValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).average().getAsDouble();
                    threethreAggs = getThreethreAggs(xMean, yMean, fluctuate);
                    break;
                case "sum":
                    xMean = xValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).sum();
                    yMean = yValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).sum();
                    threethreAggs = getThreethreAggs(xMean, yMean, fluctuate);
                    break;
                case "max":
                    xMean = xValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).max().getAsDouble();
                    yMean = yValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).max().getAsDouble();
                    threethreAggs = getThreethreAggs(xMean, yMean, fluctuate);
                    break;
                case "min":
                    xMean = xValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).min().getAsDouble();
                    yMean = yValues.stream().filter(Objects::nonNull).mapToDouble(t -> t).min().getAsDouble();
                    threethreAggs = getThreethreAggs(xMean, yMean, fluctuate);
                    break;
                default:
                    log.warn("timeAggregator不在处理范围内：avg/mean/sum/max/min/last/always");
                    break;
            }

            if (critical != null && threethreAggs > critical) {
                eventEntity.setStatus(3);
                eventEntity.setThreshold(String.valueOf(critical));
            } else if (warning != null && threethreAggs > warning) {
                eventEntity.setStatus(2);
                eventEntity.setThreshold(String.valueOf(warning));
            }

            eventEntity.setValue(threethreAggs);
            eventEntity.setAbnormalTime(lastTimestamp);
            result.put(lineDataKey, eventEntity);
        }
        return result;
    }


    /**
     * 第三次统计函数计算
     *
     * @param oneVal
     * @param twoVal
     * @param threethreAggs
     * @return
     */
    private double getThreethreAggs(double oneVal, double twoVal, String threethreAggs) {
        if (threethreAggs == null) {
            return oneVal - twoVal;
        }
        switch (threethreAggs) {
            case "valUp": {
                // 数据增加量
                return oneVal - twoVal;
            }
            case "valDown": {
                // 数据减少量
                return twoVal - oneVal;
            }
            case "yoyUp": {
                // 同比增长
                return (oneVal - twoVal) / (twoVal == 0 ? 1 : twoVal);
            }
            case "yoyDown": {
                // 同比下降
                return (twoVal - oneVal) / (twoVal == 0 ? 1 : twoVal);
            }
            default:
                return oneVal - twoVal;
        }
    }

    @Override
    public Map<Object, Object> afterCheckNoDataResult(DatabuffMonitor monitor, String mKey, Set<Map<String, String>> map, DetectQueryRequest detectQueryRequest) {
        Map<Object, Object> ret = new HashMap<>(16);

        Integer noDataTimeframe = detectQueryRequest.getNoDataTimeframe();
        // 分组
        Collection<String> byArr = detectQueryRequest.findBy();
        // 查询id下所有无数据
        final int mid = monitor.getId().intValue();
        String noDataPrefix = REDIS_PREFIX + "nodata" + SEPARATOR + mid + SEPARATOR + mKey + SEPARATOR;
        Set<String> noDataKeys = jedisService.keysS(noDataPrefix);
        Map<String, Integer> noDataMap = new HashMap<>(noDataKeys.size());
        for (String k : noDataKeys) {
            int lastIndexOf = k.lastIndexOf("_");
            noDataMap.put(k.substring(0, lastIndexOf), Integer.valueOf(k.substring(lastIndexOf + 1)));
        }
        // 置过期
        jedisService.delKeyStrs(noDataKeys);
        List<String> newKeys = new ArrayList<>(16);
        int num;
        int status = 1;
        for (Object retKey : map) {
            Map<String, String> tagMap = (Map<String, String>) retKey;
            String group = "";

            for (String groupStr : byArr) {
                if ("*".equals(group)) {
                    group = tagMap.get(groupStr);
                } else {
                    group = group + SEPARATOR + tagMap.get(groupStr);
                }
            }

            num = 1;
            // 设置无数据的key
            String noKey = noDataPrefix + group;
            if (noDataMap.containsKey(noKey)) {
                num += noDataMap.get(noKey);
            }
            EventEntity eventEntity = EventEntity.builder().way(mutation).build();
            // 超过检测周期数可生成事件
            if (num >= noDataTimeframe) {
                eventEntity.setValue(0);
                eventEntity.setNoData(true);
                eventEntity.setStatus(status);
                eventEntity.setGroup(group);
                ret.put(retKey, eventEntity);
                newKeys.add(noDataPrefix + group + "_" + num);
                continue;
            }
            eventEntity.setValue(0);
            //无数据但是没有到检测周期状态正常
            eventEntity.setStatus(0);
            eventEntity.setNoData(true);
            eventEntity.setGroup(group);
            ret.put(retKey, eventEntity);
            // 没超过检测周期数或没生成事件加入设置redis key,生成事件自动取消累积，即不加入
            newKeys.add(noDataPrefix + group + "_" + num);
        }
        jedisService.setBatchStr(newKeys, "0");

        return ret;
    }

    /**
     * 单值阈值判断
     *
     * @param thresholds
     * @param comparison
     * @param timeValue
     * @return
     */
    // status=3, 重要, status=2, 次要
    public EventEntity judgeThresholdStatus(JSONObject thresholds, String comparison, Map.Entry<Long, Double> timeValue) {
        //重要  critical 必填
        String critical = thresholds.getString("critical");
        Double criticalThreshold = null;
        if (StringUtils.isNotBlank(critical)) {
            criticalThreshold = Double.valueOf(critical);
        }
        //次要  warning %
        String warning = thresholds.getString("warning");

        EventEntity eventEntity = EventEntity.builder().way(mutation).build();
        // 根据阈值判断状态
        int status = 0;
        String threshold = "";
        final Double value = timeValue.getValue();
        switch (comparison) {
            case ">":
                if (criticalThreshold != null && value > criticalThreshold) {
                    //触发 重要
                    status = 3;
                    threshold = critical;
                } else if (StringUtils.isNotBlank(warning)) {
                    double warnThreshold = Double.parseDouble(warning);
                    //触发 次要
                    if (value > warnThreshold) {
                        status = 2;
                        threshold = warning;
                    }
                }
                break;
            case ">=":
                if (criticalThreshold != null && value >= criticalThreshold) {
                    //触发 重要
                    status = 3;
                    threshold = critical;
                } else if (StringUtils.isNotBlank(warning)) {
                    double warnThreshold = Double.parseDouble(warning);
                    //触发 次要
                    if (value >= warnThreshold) {
                        status = 2;
                        threshold = warning;
                    }
                }
                break;
            case "<":
                if (criticalThreshold != null && value < criticalThreshold) {
                    //触发 重要
                    status = 3;
                    threshold = critical;
                } else if (StringUtils.isNotBlank(warning)) {
                    double warnThreshold = Double.parseDouble(warning);
                    //触发 次要
                    if (value < warnThreshold) {
                        status = 2;
                        threshold = warning;
                    }
                }
                break;
            case "<=":
                if (criticalThreshold != null && value <= criticalThreshold) {
                    //触发 重要
                    status = 3;
                    threshold = critical;
                } else if (StringUtils.isNotBlank(warning)) {
                    double warnThreshold = Double.parseDouble(warning);
                    //触发 次要
                    if (value <= warnThreshold) {
                        status = 2;
                        threshold = warning;
                    }
                }
                break;
            default:
                if (criticalThreshold != null && value == criticalThreshold) {
                    //触发 重要
                    status = 3;
                    threshold = critical;
                } else if (StringUtils.isNotBlank(warning)) {
                    double warnThreshold = Double.parseDouble(warning);
                    //触发 次要
                    if (value == warnThreshold) {
                        status = 2;
                        threshold = warning;
                    }
                }
                break;
        }
        eventEntity.setValue(value);
        eventEntity.setAbnormalTime(timeValue.getKey());
        eventEntity.setStatus(status);
        eventEntity.setThreshold(threshold);
        return eventEntity;
    }
}
```

```java
package com.databuff.metric.impl.alarmV2;

@Component
@Slf4j
public class StatusAlarmCheck extends BaseAlarmCheckV2 implements AlarmCheckOperatorV2 {

    private final static String SEPARATOR = ":";
    private final static String REDIS_PREFIX = "monitor:metric:";

    @Override
    public Map<Object, Object> afterCheckResult(DatabuffMonitor monitor, Map<Map, Map<Object, Double>> aggTimeSeries, DetectQueryRequest detectQueryRequest, MetricAggregator metricAggregator, Collection<QueryRequest> queries) {
        Map<Object, Object> ret = new HashMap<>(16);
        if (CollectionUtils.isEmpty(aggTimeSeries)) {
            return ret;
        }
        //要求周期内完整数据
        final boolean fullWindow = detectQueryRequest.getRequireFullWindow();
        final long period = detectQueryRequest.getPeriod();

        // 删除不满足条件的数据
        for (Iterator<Map.Entry<Map, Map<Object, Double>>> iterator = aggTimeSeries.entrySet().iterator(); iterator.hasNext(); ) {
            Map.Entry<Map, Map<Object, Double>> entry = iterator.next();
            if (entry == null || entry.getKey() == null || entry.getValue() == null) {
                iterator.remove();
                continue;
            }
            Map<Object, Double> retMap = entry.getValue();
            if (CollectionUtils.isEmpty(retMap)) {
                iterator.remove();
                continue;
            }
            if (fullWindow && !dataIntegrityCheck(period, entry)) {
                iterator.remove();
            }
        }

        // 根据时间聚合器和比较符获取分组的聚合值
        if (CollectionUtils.isEmpty(aggTimeSeries)) {
            return ret;
        }

        final String way = detectQueryRequest.getWay();
        final EventEntity.DetectionType detectionType = EventEntity.DetectionType.valueOf(way);
        final ThresholdsDTO thresholds = detectQueryRequest.getThresholds();
        for (Map.Entry<Map, Map<Object, Double>> entry : aggTimeSeries.entrySet()) {
            if (entry == null) {
                continue;
            }
            final Collection<Double> realValues = entry.getValue().values();
            final EventEntity eventEntity = judgeThresholdStatus(thresholds, realValues);
            if (eventEntity == null) {
                continue;
            }
            eventEntity.setWay(detectionType);
            ret.put(entry.getKey(), eventEntity);
        }
        return ret;
    }

    @Override
    public Map<Object, Object> afterCheckNoDataResult(DatabuffMonitor monitor, String mKey, Set<Map<String, String>> map, DetectQueryRequest detectQueryRequest) {
        Map<Object, Object> ret = new HashMap<>(16);

        Integer noDataTimeframe = detectQueryRequest.getNoDataTimeframe();
        // 分组
        Collection<String> byArr = detectQueryRequest.findBy();
        // 查询id下所有无数据
        final int mid = monitor.getId().intValue();
        String noDataPrefix = REDIS_PREFIX + "nodata" + SEPARATOR + mid + SEPARATOR + mKey + SEPARATOR;
        Set<String> noDataKeys = jedisService.keysS(noDataPrefix);
        Map<String, Integer> noDataMap = new HashMap<>(noDataKeys.size());
        for (String k : noDataKeys) {
            int lastIndexOf = k.lastIndexOf("_");
            noDataMap.put(k.substring(0, lastIndexOf), Integer.valueOf(k.substring(lastIndexOf + 1)));
        }
        // 置过期
        jedisService.delKeyStrs(noDataKeys);
        List<String> newKeys = new ArrayList<>(16);
        int num;
        int status = 1;
        for (Object retKey : map) {
            Map<String, String> tagMap = (Map<String, String>) retKey;
            String group = "";

            for (String groupStr : byArr) {
                if ("*".equals(group)) {
                    group = tagMap.get(groupStr);
                } else {
                    group = group + SEPARATOR + tagMap.get(groupStr);
                }
            }

            num = 1;
            // 设置无数据的key
            String noKey = noDataPrefix + group;
            if (noDataMap.containsKey(noKey)) {
                num += noDataMap.get(noKey);
            }
            EventEntity eventEntity = EventEntity.builder().way(EventEntity.DetectionType.stateThreshold).build();
            // 超过检测周期数可生成事件
            if (num >= noDataTimeframe) {
                eventEntity.setValue(0);
                eventEntity.setStatus(status);
                eventEntity.setNoData(true);
                eventEntity.setGroup(group);
                ret.put(retKey, eventEntity);
                newKeys.add(noDataPrefix + group + "_" + num);
                continue;
            }
            eventEntity.setValue(0);
            //无数据但是没有到检测周期状态正常
            eventEntity.setStatus(0);
            eventEntity.setNoData(true);
            eventEntity.setGroup(group);
            ret.put(retKey, eventEntity);
            // 没超过检测周期数或没生成事件加入设置redis key,生成事件自动取消累积，即不加入
            newKeys.add(noDataPrefix + group + "_" + num);
        }
        jedisService.setBatchStr(newKeys, "0");

        return ret;
    }

    /**
     * 单值阈值判断
     *
     * @param thresholds
     * @param realValues
     * @return
     */
    // status=3, 重要, status=2, 次要
    public EventEntity judgeThresholdStatus(ThresholdsDTO thresholds, Collection<Double> realValues) {
        final Double criticalThreshold = thresholds.getCritical() == null ? null : thresholds.getCritical().doubleValue();
        final Double warnThreshold = thresholds.getWarning() == null ? null : thresholds.getWarning().doubleValue();
        if (criticalThreshold == null && warnThreshold == null) {
            log.warn("阈值配置错误：critical和warning至少需要配置一个");
            return null;
        }

        long criticalStateCount = 0;
        long warningStateCount = 0;

        final Number criticalState = thresholds.getCriticalState();
        if (criticalState != null) {
            criticalStateCount = realValues.stream().filter(i -> i != null && i.intValue() == criticalState.intValue()).count();
        }
        final Number warningState = thresholds.getWarningState();
        if (warningState != null) {
            warningStateCount = realValues.stream().filter(i -> i != null && i.intValue() == warningState.intValue()).count();
        }

        // 根据阈值判断状态
        if (criticalThreshold != null && criticalStateCount >= criticalThreshold) {
            return EventEntity.builder()
                    .way(EventEntity.DetectionType.stateThreshold)
                    .value(criticalStateCount)
                    .status(3)
                    .threshold(String.valueOf(criticalThreshold))
                    .build();
        } else if (warnThreshold != null && warningStateCount >= warnThreshold) {
            return EventEntity.builder()
                    .way(EventEntity.DetectionType.stateThreshold)
                    .value(warningStateCount)
                    .status(2)
                    .threshold(String.valueOf(warnThreshold))
                    .build();
        } else {
            return EventEntity.builder()
                    .way(EventEntity.DetectionType.stateThreshold)
                    .value(0)
                    .status(0)
                    .threshold(StringUtils.EMPTY)
                    .build();
        }
    }
}
```

```java
package com.databuff.metric.impl.alarmV2;

@Component
@Slf4j
@RefreshScope
public class ChangePointAlarmCheck extends BaseAlarmCheckV2 implements AlarmCheckOperatorV2 {

    private final static String SEPARATOR = ":";
    private final static String REDIS_PREFIX = "monitor:metric:";

    @Value("${databuff.changePoint.api.url.detect:http://root-engine:18666/abnormal/detect}")
    private String apiUrl;

    @Value("${databuff.changePoint.api.timeout:30}")
    private int timeout;

    /**
     * 默认时间偏移量, 默认120秒
     * 计算公式：默认时间偏移量 = 最延迟的指标数据时间（3分钟） - 60秒（最近1分钟）
     * 注意：这个延迟参数同时会导致告警延迟，所以需要根据实际情况调整
     */
    @Value("${event.metric.defTimeOffset:60}")
    protected Long defTimeOffset;

    private OkHttpClient client;

    public ChangePointAlarmCheck() {
        this.client = new OkHttpClient.Builder()
                .connectTimeout(timeout, TimeUnit.SECONDS)
                .writeTimeout(timeout, TimeUnit.SECONDS)
                .readTimeout(timeout, TimeUnit.SECONDS)
                .connectionPool(new ConnectionPool(5, 5, TimeUnit.MINUTES))
                .build();
    }

    @Override
    public Map<Object, Object> afterCheckResult(DatabuffMonitor monitor, Map<Map, Map<Object, Double>> aggTimeSeries, DetectQueryRequest detectQueryRequest, MetricAggregator metricAggregator, Collection<QueryRequest> queries) {
        final Long nowTime = NowTimeThreadLocal.getNowTime();
        // 触发时间 = endTime-1分钟
        final long endTime = roundDownToMinute(nowTime) - defTimeOffset * 1000;
        final long triggerTime = endTime - ONE_MIN_MS_LONG;
        detectQueryRequest.setTriggerTime(triggerTime);

        Map<Object, Object> ret = new LinkedHashMap<>(16);
        if (CollectionUtils.isEmpty(aggTimeSeries)) {
            return ret;
        }

        // 删除不满足条件的数据
        aggTimeSeries.entrySet().removeIf(entry -> entry == null || entry.getKey() == null || entry.getValue() == null || CollectionUtils.isEmpty(entry.getValue()));

        Collection<JSONObject> datas = new ArrayList<>();
        for (Map.Entry<Map, Map<Object, Double>> entry : aggTimeSeries.entrySet()) {
            final Map<String, String> trigger = entry.getKey();
            datas.add(new JSONObject()
                    .fluentPut("trigger", trigger)
                    .fluentPut("data", entry.getValue()));
        }

        final Collection<JSONObject> aggValue = getAggValueFromApi(detectQueryRequest, datas);

        if (CollectionUtils.isEmpty(aggValue)) {
            return ret;
        }

        for (JSONObject changePoint : aggValue) {
            if (changePoint == null) {
                continue;
            }
            final Map trigger = changePoint.getObject("trigger", Map.class);
            if (trigger == null) {
                continue;
            }
            final long timestamp = changePoint.getLongValue("timestamp");
            final String level = changePoint.getString("level");
            final double value = changePoint.getDoubleValue("value");
            final double originalValue = changePoint.getDoubleValue("originalValue");
            final double threshold = changePoint.getDoubleValue("threshold");
            final String signal = changePoint.getString("signal");

            Map<String, Number> fields = new HashMap<>();
            final Map<Object, Double> data = aggTimeSeries.get(trigger);
            if (data != null && !data.isEmpty() && data instanceof TreeMap) {
                fields.put("dataSize", (long) data.size());
                fields.put("nonNullSize", data.values().stream().filter(Objects::nonNull).count());
                fields.put("isNullSize", data.values().stream().filter(Objects::isNull).count());
                final TreeMap<String, Double> treeMap = (TreeMap) data;
                if (treeMap.firstKey() != null) {
                    fields.put("start", Long.parseLong(treeMap.firstKey()));
                }
                if (treeMap.lastKey() != null) {
                    fields.put("end", Long.parseLong(treeMap.lastKey()));
                }
            }

            fields.put("cnt", 1L);
            // 延迟多少秒
            fields.put("latency", (nowTime - timestamp) / 1000);
            fields.put("value", value);
            fields.put("originalValue", originalValue);
            fields.put("threshold", threshold);

            fields.put("triggerTime", triggerTime);
            fields.put("endTime", endTime);
            fields.put("executionTime", nowTime);

            Map<String, String> tags = new HashMap<>();
            tags.putAll(trigger);
            tags.put("level", level);
            tags.put("signal", signal);
            tags.put(METRIC, detectQueryRequest.findMetrics().toString());
            tags.put(RULE_NAME, monitor.getRuleName());
            tags.put(MONITOR_ID, monitor.getId().toString());

            EventEntity eventEntity = createEventEntity(timestamp, value, originalValue, level, threshold, signal);
            if (eventEntity != null) {
                ret.put(trigger, eventEntity);
                fields.put("status", eventEntity.getStatus());
                tags.put("isNoData", eventEntity.isNoData() ? "true" : "false");
            }
            OtelMetricUtil.logOriginalData("changePointAlarm", fields, tags, timestamp);
        }
        return ret;
    }

    private Collection<JSONObject> getAggValueFromApi(DetectQueryRequest multiQueryDTO, Collection<JSONObject> datas) {
        JSONObject requestBody = new JSONObject();
        requestBody.put("rule", multiQueryDTO);
        requestBody.put("datas", datas);
        long start = System.currentTimeMillis();

        String requestStr = requestBody.toJSONString();
        Request request = new Request.Builder()
                .url(apiUrl)
                .post(RequestBody.create(requestStr, MediaType.parse("application/json; charset=utf-8")))
                .build();

        Map<String, Number> fields = new HashMap<>();
        Map<String, String> tags = new HashMap<>();
        fields.put("cnt", 1L);

        try (Response response = client.newCall(request).execute()) {
            tags.put("responseCode", String.valueOf(response.code()));
            tags.put("isSuccessful", String.valueOf(response.isSuccessful()));
            tags.put("responseBody", String.valueOf(response.body() != null));

            if (!response.isSuccessful() || response.body() == null) {
                log.error("ChangePointAlarmCheckMMT request error, request {}, response code: {}", requestStr, response.code());
                return new ArrayList<>();
            }
            String responseStr = response.body().string();
            return JSON.parseArray(responseStr, JSONObject.class);
        } catch (IOException | IllegalArgumentException | NullPointerException e) {
            log.error("ChangePointAlarmCheckMMT request error", e);
            tags.put("error", e.getClass().getName());
            OtelMetricUtil.logException("getAggValueFromApi", e);
            return new ArrayList<>();
        } finally {
            fields.put("cost", (double) (System.currentTimeMillis() - start));
            OtelMetricUtil.logOriginalData("getAggValueFromApi", fields, tags, start);
        }
    }

    private EventEntity createEventEntity(long timestamp, double value, double originalValue, String level, double threshold, String signal) {
        EventEntity eventEntity = EventEntity.builder()
                .way(EventEntity.DetectionType.changePoint)
                .value(value)
                .originalValue(originalValue)
                .abnormalTime(timestamp)
                .threshold(String.valueOf(threshold))
                .comparison(signal)
                .build();

        switch (level) {
            case "critical":
                eventEntity.setStatus(3);
                break;
            case "warning":
                eventEntity.setStatus(2);
                break;
            case "nodata":
                eventEntity.setStatus(0);
                eventEntity.setNoData(true);
                break;
            default:
                return null;
        }
        return eventEntity;
    }

    @Override
    public Map<Object, Object> afterCheckNoDataResult(DatabuffMonitor monitor, String mKey, Set<Map<String, String>> map, DetectQueryRequest detectQueryRequest) {
        Map<Object, Object> ret = new HashMap<>(16);

        Integer noDataTimeframe = detectQueryRequest.getNoDataTimeframe();
        // 分组
        final Collection<String> byArr = detectQueryRequest.findBy();
        // 查询id下所有无数据
        final int mid = monitor.getId().intValue();
        String noDataPrefix = REDIS_PREFIX + "nodata" + SEPARATOR + mid + SEPARATOR + mKey + SEPARATOR;
        Set<String> noDataKeys = jedisService.keysS(noDataPrefix);
        Map<String, Integer> noDataMap = new HashMap<>(noDataKeys.size());
        for (String k : noDataKeys) {
            int lastIndexOf = k.lastIndexOf("_");
            noDataMap.put(k.substring(0, lastIndexOf), Integer.valueOf(k.substring(lastIndexOf + 1)));
        }
        // 置过期
        jedisService.delKeyStrs(noDataKeys);
        List<String> newKeys = new ArrayList<>(16);
        int num;
        int status = 1;
        for (Object retKey : map) {
            Map<String, String> tagMap = (Map<String, String>) retKey;
            String group = "";
            for (String groupStr : byArr) {
                if ("*".equals(group)) {
                    group = tagMap.get(groupStr);
                } else {
                    group = group + SEPARATOR + tagMap.get(groupStr);
                }
            }
            num = 1;
            // 设置无数据的key
            String noKey = noDataPrefix + group;
            if (noDataMap.containsKey(noKey)) {
                num += noDataMap.get(noKey);
            }
            // 超过检测周期数可生成事件
            if (num >= noDataTimeframe) {
                EventEntity eventEntity = EventEntity.builder().way(EventEntity.DetectionType.threshold).build();
                eventEntity.setValue(0);
                eventEntity.setNoData(true);
                eventEntity.setStatus(status);
                eventEntity.setGroup(group);
                ret.put(retKey, eventEntity);
                newKeys.add(noDataPrefix + group + "_" + num);
                continue;
            }
            EventEntity eventEntity = EventEntity.builder().way(EventEntity.DetectionType.threshold).build();
            eventEntity.setValue(0);
            //无数据但是没有到检测周期状态正常
            eventEntity.setStatus(0);
            eventEntity.setNoData(true);
            eventEntity.setGroup(group);
            ret.put(retKey, eventEntity);
            // 没超过检测周期数或没生成事件加入设置redis key,生成事件自动取消累积，即不加入
            newKeys.add(noDataPrefix + group + "_" + num);
        }
        jedisService.setBatchStr(newKeys, "0");

        return ret;
    }
}
```



（补充）redis key的组成规则：





**触发条件**：最近**5**分钟内，指标的**平均值大于**阈值时触发

**设置阈值**：

<font style="color:#DF2A3F;">重要</font>事件阈值>1000ms

<font style="color:#ED740C;">次要</font>事件阈值>100ms

```java
Map<Object, Object> checkRets = checkOperator.afterCheckResult(new DatabuffMonitor(m), aggTimeSeries, query, metricAggregator, queries);
```

<font style="color:#8A8F8D;">无数</font>据事件（noDataTimeframe）：指标连续n分钟无数据上报时触发（数字0表示被检测指标无数据时，不会触发无数据事件）

```java
log.debug("【单指标】单指标监控：{}开始无数据结果集查询===============", ruleName);
// 无数据结果集获取
Set<Map<String, String>> noDataResult = metricAggregator.noDataResult(m, queries);
log.debug("【单指标】单指标监控：{}无数据结果集数量：{}，并开始无数据结果集告警等级判断===============", ruleName, noDataResult.size());
//无数据判断是否生成
checkDataRets.putAll(checkOperator.afterCheckNoDataResult(new DatabuffMonitor(m), "1", noDataResult, query));
```

**高级配置**：不要求 检测周期内有完整数据时才进行评估。

通常情况下，您不希望规则在评估窗口数据未满时进行评估。

比如一个主机刚开机，采集数据可能还未完全上报，您不希望触发报警。

但在另一些情况下，您设备的数据因某些原因就是很稀疏的，如果必须要求周期内数据完整，则就无法触发检测评估。

首次检测某实体对象时，延迟n分钟后再做检测（evaluationDelay）

延迟评估的时间（以分钟为单位）。对于延迟数据很有用。应该是一个非负整数

```java
delayList = metricAggregator.delayResult(m, queries, evaluationDelay);
```



检测框架：基于xxljob

是否支持高可用：是

是否支持分布式：是

是否高并发：是

检测周期：1分钟（目前在xxl_job_info中配置）

#### 单指标检测（sharding-singleMetricThresholdMonitor-task）：
```java
package com.databuff.tasks.monitor;

@Component
@Slf4j
public class SingleMetricMonitorTimer extends MonitorTimerBase {
    @Autowired
    private MonitorService monitorService;
    @Autowired
    private MonitorTaskPool monitorTaskPool;
    /**
     * 当前统计时间 毫秒
     */
    private long currentStatTime;

    public void metricMonitorTimer() {
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        log.info("sharding-singleMetricThresholdMonitor-task 开始单指标监控引擎 ,分片参数：当前分片序号 = {}, 总分片数 = {}", shardIndex, shardTotal);

        // 当前统计时间
        long curTime = System.currentTimeMillis();
        //1、计算查询起始时间
        currentStatTime = (curTime / 1000 - curTime / 1000 % 60) * 1000;
        // 根据条件查询监控
        List<DatabuffMonitorView> monitorList = monitorService.findAllMonitorByClassfy("singleMetric", true);
        List<DatabuffMonitorView> shardingMonitors = ShardingUtil.sharding(monitorList, shardIndex, shardTotal);
        if (CollectionUtils.isEmpty(shardingMonitors)) {
            return;
        }

        // 当前正在执行监控的count
        int monitoringCount = shardingMonitors.size();
        OtelMetricUtil.logCounter(CURRENT_MONITOR_COUNT, monitoringCount);
        log.info("sharding-singleMetricThresholdMonitor-task 当前正在执行监控的count = {}", monitoringCount);

        for (DatabuffMonitorView shardingMonitor : shardingMonitors) {
            Runnable task = new RecordTimeExceptionHandlingRunnable(() -> {
                NowTimeThreadLocal.setNowTime(curTime);
                try {
                    // 参数：截取的每个线程需要处理的监控，主机list,标签及apiKey
                    ThreadLocalUtil.doWithThreadLocal(() -> doMonitor(shardingMonitor));
                } catch (Exception e) {
                    log.error("singleMetric mid:{},doMonitor error:", shardingMonitor.getId(), e);
                }
            });
            monitorTaskPool.submitWorker(task);
        }
        log.info("---执行单指标监控任务消耗了 ：" + (System.currentTimeMillis() - curTime) + "毫秒");
    }

    /**
     * 监控事件计算
     *
     */
    private void doMonitor(DatabuffMonitorView m) {
        List<JSONObject> events = new ArrayList<>();
        if (m == null) {
            return;
        }
        final MultiDetectQueryRequest multiDetectQueryRequest = m.getQuery();
        if (multiDetectQueryRequest == null) {
            return;
        }
        final DetectQueryRequest query = multiDetectQueryRequest.getA();
        if (query == null) {
            return;
        }
        //将当前统计时间放入
        query.setTriggerTime(currentStatTime);

        // 分组
//        Collection<String> byArr = query.findBy();
        // 检测周期 秒,默认5分钟
        Integer period = 300;
        if (query.getPeriod() != null) {
            period = query.getPeriod();
        }
        final String ruleName = m.getRuleName();
        final String way = query.getWay();
        //检测方法 threshold，baseline，mutation，stateThreshold
        if (way == null) {
            log.warn("【单指标】单指标监控：{}找不到检测方法，结束", ruleName);
            return;
        }
        final EventEntity.DetectionType detectionType = EventEntity.DetectionType.valueOf(way);
        // 变异检测周期为1小时
        if (detectionType == EventEntity.DetectionType.changePoint) {
            period = 60 * 60;
        }

        final String gid = m.getGid();
        final Collection<String> gids = Lists.newArrayList(m.getGid());

        log.debug("【单指标】开始处理单指标监控：{}=============", ruleName);
        Collection<QueryRequest> queries = query.getNonNullQueries();
        for (QueryRequest queryRequest : queries) {
            if (queryRequest == null) {
                continue;
            }
            // 如果是系统类型的检测规则，则可以查询所有对象
            final boolean allPermission = gid == null || m.getSystem();

            queryRequest.setApiKey(m.getApiKey());
            queryRequest.setPeriod(period);
            queryRequest.setTimeOffset(taskRefreshScopeConfig.getEventDefTimeOffset());
            queryRequest.setGids(gids);
            queryRequest.setAllPermission(allPermission);
            queryRequest.setDomainManagerStatusOpen(domainManagerObjService.getDomainManagerStatusOpen());
            final Collection<String> groupBy = queryRequest.getBy();
            if (groupBy != null) {
                for (Map.Entry<String, String> entry : keyValueMap.entrySet()) {
                    if (groupBy.contains(entry.getKey())) {
                        groupBy.add(entry.getValue());
                    }
                }
            }
        }
        AlarmCheckOperatorV2 checkOperator = getAlarmCheckOperatorV2(way);

        // 是否周期内完整数据，不完整不评估
        Map<Object, Object> checkDataRets = new HashMap<>();
        Integer evaluationDelay = query.getEvaluationDelay();
        Integer noDataTimeframe = query.getNoDataTimeframe();
        if (noDataTimeframe != null && noDataTimeframe > 0) {
            log.debug("【单指标】单指标监控：{}开始无数据结果集查询===============", ruleName);
            // 无数据结果集获取
            Set<Map<String, String>> noDataResult = metricAggregator.noDataResult(m, queries);
            log.debug("【单指标】单指标监控：{}无数据结果集数量：{}，并开始无数据结果集告警等级判断===============", ruleName, noDataResult.size());

            //无数据判断是否生成
            checkDataRets.putAll(checkOperator.afterCheckNoDataResult(new DatabuffMonitor(m), "1", noDataResult, query));
        }

        Set<Map<String, String>> delayList = new HashSet<>();
        if (evaluationDelay != null &&evaluationDelay > 0) {
            delayList = metricAggregator.delayResult(m, queries, evaluationDelay);
        }

        log.debug("【单指标】单指标监控：{}开始获取查询结果===============", ruleName);
        final String expr = query.getExpr();
        // 聚合以后的时序数据，聚合粒度1分钟
        Map<Map, Map<Object, Double>> aggTimeSeries = metricAggregator.aggResult(expr, query.getNonNullQueriesMap());
        if (CollectionUtils.isEmpty(aggTimeSeries) && checkDataRets.isEmpty()) {
            log.warn("【单指标】单指标监控：{}下指标{}获取结果为空", ruleName, "1");
            return;
        }
        //阈值比较
        log.debug("【单指标】单指标监控：{}数据结果集数量：{}，并开始数据结果集告警等级判断===============", ruleName, aggTimeSeries.size());
        Map<Object, Object> checkRets = checkOperator.afterCheckResult(new DatabuffMonitor(m), aggTimeSeries, query, metricAggregator, queries);
        if (checkRets != null) {
            checkRets.forEach((key, value) -> checkDataRets.putIfAbsent(key, value));
        }

//        checkOperator.needMoreTags(checkDataRets, queries, gid == null ? Lists.newArrayList() : Lists.newArrayList(gid));
        log.debug("【单指标】单指标监控：{}数据结果集数量：{}，告警等级结果集数量：{}===============", ruleName, aggTimeSeries.size(), checkRets.size());

        for (Map.Entry<Object, Object> checkEntry : checkDataRets.entrySet()) {
            if (checkEntry == null || checkEntry.getKey() == null || checkEntry.getValue() == null) {
                continue;
            }
            // "host:databuff193;device_name:sda1"
            Map<String, Object> tags = (Map<String, Object>) checkEntry.getKey();

            if (delayList.contains(tags)) {
                log.debug("【单指标】单指标监控：{} 对象：{} 延迟检测，跳过===============", ruleName, tags);
                continue;
            }

            JSONObject trigger = new JSONObject();
            if (tags != null) {
                for (Map.Entry<?, ?> entry : tags.entrySet()) {
                    Object key = entry.getKey();
                    if (key instanceof String) {
                        String strKey = (String) key;
                        if (strKey != null) {
                            trigger.put(strKey, entry.getValue());
                        }
                    }
                }
            }

            EventEntity eventEntity = (EventEntity) checkEntry.getValue();
            // 事件记录
            JSONObject event = this.getEventLogJson(eventEntity, m, query, tags);
            event.put(TAGS, trigger);
            event.put("cycle", period);
            event.put(BY, query.findBy());
            events.add(event);
            log.debug("【单指标】单指标监控：{} 对象：{} 生成事件：{} ===============", ruleName, trigger.toJSONString(), event);
        }
        //事件告警，故障场景，连续性，以及变量填充后发送库及队列
        monitorUtils.eventSinkDBMq(events, m);

    }

    /**
     * 此方法生成事件日志的 JSON 对象。
     *
     * @param eventEntity     包含事件信息的事件实体。
     * @param monitor         包含监视器信息的监视器视图。
     * @param multiQueryDTO   包含单个指标信息的 JSON 对象。
     * @return 表示事件日志的 JSON 对象。
     */
    private JSONObject getEventLogJson(EventEntity eventEntity, DatabuffMonitorView monitor, DetectQueryRequest multiQueryDTO, Map<String, Object> tags) {
        String threshold = eventEntity.getThreshold();
        int status = eventEntity.getStatus();
        double value = eventEntity.getValue();
        final Long nowTime = NowTimeThreadLocal.getNowTime();
        // 触发时间 = endTime-1分钟
        final long triggerTime = roundDownToMinute(nowTime) - taskRefreshScopeConfig.getEventDefTimeOffset() * 1000 - ONE_MIN_MS_LONG;

        // 初始化事件记录json
        JSONObject event = monitorUtils.initEventLogJson(monitor, status, value, multiQueryDTO.getUnit(), SQLParser.mapToKeyValueString(tags), triggerTime);
        final Collection<String> byArr = multiQueryDTO.findBy();
        JSONObject trigger = new JSONObject();
        if (tags != null && byArr != null && !byArr.isEmpty()) {
            for (String key : byArr) {
                trigger.put(key, tags.get(key));
            }
        }

        // 状态大于0生成事件
        if (status > 0) {
            event.put("triggerObjType", String.join(",", trigger.keySet()));
            event.put(TRIGGER, trigger);
            String comparison = multiQueryDTO.getComparison();
            final String mName = String.join(",", multiQueryDTO.findMetrics());
            if (eventEntity.getComparison() != null) {
                comparison = eventEntity.getComparison();
            }
            String eventMessage = monitor.getMessage();
            event.put("query", monitor.getQuery());
            event.put("metrics", mName);
            final ThresholdsDTO thresholds = JSONB.copy(multiQueryDTO.getThresholds());
            thresholds.setComparison(comparison);
            thresholds.setValue(value);

            String exprName = multiQueryDTO.getExprName();
            thresholds.setMName(StringUtils.isNotBlank(exprName) ? exprName : mName);

            event.put("thresholds", thresholds);
    
            String triggerThreshold = "无数据";
            if (status == 1) {
                event.put("value", null);
                event.put("trgTrd", triggerThreshold);
                event.put("message", eventMessage);
                return event;
            }

            String unit = event.getString("unit");
            String viewUnit = multiQueryDTO.getViewUnit();
            viewUnit = StringUtils.isBlank(viewUnit) ? (unit != null ? unit : "") : viewUnit; // 确保 unit 非空
            event.put("view_unit", viewUnit);

            // 处理 threshold 的空值情况
            if (threshold != null) {
                threshold = monitorUtils.unitFormat(threshold, unit, viewUnit);
            }

            // 阈值显示逻辑
            String detectionWay = (eventEntity.getWay() != null ? eventEntity.getWay() : EventEntity.DetectionType.threshold).getName();
            String trgTrd = detectionWay + threshold;
            event.put("trgTrd", trgTrd);
            thresholds.setTrgTrd(trgTrd);
            thresholds.setTrgValue(threshold);
            event.put("message", eventMessage);
        }
        return event;
    }
}
```

#### 多指标检测（sharding-multipleMetricThresholdMonitor-task）：
```java
package com.databuff.tasks.monitor;

@Component
@Slf4j
public class MultipleMetricMonitorTimer extends MonitorTimerBase {
    @Autowired
    private MonitorService monitorService;
    @Autowired
    private MonitorTaskPool monitorTaskPool;
    /**
     * 当前统计时间 毫秒
     */
    private long currentStatTime;


    public void multipleMetricMonitorTimer() {
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        log.info("sharding-multipleMetricThresholdMonitor-task 开始多指标监控引擎 ,分片参数：当前分片序号 = {}, 总分片数 = {}", shardIndex, shardTotal);

        // 当前统计时间
        long curTime = System.currentTimeMillis();
        //1、计算查询起始时间
        currentStatTime = (curTime / 1000 - curTime / 1000 % 60) * 1000;
        // 根据条件查询监控
        List<DatabuffMonitorView> monitorList = monitorService.findAllMonitorByClassfy("multipleMetric", true);
        List<DatabuffMonitorView> shardingMonitors = ShardingUtil.sharding(monitorList, shardIndex, shardTotal);
        if (CollectionUtils.isEmpty(shardingMonitors)) {
            return;
        }

        // 当前正在执行监控的count
        int monitoringCount = shardingMonitors.size();
        OtelMetricUtil.logCounter(CURRENT_MONITOR_COUNT, monitoringCount);
        log.info("sharding-multipleMetricThresholdMonitor-task 当前正在执行监控的count = {}", monitoringCount);

        for (DatabuffMonitorView shardingMonitor : shardingMonitors) {
            Runnable task = new RecordTimeExceptionHandlingRunnable(() -> {
                NowTimeThreadLocal.setNowTime(curTime);
                try {
                    // 参数：截取的每个线程需要处理的监控，主机list,标签及apiKey
                    ThreadLocalUtil.doWithThreadLocal(() -> doMonitor(shardingMonitor));
                } catch (Exception e) {
                    log.error("multipleMetric mid:{},doMonitor error:", shardingMonitor.getId(), e);
                }
            });
            monitorTaskPool.submitWorker(task);
        }
        log.info("---执行多指标监控引擎任务消耗了 ：" + (System.currentTimeMillis() - curTime) + "毫秒");
    }

    /**
     * 监控事件计算
     *
     * @param m
     */
    private void doMonitor(DatabuffMonitorView m) {
        List<JSONObject> events = new ArrayList<>();
        final MultiDetectQueryRequest allQuery = m.getQuery();

        Map<String, Map<Object, Object>> allMetricCheakRets = new HashMap<>(5);
        // 计算静默情况
        Set<Object> groupSet = new HashSet<>(16);
        int ruleSize = 0;
        log.debug("【多指标】开始处理多指标监控：{}=============", m.getRuleName());
        final Set<String> by = new HashSet<>();

        // 多指标监控最多只能配置5个指标
        final Set<String> metricIndex = Sets.newHashSet("1", "2", "3", "4", "5");

        Map<String, Set<Map<String, String>>> delayListMap = new HashMap<>();
        final String ruleName = m.getRuleName();
        final String gid = m.getGid();
        final Collection<String> metrics = allQuery.findMetrics();

        for (Map.Entry<String, DetectQueryRequest> detectQueryRequestEntry : allQuery.buildNonNullQueriesMap().entrySet()) {
            Map<Object, Object> checkDataRets = new HashMap<>();
            final String mKey = detectQueryRequestEntry.getKey();
            final DetectQueryRequest detectQueryRequest = detectQueryRequestEntry.getValue();
            if (detectQueryRequest == null) {
                continue;
            }
            if (!metricIndex.contains(mKey)) {
                continue;
            }
            log.debug("【多指标】开始处理多指标监控：{}下指标{}=============", ruleName, mKey);

            final String way = detectQueryRequest.getWay();
            if (way == null) {
                log.warn("【多指标】多指标监控：{}下指标{}找不到检测方法，结束", ruleName, mKey);
                return;
            }
            final EventEntity.DetectionType detectionType = EventEntity.DetectionType.valueOf(way);
            AlarmCheckOperatorV2 checkOperator = getAlarmCheckOperatorV2(way);
            ruleSize++;

            Integer evaluationDelay = detectQueryRequest.getEvaluationDelay();
            Integer noDataTimeframe = detectQueryRequest.getNoDataTimeframe();
            if (noDataTimeframe > 0) {
                log.debug("【多指标】多指标监控：{}下指标{}开始无数据结果集查询===============", m.getRuleName(), mKey);
                // 无数据结果集获取
                Set<Map<String, String>> noDataResult = metricAggregator.noDataResult(m, detectQueryRequest.getNonNullQueries());
                log.debug("【多指标】多指标监控：{}下指标{}无数据结果集数量：{}，并开始无数据结果集告警等级判断===============", m.getRuleName(), mKey, noDataResult.size());
                //无数据判断是否生成
                checkDataRets.putAll(checkOperator.afterCheckNoDataResult(new DatabuffMonitor(m), mKey, noDataResult, detectQueryRequest));
            }

            if (evaluationDelay > 0) {
                delayListMap.put(mKey, metricAggregator.delayResult(m, detectQueryRequest.getNonNullQueries(), evaluationDelay));
            }

            final String expr = detectQueryRequest.getExpr();

            final Collection<QueryRequest> queryRequests = detectQueryRequest.getNonNullQueries();
            if (queryRequests == null) {
                continue;
            }

            final Collection<String> gids = Lists.newArrayList(m.getGid());

            for (QueryRequest queryRequest : queryRequests) {
                if (queryRequest == null) {
                    continue;
                }

                // 如果是系统类型的检测规则，则可以查询所有对象
                final boolean allPermission = gid == null || m.getSystem();

                //将当前统计时间放入
                queryRequest.setCurrentStatTime(currentStatTime);
                Integer period = queryRequest.getPeriod();
                // 变异检测周期为1小时
                if (detectionType == EventEntity.DetectionType.changePoint) {
                    period = 60 * 60;
                } else if (period == null) {
                    // 检测周期 秒,默认5分钟
                    period = 300;
                }
                queryRequest.setApiKey(m.getApiKey());
                queryRequest.setPeriod(period);
                queryRequest.setTimeOffset(taskRefreshScopeConfig.getEventDefTimeOffset());
                queryRequest.setGids(gids);
                queryRequest.setAllPermission(allPermission);
                queryRequest.setDomainManagerStatusOpen(domainManagerObjService.getDomainManagerStatusOpen());

                final Collection<String> groupBy = queryRequest.getBy();
                if (groupBy != null) {
                    for (Map.Entry<String, String> entry : keyValueMap.entrySet()) {
                        if (groupBy.contains(entry.getKey())) {
                            groupBy.add(entry.getValue());
                        }
                    }
                }
            }

            log.debug("【多指标】多指标监控：{}下指标{}开始获取查询结果===============", ruleName, mKey);
            final Map<String, QueryRequest> queriesMap = detectQueryRequest.getNonNullQueriesMap();
            Map<Map, Map<Object, Double>> aggTimeSeries = metricAggregator.aggResult(expr, queriesMap);
            if (CollectionUtils.isEmpty(aggTimeSeries) && checkDataRets.isEmpty()) {
                log.warn("【多指标】多指标监控：{}下指标{}获取结果为空", ruleName, mKey);
                return;
            }
            // 是否周期内完整数据，不完整不评估
            //阈值比较
            log.debug("【多指标】多指标监控：{}下指标{}数据结果集数量：{}，并开始数据结果集告警等级判断===============", m.getRuleName(), mKey, aggTimeSeries.size());
            Map<Object, Object> checkRets = checkOperator.afterCheckResult(new DatabuffMonitor(m), aggTimeSeries, detectQueryRequest, metricAggregator, queriesMap.values());
            log.debug("【多指标】多指标监控：{}下指标{}数据结果集数量：{}，告警等级结果集数量：{}===============", m.getRuleName(), mKey, aggTimeSeries.size(), checkRets.size());
            // 是否周期内完整数据，不完整不评估
            //阈值比较
            if (checkRets != null) {
                checkRets.forEach((key, value) -> checkDataRets.putIfAbsent(key, value));
            }
//            checkOperator.needMoreTags(checkDataRets, queryRequests, gid == null ? Lists.newArrayList() : Lists.newArrayList(gid));
            //获取所有分组
            groupSet.addAll(checkDataRets.keySet());
            allMetricCheakRets.put(mKey, checkDataRets);
        }

        log.debug("【多指标】多指标监控：{} 各指标告警等级结果集数量：{}===============", m.getRuleName(), allMetricCheakRets.size());
        //3
        LogicalOperator criticalConnector = allQuery.getCritical();
        //2
        LogicalOperator warningConnector = allQuery.getWarning();
        //1
        LogicalOperator noDataConnector = allQuery.getNoData();

        for (Object groupObj : groupSet) {
            try {
                int criticalCount = 0;
                List<EventEntity> criticalEventList = new ArrayList<>();
                int warnCount = 0;
                List<EventEntity> warnEventList = new ArrayList<>();
                int noDataCount = 0;
                List<EventEntity> noDataEventList = new ArrayList<>();
                List<EventEntity> normalEventList = new ArrayList<>();
                for (Map.Entry<String, Map<Object, Object>> entry : allMetricCheakRets.entrySet()) {
                    String mKey = entry.getKey();
                    final Set<Map<String, String>> delayList = delayListMap.get(mKey);
                    if (delayList != null && delayList.contains(groupObj)) {
                        log.debug("【多指标】多指标监控：{}下指标{}分组{}存在延迟数据，跳过===============", m.getRuleName(), mKey, groupObj);
                        continue;
                    }
                    Map<Object, Object> checkDataRets = entry.getValue();
                    EventEntity eventEntity = (EventEntity) checkDataRets.get(groupObj);
                    if (eventEntity == null) {
                        //这个对象的这个指标一直没有这个分组
                        continue;
                    }
                    eventEntity.setMKey(mKey);
                    int status = eventEntity.getStatus();
                    if (status == 3) {
                        //critical
                        criticalCount++;
                        criticalEventList.add(eventEntity);
                        //如果是重要，这里判断肯定也触发了次要的判断数量也得+1
                        warnCount++;
                        EventEntity warnEventEntity = EventEntity.builder()
                                .way(eventEntity.getWay())
                                .threshold(eventEntity.getThreshold())
                                .existsEvent(eventEntity.isExistsEvent())
                                .status(2)
                                .group(eventEntity.getGroup())
                                .value(eventEntity.getValue())
                                .mKey(mKey)
                                .build();
                        warnEventList.add(warnEventEntity);
                    } else if (status == 2) {
                        //warning
                        warnCount++;
                        warnEventList.add(eventEntity);
                    } else if (status == 1) {
                        //nodata
                        noDataCount++;
                        noDataEventList.add(eventEntity);
                    } else {
                        normalEventList.add(eventEntity);
                    }
                }

                JSONObject trigger = new JSONObject();
                if (groupObj != null && groupObj instanceof Map) {
                    Map<?, ?> tempMap = (Map<?, ?>) groupObj;
                    for (Map.Entry<?, ?> entry : tempMap.entrySet()) {
                        Object key = entry.getKey();
                        if (key instanceof String) {
                            String strKey = (String) key;
                            if (strKey != null) {
                                trigger.put(strKey, entry.getValue());
                            }
                        }
                    }
                }

                log.debug("【多指标】多指标监控：{} 对象：{},总检测指标数:{},符合重要数:{},符合次要数:{},符合无数据数:{},===============", m.getRuleName(), trigger.toJSONString(), criticalCount, warnCount, noDataCount);


                List<EventEntity> allEventEntitys = new ArrayList<>();
                allEventEntitys.addAll(criticalEventList);
                allEventEntitys.addAll(warnEventList);
                allEventEntitys.addAll(noDataEventList);
                allEventEntitys.addAll(normalEventList);
                if (CollectionUtils.isEmpty(allEventEntitys)) {
                    continue;
                }
                List<EventEntity> triggerEventEntitys = new ArrayList<>();
                int status = 0;
                if (LogicalOperator.AND.equals(criticalConnector)) {
                    if (criticalCount >= ruleSize) {
                        //重要的条数大于等于总条数，说明所有的都是重要的
                        //生成重要事件
                        triggerEventEntitys.addAll(criticalEventList);
                        status = 3;
                    }
                } else if (LogicalOperator.OR.equals(criticalConnector)) {
                    if (criticalCount > 0) {
                        //重要的条数大于1条
                        //生成重要事件
                        triggerEventEntitys.addAll(criticalEventList);
                        status = 3;
                    }
                }
                //只有状态还是0的时候才计算次要和无数据
                if (status == 0 && LogicalOperator.AND.equals(warningConnector)) {
                    if (warnCount >= ruleSize) {
                        //次要的条数大于等于总条数
                        //生成次要事件
                        triggerEventEntitys.addAll(warnEventList);
                        status = 2;
                    }
                } else if (status == 0 && LogicalOperator.OR.equals(warningConnector)) {
                    if (warnCount > 0) {
                        //次要的条数大于1条
                        //生成次要事件
                        triggerEventEntitys.addAll(warnEventList);
                        status = 2;
                    }
                }
                //只有状态还是0的时候才计算次要和无数据
                if (status == 0 && LogicalOperator.AND.equals(noDataConnector)) {
                    if (noDataCount >= ruleSize) {
                        //无数据的条数大于等于总条数
                        //生成无数据事件
                        triggerEventEntitys.addAll(noDataEventList);
                        status = 1;
                    }
                } else if (status == 0 && LogicalOperator.OR.equals(noDataConnector)) {
                    if (noDataCount > 0) {
                        //无数据的条数大于0
                        //生成无数据事件
                        triggerEventEntitys.addAll(noDataEventList);
                        status = 1;
                    }
                }
                if (status == 0) {
                    //正常事件处理
                    triggerEventEntitys.addAll(normalEventList);
                }
                if (triggerEventEntitys.isEmpty()) {
                    log.debug("【多指标】多指标监控：{} 对象：{} eventEntitys为空 ===============", m.getRuleName(), trigger.toJSONString());
                    continue;
                }
                // 事件记录
                JSONObject event = this.getEventLogJson(allEventEntitys, status, m, allQuery, trigger);
                event.put(TAGS, trigger);
                event.put("metrics", metrics);
                event.put(BY, by);
                log.debug("【多指标】多指标监控：{} 触发对象：{} 生成事件：{} ===============", m.getRuleName(), trigger.toJSONString(), event);
                events.add(event);
            } catch (Exception e) {
                log.error(String.format("【多指标】多指标监控：%s 对象：%s 判断告警异常", m.getRuleName(), groupObj), e);
            }
        }
        //事件告警，故障场景，连续性，以及变量填充后发送库及队列
        monitorUtils.eventSinkDBMq(events, m);

    }


    /**
     * 生成事件记录
     *
     * @param eventEntitys
     * @param status          当前状态
     * @param monitor         当前监控
     * @return 事件记录
     */
    private JSONObject getEventLogJson(List<EventEntity> eventEntitys, int status, DatabuffMonitorView monitor, MultiDetectQueryRequest allQuery, JSONObject trigger) {

        final Long nowTime = NowTimeThreadLocal.getNowTime();
        // 触发时间 = endTime-1分钟
        final long triggerTime = roundDownToMinute(nowTime) - taskRefreshScopeConfig.getEventDefTimeOffset() * 1000L - ONE_MIN_MS_LONG;

        // 初始化事件记录json
        JSONObject event = monitorUtils.initMultipleMetricEventJson(monitor, status, trigger, triggerTime);
        // 多指标监控最多只能配置5个指标
        final Set<String> metricIndex = Sets.newHashSet("1", "2", "3", "4", "5");
        Map<String, ThresholdsDTO> mKeyQueryMap = new HashMap<>(5);
        final Map<String, DetectQueryRequest> nonNullQueriesMap = allQuery.buildNonNullQueriesMap();
        for (Map.Entry<String, DetectQueryRequest> entry : nonNullQueriesMap.entrySet()) {
            final String mKey = entry.getKey();
            final DetectQueryRequest detectQueryRequest = entry.getValue();
            if (!metricIndex.contains(mKey) || detectQueryRequest == null) {
                continue;
            }
            ThresholdsDTO thresholds = detectQueryRequest.getThresholds();
            thresholds.setComparison(detectQueryRequest.getComparison());
            thresholds.setUnit(detectQueryRequest.getUnit());
            thresholds.setCheckName(mKey);
            String exprName = detectQueryRequest.getExprName();
            if (StringUtils.isNotBlank(exprName)) {
                //表达式名称 后续无数据生成告警 用
                thresholds.setMName(exprName);
            } else {
                //指标名称 后续无数据生成告警 用
                thresholds.setMName(detectQueryRequest.findMetrics().toString());
            }
            mKeyQueryMap.put(mKey, thresholds);
        }

        for (EventEntity eventEntity : eventEntitys) {
            if (eventEntity == null) {
                continue;
            }
            final ThresholdsDTO thresholds = mKeyQueryMap.get(eventEntity.getMKey());

            if (!eventEntity.isNoData()) {
                thresholds.setValue(eventEntity.getValue());
            }

            if (eventEntity.getComparison() != null) {
                thresholds.setComparison(eventEntity.getComparison());
            }

            if (status == 1) {
                // 无数据不需要阈值判断
                thresholds.setTrgTrd("无数据");
                thresholds.setTrgValue("空");
            } else {
                String threshold = eventEntity.getThreshold();
                // 单位格式化
                threshold = monitorUtils.unitFormat(threshold, thresholds.getUnit(), thresholds.getUnit());
                //此处产品更改1.30
                //阈值：即监控中设置的阈值；显示的单位优先以阈值设置时的单位为基准；当是静态阈值时，显示为“静态阈值X”；当是动态基线时，显示为“动态基线Y”；
                final EventEntity.DetectionType way = eventEntity.getWay();
                if (way != null) {
                    thresholds.setTrgTrd(way.getName() + threshold);
                } else {
                    thresholds.setTrgTrd(EventEntity.DetectionType.threshold.getName() + threshold);
                }
                String value = eventEntity.getValue() + "";
                value = monitorUtils.unitFormat(value, thresholds.getUnit(), thresholds.getUnit());
                thresholds.setTrgValue("实际值" + value);
            }
            mKeyQueryMap.put(eventEntity.getMKey(), thresholds);
        }
        // todo 算法不在需要 multithresholds, thresholds.
        event.put("multithresholds", mKeyQueryMap);

        // 状态大于0生成事件
        if (status > 0) {
            event.put("triggerObjType", String.join(",", trigger.keySet()));
            event.put(TRIGGER, trigger);
            String eventMessage = monitor.getMessage();
            if (allQuery != null) {
                // todo 前端还需要query对象去查询指标趋势图，暂不能删除
                event.put("query", allQuery);
            }
            String triggerThreshold = "无数据";
            if (status == 1) {
                // 无数据不需要阈值判断
                event.put("trgTrd", triggerThreshold);
                event.put("message", eventMessage);
                // 无数据事件封装完成返回
                return event;
            }
            event.put("message", eventMessage);
        }
        return event;
    }
}
```



#### 时间同步机制：
**背景**：

每个检测规则执行时间不一致，导致事件触发时间也不一致。

**实现**：

在每次xxljob任务启动时通过<font style="color:#080808;background-color:#ffffff;">ThreadLocal的</font>静态变量记录该时间，并将该值传入到每个检测规则子线程中。

```java
package com.databuff.common.threadLocal;

/**
 * NowTimeThreadLocal类用于管理当前线程的时间。
 * 它使用ThreadLocal来存储和检索当前线程的时间值。
 */
public class NowTimeThreadLocal {
    private static final ThreadLocal<Long> nowTime = new ThreadLocal<>();

    /**
     * 设置当前线程的时间。
     * 注意：使用setNowTime方法设置的时间值只对当前线程有效。在其他线程中无法访问。
     * 如果需要在多个线程中共享时间值，请使用其他方式。
     * 在使用结束后，应该调用remove方法来清除当前线程的时间值。
     *
     * @param time 要设置的时间值（毫秒）。
     */
    public static void setNowTime(Long time) {
        nowTime.set(time);
    }

    /**
     * 获取当前线程的时间。
     *
     * @return 当前线程的时间值（毫秒），如果未设置则返回null。
     */
    public static Long getNowTime() {
        final Long now = nowTime.get();
        if (now == null) {
            return System.currentTimeMillis();
        }
        return now;
    }

    /**
     * 移除当前线程的时间值。
     */
    public static void remove() {
        nowTime.remove();
    }
}
```

```java
package com.databuff.common.exception;


import com.databuff.common.threadLocal.NowTimeThreadLocal;

public class RecordTimeExceptionHandlingRunnable extends ExceptionHandlingRunnable implements Runnable {

    public RecordTimeExceptionHandlingRunnable(Runnable delegate) {
        super(delegate);
    }

    @Override
    public void run() {
        try {
            super.run();
        } finally {
            NowTimeThreadLocal.remove();
        }
    }
}
```

```java
for (DatabuffMonitorView shardingMonitor : shardingMonitors) {
    Runnable task = new RecordTimeExceptionHandlingRunnable(() -> {
        NowTimeThreadLocal.setNowTime(curTime);
        try {
            // 参数：截取的每个线程需要处理的监控，主机list,标签及apiKey
            ThreadLocalUtil.doWithThreadLocal(() -> doMonitor(shardingMonitor));
        } catch (Exception e) {
            log.error("singleMetric mid:{},doMonitor error:", shardingMonitor.getId(), e);
        }
    });
    monitorTaskPool.submitWorker(task);
}
```



##### 事件标签丰富（辅助，非通用）：
背景：对于特殊业务的标签，可能需要调用其他接口查询更多关联数据并添加到事件标签中。

逻辑优化：若datahub支持复杂的标签丰富功能，建议将这块逻辑迁移出去。

```java
for (JSONObject event : events) {
    if (event == null) {
        continue;
    }
    final String apiKey = m.getApiKey();
    final JSONObject tags = event.getJSONObject(TAGS);
    if (tags == null || apiKey == null) {
        continue;
    }
    richHostIP(tags, apiKey);
    richServiceNamespace(tags, apiKey);
    richBizInfo(tags);
    richBusName(event, tags, m);
    if (allEntityPermission) {
        // 增加域ID信息
        event.put("gid", null);
    } else {
        // 增加域ID信息
        event.put("gid", gid);
    }

    // bugfix: 事件中的API_KEY2字段需要转换为API_KEY字段
    if (!event.containsKey(API_KEY) && event.containsKey(API_KEY2)) {
        event.put(API_KEY, event.getString(API_KEY2));
    }
}
```

目前支持的丰富标签：

1. host找ip,<font style="color:#080808;background-color:#ffffff;">hostIp,ipv6</font>
2. <font style="color:#080808;background-color:#ffffff;">serviceId找k8sNamespace</font>
3. <font style="color:#080808;background-color:#ffffff;">serviceId找bizEventId，bizEventName</font>
4. <font style="color:#080808;background-color:#ffffff;">serviceId/srcServiceId/子busName找父busName</font>
5. <font style="color:#080808;background-color:#ffffff;">添加域ID（gid）</font>

