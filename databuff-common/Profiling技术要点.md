profiling分配置管理，配置下发，数据上报和关联查询。



配置入口：部署配置-配置管理-实体监控-服务监控-应用配置

选择服务并开启Profiling开关（仅支持Linux操作系统）

On CPU: 线程花费在CPU上的时间

Off CPU：线程阻塞在I/O、锁、计时器、页交换等场景中的等待时间

内存 Profiling：内存模式采样需要jdk有调试符号(Debug Symbols)，如果应用部署在容器内，容器的基础镜像是类似alpine的精简jdk没有调试符号, 不支持内存采样

锁Profiling：Lock模式采样仅支持oracle-jdk 11及以上

![](https://cdn.nlark.com/yuque/0/2025/png/40720541/1747989495113-e36bbe24-a61f-4f04-a237-0bc1e2eb3b34.png)

配置完成后可导航到动态配置页面确认数据是否正常下发到zk  
[https://**************/databuff/advancedConfig?type=javaagent](https://**************/databuff/advancedConfig?type=javaagent) 密码：Ki5@tH0$vM

![](https://cdn.nlark.com/yuque/0/2025/png/40720541/1747989724763-8439a4d5-b65f-44cf-b1a1-336be35c96fa.png)

```json
{
  "profiling": {
    "enabled": true,
    "events": {
      "cpu": "8ms",
      "wall": "30ms"
    },
    "serviceInstances": [
      "**************",
      "**************"
    ]
  }
}
```



下发完成后next agent 每分钟会调用dts <font style="color:#080808;background-color:#ffffff;">/api/v1/agent/config接口获取上面配置并同步给指定服务的javaagent</font>

<font style="color:#080808;background-color:#ffffff;"></font>

**问题排查**

1. 由于配置下发和agent接收配置都依赖zk，所以优先确认同环境下zk的ip端口是否配置正确，是否可正常访问。
2. 数据是否已经上报？

自监控入口：数据报表-仪表盘-自监控看板-Databuff-profiling

[https://**************/databuff/dashboard?__ps=m](https://**************/databuff/dashboard?__ps=m)

3. jdk版本是否适配

![](https://cdn.nlark.com/yuque/0/2025/png/40720541/1747989989746-debe3019-258e-451a-9c73-df14eb722ba5.png)





查看入口：profiling没有一级入口，但是有多个子入口可以下钻到profiling主页。profiling本身就是基于agent的采样，并非每一个接口链路都会有。是否采集到和配置息息相关。

1. 应用性能-服务-接口详情-接口Profiling

![](https://cdn.nlark.com/yuque/0/2025/png/40720541/1747987458735-60507654-7f3f-463f-afe7-04d4f74dfd9a.png)

2. 应用性能-链路追踪-链路详情-包含有入口链路的span数据 有可能关联上（不是100%）

![](https://cdn.nlark.com/yuque/0/2025/png/40720541/1747987891172-e61ebd9a-f12f-4bac-8d03-8c187f9604c9.png)



Profiling详情页面

![](https://cdn.nlark.com/yuque/0/2025/png/40720541/1747987440599-acb924a9-5970-42ab-83fe-2fadd491a03b.png)



#### Profiling查询接口
```java
package com.databuff.webapp.profiling;

@Api(tags = "Profiling接口")
@RestController
@RequestMapping("/v3/profiling")
public class ProfilingControllerV3 {

    @Autowired
    private ProfilingStackService profilingStackService;

    @ApiOperation(value = "获取火焰图数据", notes = "根据传入的参数获取火焰图数据")
    @PostMapping("/flame")
    @DomainManagerRequired
    public Collection<ProfilingFlameNodeVO> flame(
            @ApiParam(value = "查询参数", required = true) @RequestBody ProfilingSearchParamsV2 params) {
        return profilingStackService.flameGraph(params);
    }

    @ApiOperation(value = "获取基础服务实例", notes = "根据服务ID和时间范围获取基础服务实例列表")
    @GetMapping("/service/getBasicServiceInstance")
    @DomainManagerRequired
    public List<String> getBasicServiceInstance(
            @ApiParam(value = "服务ID", required = true) @RequestParam String serviceId,
            @ApiParam(value = "开始时间", required = true) @RequestParam String fromTime,
            @ApiParam(value = "结束时间", required = true) @RequestParam String toTime) {
        return profilingStackService.getBasicServiceInstance(serviceId, fromTime, toTime);
    }

    @ApiOperation(value = "获取标签", notes = "根据传入的字段和时间范围获取标签列表")
    @GetMapping("/tags")
    @DomainManagerRequired
    public Map<String, List<String>> getTagsByFields(
            @ApiParam(value = "服务ID") @RequestParam String serviceId,
            @ApiParam(value = "字段列表") @RequestParam(required = false) Set<String> fields,
            @ApiParam(value = "开始时间") @RequestParam(required = false) String fromTime,
            @ApiParam(value = "结束时间") @RequestParam(required = false) String toTime) {
        return profilingStackService.getTagsByFields(fields, serviceId, fromTime, toTime);
    }
}
```

