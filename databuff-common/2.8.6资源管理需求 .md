------

# RUM 授权与采样需求文档

## 1. 商务合作方式

1. **订阅**

   - **付费形式**：按年续费，每年的月活/PV 额度需要重新购买。

   - 授权分配方式

     （需在后台选择）：

     - **月均**：将总额度按月度均分，例如 10 万月活/年 → 每月 0.83 万；
     - **年包**：年度总量一次给出，内部可按月/日分摊，但从商务视角是一年 10 万。

   - **不结转**：当月或当年未用完的配额，不会滚存到下个月/下一年。

2. **买断**

   - **付费形式**：一次性付费，后续仅维保；**整个平台使用期限**内拥有固定总量的月活/PV，用完即止。
   - **授权分配方式字段**置灰，不用选择月均/年包；实际可由系统**按月/日**从总量中扣减，或一次性大池子。
   - **示例**：3 年总共 10 万月活，若 1 年内就把 10 万用光，则余下 2 年无可用额度。

------

## 2. 授权月活 / 授权 PV

1. 统计周期

   - **RUM 平台**的月活采集，总是**自然月**刷新(每月 1 号 00:00)；
   - 跨月后，系统**清空**或让 Redis Key 过期，以便把设备视为“新设备”重新计数。
2. 月活定义

   - 同一设备在**同一个月**内第一次上报 → “新设备”，若被授权则“已用量”+1；
   - 下个月再次上报 → 再次被视为“新设备”，重新计数；
3. PV的授权限额

   - 与“月活”类似，只是同一设备id的限制改成同一页面id,  也可做月度/日度控制，超出后拒绝或进入“失败”统计。
4. 即便授权文件中标注「完整月 / 自然月」仅影响**授权本身**如何分摊配额，但**设备**在 RUM 上报时，每月 1 号 0 点都被视为“新月活”，重新判定授权。
5. 故若授权侧是完整月，但 RUM 依旧在**自然月**1 号做设备清零，需要后台在“扣减授权时”匹配对应周期即可。

**总结**：

- “月均 / 年包 / 买断”都是先算**日均**来应对不完整月份/年份；
- RUM 实际月活刷新=每月 1 号 0 点；在**多授权并行**时，只要设备**新**出现(跨月被重置)，就依次判断授权A/B/C 是否还有剩余量；若有则 `used++`；若都满了则失败。

------

## 3. RUM 资源管理：应用级限制

在 **WebApp** 后台针对**每个应用**，可以配置以下“自定义采集限制”参数：

1. **应用采样率**（0% ~ 100%）
   - 在**每日**0点后，对该应用当天出现的“新设备/新PV”进行**随机抽样**：若设置 10%，则有 10% 概率被判定为“允许”，90% 概率“拒绝”。
   - **逐日生效**：每天都会重置抽样状态；即**昨日被拒**的设备，**今天**仍可能再次抽到“允许”；或反之。
   - **注意**：存在其他限制（见下文）时，实际**当月**或**当日**统计下来的采样率往往会**低于**（或小于等于）配置值。
2. **日授权活跃设备数**
   - 每日最多允许 X 个“新设备/新PV”进入授权(放行)，其余超出的算“失败/拒绝”。
   - 同样在**每天 0 点**重置。
3. **月授权活跃设备数**
   - 每月最多允许 X 台新设备；超过后均失败/拒绝。
   - 每月 1 号（或完整月开始日）重新计数。
4. **日授权 PV 数 / 月授权 PV 数**（可选）
   - 与设备数类似，对 PV 有相应日/月上限；超限后拒绝。

**多项限制同时开启**时，只要有一项超限或抽样拒绝，就会导致该设备/请求失败 → 实际采样率**下降**。

------

## 4. 多授权并行

1. **多份授权**可能在同一时段同时生效（如“授权A(订阅) + 授权B(买断) + 授权C(订阅年包)”）。
2. **优先级**：平台可按顺序依次尝试扣减授权A → 若剩余有量则 “usedA++”；若 A 满 → 尝试 B；B 满 → 尝试 C……
3. **只要在某一授权成功** → 该设备算“成功”；若所有授权都满 → 失败。
4. 不同授权内部还受各自的**配额周期**影响：订阅月均是每月重置，买断不重置只扣总量，或后台把它均分到月度/日度。

------

## 5. 设备每日可重新授权判定

1. 采样率（如 10%）一般逐日执行：

   - 设备今天若抽中 → 放行；若抽不中 → 今日失败；**明天**依然有可能抽中成功。

2. 日限额(如 10 台)：

   - 今日若满 10 台之后出现的新设备，无论抽样结果如何，也会被拒；次日 0 点重置日限额，再给所有未授权设备新的机会。

3. 月限额(如 100 台)：

   - 若当月已用满，后面新设备就算抽中也无效 → 当月都拒绝；下个月(或完整月下一周期)再重新计数。

> 因此，在有“逐日”判定的模式下，设备可能**第一天失败、第二天成功**。月度统计时，只要它**当月任意一天**最终被允许采集，即可记为“成功设备”(若按唯一设备口径统计)。但若按“设备-日对”统计则会出现某天失败、某天成功的记录。

------

## 6. 本月采样率计算

1. **公式**

   

    $$ \text{本月采样率} = \frac{\text{本月已放行（允许）的新设备数}}{\text{本月所有新设备数（允许+失败）}} $$

   

   - “本月所有新设备数” 指该月第一次出现时无论成功/失败都计入分母；
   - “放行”指通过应用采样率判定 + 未达日/月限额 + 有可用授权。

2. **为何实际 < 配置采样率**

   - 当日限额、月限额、授权额度耗尽等情况发生时，设备即使“抽到”也被拒绝；
   - 因此实际成功数会比“理想抽样率×全部设备数”更低。

3. **每日重新判定的影响**

   - 若设备在某天失败，次日再次抽可能成功 → 在月度统计时，若仅看**唯一设备**，它只要最终一天成功就算成功；
   - 这在**实际**落地中可能“滞后”一些设备的成功时间，也会导致当月最终采样率在动态变化。

------

## 7. 订阅 vs 买断对月度采样的影响

1. **订阅**（月均/年包）
   - 每到月初(或年包分摊节点)自动重置 `used=0`，新增设备可继续放行 ，当月用完后即无可用额度。
   - 若月中用完本月配额 → 剩下天数该授权无效；若没有其他可用授权则设备失败。
   - **月活**照常每月都统计一次。
2. **买断**（一次性）
   - 不会自动“重置 used”；**总量**随使用逐渐消耗；用完前都可放行新设备(若未触发采样拒绝或其他限额)；用完后则全部失败。
   - **月活**依旧是每月统计新设备，但若需避免一次性耗尽买断额度，可在 RUM 资源管理里配置“月授权活跃设备数”等规则做**月度限流**。



## 8. 需求总结

1. 商务：

   - 订阅=每年续费，买断=一次付费；
   - 授权分配方式：月均或年包(仅对订阅可选)；
   
2. 授权控制：

   - 无授权=没有可扣减的额度=直接失败
   - 月活/PV 默认按自然月计数，跨月清零；买断则从总量中扣减。

3. 应用采样：

   - 按**逐日**抽样(如 10%)，并与日限额、月限额叠加；
   - 实际月度采样率低于配置的 10%/X%。
   
4. 多授权并行：

   - 同一设备只要在任一授权成功扣减 → 算成功；所有授权无量 → 失败；
   - 各授权的配额周期(订阅 / 买断)在后台独立维护。
   
5. 每日重新判定：

   - 如果本月已经成功授权，那就是旧设备，不需要重新判定。
   - 昨日失败的设备，今天可再次抽样；日限额也重置；
   - 月限额累计到当月上限则拒绝；下个月再恢复。
   
6. 本月采样率：

   - RUM 平台按“(本月成功设备) / (本月所有新设备)”实时统计显示；
- 受采样率设置 + 日/月授权活跃数 + 多授权可用量等共同影响。

这份文档**整合了**之前所有讨论的关键点，包括商务模式、授权分配、买断/订阅差异、日/月限额、应用采样率、每日重新判定机制、多授权并行等。并强调了**采样率是“逐日”判定**，最终月度统计结果常**低于**设定值。





下面是一份**在原先需求文档基础上**，**单独强化“多授权并行”\**部分的\**详细说明**。其余章节可与先前文档一致，这里仅着重补充**多授权**的运作规则、与日/月限额及采样率等各类约束的交互，以及可能出现的冲突或特殊情况。

------

# 多授权并行：详细需求与规则

## 1. 何为多授权并行

- **场景**：同一应用在同一时间段内拥有多条有效授权（如**授权A**：订阅月均、**授权B**：买断、**授权C**：订阅年包…）。
- **目标**：当应用有“新设备”或“新PV”上报时，只要能从任意授权里扣减成功，都算“授权通过”。若所有授权都无剩余(或受限等原因无法扣减)，则设备/请求判定“失败/未授权”。

> 与单一授权相比，“多授权并行”在**扣减逻辑**和**限额交互**上更为复杂，需要明确优先级、每日/月度限额叠加处理等细节。

------

## 2. 多授权运作方式

### 2.1 优先级与分配策略

1. **顺序尝试**（典型做法）
   - 后台配置一份“授权优先级”列表，如：先用**授权A** → 若已满或不可用，再尝试**授权B** → … 直到找到可扣减的授权；
   - 一旦在某个授权上 `used++` 成功，**立即**结束分配，判定本次“成功”。
   - **好处**：避免一台设备重复消耗多份授权配额，易于理解、实现简单；
   - **潜在问题**：如果授权A 月度限额很小，却总被优先尝试，可能导致很多设备都在A上失败，再转到B，或浪费性能；具体看商务需求。
2. **并行拆分**（不常见）
   - 也有业务希望“同一个设备可以同时用两个授权”，但这通常会**重复扣费**，大部分产品并不采用。

> **本需求**默认采纳“顺序尝试”模式：找到首个可用的授权就“成功”，其余授权**不再扣减**。

### 2.2 各授权自身的周期与限额

1. 订阅（月均/年包）
   - 在授权周期内(如 2024/6/1~2025/6/1)，每到月初(或年包分摊后的月初)重置其 `used=0`；
   - 若本月分配完后(如月均 10万月活已用光)，则无法再扣减此授权，需尝试下一个授权。
2. 买断
   - 一次性总量，不会按月自动重置 `used`；只要总量内还够，就可扣减；用完则无法再扣减。
   - 也可能在后台配置“买断 10万总量拆成每月1万”以做技术限流，但**本质**仍是一份买断授权。

### 2.3 与应用采样率、日/月限额的关系

- 设备/请求先经过应用级限制：
  1. 检查**应用采样率**(如 10%)：若抽到“失败”，则直接不授权；若抽到“成功”，进入下一步
  2. 检查**日限额**(如每日最多 100 个新设备)：若已满，判定失败；否则继续
  3. 检查**月限额**(如本月最多 1000 个新设备)：若已满，判定失败；否则继续
  4. **若以上都通过** → 再去多授权分配
- 多授权分配：
  1. **授权A**：若其本月(或总量)未满 → 扣减 `usedA++` → 成功结束；若满 → 尝试**授权B**
  2. **授权B**：若 B 未满 → `usedB++`，成功结束；若满 → 尝试**授权C**
  3. … 直到所有授权都无法扣减 → 判定最终失败
- **结果**：如果设备最终使用到某个授权，就在**当日/当月 allowedSet**(或计数器)里+1；否则放入 failed 统计。
- **注意**：只要**任意授权**成功，即可算“放行”，不会二次扣其他授权。



------



### 2.4 每条授权的「可用量」计算

1. **月均**
   - 按**起止日期**天数 → 先算**日均**（授权总量 ÷ 总天数），再乘以**当月天数**(该当月若是**完整月**以起始日期对应日为界，若是**自然月**以1号~月底)向上取整，得到该月可用量。
   - 若授权期限非整月(如 2025/1/15 ~ 2026/1/10)，每个**完整月**或**不完整月**都需按实际天数去算。
2. **年包**
   - 视为“完整年”池，但若授权实际跨**不整年**（如 2025/1/2 ~ 2026/6/30 超过1年半），也先算**日均**= (总量 ÷ 总天数)。
   - **每年**(或每个整年区段)的配额 = 日均 ×(该年内的天数) 向上取整；若超过一年仍剩余时，再算第2年（或余下天数）。
   - 例如：2025/1/2 ~ 2026/6/30 → 先算2025/1/2 ~2026/1/2 (1年)配额，再算 2026/1/3~6/30(半年)的配额。
3. **买断**
   - **默认**一次性“总量= X”，不做月度/年度拆分；用完即止。
   - 若要避免在某月用太多，可在 RUM 资源管理中配置“月/日限额”，或技术上同样用**日均**公式对买断做拆分限制（非默认做法）。

> **注意**：以上仅是计算“**授权本身**”的月/年可用量，用于在后台判断 `used < limit`。真正**设备月活**何时重置，取决于 **RUM 模块月活刷新**(2. 授权月活 / 授权 PV)。



## 3. 可能的冲突与特殊情况

### 3.1 不同授权“刷新日”不一致

- **授权A**：自然月(1号~月底)
- **授权B**(年包)：可能在每月 15 号重置(完整月)
- **授权C**(买断)：从不重置，只减少总量

**场景**：6/1 ~ 6/14，B 仍处于上一个整月周期(5/15~6/14)；6/15 B 重置 `used=0`；而 A 却在 6/1~6/30 都是一整月周期。

- 影响：
  1. 当 B 旧周期已满(6/1~6/14这段)时，可能都要用 A → A 用量上升；
  2. 6/15 B 新周期开始 → B 有大量空余，但 A 也可能在 6/1~6/14已被大量设备消耗…
  3. 对设备而言，系统仍是先尝试 A → 若 A 满，再尝试 B；只是在后台，这两个授权的时间分割点不一致。
- **无**必然冲突：只要系统**记录**每个授权的“当前剩余/used”并按**优先级(导入时间)**尝试即可。

### 3.2 同一天内，不同授权日限额 or限制

- 有些授权(订阅)可能在**日级**也做拆分(如每天 3000 设备)，买断不做日拆分；
- 如果设备到来时，授权A 日度限额已满 → 尝试 B → B 仍有量 → 扣减成功；
- 最终仍是只要找到一个可行授权即可。

### 3.3 多授权 + 应用级“月限额”

- 应用级**月限额**1000 → 意味着**整**个应用一个月最多放行1000台新设备；
- 即使授权A+B+C 叠加后理论上能放行 1万设备，也会被**应用月限**截断在 1000；
- 不会出现“双向计数”冲突，因为应用级限制先挡住一部分设备；只留下<=1000 的新设备进入授权分配流程。

### 3.4 设备在同一个月内能否“切换”授权？

- **默认**：一台设备月内被授权成功后，就**不**再重复扣减别的授权；它的“授权归属”本月内固定。
- 若下一次(同月)上报再来，还属同一个“已授权”设备，无需再次扣减任何授权；

### 3.5 超大并发 & race condition

- 当大量设备瞬时并发请求，多授权“先A后B”的顺序性需要**原子操作**(Lua脚本/分布式锁)才能保证**用量**不超；
- 若没处理好，可能出现“多线程都认为 A 还有余量”并同时扣减 -> 超标；
- 这是**技术实现**的细节，但需求层面要知道并发带来的一些一致性挑战。

------

## 4. 对最终月度采样率的影响

1. **只要任意授权能放行** → 设备算“成功”；
2. 多授权并行下，**理论**上更容易“放行”设备(除非都满了)；
3. 这可能**提高**在同等“应用采样率 + 日限额”情况下的成功数量，从而**增加**本月采样率；
4. 但**如果多个授权都小**(或都挤满)，依旧会出现大量失败 -> 降低采样率。
5. 总之，**多授权并行**是把不同商务模式的额度资源“叠加”，但始终受“应用采样率/日限/月限”这三大限制前置。

------

## 5. 总结 & 需求要点

1. 多授权并行

   在本系统中遵循“顺序优先 \+ 找到即可成功”的策略：

   - **优先级**：授权A → B → C → …
   - **设备只在一个授权中计数**(used++)

2. 交互：

   - **应用采样率、日限额、月限额**等**先行过滤** → 通过后再尝试多授权扣减；
   - 若所有授权可使用量都用完了，或者当前日期不在所有的授权范围内，导致找不到授权 -> 该设备或页面本次判定失败。

3. 冲突处理：

   - 不同授权的刷新日不一致不算冲突，只要记录各自 used/limit 并顺序扣减即可；
   - 应用级限额(或采样率)可强行限缩放行数，不会与多授权产生矛盾，只是减少可分配机会；
   - 若想让设备月内“换”授权，需要复杂逻辑(多数场景下不做)。

4. 采样率：

   - 多授权并行可能提升放行数，但依旧会因限额/抽样不足而降低实际效果；
   - 最终月度采样率依旧由**成功 / (成功+失败)**在统计层计算，与是否多授权无直接冲突，只是增加可放行渠道而已。

> **结论**：在**所有规则**（应用采样率、日/月限、授权周期/额度差异、并发一致性）下，多授权并行方式**可行**，也不会破坏既有逻辑；只要明确“先后顺序”与“只需一次成功即放行”的原则，就能与现有系统无缝对接。