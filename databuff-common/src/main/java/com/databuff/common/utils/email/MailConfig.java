package com.databuff.common.utils.email;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * @author:TianMing
 * @date: 2021/12/20
 * @time: 11:37
 */
@Data
@Getter
@Setter
public class MailConfig {

    private String smtpHost;
    /** 阿里非ssl-smtp:25或80  阿里ssl-smtp端口:465 */
    private int smtpPort;
    /**默认不开启ssl加密*/
    private Boolean useSsl;

    /** 发件人的账号 和 密码*/
    private String user;
    //发件人昵称
    private String nick;
    private String password;
    private String code;

    public MailConfig(String smtpHost, int smtpPort, Boolean useSsl, String user,String password) {
        this.smtpHost = smtpHost;
        this.smtpPort = smtpPort;
        this.useSsl = useSsl;
        this.user = user;
        this.password = password;
    }
    public MailConfig(String smtpHost, int smtpPort, Boolean useSsl, String user,String nick, String password) {
        this.smtpHost = smtpHost;
        this.smtpPort = smtpPort;
        this.useSsl = useSsl;
        this.user = user;
        this.nick = nick;
        this.password = password;
    }

    public MailConfig() {}
}
