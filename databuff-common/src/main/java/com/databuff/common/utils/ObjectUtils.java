package com.databuff.common.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

import static com.databuff.common.utils.TimeUtil.formatLongToString;

@Slf4j
public class ObjectUtils {

    /**
     * 通过key查找对象中的值
     * 查找到第一个满足条件的value并返回
     * @param obj 对象
     * @param key 要查找的键
     * @param deep 当前查找深度
     * @param maxDeep 最大查找深度
     * @return 查找到的值，如果未找到则返回null
     */
    public static Object findFirstValueByKey(Object obj, String key, int deep, int maxDeep) {
        if (deep > maxDeep) {
            log.error("findValueByKey deep > maxDeep");
            return null;
        }
        if (obj == null || key == null) {
            log.error("findValueByKey obj or key is null");
            return null;
        }

        if (obj instanceof Map) {
            return ((Map) obj).get(key);
        }
        // 查找深度+1
        deep = ++deep;
        try {
            Field[] fields = obj.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(obj);
                if (value == null) {
                    continue;
                }
                if ((value instanceof String || value instanceof Number) && (field.getName().equals(key))) {
                    return value;
                } else if ((value instanceof Collection) && (field.getName().equals(key))) {
                    try {
                        Collection<String> stringCollection = (Collection<String>) value;
                        return stringCollection;
                    } catch (ClassCastException e) {
                        e.printStackTrace();
                    }
                } else if (value instanceof Map) {
                    final Object result = findFirstValueByKey(value, key, deep, maxDeep);
                    if (result == null) {
                        continue;
                    }
                    return result;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 在给定的对象中查找第一个与指定键匹配的单一值（字符串或数字）。
     * 该方法支持递归查找，且可以控制递归的最大深度。
     *
     * @param obj     要查找的对象，可以是Map或普通Java对象
     * @param key     要查找的键
     * @param deep    当前递归深度
     * @param maxDeep 最大递归深度
     * @return 返回与键匹配的第一个单一值（字符串或数字），如果未找到或超出最大深度则返回null
     */
    public static Object findFirstSingleValueByKey(Object obj, String key, int deep, int maxDeep) {
        // 检查当前递归深度是否超过最大深度
        if (deep > maxDeep) {
            log.error("findValueByKey deep > maxDeep");
            return null;
        }
        // 检查输入对象或键是否为null
        if (obj == null || key == null) {
            log.error("findValueByKey obj or key is null");
            return null;
        }
        // 增加递归深度
        deep = ++deep;

        // 如果对象是Map类型，则处理Map中的键值对
        if (obj instanceof Map<?, ?>) {
            Map<?, ?> map = (Map<?, ?>) obj;
            final Optional<?> first = map.keySet().stream().findFirst();
            // 检查Map的第一个键是否为String类型
            if (!first.isPresent() || !(first.get() instanceof String)) {
                return null;
            }
            // 获取指定键对应的值
            Object value = map.get(key);
            if (value == null) {
                return null;
            }
            // 如果值是字符串或数字，则直接返回
            if ((value instanceof String || value instanceof Number)) {
                return value;
            } else if ((value instanceof Collection)) {
                // 如果值是集合类型，则返回null
                return null;
            } else if (value instanceof Map) {
                // 如果值是Map类型，则递归查找
                final Object result = findFirstSingleValueByKey(value, key, deep, maxDeep);
                if (result == null) {
                    return null;
                }
                return result;
            }
        }

        try {
            // 如果对象不是Map类型，则通过反射获取对象的getter方法
            Method[] methods = obj.getClass().getDeclaredMethods();
            for (Method method : methods) {
                // 检查方法是否为getter方法
                if (method.getName().startsWith("get") && method.getParameterCount() == 0) {
                    method.setAccessible(true);
                    // 调用getter方法获取属性值
                    Object value = method.invoke(obj);
                    if (value == null) {
                        continue;
                    }
                    // 获取属性名
                    String fieldName = method.getName().substring(3);
                    fieldName = Character.toLowerCase(fieldName.charAt(0)) + fieldName.substring(1);
                    // 如果属性值与键匹配且为字符串或数字，则返回该值
                    if ((value instanceof String || value instanceof Number) && fieldName.equals(key)) {
                        return value;
                    } else if (value instanceof Map) {
                        // 如果属性值是Map类型，则递归查找
                        final Object result = findFirstSingleValueByKey(value, key, deep, maxDeep);
                        if (result == null) {
                            continue;
                        }
                        return result;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }



    public static String getReplacementValue(String varValue, Object value, JSONObject config) {
        if (varValue == null || value == null) {
            return null;
        }

        final JSONObject transMap = config.getJSONObject(varValue);
        if (value instanceof Long) {
            value = formatLongToString((Long) value);
        } else if (value instanceof String && transMap != null) {
            value = transMap.get(value);
        } else if (value instanceof Integer && transMap != null) {
            value = transMap.get(value);
        } else if (value instanceof Collection && transMap != null) {
            List<String> tmp = new ArrayList<>();
            for (Object o : (Collection) value) {
                if (o instanceof String) {
                    final Object v = transMap.get(o);
                    if (v != null) {
                        tmp.add(v.toString());
                    }
                }
            }
            value = String.join(",", tmp);
        } else if (value instanceof Collection && "message".equals(varValue)) {
            value = String.join("\n", (Collection) value);
        } else if (transMap != null) {
            value = transMap.get(value);
        }
        // 对持续时间字段做特殊处理
        if ("duration".equals(varValue)) {
            return String.format(" %s秒 ", value == null ? "未知" : value.toString());
        }
        return String.format(" %s ", value == null ? "未知" : value.toString());
    }
}