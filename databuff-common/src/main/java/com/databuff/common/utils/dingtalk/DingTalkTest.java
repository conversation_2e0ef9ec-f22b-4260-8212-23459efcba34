package com.databuff.common.utils.dingtalk;

import com.databuff.common.constants.Constant;
import com.databuff.common.utils.DateUtils;
import org.apache.commons.codec.digest.DigestUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @author:TianMing
 * @date: 2021/12/17
 * @time: 14:35
 */
public class DingTalkTest {

    public static void main(String[] args) {


        String appKeyAppsecret = Constant.Notify.appkey + "_" + Constant.Notify.appsecret;
        String redisKey = DigestUtils.md5Hex(appKeyAppsecret);
        String token = DingTalkUtils.getAccessToken(Constant.Notify.appkey, Constant.Notify.appsecret);

        System.out.println("token: " + token);

//        String token = "119ba9f6329332beb2c205a892acec26";
//        119ba9f6329332beb2c205a892acec26
//        if (.exists(redisKey)){
//            token = JedisUtil.getObject(redisKey).toString();
//        }else{
//            token = DingTalkUtils.getAccessToken(MetricsConstants.appkey,MetricsConstants.appsecret);
//            int expiresIn = 7200;
//            //access_token的有效期为7200秒（2小时），有效期内重复获取会返回相同结果并自动续期，过期后获取会返回新的access_token。
//            //这里将过去时间减去200秒避免这边没过期，钉钉服务器已经过期了。
//            JedisUtil.setObject(redisKey,token,expiresIn>200?(expiresIn-200):1);
//        }

        System.out.println(token);
//        0238000432833397
        String userId = DingTalkUtils.getUserIdByMobile(token,"15824145588");
//        String userId2 = DingTalkUtils.getUserIdByMobile(token,"15658100218");
//        String userId3 = DingTalkUtils.getUserIdByMobile(token,"15658100218");
//        String userId = DingTalkUtils.getUserIdByMobile(token,"18867508958");
        System.out.println("userId: " + userId);
        List<String> useridList = new ArrayList<>();
        useridList.add(userId);
//        useridList.add("123123");
//        useridList.add("12312412");
//        useridList.add(userId2);
//        useridList.add(userId3);
//        useridList.add(userId4);
        System.out.println(userId);
        NotifyDingTalkConfig config = new NotifyDingTalkConfig();
        config.setDingAgentId(Constant.Notify.agentId);
        List<String> us = new ArrayList<>();
        us.add("18867508958");
//        us.add("15658100218");
//        us.add("15168274770");
        StringBuilder content = new StringBuilder();
        content.append("**Databuff 温馨提醒:**");
        content.append("\n\n");//换行
        content.append("------"); //下划线
        content.append("\n\n");//换行

        content.append("测试，测试机器人");
        content.append("\n\n &nbsp;");//多个\n只能换一行，加上一个空格即可辅助完成换行
        content.append("\n\n");
        content.append("<br>").append(DateUtils.getDate("yyyy-MM-dd HH:mm:ss"));

        String title = "Databuff-测试";
        String text = content.toString();
        String textType = "markdown";

        String secret = "SEC0b9f33b51d5b3df784b8c4e96d614503950fb00317b4259548c8066a38eb2a67";
        String webhookUrl = "https://oapi.dingtalk.com/robot/send?access_token=9eb61b88f32f96a686c582c3642f37c4827dc5476dbe330da5ae8184cd007c9c";


        try {
            DingTalkUtils.sendNoticeMessage(config, token, title, text, textType, useridList);
//            DingTalkUtils.sendRobotMessage(secret, webhookUrl, title, text, textType, us);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}