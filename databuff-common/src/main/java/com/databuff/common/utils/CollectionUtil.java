package com.databuff.common.utils;

import com.alibaba.fastjson.JSON;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2022/6/29
 * @Description:
 */
public class CollectionUtil {

    /**
     * 取差集（取存在一个集合中，但不存在于另外一个集合中的元素）
     *
     * @return 存在A集合，不存在B集合的数据
     */
    public static Set<String> differenceSet(Set<String> setA, Set<String> setB) {
        Set<String> resSet = new HashSet<>(setA);
        resSet.removeAll(setB);
        return resSet;
    }

    /**
     * 取差集（取存在一个集合中，但不存在于另外一个集合中的元素）
     *
     * @return 存在A集合，不存在B集合的数据
     */
    public static Set differenceSet(Collection setA, Collection setB) {
        Set resSet = new HashSet<>(setA);
        resSet.removeAll(setB);
        return resSet;
    }

    /**
     * 取交集
     *
     * @return A集合，B集合都存在的数据
     */
    public static Set intersection(Collection setA, Collection setB) {
        Set resSet = new HashSet<>(setA);
        resSet.retainAll(setB);
        return resSet;
    }

    public static void main(String[] args) {
        Set<String> setA = new HashSet<>(Arrays.asList("aaa", "bbb", "ccc"));

        Set<String> setB = new HashSet<>(Arrays.asList("aaa", "bbb", "ddd"));

        Set<String> setC = new HashSet<>();

        System.out.println(JSON.toJSONString(differenceSet(setA, setB)));
        System.out.println(JSON.toJSONString(differenceSet(setB, setA)));
        System.out.println(JSON.toJSONString(differenceSet(setC,setA)));
        System.out.println(JSON.toJSONString(differenceSet(setA,setC)));
        System.out.println(JSON.toJSONString(differenceSet(setC,setC)));
    }
}
