package com.databuff.common.tsdb.adapter;

import com.databuff.common.tsdb.pool.TSDBConnectPool;

public class DatabaseAdapterFactory {
    public DatabaseAdapter getAdapter(TSDBConnectPool tsdbConnectPool) {
        if (tsdbConnectPool == null) {
            return null;
        }
        try {
            switch (tsdbConnectPool.getTSDBClient().getDatabaseType()) {
                case MOREDB:
                    return new MoreDBAdapter(tsdbConnectPool);
                case OPENGEMINI:
                    return new OpenGeminiAdapter(tsdbConnectPool);
                default:
                    throw new IllegalArgumentException("Unsupported Database");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
