package com.databuff.common.tsdb.config;

import com.databuff.common.tsdb.TSDBOperateUtil;
import com.databuff.common.tsdb.TSDBReader;
import com.databuff.common.tsdb.TSDBWriter;
import com.databuff.common.tsdb.factory.TSDBOperatorFactory;
import com.databuff.common.tsdb.pool.TSDBConnectPool;
import com.databuff.common.tsdb.pool.TSDBPoolConfig;
import com.databuff.common.tsdb.pool.TSDBPoolFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * TSDB Bean配置类
 * 负责创建和管理TSDB相关的Bean
 */
@Slf4j
@Configuration
public class TSDBBeanConfig {

    /**
     * 创建TSDBRefreshScopeConfig Bean
     */
    @Bean
    public TSDBRefreshScopeConfig tsdbRefreshScopeConfig() {
        return new TSDBRefreshScopeConfig();
    }

    /**
     * 创建TSDB连接池
     */
    @Bean(name = "tsdbConnectPool")
    @Primary
    public TSDBConnectPool tsdbConnectPool(TSDBRefreshScopeConfig config) {
        //这里一定要加这个设置，不然springboot启动会报已经注册了某个bean的错误
        config.setJmxEnabled(false);
        TSDBPoolFactory tsdbPoolFactory = TSDBPoolFactory.builder()
                .tsdb(config.getDb())
                .url(config.getUrl())
                .tsdbApi(config.getApi())
                .user(config.getUser())
                .pwd(config.getPassword())
                .queryTimeout(config.getQueryTimeout())
                .duration(config.getDuration())
                .shard(config.getShard())
                .replication(config.getReplication())
                .interval(config.getInterval())
                .jmxEnabled(false)
                .build();

        log.info("创建TSDB连接池成功, db: {}, url: {}", config.getDb(), config.getUrl());
        // 使用原始类型创建对象，确保类型兼容性
        TSDBConnectPool pool = new TSDBConnectPool(tsdbPoolFactory, config);
        // 确保对象已经初始化
        try {
            // 测试连接池是否正常工作
            pool.getTSDBClient();
            log.info("测试TSDB连接池成功");
        } catch (Exception e) {
            log.warn("测试TSDB连接池失败，但仍然创建连接池对象", e);
        }
        return pool;
    }

    /**
     * 创建TSDB操作工具
     * 支持多写入器
     */
    @Bean(name = "tsdbOperateUtil")
    @Primary
    public TSDBOperateUtil tsdbOperateUtil(TSDBConnectPool tsdbConnectPool, TSDBRefreshScopeConfig tsdbRefreshScopeConfig) {
        return new TSDBOperateUtil(tsdbConnectPool, tsdbRefreshScopeConfig);
    }

    /**
     * 创建TSDB操作工厂
     */
    @Bean
    public TSDBOperatorFactory tsdbOperatorFactory(TSDBConnectPool tsdbConnectPool, TSDBRefreshScopeConfig tsdbRefreshScopeConfig) {
        return new TSDBOperatorFactory(tsdbConnectPool, tsdbRefreshScopeConfig);
    }

    /**
     * 创建读操作实例
     */
    @Bean(name = "tsdbReader")
    public TSDBReader tsdbReader(TSDBOperatorFactory tsdbOperatorFactory) {
        return tsdbOperatorFactory.createReader();
    }

    /**
     * 创建写操作实例
     */
    @Bean(name = "tsdbWriter")
    @Primary
    public TSDBWriter tsdbWriter(TSDBOperatorFactory tsdbOperatorFactory) {
        return tsdbOperatorFactory.createWriter();
    }

    /**
     * 创建连接池配置
     * 配置连接池的各种参数，包括大小限制、验证策略和驱逐策略
     *
     * @param config 配置对象
     * @return 配置好的连接池配置对象
     */
    private TSDBPoolConfig createPoolConfig(TSDBRefreshScopeConfig config) {
        TSDBPoolConfig tsDBPoolConfig = new TSDBPoolConfig();

        // 池大小配置
        tsDBPoolConfig.setMaxTotal(config.getMaxTotal());
        tsDBPoolConfig.setMaxIdle(config.getMaxIdle());
        tsDBPoolConfig.setMinIdle(config.getMinIdle());
        tsDBPoolConfig.setMaxWaitMillis(config.getMaxWaitMillis());

        // 验证配置
        tsDBPoolConfig.setTestOnBorrow(config.isTestOnBorrow()); // 借用对象前验证
        tsDBPoolConfig.setTestWhileIdle(config.isTestWhileIdle()); // 空闲时验证

        // 驱逐配置
        tsDBPoolConfig.setSoftMinEvictableIdleTimeMillis(config.getSoftMinEvictableIdleTimeMillis());
        tsDBPoolConfig.setTimeBetweenEvictionRunsMillis(config.getTimeBetweenEvictionRunsMillis());

        // 其他配置
        tsDBPoolConfig.setJmxEnabled(false);

        log.info("创建连接池配置: maxTotal={}, maxIdle={}, minIdle={}, testOnBorrow={}, testWhileIdle={}",
                config.getMaxTotal(), config.getMaxIdle(), config.getMinIdle(),
                config.isTestOnBorrow(), config.isTestWhileIdle());

        return tsDBPoolConfig;
    }
}
