package com.databuff.common.tsdb.executor;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.annotation.LogExecutionTime;
import com.databuff.common.tsdb.adapter.DatabaseAdapter;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.model.AggFun;
import com.databuff.common.tsdb.model.TSDBDatabaseInfo;
import com.databuff.common.tsdb.model.TSDBPoint;
import com.databuff.common.tsdb.model.TSDBResultSet;
import com.databuff.common.tsdb.wrapper.DatabaseWrapper;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class TSDBExecutor implements AutoCloseable {
    private DatabaseAdapter adapter;

    public TSDBExecutor(DatabaseAdapter adapter) {
        this.adapter = adapter;
    }

    @LogExecutionTime(tag = {"query", "databaseName"}, logOnException = true)
    public TSDBResultSet execute(DatabaseWrapper dbClient, String query, String databaseName, QueryBuilder builder) {
        try {
            return adapter.executeQuery(dbClient, query, databaseName, builder);
        } catch (Exception e) {
            log.error("未知异常发生在 TSDB 查询中，database: {}, query: {}", databaseName, query, e);
            throw new RuntimeException("未知异常发生在 TSDB 查询中: " + query, e);
        }
    }


    public Map<String, Set<String>> executeShowTagValues(DatabaseWrapper dbClient, String query, String databaseName) {
        // 调用适配器执行查询，并返回查询结果
        return adapter.executeShowTagValues(dbClient, query, databaseName);
    }

    /**
     * 构建完整的sql
     *
     * @param build
     * @return
     */
    public String buildFullSql(QueryBuilder build) {
        // 调用适配器执行翻译sql，并返回sql
        return adapter.buildFullSql(build);
    }

    /**
     * show tag values
     *
     * @param build
     * @return
     */
    public Map<String, String> buildShowTagValueSql(DatabaseWrapper dbClient, QueryBuilder build) {
        // 调用适配器执行翻译sql，并返回sql
        return adapter.buildShowTagValueSql(dbClient, build);
    }

    public String buildWhereSql(QueryBuilder build) {
        // 调用适配器执行翻译sql，并返回sql
        return adapter.buildWhereSql(build);
    }

    public boolean createDatabase(DatabaseWrapper dbClient, TSDBDatabaseInfo databaseInfo) {
        // 调用适配器执行创建数据库，并返回结果
        return adapter.createDatabase(dbClient, databaseInfo);
    }

    public boolean writePoint(DatabaseWrapper dbClient, String databaseName, TSDBPoint tsdbPoint) {
        // 调用适配器执行写入数据，并返回结果
        return this.writePoints(dbClient, databaseName, Arrays.asList(tsdbPoint));
    }

    public boolean writePoints(DatabaseWrapper dbClient, String databaseName, List<TSDBPoint> tsdbPoints) {
        // 调用适配器执行写入数据，并返回结果
        return adapter.writePoints(dbClient, databaseName, tsdbPoints);
    }

    public TSDBResultSet apmPercentageLatency(DatabaseWrapper dbClient, QueryBuilder builder) {
        // 调用适配器执行查询，并返回查询结果
        return adapter.apmPercentageLatency(dbClient, builder);
    }

    public TSDBResultSet executeQuery(DatabaseWrapper dbClient, QueryBuilder builder, AggFun tsAgg, AggFun valAgg, AggFun topAgg, JSONObject otherParam) throws Exception {
        // 调用适配器执行查询，并返回查询结果
        return adapter.executeQuery(dbClient, builder, tsAgg, valAgg, topAgg, otherParam);
    }

    public Map<String, Object> getConfigs() {
        //获取相关配置
        return adapter.getConfigs();
    }

    public void close() throws Exception {
        adapter.close();
    }
}
