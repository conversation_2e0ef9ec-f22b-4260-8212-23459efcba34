package com.databuff.common.tsdb.impl;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.tsdb.TSDBReader;
import com.databuff.common.tsdb.adapter.DatabaseAdapterFactory;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.builder.QueryBuilderX;
import com.databuff.common.tsdb.config.TSDBRefreshScopeConfig;
import com.databuff.common.tsdb.executor.TSDBExecutor;
import com.databuff.common.tsdb.model.*;
import com.databuff.common.tsdb.pool.TSDBConnectPool;
import com.databuff.common.tsdb.wrapper.DatabaseWrapper;
import com.databuff.common.utils.OtelMetricUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.databuff.common.constants.MetricName.*;
import static com.databuff.common.tsdb.model.WhereOp.*;

/**
 * TSDB读取操作实现类
 * 实现TSDBReader接口
 */
@Slf4j
@Component
public class TSDBReaderImpl implements TSDBReader {

    private TSDBExecutor tsdbExecutor;
    private TSDBConnectPool pool;

    @Autowired
    private TSDBRefreshScopeConfig tsdbConfig;

    public TSDBReaderImpl() {
    }

    @Autowired(required = false)
    public TSDBReaderImpl(TSDBConnectPool tsdbConnectPool) {
        this.pool = tsdbConnectPool;
        this.tsdbExecutor = new TSDBExecutor(new DatabaseAdapterFactory().getAdapter(tsdbConnectPool));
    }

    /**
     * 带有TSDBConfig参数的构造函数
     * 用于手动注入TSDBConfig
     */
    public TSDBReaderImpl(TSDBConnectPool tsdbConnectPool, TSDBRefreshScopeConfig tsdbRefreshScopeConfig) {
        this.pool = tsdbConnectPool;
        this.tsdbExecutor = new TSDBExecutor(new DatabaseAdapterFactory().getAdapter(tsdbConnectPool));
        this.tsdbConfig = tsdbRefreshScopeConfig;
    }

    @Override
    public String buildFullSql(QueryBuilder builder) {
        return tsdbExecutor.buildFullSql(builder);
    }

    @Override
    public String buildWhereSql(QueryBuilder builder) {
        return tsdbExecutor.buildWhereSql(builder);
    }

    @Override
    public TSDBResultSet executeQuery(QueryBuilder builder) {

        String sql = tsdbExecutor.buildFullSql(builder);
        // 根据配置决定是否打印SQL
        if (tsdbConfig != null && tsdbConfig.isSqlPrintEnabled()) {
            log.info("执行TSDB查询: {}", sql);
        }
        checkIntervalValid(builder, sql);
        String databaseName = builder.getDatabaseName();
        Map<String, String> tags = new HashMap<>();
        tags.put("database", databaseName);
        long startTime = System.currentTimeMillis();
        DatabaseWrapper dbClient = null;
        try {
            dbClient = pool.getTSDBClient();
            long getClientTime = System.currentTimeMillis();
            OtelMetricUtil.logHistogram(TSDB_CONNECTION_COST, tags, getClientTime - startTime);
            if (dbClient == null) {
                throw new RuntimeException("无法获取数据库连接");
            }
            return tsdbExecutor.execute(dbClient, sql, databaseName, builder);
        }catch (Exception e) {
            log.error("查询数据失败,databaseName:{},sql:{},error:", databaseName, sql, e);
            OtelMetricUtil.logCounter(TSDB_QUERY_FAILED, tags, 1);
            throw new RuntimeException("查询数据失败", e);
        } finally {
            if (dbClient != null) {
                pool.releaseConnection(dbClient);
            }
            long endTime = System.currentTimeMillis();
            OtelMetricUtil.logHistogram(TSDB_QUERY_COST, tags, endTime - startTime);
        }
    }

    private void checkIntervalValid(QueryBuilder builder, String sql) {
        Integer interval = builder.getInterval();
        if (interval == null || interval <= 0 || interval > 60) {
            return;
        }
        long startTime = 0;
        long endTime = 0;
        List<Where> wheres = builder.getWheres();
        for (Where where : wheres) {
            if (where == null || where.getField() == null) {
                continue;
            }
            if (where.getField().equals("time")) {
                WhereOp whereOp = where.getOperator();
                if (whereOp.equals(GTE) || whereOp.equals(GT)) {
                    startTime = (long) where.getValue();
                } else if (whereOp.equals(LT) || whereOp.equals(LTE)) {
                    endTime = (long) where.getValue();
                }
            }
        }
        long diff = endTime - startTime;
        if (diff > 3 * 24 * 3600000) {
            RuntimeException r = new RuntimeException("查询粒度太细 " + sql);
            r.printStackTrace();
            throw r;
        }
    }

    @Override
    public List<TSDBSeries> executeQueryForAllGroups(QueryBuilder builder) {
        TSDBResultSet resultSet = executeQuery(builder);
        if (resultSet == null || CollectionUtils.isEmpty(resultSet.getResults())) {
            return Collections.emptyList();
        }
        List<TSDBSeries> seriesList = resultSet.getResults().get(0).getSeries();
        return seriesList == null ? Collections.emptyList() : seriesList;
    }

    @Override
    public TSDBSeries executeQueryForOneGroup(QueryBuilder builder) {
        TSDBResultSet resultSet = executeQuery(builder);
        if (resultSet == null || CollectionUtils.isEmpty(resultSet.getResults())) {
            return null;
        }
        List<TSDBSeries> seriesList = resultSet.getResults().get(0).getSeries();
        if (seriesList == null || seriesList.size() == 0) {
            return null;
        }
        return seriesList.get(0);
    }

    @Override
    public TSDBResultSet apmPercentageLatency(QueryBuilder builder) {
        String databaseName = builder.getDatabaseName();
        Map<String, String> tags = new HashMap<>();
        tags.put("database", databaseName);
        long startTime = System.currentTimeMillis();
        DatabaseWrapper dbClient = null;
        try {
            dbClient = pool.getTSDBClient();
            long getClientTime = System.currentTimeMillis();
            OtelMetricUtil.logHistogram(TSDB_CONNECTION_COST, tags, getClientTime - startTime);
            if (dbClient == null) {
                throw new RuntimeException("无法获取数据库连接");
            }
            // 根据配置决定是否打印SQL
            if (tsdbConfig != null && tsdbConfig.isSqlPrintEnabled()) {
                String sql = tsdbExecutor.buildFullSql(builder);
                log.info("执行TSDB百分比查询: {}", sql);
            }
            // 执行查询
            return tsdbExecutor.apmPercentageLatency(dbClient, builder);
        }catch (Exception e) {
            log.error("查询数据失败,databaseName:{},error:", databaseName, e);
            OtelMetricUtil.logCounter(TSDB_QUERY_FAILED, tags, 1);
            throw new RuntimeException("ApmPercentageLatency 查询数据失败", e);
        } finally {
            if (dbClient != null) {
                pool.releaseConnection(dbClient);
            }
            long endTime = System.currentTimeMillis();
            OtelMetricUtil.logHistogram(TSDB_QUERY_COST, tags, endTime - startTime);
        }
    }

    /**
     * 直接使用查询语句和数据库名称获取标签值
     *
     * @param query        查询语句
     * @param databaseName 数据库名称
     * @return 标签名到标签值集合的映射
     */
    @Override
    public Map<String, Set<String>> showTagValues(String query, String databaseName) {
        Map<String, String> tags = new HashMap<>();
        tags.put("database", databaseName);
        long startTime = System.currentTimeMillis();
        DatabaseWrapper dbClient = null;
        try {
            dbClient = pool.getTSDBClient();
            long getClientTime = System.currentTimeMillis();
            OtelMetricUtil.logHistogram(TSDB_CONNECTION_COST, tags, getClientTime - startTime);
            if (dbClient == null) {
                throw new RuntimeException("无法获取数据库连接");
            }
            return tsdbExecutor.executeShowTagValues(dbClient, query, databaseName);
        }catch (Exception e) {
            log.error("showTagValues失败,databaseName:{},error:", databaseName, e);
            OtelMetricUtil.logCounter(TSDB_QUERY_FAILED, tags, 1);
            throw new RuntimeException("showTagValues 查询数据失败", e);
        } finally {
            if (dbClient != null) {
                pool.releaseConnection(dbClient);
            }
            long endTime = System.currentTimeMillis();
            OtelMetricUtil.logHistogram(TSDB_QUERY_COST, tags, endTime - startTime);
        }
    }

    @Override
    public Map<String, Set<String>> showTagValues(QueryBuilder builder) {
        // 构建查询SQL
        String dbName = builder.getDatabaseName();
        Map<String, String> tags = new HashMap<>();
        tags.put("database", dbName);
        long startTime = System.currentTimeMillis();
        DatabaseWrapper dbClient = null;
        Map<String, String> sqlMap = null ;
        try {
            dbClient = pool.getTSDBClient();
            long getClientTime = System.currentTimeMillis();
            OtelMetricUtil.logHistogram(TSDB_CONNECTION_COST, tags, getClientTime - startTime);
            if (dbClient == null) {
                throw new RuntimeException("无法获取数据库连接");
            }
            sqlMap = tsdbExecutor.buildShowTagValueSql(dbClient, builder);
        }catch (Exception e) {
            log.error("buildShowTagValueSql失败,databaseName:{},error:", dbName, e);
            OtelMetricUtil.logCounter(TSDB_QUERY_FAILED, tags, 1);
            throw new RuntimeException("buildShowTagValueSql 查询数据失败", e);
        } finally {
            if (dbClient != null) {
                pool.releaseConnection(dbClient);
            }
            long endTime = System.currentTimeMillis();
            OtelMetricUtil.logHistogram(TSDB_QUERY_COST, tags, endTime - startTime);
        }
        Map<String, Set<String>> result = new HashMap<>();
        if (sqlMap == null) {
            return result;
        }

        // 直接调用TSDBExecutor的execute方法执行查询并返回Map<String, Set<String>>格式
        for (Map.Entry<String, String> sqlEntry : sqlMap.entrySet()) {
            final String sql = sqlEntry.getValue();
            final String databaseName = builder.getDatabaseName();

            // 调用新的showTagValues方法执行查询
            Map<String, Set<String>> queryResult = showTagValues(sql, databaseName);

            // 合并结果
            for (Map.Entry<String, Set<String>> entry : queryResult.entrySet()) {
                result.computeIfAbsent(entry.getKey(), k -> new HashSet<>()).addAll(entry.getValue());
            }
        }
        return result;
    }

    @Override
    public Map<String, Set<String>> showTagValues(String field, String databaseName, String measurement, List<Where> wheres) {
        return showTagValues(field, databaseName, measurement, wheres, null);
    }

    @Override
    public Map<String, Set<String>> showTagValues(String field, String databaseName, String measurement, List<Where> wheres, Collection<String> keys) {
        return showTagValues(field, databaseName, measurement, wheres, keys, null, null);
    }

    @Override
    public Map<String, Set<String>> showTagValues(String field, String databaseName, String measurement, List<Where> wheres, Collection<String> keys, Long start, Long end) {
        if (databaseName == null || measurement == null) {
            throw new IllegalArgumentException("Database name and measurement cannot be null");
        }

        // 构建基础查询配置
        QueryBuilderX builder = new QueryBuilderX();
        if (field != null) {
            builder.addAgg(new Aggregation(AggFun.LAST, field));
        }
        builder.setDatabaseName(databaseName);
        builder.setMeasurement(measurement);

        // 合并时间条件和其他查询条件
        List<Where> newWheres = new ArrayList<>(wheres != null ? wheres : Collections.emptyList());
        Where timeWhere = new Where();
        if (start != null) {
            newWheres.add(timeWhere.gte("time", start));
        }
        if (end != null) {
            newWheres.add(timeWhere.lt("time", end));
        }
        builder.setWheres(newWheres);

        // 处理分组条件
        if (keys == null) {
            keys = Collections.emptyList();
        }
        if (!keys.isEmpty()) {
            builder.setGroupBy(new ArrayList<>(keys));
        }

        // 执行查询并返回结果
        return showTagValues(builder);
    }

    @Override
    public Map<String, Set<String>> showPeriodTagValues(String field, String databaseName, String measurement, List<Where> wheres, Collection<String> keys, Integer period, Integer timeOffset) {
        if (timeOffset == null) {
            timeOffset = 0;
        }
        if (period == null) {
            throw new IllegalArgumentException("period不能为空");
        }
        if (period < 0) {
            throw new IllegalArgumentException("period必须为非负数");
        }
        if (timeOffset < 0) {
            throw new IllegalArgumentException("timeOffset必须为非负数");
        }

        final long now = System.currentTimeMillis();
        try {
            // 使用精确计算检测溢出
            long totalOffset = Math.addExact(timeOffset, period);
            long start = Math.subtractExact(now, totalOffset);
            long end = Math.subtractExact(now, timeOffset);
            return showTagValues(field, databaseName, measurement, wheres, keys, start, end);
        } catch (ArithmeticException e) {
            throw new IllegalArgumentException("时间参数过大导致算术溢出", e);
        }
    }

    @Override
    public Map<String, Set<String>> showPeriodTagValues(String field, String databaseName, String measurement, List<Where> wheres, Collection<String> keys, Integer period) {
        return showPeriodTagValues(field, databaseName, measurement, wheres, keys, period, 0);
    }

    @Override
    public TSDBResultSet executeQuery(QueryBuilder builder, AggFun tsAgg, AggFun valAgg, AggFun topAgg, JSONObject otherParam) throws Exception {
        String databaseName = builder.getDatabaseName();
        Map<String, String> tags = new HashMap<>();
        tags.put("database", databaseName);
        long startTime = System.currentTimeMillis();
        DatabaseWrapper dbClient = null;
        try {
            dbClient = pool.getTSDBClient();
            long getClientTime = System.currentTimeMillis();
            OtelMetricUtil.logHistogram(TSDB_CONNECTION_COST, tags, getClientTime - startTime);
            // 根据配置决定是否打印SQL
            if (tsdbConfig != null && tsdbConfig.isSqlPrintEnabled()) {
                String sql = tsdbExecutor.buildFullSql(builder);
                log.info("执行复杂TSDB查询: {} [聚合函数: ts={}, val={}, top={}]",
                        sql, tsAgg, valAgg, topAgg);
            }
            return tsdbExecutor.executeQuery(dbClient, builder, tsAgg, valAgg, topAgg, otherParam);
        }catch (Exception e) {
            log.error("查询数据失败,databaseName:{},error:", databaseName, e);
            OtelMetricUtil.logCounter(TSDB_QUERY_FAILED, tags, 1);
            throw new RuntimeException("executeQuery 查询数据失败", e);
        } finally {
            if (dbClient != null) {
                pool.releaseConnection(dbClient);
            }
            long endTime = System.currentTimeMillis();
            OtelMetricUtil.logHistogram(TSDB_QUERY_COST, tags, endTime - startTime);
        }
    }

    @Override
    public void close() throws Exception {
        tsdbExecutor.close();
    }

    @Override
    public Map<String, Object> getConfigs() {
        return tsdbExecutor.getConfigs();
    }
}
