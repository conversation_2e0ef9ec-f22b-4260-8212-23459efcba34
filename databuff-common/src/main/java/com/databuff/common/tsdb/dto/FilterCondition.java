package com.databuff.common.tsdb.dto;

import com.databuff.common.tsdb.model.LogicalOperator;
import com.databuff.common.tsdb.model.WhereOp;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 定义过滤条件的接口，包含左操作数、操作符和右操作数。
 * @param <X> 左操作数的类型
 * @param <Y> 右操作数的类型
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public interface FilterCondition<X, Y> {
    /**
     * 获取过滤条件的类型标识。
     * @return 条件类型标识字符串（默认为空）
     */
    default String getType() {
        return "";
    }

    /**
     * 获取过滤条件的左操作数。
     * @return 左操作数的值
     */
    X getLeft();

    /**
     * 获取过滤条件的操作符。
     * @return 用于比较的运算符字符串（如"="、">"等）
     */
    WhereOp getOperatorEnum();

    /**
     * 获取过滤条件的大小写不敏感标识符。
     * @return 指示是否启用大小写不敏感的标识符字符串（如"CASE_INSENSITIVE"）
     */
    Boolean getCaseInsensitive();

    /**
     * 获取连接当前条件与其他条件的逻辑运算符。
     * @return 逻辑连接符字符串（如"AND"或"OR"）
     */
    LogicalOperator getConnector();

    /**
     * 获取过滤条件的右操作数。
     * @return 右操作数的值
     */
    Y getRight();
}
