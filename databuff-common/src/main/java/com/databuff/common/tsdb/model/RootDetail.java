package com.databuff.common.tsdb.model;

import java.util.Map;

public class RootDetail {

    private long abnormalStartTime;
    private long abnormalEndTime;
    private Map<String, String> abnormalReasonAndLinks;

    private String problemId;
    private String problemDesc;

    public long getAbnormalStartTime() {
        return abnormalStartTime;
    }

    public void setAbnormalStartTime(long abnormalStartTime) {
        this.abnormalStartTime = abnormalStartTime;
    }

    public long getAbnormalEndTime() {
        return abnormalEndTime;
    }

    public void setAbnormalEndTime(long abnormalEndTime) {
        this.abnormalEndTime = abnormalEndTime;
    }

    public Map<String, String> getAbnormalReasonAndLinks() {
        return abnormalReasonAndLinks;
    }

    public void setAbnormalReasonAndLinks(Map<String, String> abnormalReasonAndLinks) {
        this.abnormalReasonAndLinks = abnormalReasonAndLinks;
    }

    public String getProblemId() {
        return problemId;
    }

    public void setProblemId(String problemId) {
        this.problemId = problemId;
    }

    public String getProblemDesc() {
        return problemDesc;
    }

    public void setProblemDesc(String problemDesc) {
        this.problemDesc = problemDesc;
    }
}
