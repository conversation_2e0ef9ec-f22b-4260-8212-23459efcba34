package com.databuff.common.tsdb.dto.detect;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.alibaba.fastjson.util.TypeUtils;
import com.databuff.common.tsdb.model.LogicalOperator;

import java.lang.reflect.Type;
import java.util.Map;

public class MultiDetectQueryRequestDeserializerFastjson implements ObjectDeserializer {

    @Override
    public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {

        MultiDetectQueryRequest request = new MultiDetectQueryRequest();
        Map<String, Object> jsonMap = parser.parseObject();

        // 反序列化 "1" -> A、"2" -> B 等字段
        for (Map.Entry<String, Object> entry : jsonMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (key.equals("critical") || key.equals("warning") || key.equals("noData")) {
                // 处理枚举字段（如 critical）
                processEnumField(request, key, value);
            } else if (key.matches("^[1-5]$")) { // 匹配 "1"~"5"
                // 处理嵌套的 DetectQueryRequest 对象（如 "1": {"A": { ... }})
                DetectQueryRequest detectQueryRequest = TypeUtils.castToJavaBean(value, DetectQueryRequest.class);
                setDetectQueryRequest(request, key, detectQueryRequest);
            }
        }

        return (T) request;
    }

    private void processEnumField(
            MultiDetectQueryRequest request,
            String fieldName,
            Object value) {
        if (value instanceof String) {
            String operatorStr = (String) value;
            LogicalOperator operator =
                    operatorStr.equalsIgnoreCase("AND")
                            ? LogicalOperator.AND
                            : LogicalOperator.OR;
            switch (fieldName) {
                case "critical":
                    request.setCritical(operator);
                    break;
                case "warning":
                    request.setWarning(operator);
                    break;
                case "noData":
                    request.setNoData(operator);
                    break;
            }
        }
    }

    private void setDetectQueryRequest(
            MultiDetectQueryRequest request,
            String key,
            DetectQueryRequest detectQueryRequest) {
        switch (key) {
            case "1":
                request.setA(detectQueryRequest);
                break;
            case "2":
                request.setB(detectQueryRequest);
                break;
            case "3":
                request.setC(detectQueryRequest);
                break;
            case "4":
                request.setD(detectQueryRequest);
                break;
            case "5":
                request.setE(detectQueryRequest);
                break;
        }
    }


    /**
     * @return
     */
    @Override
    public int getFastMatchToken() {
        return 0;
    }
}
