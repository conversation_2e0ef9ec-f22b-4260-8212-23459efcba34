package com.databuff.common.tsdb.model;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import static com.databuff.common.tsdb.model.LogicalOperator.AND;


@Data
public class Where implements Cloneable, Serializable {
    private String field;
    private WhereOp operator; // 操作符
    private Object value; // 值，可能是 String, List, NULL
    private LogicalOperator logicalOperator;  // 逻辑运算符（AND/OR）
    private List<Where> subConditions;  // 支持嵌套条件

    @ApiModelProperty(value = "是否启用大小写不敏感匹配", example = "false")
    private Boolean caseInsensitive;

    // 构造简单条件
    public Where(String field, WhereOp operator, Object value) {
        this.field = field;
        this.operator = operator;
        this.value = value;
        this.logicalOperator = AND;  // 默认AND
    }

    // 构造简单条件
    public Where(String field, WhereOp operator) {
        this.field = field;
        this.operator = operator;
        this.logicalOperator = AND;  // 默认AND
    }

    // 构造复杂条件，带子查询
    public Where(LogicalOperator logicalOperator, List<Where> subConditions) {
        this.logicalOperator = logicalOperator;
        this.subConditions = subConditions;
    }

    public Where() {

    }

    public String getWhere() {
        if (subConditions != null) {
            return "(" + String.join(" " + logicalOperator + " ",
                    subConditions.stream().map(Where::toString).collect(Collectors.toList())) + ")";
        }
        return field + " " + operator + " " + value;
    }

    // 静态工厂方法
    public static Where simple(String column, WhereOp operator, Object value) {
        return new Where(column, operator, value);
    }

    public static Where simple(String column, WhereOp operator) {
        return new Where(column, operator);
    }

    public static Where or(List<Where> subConditions) {
        return new Where(LogicalOperator.OR, subConditions);
    }

    public static Where and(List<Where> subConditions) {
        return new Where(AND, subConditions);
    }

    public static Where eq(String field, Object value) {
        return new Where(field, WhereOp.EQ, value);
    }

    public static Where neq(String field, Object value) {
        return new Where(field, WhereOp.NEQ, value);
    }

    public static Where in(String field, Collection<? extends Object> values) {
        return new Where(field, WhereOp.IN, values);
    }

    public static Where notIn(String field, Collection<? extends Object> values) {
        return new Where(field, WhereOp.NOT_IN, values);
    }

    public static Where like(String field, Object value) {
        return new Where(field, WhereOp.LIKE, value);
    }

    public static Where notLike(String field, Object value) {
        return new Where(field, WhereOp.NOT_LIKE, value);
    }

    public static Where startWith(String field, Object value) {
        return new Where(field, WhereOp.START_WITH, value);
    }

    public static Where endWith(String field, Object value) {
        return new Where(field, WhereOp.END_WITH, value);
    }

    public static Where regex(String field, Object value) {
        return new Where(field, WhereOp.REGEX, value);
    }

    /**
     * field 小于 value
     *
     * @param field
     * @param value
     * @return
     */
    public static Where lt(String field, Object value) {
        return new Where(field, WhereOp.LT, value);
    }

    /**
     * field 大于 value
     *
     * @param field
     * @param value
     * @return
     */
    public static Where gt(String field, Object value) {
        return new Where(field, WhereOp.GT, value);
    }

    /**
     * field 小于等于 value
     *
     * @param field
     * @param value
     * @return
     */
    public static Where lte(String field, Object value) {
        return new Where(field, WhereOp.LTE, value);
    }

    /**
     * field 大于等于 value
     *
     * @param field
     * @param value
     * @return
     */
    public static Where gte(String field, Object value) {
        return new Where(field, WhereOp.GTE, value);
    }

    /**
     * 将当前Where条件与指定的新Where条件合并，使用给定的逻辑操作符。
     *
     * @param newWhere  要合并的新Where条件
     * @param connector 逻辑连接符，决定合并方式（AND或OR）
     * @return 无返回值
     * <AUTHOR>
     */
    public Where merge(Where newWhere, LogicalOperator connector) {
        if (connector == null) {
            connector = AND;
        }
        switch (connector) {
            case AND:
                // 使用AND逻辑运算符合并当前条件与newWhere
                return Where.and(Lists.newArrayList(this, newWhere));
            case OR:
                // 使用OR逻辑运算符合并当前条件与newWhere
                return Where.or(Lists.newArrayList(this, newWhere));
            default:
                return newWhere;
        }
    }

    @Override
    public Where clone() {
        try {
            return (Where) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new RuntimeException("Where克隆失败", e);
        }
    }

}
