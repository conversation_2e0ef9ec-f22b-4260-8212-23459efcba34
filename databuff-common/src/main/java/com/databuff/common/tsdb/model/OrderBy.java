package com.databuff.common.tsdb.model;

import java.io.Serializable;

public class OrderBy implements Cloneable, Serializable {
    private String field;
    private boolean isAsc;

    public OrderBy(String field, boolean isAsc) {
        this.field = field;
        this.isAsc = isAsc;
    }

    public String getField() {
        return field;
    }

    public boolean isAsc() {
        return isAsc;
    }

    /**
     * 升序
     * @param field
     * @return
     */
    public static OrderBy asc(String field) {
        return new OrderBy(field, true);
    }

    /**
     * 降序
     * @param field
     * @return
     */
    public static OrderBy desc(String field) {
        return new OrderBy(field, false);
    }

    @Override
    public OrderBy clone() {
        try {
            return (OrderBy) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new RuntimeException("OrderBy克隆失败", e);
        }
    }
}
