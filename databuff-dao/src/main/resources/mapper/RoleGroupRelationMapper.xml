<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.databuff.dao.mysql.RoleGroupRelationMapper">

    <!-- 插入角色与管理域的关系 -->
    <insert id="insertRoleGroupRelation">
        INSERT INTO df_role_group_relation (role_id, gid, config_auth, data_auth, api_key, create_time) VALUES
        <foreach collection="relations" item="item" separator=",">
            (#{item.roleId}, #{item.gid},#{item.configAuth},#{item.dataAuth},#{item.apiKey},NOW())
        </foreach>
    </insert>

    <!-- 根据角色ID获取角色与管理域的关系 -->
    <select id="getRoleGroupRelationsByRoleId" resultType="com.databuff.entity.RoleGroupRelation">
        SELECT * FROM df_role_group_relation WHERE role_id = #{roleId} and api_key = #{apiKey}
    </select>

    <!-- 删除角色与管理域的绑定关系 -->
    <delete id="deleteRoleGroupRelation">
        DELETE FROM df_role_group_relation WHERE api_key = #{apiKey}
        <if test="roleId != null">
            AND role_id = #{roleId}
        </if>
        <if test="gid != null">
            AND gid = #{gid}
        </if>
    </delete>

    <select id="getAllRoleGroupRelations" resultType="com.databuff.entity.RoleGroupRelation">
        SELECT * FROM df_role_group_relation WHERE api_key = #{apiKey}
    </select>

    <update id="updateRoleGroupRelationAuth">
        <foreach collection="relations" item="relation" separator=";">
            UPDATE df_role_group_relation
            SET config_auth = #{relation.configAuth},
            data_auth = #{relation.dataAuth}
            WHERE role_id = #{relation.roleId}
            AND gid = #{relation.gid}
            AND api_key = #{relation.apiKey}
        </foreach>
    </update>
</mapper>
