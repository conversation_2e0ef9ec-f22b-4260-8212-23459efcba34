<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.databuff.dao.mysql.RumJsErrorMapper">

    <select id="getFixedAndIgnoredErrorMessages" resultType="java.lang.String">
        SELECT error_message
        FROM dc_rum_js_errors
        WHERE status IN ('fixed', 'ignored')
    </select>


    <select id="getJsErrorList" resultType="com.databuff.entity.rum.mysql.RumJsError">
        SELECT * FROM dc_rum_js_errors
        WHERE app_id = #{appId}
        <if test="handler != null and handler != ''">
            AND handler LIKE CONCAT('%', #{handler}, '%')
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="errorMessage != null and errorMessage != ''">
            AND error_message LIKE CONCAT('%', #{errorMessage}, '%')
        </if>
        <if test="sortField != null and sortField != ''">
            ORDER BY
            <choose>
                <when test="sortField == 'handler'">handler</when>
                <when test="sortField == 'status'">status</when>
                <when test="sortField == 'errorMessage'">error_message</when>
                <otherwise>error_message</otherwise>
            </choose>
            <choose>
                <when test="sortOrder.toString() == 'DESC'">DESC</when>
                <otherwise>ASC</otherwise>
            </choose>
        </if>
        LIMIT #{pageSize} OFFSET #{offset}
    </select>


    <select id="getAllErrorStatuses" resultType="com.databuff.entity.rum.mysql.RumJsError">
        SELECT error_message, status, handler
        FROM dc_rum_js_errors
        WHERE app_id = #{appId}
    </select>


    <select id="getJsErrorListByAppIdAndErrorMessage" resultType="com.databuff.entity.rum.mysql.RumJsError">
        SELECT * FROM dc_rum_js_errors
        WHERE app_id = #{appId}
        <if test="errorMessage != null and errorMessage != ''">
            AND error_message LIKE CONCAT('%', #{errorMessage}, '%')
        </if>
    </select>

    <select id="getJsErrorListByAppIdAndHandler" resultType="com.databuff.entity.rum.mysql.RumJsError">
        SELECT * FROM dc_rum_js_errors
        WHERE app_id = #{appId}
        AND handler LIKE CONCAT('%', #{handler}, '%')
        <if test="errorMessage != null and errorMessage != ''">
            AND error_message LIKE CONCAT('%', #{errorMessage}, '%')
        </if>
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <select id="countJsErrorsByAppIdAndHandler" resultType="long">
        SELECT COUNT(*) FROM dc_rum_js_errors
        WHERE app_id = #{appId}
        AND handler LIKE CONCAT('%', #{handler}, '%')
        <if test="errorMessage != null and errorMessage != ''">
            AND error_message LIKE CONCAT('%', #{errorMessage}, '%')
        </if>
    </select>

    <select id="getJsErrorListByAppIdAndNotUnfixedStatus" resultType="com.databuff.entity.rum.mysql.RumJsError">
        SELECT * FROM dc_rum_js_errors
        WHERE app_id = #{appId}
        AND status != 'unfixed'
        <if test="errorMessage != null and errorMessage != ''">
            AND error_message LIKE CONCAT('%', #{errorMessage}, '%')
        </if>
    </select>

    <select id="getJsErrorListByAppIdAndStatus" resultType="com.databuff.entity.rum.mysql.RumJsError">
        SELECT * FROM dc_rum_js_errors
        WHERE app_id = #{appId}
        AND status = #{status}
        <if test="errorMessage != null and errorMessage != ''">
            AND error_message LIKE CONCAT('%', #{errorMessage}, '%')
        </if>
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <select id="countJsErrorsByAppIdAndStatus" resultType="long">
        SELECT COUNT(*) FROM dc_rum_js_errors
        WHERE app_id = #{appId}
        AND status = #{status}
        <if test="errorMessage != null and errorMessage != ''">
            AND error_message LIKE CONCAT('%', #{errorMessage}, '%')
        </if>
    </select>

    <select id="getJsErrorListByAppIdHandlerAndStatus" resultType="com.databuff.entity.rum.mysql.RumJsError">
        SELECT * FROM dc_rum_js_errors
        WHERE app_id = #{appId}
        AND handler LIKE CONCAT('%', #{handler}, '%')
        AND status = #{status}
        <if test="errorMessage != null and errorMessage != ''">
            AND error_message LIKE CONCAT('%', #{errorMessage}, '%')
        </if>
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <select id="countJsErrorsByAppIdHandlerAndStatus" resultType="long">
        SELECT COUNT(*) FROM dc_rum_js_errors
        WHERE app_id = #{appId}
        AND handler LIKE CONCAT('%', #{handler}, '%')
        AND status = #{status}
        <if test="errorMessage != null and errorMessage != ''">
            AND error_message LIKE CONCAT('%', #{errorMessage}, '%')
        </if>
    </select>


    <update id="updateHandler">
        INSERT INTO dc_rum_js_errors (app_id, error_message, handler)
        VALUES (#{appId}, #{errorMessage}, #{handler})
        ON DUPLICATE KEY UPDATE handler = #{handler}
    </update>

    <update id="updateStatus">
        INSERT INTO dc_rum_js_errors (app_id, error_message, status)
        VALUES (#{appId}, #{errorMessage}, #{status})
        ON DUPLICATE KEY UPDATE status = #{status}
    </update>

    <select id="getAllHandlers" resultType="java.lang.String">
        SELECT DISTINCT handler
        FROM dc_rum_js_errors
        WHERE app_id = #{appId} AND handler IS NOT NULL AND handler != ''
    </select>

    <select id="getHandlerByErrorMessage" resultType="java.lang.String">
        SELECT handler FROM dc_rum_js_errors
        WHERE app_id = #{appId} AND error_message = #{errorMessage}
        LIMIT 1
    </select>


</mapper>
