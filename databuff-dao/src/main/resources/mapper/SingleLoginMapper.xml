<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.databuff.dao.mysql.SingleLoginMapper">

    <resultMap id="BaseResultMap" type="com.databuff.entity.HuaweiImcAdmin" >
        <result column="appid" property="appid" />
        <result column="name" property="name" />
        <result column="tenant" property="tenant" />
        <result column="orderid" property="orderid" />
        <result column="clientid" property="clientid" />
        <result column="clientsecret" property="clientsecret" />
        <result column="domainname" property="domainname" />
        <result column="mobile" property="mobile" />
        <result column="email" property="email" />
        <result column="company" property="company" />
        <result column="apikey" property="apikey" />
        <result column="verifystatus" property="verifystatus" />
        <result column="isdelete" property="isdelete" />
    </resultMap>


    <sql id="Base_Column_List">
                appid,
                `name`,
                tenant,
                orderid,
                clientid,
                clientsecret,
                domainname,
                mobile,
                email,
                company,
                apikey,
                verifystatus,
                isdelete
    </sql>

    <insert id="insertHuaweiImcAdmin"  parameterType="com.databuff.entity.HuaweiImcAdmin">
        INSERT INTO dc_huawei_imc_admin
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != appid and '' != appid">
                appid,
            </if>
            <if test="null != name and '' != name">
                `name`,
            </if>
            <if test="null != tenant and '' != tenant">
                tenant,
            </if>
            <if test="null != orderid and '' != orderid">
                orderid,
            </if>
            <if test="null != clientid and '' != clientid">
                clientid,
            </if>
            <if test="null != clientsecret and '' != clientsecret">
                clientsecret,
            </if>
            <if test="null != domainname and '' != domainname">
                domainname,
            </if>
            <if test="null != mobile and '' != mobile">
                mobile,
            </if>
            <if test="null != email and '' != email">
                email,
            </if>
            <if test="null != company and '' != company">
                company,
            </if>
            <if test="null != apikey and '' != apikey">
                apikey,
            </if>
            <if test="null != verifystatus and '' != verifystatus">
                verifystatus,
            </if>
            <if test="null != isdelete and '' != isdelete">
                isdelete
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != appid and '' != appid">
                #{appid},
            </if>
            <if test="null != name and '' != name">
                #{name},
            </if>
            <if test="null != tenant and '' != tenant">
                #{tenant},
            </if>
            <if test="null != orderid and '' != orderid">
                #{orderid},
            </if>
            <if test="null != clientid and '' != clientid">
                #{clientid},
            </if>
            <if test="null != clientsecret and '' != clientsecret">
                #{clientsecret},
            </if>
            <if test="null != domainname and '' != domainname">
                #{domainname},
            </if>
            <if test="null != mobile and '' != mobile">
                #{mobile},
            </if>
            <if test="null != email and '' != email">
                #{email},
            </if>
            <if test="null != company and '' != company">
                #{company},
            </if>
            <if test="null != apikey and '' != apikey">
                #{apikey},
            </if>
            <if test="null != verifystatus and '' != verifystatus">
                #{verifystatus},
            </if>
            <if test="null != isdelete and '' != isdelete">
                #{isdelete}
            </if>
        </trim>
    </insert>

    <delete id="deleteHuaweiImcAdmin" >
        UPDATE dc_huawei_imc_admin set isdelete = 1
        WHERE tenant = #{tenant}
    </delete>

    <update id="updateHuaweiImcAdmin" parameterType="com.databuff.entity.HuaweiImcAdmin">
        UPDATE dc_huawei_imc_admin
        <set>
            <if test="null != appid and '' != appid">appid = #{appid},</if>
            <if test="null != name and '' != name">`name` = #{name},</if>
            <if test="null != orderid and '' != orderid">orderid = #{orderid},</if>
            <if test="null != clientid and '' != clientid">clientid = #{clientid},</if>
            <if test="null != clientsecret and '' != clientsecret">clientsecret = #{clientsecret},</if>
            <if test="null != domainname and '' != domainname">domainname = #{domainname},</if>
            <if test="null != mobile and '' != mobile">mobile = #{mobile},</if>
            <if test="null != email and '' != email">email = #{email},</if>
            <if test="null != company and '' != company">company = #{company},</if>
            <if test="null != apikey and '' != apikey">apikey = #{apikey},</if>
            <if test="null != verifystatus and '' != verifystatus">verifystatus = #{verifystatus},</if>
            <if test="null != isdelete and '' != isdelete">isdelete = #{isdelete}</if>
        </set>
        WHERE tenant = #{tenant}
    </update>

    <select id="loadHuaweiImcAdminByTenant" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM dc_huawei_imc_admin
        WHERE tenant = #{tenant}
        <if test="null != isdelete and '' != isdelete">
            and isDelete = #{isDelete}
        </if>
    </select>
</mapper>