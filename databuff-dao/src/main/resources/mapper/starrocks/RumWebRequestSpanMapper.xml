<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.databuff.dao.starrocks.RumWebRequestSpanMapper">

    <select id="getRequestTraceList" resultType="com.databuff.entity.rum.web.RumWebRequestTraceListDto">
        SELECT
        startTime,
        processed_http_url as processedHttpUrl,
        http_url as httpUrl,
        user_id as userId,
        status_code as statusCode,
        duration,
        span_id as spanId,
        session_id as sessionId,
        ip,
        server_time as serverTime,
        network_time as networkTime,
        transfer_size / 1024.0 as transferSize,
        region,
        isp,
        location_href as locationHref,
        backend_span_id as backendSpanId,
        service
        FROM
        dc_rum_web_request_span
        WHERE
        app_id = #{appId}
        <if test="processedHttpUrl != null and processedHttpUrl != ''">
            AND processed_http_url = #{processedHttpUrl}
        </if>
        <if test="userId != null and userId != ''">
            AND user_id = #{userId}
        </if>
        <if test="ip != null and ip != ''">
            AND ip = #{ip}
        </if>
        <if test="sessionId != null">
            AND session_id = #{sessionId}
        </if>
        <if test="fromTime != null">
            AND startTime >= #{fromTime}
        </if>
        <if test="toTime != null">
            AND startTime &lt;= #{toTime}
        </if>
        ORDER BY
        <choose>
            <when test="sortField == 'startTime'">startTime</when>
            <when test="sortField == 'duration'">duration</when>
            <when test="sortField == 'serverTime'">server_time</when>
            <when test="sortField == 'transferSize'">transfer_size</when>
            <otherwise>startTime</otherwise>
        </choose>
        <choose>
            <when test="sortOrder == 'ASC'">ASC</when>
            <otherwise>DESC</otherwise>
        </choose>
    </select>


    <select id="getRequestTraceDetail" resultType="com.databuff.entity.rum.web.RumWebRequestTraceDetailResponseDto">
        SELECT
            startTime,
            http_url as httpUrl,
            user_id as userId,
            status_code as statusCode,
            duration,
            span_id as spanId,
            session_id as sessionId,
            ip,
            server_time as serverTime,
            network_time as networkTime,
            transfer_size / 1024.0 as transferSize,
            region,
            isp,
            location_href as locationHref,
            backend_span_id as backendSpanId,
            service,
            url_param as urlParam,
            http_request_header as httpRequestHeader,
            http_request_body as httpRequestBody,
            http_response_header as httpResponseHeader,
            http_response_body as httpResponseBody
        FROM
            dc_rum_web_request_span
        WHERE
            app_id = #{appId}
          AND processed_http_url = #{processedHttpUrl}
          AND span_id = #{spanId}
          AND startTime = #{fromTime}
            LIMIT 1
    </select>

</mapper>
