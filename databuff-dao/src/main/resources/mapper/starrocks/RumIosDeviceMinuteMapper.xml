<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.databuff.dao.starrocks.RumIosDeviceMinuteMapper">
    <select id="getDeviceCount" resultType="com.databuff.entity.dto.TimeValue">
        SELECT 
            CAST(UNIX_TIMESTAMP(start_time) / ${timeBucket} AS UNSIGNED) * ${timeBucket} * 1000 AS ts,
            COUNT(DISTINCT device_count) as value
        FROM dc_rum_ios_device_minute
        WHERE app_id = #{appId}
        AND start_time BETWEEN #{fromTime} AND #{toTime}
        GROUP BY ts
        ORDER BY ts
    </select>
</mapper>
