<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.databuff.dao.starrocks.GroupSrMapper">

    <resultMap id="WorkloadType" type="com.databuff.entity.K8sWorkloadSrEntity">
        <result column="spuid" property="spuid"/>
        <result column="name" property="name"/>
        <result column="clusterId" property="clusterId"/>
        <result column="clusterName" property="clusterName"/>
        <result column="namespace" property="namespace"/>
        <result column="df-api-key" property="dfApiKey"/>
        <result column="timestamp" property="timestamp"/>
        <result column="type" property="type"/>
        <result column="data" property="data"/>
    </resultMap>

    <select id="getNamespacesByRules" resultType="com.databuff.entity.domainObj.K8sNamespaceObj">
        select  clusterId,clusterName,`df-api-key` as apikey,`name` as namespaceName from `dc_k8s_latest_namespace`
        <where>
            `df-api-key` = #{apiKey} and
            <include refid="whereSql" />
        </where>
    </select>


    <sql id="whereSql">
        <choose>
            <when test="rules != null and rules.size() > 0">
                <foreach collection="rules" item="ruleList" separator=" OR ">
                    <trim prefix="(" suffix=")" suffixOverrides=" AND ">
                        <foreach collection="ruleList" item="rule" separator=" AND ">
                            <choose>
                                <when test='rule.operator != null and rule.operator == "="'>
                                    <!-- 使用 #{rule.left} 和 #{rule.right} 来绑定动态字段值 -->
                                    ${rule.left} = #{rule.right}
                                </when>
                                <when test='rule.operator != null and rule.operator.equals("!=")'>
                                    ${rule.left} != #{rule.right}
                                </when>
                                <when test='rule.operator != null and rule.operator.equals("like")'>
                                    ${rule.left} LIKE CONCAT('%', #{rule.right}, '%')
                                </when>
                                <when test='rule.operator != null and rule.operator.equals("notLike")'>
                                    ${rule.left} NOT LIKE CONCAT('%', #{rule.right}, '%')
                                </when>
                                <when test='rule.operator != null and rule.operator.equals("startWith")'>
                                    ${rule.left} LIKE CONCAT(#{rule.right}, '%')
                                </when>
                                <when test='rule.operator != null and rule.operator.equals("endWith")'>
                                    ${rule.left} LIKE CONCAT('%', #{rule.right})
                                </when>
                                <when test='rule.operator != null and rule.operator.equals("in")'>
                                    ${rule.left} IN
                                    <foreach item='item' index='index' collection='rule.right.split(",")' open='(' separator=',' close=')'>
                                        #{item}
                                    </foreach>
                                </when>
                                <when test='rule.operator != null and rule.operator.equals("notIn")'>
                                    ${rule.left} NOT IN
                                    <foreach item='item' index='index' collection='rule.right.split(",")' open='(' separator=',' close=')'>
                                        #{item}
                                    </foreach>
                                </when>
                                <when test='rule.operator != null and rule.operator.equals("match")'>
                                    1=1
                                </when>
                                <otherwise>
                                    <!-- 如果没有任何条件满足，添加 1=2  -->
                                    1=2
                                </otherwise>
                            </choose>
                        </foreach>
                    </trim>
                </foreach>
            </when>
        </choose>
    </sql>

    <select id="getNamespaces" resultType="com.databuff.entity.domainObj.K8sNamespaceObj">
        select  clusterId,clusterName,`df-api-key` as apikey,`name` as namespaceName from `dc_k8s_latest_namespace`
        where `df-api-key` = #{apiKey}  <if test="null != query and '' != query">
        and `name` like concat('%',#{query},'%')
    </if>
    </select>


    <select id="getNamespaceData" resultType="com.databuff.entity.K8sEntity">
        SELECT *
        FROM dc_k8s_latest_namespace
        where `df-api-key` = #{apiKey}
          <if test="clusterIdNamespaces != null and clusterIdNamespaces.size() > 0">
            and CONCAT(`clusterId`,"_", `name`) in
            <foreach collection="clusterIdNamespaces" item="clusterIdNamespace" open="(" close=")" separator=",">
                #{clusterIdNamespace}
            </foreach>
        </if>
    </select>
</mapper>