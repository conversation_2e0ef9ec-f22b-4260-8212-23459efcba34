<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.databuff.dao.mysql.PluginMapper">
    <insert id="addPlugStatus">
        INSERT INTO `dc_databuff_plugin`(`api_key`, `plugin_dic_id`,is_install)
        VALUES(#{apiKey},#{id},#{isInstall})
        ON DUPLICATE KEY
        UPDATE is_install=#{isInstall};
    </insert>
    <insert id="addPlugDashboard" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into dashboard (version, slug, title, data, org_id, created, updated,folder_id,is_folder,has_acl, uid)
        values (#{version},#{slug},#{title},#{data},#{orgId},#{created},#{updated},#{folderId},#{isFolder},#{hasAcl},#{uid});
    </insert>
    <insert id="savePlugDashboardInfo">
     insert into dc_databuff_plugin_dashboard (api_key, plugin_dic_id, dashboard_id)
     values (#{apiKey},#{pluginDicId},#{dashboardId});
    </insert>
    <update id="updatePluginCheckDataTimeByApiKeyDicId">
        INSERT INTO `dc_databuff_plugin`(`api_key`, `plugin_dic_id`,last_cheak_data_time)
        VALUES(#{apiKey},#{id},now())
        ON DUPLICATE KEY
        UPDATE last_cheak_data_time = now()
    </update>
    <select id="queryPluginList" resultType="com.databuff.entity.DatabuffPlugin">
        select t1.*,t2.is_install,t2.last_cheak_data_time from (
            select id,`plugin_name`,`plugin_describe`,`plugin_icon`,`plugin_overide`,`plugin_conf`,update_time,
            `plugin_metric`,`plugin_collector`,`is_hot`,`is_enable`,`base_show`,`plugin_check`
            from dc_databuff_plugin_dic
            <where>
                plugin_name != '主机'
                <if test="pluginName != null">
                    and plugin_name like CONCAT('%',#{pluginName},'%')
                </if>
            </where>
        ) t1 left join (select plugin_dic_id,is_install,last_cheak_data_time,
          create_time,update_time from dc_databuff_plugin where api_key=#{apiKey}
        ) t2 on t1.id = t2.plugin_dic_id order by t1.update_time
    </select>
    <select id="getPluginByApiKeyPlugId" resultType="com.databuff.entity.DatabuffPlugin">
        select id,plugin_name from dc_databuff_plugin_dic where id=#{id}
    </select>
    <select id="getDashboardUId" resultType="java.lang.String">
        select uid from dashboard where title = #{title} and org_id = (
            select id from org where `name` = #{apiKey})
    </select>
    <select id="getDashboardData" resultType="java.lang.String">
        select `data` from dashboard where title = #{title} and org_id = (
            select id from org where `name` = #{apiKey})
    </select>
    <select id="getMenuPathByApiKey" resultType="java.lang.String">
        select t1.path from dc_resources t1
        JOIN (select resource_id from dc_saas_tenant_resource where api_key = #{apiKey}) t2
        where t1.id = t2.resource_id
    </select>
    <select id="getMenuPathByUser" resultType="java.lang.String">
        select r.path from dc_user u
        LEFT JOIN dc_user_role ur on u.id=ur.user_id LEFT JOIN dc_role_resources rr on rr.role_id=ur.role_id
        RIGHT JOIN dc_resources r ON rr.resources_id=r.id where u.account=#{account} and hidden=1 order by `order`
    </select>
    <select id="queryPluginCollector" resultType="java.lang.String">
        select t1.plugin_collector from (
        select id,`plugin_name`,`plugin_describe`,`plugin_icon`,`plugin_overide`,`plugin_conf`,update_time,
        `plugin_metric`,`plugin_collector`,`is_hot`,`is_enable`,`base_show`
        from dc_databuff_plugin_dic where id >11
        ) t1 left join (select plugin_dic_id,is_install,last_cheak_data_time,
        create_time,update_time from dc_databuff_plugin
        <where>
            1=1
            <if test="apiKey != null">
                and api_key = #{apiKey}
            </if>
        </where>
        ) t2 on t1.id = t2.plugin_dic_id
        <where>
            1=1
            <if test="isInstall != null">
                and is_install = #{isInstall}
            </if>
        </where>
        order by t1.update_time
    </select>
    <select id="queryInstalledPluginApps" resultType="java.lang.String">
        SELECT app from dc_databuff_plugin_dic ddpd where is_enable =1 and base_show =1 and  id  &lt;= 11 or id in ( SELECT plugin_dic_id from dc_databuff_plugin where api_key=  #{apiKey} and is_install = #{isInstall})
    </select>

    <select id="getPluginOpenMetrics" resultType="com.databuff.entity.DatabuffPluginMetric">
        SELECT pm.* FROM `dc_databuff_plugin_metric` pm  where pm.api_key = #{apiKey} and pm.app= #{app}
    </select>

    <select id="getPluginOpenMetricsAppList" resultType="java.lang.String">
        SELECT pm.app FROM `dc_databuff_plugin_metric` pm  where pm.api_key = #{apiKey}
    </select>

    <select id="getAllPluginOpenMetrics" resultType="com.databuff.entity.DatabuffPluginMetric">
        SELECT api_key as apiKey,app, update_user as updateUser, open_metrics as openMetrics,version FROM
        `dc_databuff_plugin_metric` where api_key= #{apiKey}
    </select>

    <select id="getMetaConfig" resultType="com.databuff.entity.MetaConfigEntity">
        select * from `dc_sys_meta_config` where code=#{code}
    </select>

    <select id="getAAUList" resultType="com.databuff.entity.AgentAuthUnit">
        select * from `dc_databuff_aau` where api_key=#{apiKey}
    </select>

    <select id="getAAUByHostId" resultType="com.databuff.entity.AgentAuthUnit">
        select * from `dc_databuff_aau` where host_id=#{hostId} and api_key=#{apiKey} limit 1
    </select>

    <update id="updateAAUByHostId">
        update `dc_databuff_aau`
        <set>
            <if test="null != hostMem and '' != hostMem">host_mem = #{hostMem},</if>
            <if test="null != hostName and '' != hostName">host_name = #{hostName},</if>
            <if test="null != aau and '' != aau">aau = #{aau},</if>
            <if test="null != lastTime">last_time = #{lastTime},</if>
            <if test="null != updateTime">update_time = #{updateTime},</if>
        </set>
        where host_id = #{hostId} and api_key=#{apiKey}
    </update>

    <insert id="addAAU">
        insert into `dc_databuff_aau` (`host_id`,`host_mem`,`host_name`,`aau`,`last_time`,`update_time`,`api_key`)
            values (#{hostId},#{hostMem},#{hostName},#{aau},#{lastTime},#{updateTime},#{apiKey})
        ON DUPLICATE KEY
        UPDATE last_time=#{lastTime}
    </insert>

    <delete id="deleteAAUByHostId">
        delete from `dc_databuff_aau` where host_id = #{hostId} and api_key=#{apiKey}
    </delete>

    <delete id="batchDelAAUByHostIds">
        delete from `dc_databuff_aau` where host_id in
        <foreach collection="hostIds" item="hostId" open="(" separator="," close=")">
            #{hostId}
        </foreach>
        and api_key=#{apiKey}
    </delete>

    <update id="updateAAUUpdateTimeByHostId">
        update `dc_databuff_aau`
        <set>
            <if test="null != lastTime and '' != lastTime">last_time = #{lastTime},</if>
            <if test="null != updateTime and '' != updateTime">update_time = #{updateTime},</if>
        </set>
        where host_id = #{hostId} and api_key=#{apiKey}
    </update>

    <insert id="savePluginOpenMetrics">
        INSERT INTO `dc_databuff_plugin_metric`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != apiKey and '' != apiKey">
                api_key,
            </if>
            <if test="null != app and '' != app">
                app,
            </if>
            <if test="null != openMetrics and '' != openMetrics">
                open_metrics,
            </if>
            <if test="null != version ">
                version,
            </if>
            <if test="null != updateUser and '' != updateUser">
                update_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != apiKey and '' != apiKey">
                #{apiKey},
            </if>
            <if test="null != app and '' != app">
                #{app},
            </if>
            <if test="null != openMetrics and '' != openMetrics">
                #{openMetrics},
            </if>
            <if test="null != version ">
                #{version},
            </if>
            <if test="null != updateUser and '' != updateUser">
                #{updateUser},
            </if>
        </trim>
        ON DUPLICATE KEY UPDATE
        <if test="null != openMetrics and '' != openMetrics">open_metrics=#{openMetrics},</if>
        <if test="null != updateUser and '' != updateUser">update_user=#{updateUser},</if>
        <if test="null != updateTime ">update_time=#{updateTime},</if>
        version=#{version}
    </insert>

</mapper>