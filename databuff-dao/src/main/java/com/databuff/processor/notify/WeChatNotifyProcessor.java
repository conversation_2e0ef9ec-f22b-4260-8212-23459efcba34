package com.databuff.processor.notify;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.constants.Constant;
import com.databuff.common.exception.CustomException;
import com.databuff.common.utils.dingtalk.DingTalkUtils;
import com.databuff.common.utils.dingtalk.NotifyDingTalkConfig;
import com.databuff.common.utils.wechat.NotifyWeChatConfig;
import com.databuff.dao.mysql.NotifyMapper;
import com.databuff.entity.NotifyConfig;
import com.databuff.entity.RcvUser;
import com.databuff.service.NotifyService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class WeChatNotifyProcessor implements NotifyProcessor {

    @Autowired
    private NotifyMapper notifyMapper;
    @Autowired
    private NotifyService notifyService;
    private final static String SEPARATOR = "#&#";
    @Override
    public String sendNotification(String apiKey, JSONObject params) throws Exception {
        NotifyDingTalkConfig notifyConfig = getDingTalkConfig(apiKey);
        if (notifyConfig.getRobotEnable() == 0) {
            throw new CustomException("请启动钉钉机器人消息发送开关");
        }

        notifyConfig.setDingSecret(params.getString("dingSecret"));
        notifyConfig.setDingWebhook(params.getString("dingWebhook"));

        try {
            DingTalkUtils.sendRobotMessage(
                    notifyConfig.getDingSecret(),
                    notifyConfig.getDingWebhook(),
                    "【Databuff可观测平台】这是一条测试通知",
                    "【Databuff可观测平台】这是一条测试通知",
                    "text",
                    null
            );
        } catch (Exception e) {
            throw new CustomException("钉钉消息发送失败: " + e.getMessage());
        }

        return "ok";
    }

    private NotifyDingTalkConfig getDingTalkConfig(String apiKey) {
        NotifyConfig config = notifyMapper.getNotifyConfig(apiKey, Constant.Notify.DINGTALK, null);
        NotifyDingTalkConfig notifyConfig = new NotifyDingTalkConfig();
        if (config != null) {
            BeanUtils.copyProperties(config, notifyConfig);
        }
        return notifyConfig;
    }

    @Override
    public List<JSONObject> doNotify(Map<String, Object> notifyConfigMap, Map<Integer,RcvUser> allRcvUsers, JSONObject alarm, JSONObject respAction) throws Exception {

        String apiKey = alarm.getString("apiKey");
        String contentMsg = alarm.getString("description");

        NotifyWeChatConfig notifyConfig = (NotifyWeChatConfig) notifyConfigMap.get(apiKey + ":" + Constant.Notify.WECHAT);
        if (StringUtils.isBlank(respAction.getString("uid")) ) {
            throw new CustomException("企业微信个人通知未发现用户id");
        }
        if ( notifyConfig.getTenantEnable() == 0) {
            throw new CustomException("企业微信个人通知未开启");
        }
        //个人通知
        if (StringUtils.isBlank(notifyConfig.getCorpid()) || StringUtils.isBlank(notifyConfig.getCorpsecret()) || notifyConfig.getWechatAgentId() == null) {
            throw new CustomException("企业微信通知corpid,corpsecret,agentid存在空值");
        }
        List<RcvUser> rcvUsers = NotifyUtil.getNoticeObj(allRcvUsers,respAction);
        Set<String> useridSet = new HashSet<>();
        List<RcvUser> sendRcvUsers = new ArrayList<>();
        for (RcvUser rcvUser : rcvUsers) {
            if (StringUtils.isNotBlank(rcvUser.getWechatUid())) {
                //企业微信个人通知
                useridSet.add(rcvUser.getWechatUid());
                sendRcvUsers.add(rcvUser);
            }
        }
        if (useridSet.size() == 0) {
            throw new CustomException("企业微信个人通知未获取到相应的发送对象");
        }
        String errorMsg = null ;
        try {
            notifyService.weChatAlarmToOne(notifyConfig, apiKey, contentMsg.toString(), "text", new ArrayList<>(useridSet));
        } catch (Exception e) {
            errorMsg = e.getMessage();
        }
        List<JSONObject> notifyInfos = new ArrayList<>();
        //正常通知生成通知记录
        JSONObject notifyInfo = new JSONObject();
        NotifyUtil.setNotifyInfo(sendRcvUsers,notifyInfo,errorMsg);
        notifyInfos.add(notifyInfo);
        return notifyInfos;
    }
}
