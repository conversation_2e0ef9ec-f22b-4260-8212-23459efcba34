package com.databuff.processor.apm;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.constants.Constant;
import com.databuff.service.ServiceSyncService;

import java.util.List;
import java.util.Map;


public abstract class Processor {

    public abstract List<String> getCaseType();

    public abstract Map<String, String> getFilteredColumns();

    public abstract String processRootUrl(String apiKey, String service, String serviceId, long fromTime, long toTime, long abnormalFromTime, long abnormalToTime, ServiceSyncService serviceSyncService, JSONObject rootJSONObject);

    public void processRootItemUrl(String apiKey, String service, String serviceId, long fromTime, long toTime, List<String> filteredColumn, String rootUrl, ServiceSyncService serviceSyncService, JSONObject rootItem) {
        String rootItemTotalUrl = processRootItemTotalUrl(apiKey, service, serviceId, fromTime, toTime, rootItem);
        if (rootItemTotalUrl == null) {
            rootItemTotalUrl = rootUrl;
        }
        StringBuilder rootItemBuilder = new StringBuilder(rootItemTotalUrl);
        for (String column : filteredColumn) {
            String columnParams = getFilteredColumns().get(column);
            rootItemBuilder.append(Constant.PARAMS_JOIN).append(columnParams).append(rootItem.getString(column));
        }
        rootItem.put(Constant.URL, rootItemBuilder.toString());
    }

    public String processRootItemTotalUrl(String apiKey, String service, String serviceId, long fromTime, long toTime, JSONObject rootItem) {
        return null;
    }


}
