package com.databuff.processor.apm.impl.out;

import java.util.Arrays;
import java.util.List;

import static com.databuff.common.constants.Constant.*;

public class ObjectPoolProcessor extends PoolProcessor {
    @Override
    public List<String> getCaseType() {
        return Arrays.asList(CASE_TYPE_OBJECT_POOL);
    }

    @Override
    public String getComponentType() {
        return TYPE_SERVICE_REDIS;
    }

    @Override
    public String getSt() {
        return ST_CACHE;
    }

    @Override
    protected String getViewType() {
        return VIEW_TYPE_OBJECT;
    }
}
