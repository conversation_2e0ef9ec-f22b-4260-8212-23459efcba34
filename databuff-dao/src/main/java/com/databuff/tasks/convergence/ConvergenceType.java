package com.databuff.tasks.convergence;

/**
 * 收敛类型
 */
public enum ConvergenceType {
    /**
     * 无
     */
    NONE("无"),
    /**
     * 固定时长窗口
     */
    FIXED_DURATION_WINDOW("固定时长窗口"),
    /**
     * 无事件窗口
     */
    END_JUDGMENT_WINDOW("无事件窗口"),
    /**
     * 智能窗口
     */
    SAME_ROOT_CAUSE("智能窗口"),
    /**
     * 默认收敛
     */
    DEFAULT("");

    private final String nameZh;

    ConvergenceType(String nameZh) {
        this.nameZh = nameZh;
    }

    /**
     * 获取中文名称
     */
    public String getNameZh() {
        return nameZh;
    }
}