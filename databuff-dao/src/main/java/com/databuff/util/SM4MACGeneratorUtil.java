package com.databuff.util;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Hex;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.security.Security;
import java.util.Arrays;

/**
 * 山东城商行 加密
 */
public class SM4MACGeneratorUtil {

    private static final String ALGORITHM = "SM4/ECB/NoPadding";

    public static void main(String[] args) {
        Security.addProvider(new BouncyCastleProvider());

        try {
            String mabHex = "000068300047940250120241114135209070601212000089600448981";
            String makHex = "F989B573AC8B177340941E0A860E1505";

            byte[] mab = mabHex.getBytes();
            byte[] mak = Hex.decode(makHex);

            byte[] mac = generateSingleLengthKeyMAC(mab, mak);
            //D9E8D34625F5654C57A71BA3DE6A2D25
            System.out.println("MAB:"+mabHex+"   MAK:"+makHex+"  Generated MAC: " + bytesToHex(mac));


            String bmk = "********************************";
            String cipherKey="084E901F3EA79CC87947D32D7FA0F20B8933BED534EE1610";
            byte[] bmkBytes = Hex.decode(bmk);
            byte[] cipherKeyBytes = cipherKey.getBytes();
            byte[]  mab_decrypt = decrypt(bmkBytes,cipherKeyBytes);
            //F989B573AC8B177340941E0A860E1505
            System.out.println("  decrypt MAC: " + bytesToHex(mab_decrypt));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static byte[] generateSingleLengthKeyMAC(byte[] mab, byte[] key) throws Exception {
        // 确保MAB是8字节的倍数
        if (mab.length % 8 != 0) {
            int paddingLength = 8 - (mab.length % 8);
            mab = Arrays.copyOf(mab, mab.length + paddingLength);
            for (int i = mab.length - paddingLength; i < mab.length; i++) {
                mab[i] = (byte) 0x00;
            }
        }

        SecretKeySpec secretKey = new SecretKeySpec(key, "SM4");
        Cipher cipher = Cipher.getInstance(ALGORITHM, "BC");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);

        byte[] result = new byte[16];
        int length = mab.length;

        for (int i = 0; i < length; i += 16) {
            byte[] group = new byte[16];
            System.arraycopy(mab, i, group, 0, 16);

            // 与前一次加密结果进行异或
            for (int j = 0; j < 16; j++) {
                result[j] ^= group[j];
            }

            // 加密结果
            result = cipher.doFinal(result);
        }

        return result;
    }


    public static byte[] decrypt(byte[] encryptedData, byte[] key) throws Exception {
        Security.addProvider(new BouncyCastleProvider());
        SecretKeySpec secretKey = new SecretKeySpec(key, "SM4");
        Cipher cipher = Cipher.getInstance(ALGORITHM, "BC");
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        return cipher.doFinal(encryptedData);
    }

    public static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString().toUpperCase();
    }
}