package com.databuff.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.databuff.kafka.KafkaSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @package com.dctech.andrea.util
 * @company: dacheng
 * @author: zlh
 * @createDate: 2020/8/3
 */
@Slf4j
@Component
public class KafkaUtil {

    @Autowired
    public KafkaSender kafkaSender;

    /**
     * 发送数据
     *
     * @param list
     */
    public void producerSend(List<JSONObject> list, String topic) throws Exception {
        try {

            for (JSONObject jsonObject : list) {
                producerSend(jsonObject, topic);
            }
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    /**
     * 发送数据
     *
     * @param object
     */
    public void producerSend(JSONObject object, String topic) throws Exception {
        try {
            kafkaSender.data2Kafka(object, topic);
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    public void producerSend(String topic, String key, Map object) throws Exception {
        try {
            //发送
            kafkaSender.data2Kafka(JSON.toJSONString(object), topic, key);
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    public void producerSend(String topic, String key, Object object) throws Exception {
        try {
            //发送
            kafkaSender.data2Kafka(JSON.toJSONString(object), topic, key);
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    public void producerSend(String topic, String key, String object) throws Exception {
        try {
            //发送
            kafkaSender.data2Kafka(object, topic, key);
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

}
