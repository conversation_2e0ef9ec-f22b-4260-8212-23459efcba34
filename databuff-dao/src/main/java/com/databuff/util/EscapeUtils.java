package com.databuff.util;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collection;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * EscapeUtils类提供了转义字符串中特殊字符的方法。
 */
@Slf4j
public class EscapeUtils {
    private static final String REGEX = "[\\\\\"!#$%&'()*+,-./:;<=>?@\\[\\]^_`{|}~]";
    private static final String REPLACEMENT = "\\\\$0";

    /**
     * 用于转义一个字符串中的特殊字符
     *
     * @param s
     * @return
     */
    public static String escape(String s) {
        if (s == null || s.isEmpty()) {
            return s;
        }
        // 检查 s 是否已经转义
        if (s.contains("\\=")) {
            log.warn("String {} has already been escaped", s);
            return s;
        }
        // 如果 s 尚未转义，则转义它
        Pattern p = Pattern.compile(REGEX);
        Matcher m = p.matcher(s);
        return m.replaceAll(REPLACEMENT);
    }

    /**
     * 用于转义一个字符串集合中的所有字符串
     *
     * @param arr
     * @return
     */
    public static Collection<String> escape(Collection<String> arr) {
        if (arr == null || arr.isEmpty()) {
            return arr;
        }
        Collection<String> result = new ArrayList<>(arr.size());
        for (String s : arr) {
            result.add(escape(s));
        }
        return result;
    }
}