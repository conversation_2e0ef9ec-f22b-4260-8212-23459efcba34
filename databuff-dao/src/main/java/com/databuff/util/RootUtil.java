package com.databuff.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.databuff.common.constants.Constant;
import com.databuff.entity.TraceServiceEntity;
import com.databuff.moredb.model.util.Pair;
import com.databuff.moredb.model.util.TimeUtil;
import com.databuff.service.ServiceSyncService;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.*;

import static com.databuff.common.constants.Constant.*;

public class RootUtil {

    public static Map<Pair<Long, Long>, Set<String>> collectTimeRangeServiceIdSet(ServiceSyncService serviceSyncService, List<JSONObject> jsonObjects) {
        Map<Pair<Long, Long>, Set<String>> timeRangeForServiceIdSet = new HashMap<>();
        if (jsonObjects == null) {
            return timeRangeForServiceIdSet;
        }
        for (JSONObject jsonObject : jsonObjects) {
            doCollectServiceId(timeRangeForServiceIdSet, serviceSyncService, jsonObject);
        }
        return timeRangeForServiceIdSet;
    }

    private static void doCollectServiceId(Map<Pair<Long, Long>, Set<String>> timeRangeForServiceIdSet, ServiceSyncService serviceSyncService, JSONObject jsonObject) {
        JSONArray rootsJSONArray = jsonObject.getJSONArray(Constant.ROOTS);
        if (rootsJSONArray == null) {
            return;
        }
        String service = jsonObject.getString(Constant.Trace.SERVICE);
        if (StringUtils.isEmpty(service)) {
            return;
        }
        TraceServiceEntity traceServiceEntity = serviceSyncService.getTraceServiceEntityByChineseName(service);
        if (traceServiceEntity != null) {
            Pair<Long, Long> timeRange = collectTimeRange(jsonObject);
            timeRangeForServiceIdSet.computeIfAbsent(new Pair<>(timeRange.getKey(), timeRange.getValue()), k -> new HashSet<>())
                    .add(traceServiceEntity.getId());
            jsonObject.put(Constant.Trace.SERVICE_ID, traceServiceEntity.getId());
        }
        JSONArray childrenJSONArray = jsonObject.getJSONArray(Constant.CHILDREN);
        if (childrenJSONArray == null || childrenJSONArray.size() == 0) {
            return;
        }
        for (Object childObject : childrenJSONArray) {
            JSONObject childJSONObject = (JSONObject) childObject;
            doCollectServiceId(timeRangeForServiceIdSet, serviceSyncService, childJSONObject);
        }
    }

    public static Pair<Long, Long> collectTimeRange(JSONObject jsonObject) {
        long fromTime = jsonObject.getLongValue(START_TIME);
        long toTime = jsonObject.getLongValue(END_TIME);
        JSONObject abnormalDetailObj = jsonObject.getJSONObject(ABNORMAL_DETAIL);
        if (abnormalDetailObj != null) {
            int abnormalFirstIndex = abnormalDetailObj.getIntValue(ABNORMAL_FIRST_INDEX);
            int abnormalLastIndex = abnormalDetailObj.getIntValue(ABNORMAL_LAST_INDEX);
            long originalFromTime = fromTime;
            fromTime = originalFromTime + abnormalFirstIndex * TimeUtil.ONE_MINUTE;
            toTime = originalFromTime + (abnormalLastIndex + 1) * TimeUtil.ONE_MINUTE;
        }
        return new Pair<>(fromTime / 60000 * 60000, toTime / 60000 * 60000);
    }

    public static String generateProblemId(String apiKey, String serviceType, String service, long abnormalFirstTime, String gid) {
        return RootUtil.generateUid(gid + apiKey + serviceType + service + abnormalFirstTime);
    }

    public static String generateUid(String key) {
        UUID uuid = UUID.nameUUIDFromBytes(key.getBytes(StandardCharsets.UTF_8));
        return uuid.toString().replace("-", "");
    }

    public static String generateProblemDesc(JSONObject rootObject, JSONObject rootItem) {
        List<Object> columns = rootObject.getJSONArray(COLUMNS);
        List<Object> columnsDesc = rootObject.getJSONArray(COLUMNS_DESC);
        StringBuilder sb = new StringBuilder();
        for (int i = 0, len = columns.size(); i < len; i++) {
            String column = columns.get(i).toString();
            if (column.equals(COLUMN_ROOT_RESOURCE)) {
                continue;
            }
            String columnDesc = columnsDesc.get(i).toString();
            String columnValue = rootItem.getString(column);
            sb.append(columnDesc).append(" 【").append(columnValue).append("】，");
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }

}
