package com.databuff.entity.dto;

import lombok.Data;

import java.util.Date;

/**
 * @author:TianMing
 * @date: 2024/1/6
 * @time: 13:54
 */
@Data
public class LogEntity {
    /**
     * 日志时间毫秒
     */
    private Date timestamp;

    /**
     * 服务名
     */
    private String service;

    /**
     * 服务id
     */
    private String serviceId;

    /**
     * 服务实例
     */
    private String serviceInstance;

    /**
     * span_id
     */
    private String span_id;

    /**
     * trace_id
     */
    private String trace_id;

    /**
     * 信息
     */
    private String message;

    /**
     * 日志时间纳秒
     */
    private String ts;

    /**
     * 状态
     */
    private String status;

    /**
     * apiKey
     */
    private String apiKey;

    /**
     * 主机名
     */
    private String hostname;

    /**
     * uid )PARTITION BY RANGE( timestamp ) ()DISTRIBUTED BY HASH( uid )
     */
    private String uid;
}
