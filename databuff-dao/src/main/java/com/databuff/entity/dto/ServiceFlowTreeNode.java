package com.databuff.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.*;

/**
 * @author:TianMing
 * @date: 2022/4/14
 * @time: 11:07
 */
@Data
public class ServiceFlowTreeNode {
    @ApiModelProperty("该层级服务uid")
    private String uid;
    @ApiModelProperty("父级服务uid")
    private String parentId;
    @ApiModelProperty("层级")
    private Integer level;
    @ApiModelProperty("服务名称")
    private String service;
    @ApiModelProperty("资源名称")
    private String resource;
    @ApiModelProperty("服务id")
    private String serviceId;
    @ApiModelProperty("来源于上级的调用次数")
    private Long srcCall = 1L;
    @ApiModelProperty("总调用次数（包含自己调用自己）")
    private Long call = 1L;
    @ApiModelProperty("总调用次数（包含自己调用自己）")
    private Integer isIn = 1;
    @ApiModelProperty("主机名称")
    private String hostName;
    @ApiModelProperty("主机ip")
    private String hostIp;
    @ApiModelProperty("主机id")
    private String hostId;

    @ApiModelProperty("当前服务接收到的请求中需要调用下级各个服务的请求数量")
    private Map<String, Long> callMap = new HashMap<>();

    @ApiModelProperty("外发请求数（将子节点srccall相加")
    private Long outCall = 1L;
    @ApiModelProperty("总错误次数（包含自己调用自己）")
    private Long error;
    @ApiModelProperty("总响应时间（包含自己调用自己）")
    private Long duration;
    @ApiModelProperty("平均响应时间（包含自己调用自己）")
    private Long avgDuration;
    @ApiModelProperty("涉及调用目标服务的百分比，当前srccall/上级call")
    private Double callPct;
    @ApiModelProperty("平均调用次数，当前call/当前srccall")
    private Double avgCall;
    @ApiModelProperty("响应时间贡献度，avgDuration * callPct * avgCall / 根的avgDuration ")
    private Double durationCvPct;
    private List<ServiceFlowTreeNode> children = new Vector<>();

    @ApiModelProperty("该节点最大响应时间")
    private Long maxDuration;
    @ApiModelProperty("该节点最小响应时间")
    private Long minDuration;
    @ApiModelProperty("是否属于外部调用")
    private Integer isCall = 1;
    private Set<String> resources;

}
