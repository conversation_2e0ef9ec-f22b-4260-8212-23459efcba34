package com.databuff.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "查询参数")
@Data
public class MonitorSearchParams extends CommonSearchParams {

    @ApiModelProperty(value = "是否为系统内置监控规则", example = "true")
    @TableField(value = "`system`")
    private boolean system;

    @ApiModelProperty(value = "apiKey", example = "1")
    private String apiKey;
    @ApiModelProperty(value = "规则名称", example = "1")
    private String ruleName;

    @ApiModelProperty(value = "检测类型", example = "1")
    private String classification;

    @ApiModelProperty(value = "监控目标类型")
    @TableField(value = "target")
    private String target;
    @ApiModelProperty(value = "实体对象服务ID")
    @TableField(value = "service_ids")
    private String serviceId;

    @ApiModelProperty(value = "实体对象服务实例")
    @TableField(value = "service_instances")
    private String serviceInstance;

    @ApiModelProperty(value = "实体对象主机名")
    @TableField(value = "hosts")
    private String host;

    @ApiModelProperty(value = "实体对象进程组名")
    @TableField(value = "pnames")
    private String pname;
    @ApiModelProperty(value = "实体对象业务系统名")
    @TableField(value = "businessNames")
    private String businessName;



}