package com.databuff.entity;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ProcessIdentifyRulesEntity {

    // id
    private Long id;

    // 进程名称
    private String processName;

    // 匹配进程
    private String matchProcess;

    // 0 预置，1 非预置
    private Integer type;

    // 1 启用，0 禁用
    private Integer status;

    // 1 内置 0 自定义
    private Integer inner;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
