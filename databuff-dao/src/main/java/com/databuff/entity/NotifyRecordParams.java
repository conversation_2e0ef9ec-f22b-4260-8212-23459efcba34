package com.databuff.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author:TianMing
 * @date: 2023/12/13
 * @time: 10:24
 */
@Data
public class NotifyRecordParams extends NotifyRecord {

    private Integer pageNum = 1;

    private Integer pageSize = 10;

    private Date from;

    private Date to;

    @ApiModelProperty("排序字段")
    private String sortField = "NOTICE_TIME";
    @ApiModelProperty("排序方式 asc desc")
    private String sortOrder = "DESC";
}
