package com.databuff.entity.extend;

import lombok.Getter;
import lombok.Setter;

/**
 * @package com.databuff.webapp.infrastructure.model
 * @company: dacheng
 * @author: zlh
 * @createDate: 2022/4/25
 */
@Getter
@Setter
public class NetDeviceDto {
    /**
     * 设备标识，名称+ip
     */
    private String netDevice;
    private String apiKey;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 设备ip
     */
    private String deviceIp;
    private Long updateTime;

    public NetDeviceDto(){}
    public NetDeviceDto(String netDevice, String apiKey, String deviceName, String deviceIp, Long updateTime){
        this.netDevice = netDevice;
        this.apiKey = apiKey;
        this.deviceIp = deviceIp;
        this.deviceName = deviceName;
        this.updateTime = updateTime;
    }
}
