package com.databuff.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Setter
@Getter
@ToString
@TableName( value ="dc_report_template" )
public class ReportTemplateEntity {

    /**
     * 报告模版名称
     */
    @TableField(value="name")
    private String name;

    /**
     * 报告模版id
     */
    @TableId(value="id", type= IdType.AUTO)
    private Integer id;

    /**
     * 报告类型：1-日报，2-周报，3-自定义
     */
    @TableField(value="type")
    private Integer type;

    /**
     * 报告模版是否启用：0-停用，1-启用
     */
    @TableField(value="status")
    private Integer status;

    /**
     * 报告模版创建时间
     */
    @TableField(value="create_time")
    private Date createTime;

    /**
     * 报告模版更新时间
     */
    @TableField(value="update_time")
    private Date updateTime;

    /**
     * 报告模版待生成的周期时间
     * { type: 'week'|'day', day: 1-7, time: 'HH:mm:ss', range: 1|-1|7|-7 }
     * type: 周期类型，week-周，day-日
     * day: 周期类型为周时，表示周几，1-7分别表示周一到周日；周期类型为日时，表示每日
     * time: 周期时间，格式为hh:mm:ss
     * range: 周期范围，1表示当天，-1表示前一天，7表示本周，-7表示前一周
     */
    @TableField(value="cycle_time")
    private String cycleTime;

    /**
     * 报告模版待生成的自定义时间
     * { execute: 'YYYY-MM-DD HH:mm:ss', fromTime: 'YYYY-MM-DD HH:mm:ss', toTime: 'YYYY-MM-DD HH:mm:ss' }
     */
    @TableField(value="custom_time")
    private String customTime;

    /**
     * 报告模版内容
     */
    @TableField(value="content")
    private String content;

    /**
     * 报告接收人
     */
    @TableField(value="receivers")
    private String receivers;

    /**
     * 是否启用邮件通知
     */
    @TableField(value="emailable")
    private Integer emailable;

    /**
     * 是否启用钉钉机器人
     */
    @TableField(value="dingable")
    private Integer dingable;

    /**
     * 报告模版创建者
     */
    @TableField(value="creator")
    private String creator;
    /**
     * 报告模版创建者
     */
    @TableField(value="api_key")
    private String apiKey;
}
