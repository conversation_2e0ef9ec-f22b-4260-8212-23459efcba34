package com.databuff.entity;

import com.databuff.common.tsdb.dto.CompositeCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "单条异常分类规则配置")
public class BizEventExceptionRule implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "错误类型", required = true, example = "biz", allowableValues = "biz, system",
            notes = "'biz' 表示业务逻辑错误, 'system' 表示系统或基础设施错误")
    private String errorType;

    @ApiModelProperty(value = "匹配此规则后指定的错误名称或标识符", required = true, example = "INSUFFICIENT_BALANCE")
    private String errorName;

    @ApiModelProperty(value = "规则的优先级，数字越小优先级越高", required = true, example = "10")
    private Integer priority;

    @ApiModelProperty(value = "conditions 生成的展示名称", required = true, example = "A AND B AND (C OR D)")
    private String queryStr;

    /**
     * 匹配此规则需要满足的条件。
     * 这通常是一个嵌套的条件结构对象（例如，映射到 com.databuff.common.tsdb.dto.CompositeCondition 或类似结构）。
     * 使用 Object 类型以提供灵活性，具体结构由 JSON 决定，并在 Service 层进行校验。
     *
     * @see com.databuff.common.tsdb.dto.CompositeCondition
     * @see com.databuff.common.tsdb.dto.FilterCondition
     */
    @ApiModelProperty(value = "规则匹配条件 (嵌套结构，例如 CompositeCondition)", required = true,
            example = "[{\"connector\":\"AND\",\"left\":\"hostName\",\"operator\":\"=\",\"right\":\"193\",\"caseInsensitive\":true}," +
                    "{\"connector\":\"AND\",\"left\":\"hotspot\",\"operator\":\"=\",\"right\":\"80\",\"caseInsensitive\":true}," +
                    "{\"connector\":\"AND\",\"left\":[{\"connector\":\"OR\",\"left\":\"error\",\"operator\":\"=\",\"right\":\"1\",\"caseInsensitive\":true}," +
                    "{\"connector\":\"OR\",\"left\":\"type\",\"operator\":\"=\",\"right\":\"service\",\"caseInsensitive\":true}],\"right\":[]}]")
    private List<CompositeCondition> conditions;

    @ApiModelProperty(value = "是否内置规则,目前只有探针上报的error ,给前端判断可不可以修改", required = true)
    private Boolean preset;
}
