package com.databuff.entity.datahubv2;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.databuff.handler.TimestampToLongTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "datahub_pipeline", autoResultMap = true)
public class DataHubPipeline {
    @TableId(value = "pipeline_id", type = IdType.AUTO)
    private Integer pipelineId;

    @TableField("cluster_id")
    private Integer clusterId;

    private String name;

    private String description;

    @TableField("data_type")
    private String dataType;

    private String status;

    @TableField("total_receivers")
    private Long totalReceivers;

    @TableField(value = "last_update_time",  typeHandler = TimestampToLongTypeHandler.class)
    private Long lastUpdateTime;

    private String config;

    @TableField(value = "created_at", typeHandler = TimestampToLongTypeHandler.class)
    private Long createdAt;

    @TableField(value = "updated_at", typeHandler = TimestampToLongTypeHandler.class)
    private Long updatedAt;

    @TableField(value = "create_user_id")
    private String createUserId;

    @TableField(value = "update_user_id")
    private String updateUserId;


    /* Getter 和 Setter 方法 */

    public Integer getPipelineId() {
        return pipelineId;
    }

    public void setPipelineId(Integer pipelineId) {
        this.pipelineId = pipelineId;
    }

    public Integer getClusterId() {
        return clusterId;
    }

    public void setClusterId(Integer clusterId) {
        this.clusterId = clusterId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getTotalReceivers() {
        return totalReceivers;
    }

    public void setTotalReceivers(Long totalReceivers) {
        this.totalReceivers = totalReceivers;
    }

    public Long getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Long lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public Long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Long updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public String getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
    }
}