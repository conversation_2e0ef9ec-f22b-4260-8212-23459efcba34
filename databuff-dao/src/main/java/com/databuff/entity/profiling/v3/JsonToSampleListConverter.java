package com.databuff.entity.profiling.v3;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;

import java.util.List;

public class JsonToSampleListConverter {

    public static SampleDataV3 convert(JSONObject jsonObject) {
        SampleDataV3 sampleList = new SampleDataV3();
        sampleList.setObserverTool(jsonObject.getString("observerTool"));
        sampleList.setApiKey(jsonObject.getString("apiKey"));
        sampleList.setService(jsonObject.getString("service"));
        sampleList.setHost(jsonObject.getString("host"));
        sampleList.setServiceInstance(jsonObject.getString("serviceInstance"));

        List<ThreadStack> samples = jsonObject.getObject("samples", new TypeReference<List<ThreadStack>>() {});
        sampleList.setSamples(samples);

        List<ThreadStackBase> sampleBases = jsonObject.getObject("sampleBases", new TypeReference<List<ThreadStackBase>>() {});
        sampleList.setSampleBases(sampleBases);

        return sampleList;
    }
}