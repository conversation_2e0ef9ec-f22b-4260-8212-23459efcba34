package com.databuff.entity.profiling.v3;

import com.databuff.entity.dto.IDGenerator;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import static com.databuff.entity.profiling.v3.SampleDataV3Converter.parseMethod;

public class ProfilingStackConverter {

    /**
     * 合并堆栈和样本数据，并计算每个方法的占比率。
     *
     * @param stackDTOS 包含堆栈信息的DTO列表
     * @param busMethod 是否只显示业务方法
     * @return 合并后的ProfilingStackMethodVO集合
     */
    public static Map<String, ProfilingStackMethodVO> mergeStackAndSamples(List<ProfilingStackDTO> stackDTOS, Boolean busMethod) {
        if (stackDTOS == null) {
            return new HashMap<>();
        }

        // 合并堆栈相同的方法. 规则: 同一层级的方法名相同的方法合并为一个方法,并计算样本数 samples
        final Map<String, ProfilingStackMethodVO> fullLayerStackMethodVOMap = new TreeMap<>();
        for (ProfilingStackDTO stackDTO : stackDTOS) {
            if (stackDTO == null || stackDTO.getStackTrace() == null) {
                continue;
            }
            final Map<String, ProfilingStackMethodVO> layerMethodMap = convertStackTrace(stackDTO.getStackTrace(), stackDTO.getSamples(), stackDTO.getRsFlagIndex(), busMethod);
            for (Map.Entry<String, ProfilingStackMethodVO> integerMapEntry : layerMethodMap.entrySet()) {
                if (integerMapEntry == null) {
                    continue;
                }
                final String id = integerMapEntry.getKey();
                final ProfilingStackMethodVO methodVOMap = integerMapEntry.getValue();
                final ProfilingStackMethodVO stackMethodVOMap = fullLayerStackMethodVOMap.get(id);
                if (stackMethodVOMap == null) {
                    fullLayerStackMethodVOMap.put(id, methodVOMap);
                } else {
                    stackMethodVOMap.setSamples(stackMethodVOMap.getSamples() + methodVOMap.getSamples());
                }
            }
        }

        Integer total = 0;
        for (ProfilingStackMethodVO value : fullLayerStackMethodVOMap.values()) {
            if (value == null) {
                continue;
            }
            if (value.getLayer() == 0) {
                total = total + value.getSamples();
            }
        }
        // 计算 rate
        if (total > 0) {
            for (ProfilingStackMethodVO value : fullLayerStackMethodVOMap.values()) {
                value.setRate(value.getSamples() * 100.0 / total);
            }
        }
        return fullLayerStackMethodVOMap;
    }

    /**
     * 将堆栈跟踪信息转换为ProfilingStackMethodVO对象的映射。
     *
     * @param stackTrace  堆栈跟踪信息列表
     * @param samples     样本数量
     * @param rsFlagIndex 业务方法对应堆栈索引
     * @return 包含ProfilingStackMethodVO对象的映射，键为层级
     */
    private static Map<String, ProfilingStackMethodVO> convertStackTrace(List<String> stackTrace, Integer samples, Integer rsFlagIndex, Boolean busMethod) {
        Map<String, ProfilingStackMethodVO> layerStackMap = new TreeMap<>();
        int lastIndex = (Boolean.TRUE.equals(busMethod) && rsFlagIndex != null && rsFlagIndex >= 0) ? rsFlagIndex : stackTrace.size() - 1;

        String pid = null;
        for (int i = lastIndex; i >= 0; i--) {
            final int layer = lastIndex - i;
            ProfilingStackMethodVO methodVO = new ProfilingStackMethodVO();
            final String method = stackTrace.get(i);
            final String[] parsed = parseMethod(method);
            methodVO.setMethod(method);
            methodVO.setNamespace(parsed[0]);
            methodVO.setClassName(parsed[1]);
            methodVO.setMethodName(parsed[2]);
            // id 生成规则: 从当前层级到最后一个元素的堆栈信息作为种子
            final String seed = stackTrace.subList(i, lastIndex + 1).toString();
            final String id = IDGenerator.getNextHashID(seed);
            methodVO.setParentId(pid);
            methodVO.setId(id);
            methodVO.setLayer(layer);
            methodVO.setSamples(samples);
            methodVO.setBusMethod(Boolean.TRUE.equals(busMethod) && rsFlagIndex != null && i <= rsFlagIndex);
            layerStackMap.put(methodVO.getId(), methodVO);
            pid = id;
        }
        return layerStackMap;
    }
}