package com.databuff.entity.profiling.v3;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;
import java.util.TreeSet;

@Data
public class ProfilingFlameNodeVO implements Comparable<ProfilingFlameNodeVO> {
    @ApiModelProperty(value = "渲染偏移量，x轴")
    private Integer x;

    @ApiModelProperty(value = "方法所在层级，y轴")
    private Integer y;

    @ApiModelProperty(value = "样本量")
    private Integer width;

    @ApiModelProperty(value = "样本量")
    private Integer samples;

    @ApiModelProperty(value = "方法类型")
    private Integer type;

    @ApiModelProperty(value = "方法名称")
    private String title;

    @ApiModelProperty(value = "是否有效节点")
    @JSONField(serialzeFeatures = SerializerFeature.WriteMapNullValue)
    @JsonIgnore
    private boolean enabled;

    @ApiModelProperty(value = "节点ID")
    @JSONField(serialzeFeatures = SerializerFeature.WriteMapNullValue)
    @JsonIgnore
    private int id;

    @ApiModelProperty(value = "父节点ID")
    @JSONField(serialzeFeatures = SerializerFeature.WriteMapNullValue)
    @JsonIgnore
    private int parentId;

    @ApiModelProperty(value = "子节点")
    @JSONField(serialize = false, deserialize = false)
    @JsonIgnore
    private Set<ProfilingFlameNodeVO> subNodes = new TreeSet<>();

    @ApiModelProperty(value = "是否为业务方法")
    @JSONField(serialize = false, deserialize = false)
    @JsonIgnore
    private Boolean busMethod;


    @JsonIgnore
    private transient int cachedHashCode = 0;

    /**
     * 优化hashCode的计算效率
     *
     * @return
     */
    @Override
    public int hashCode() {
        if (cachedHashCode == 0) {
            final int prime = 31;
            int result = 1;
//            result = prime * result + ((id == null) ? 0 : id.hashCode());
            result = prime * result + id;
            cachedHashCode = result;
        }
        return cachedHashCode;
    }

    /**
     * 将此对象与指定对象进行比较以确定顺序。返回一个负整数、零或正整数，
     * 以指示此对象小于、等于或大于指定对象。
     *
     * <p>实现者必须确保 <tt>sgn(x.compareTo(y)) ==
     * -sgn(y.compareTo(x))</tt> 对于所有 <tt>x</tt> 和 <tt>y</tt> 成立。
     * （这意味着 <tt>x.compareTo(y)</tt> 必须抛出异常当且仅当
     * <tt>y.compareTo(x)</tt> 抛出异常。）
     *
     * <p>实现者还必须确保关系是传递的：
     * <tt>(x.compareTo(y)&gt;0 &amp;&amp; y.compareTo(z)&gt;0)</tt> 意味着
     * <tt>x.compareTo(z)&gt;0</tt>。
     *
     * <p>最后，实施者必须确保 <tt>x.compareTo(y)==0</tt>
     * 意味着 <tt>sgn(x.compareTo(z)) == sgn(y.compareTo(z))</tt>，对于所有
     * <tt>z</tt> 成立。
     *
     * <p>强烈建议但<i>不</i>严格要求
     * <tt>(x.compareTo(y)==0) == (x.equals(y))</tt>。一般来说，任何
     * 实现 <tt>Comparable</tt> 接口并违反此条件的类应清楚地表明这一点。
     * 推荐的语言是“注意：此类具有与 equals 不一致的自然排序。”
     *
     * <p>在上述描述中，符号
     * <tt>sgn(</tt><i>expression</i><tt>)</tt> 表示数学上的
     * <i>signum</i> 函数，该函数定义为返回 <tt>-1</tt>、
     * <tt>0</tt> 或 <tt>1</tt> 之一，具体取决于
     * <i>expression</i> 的值是负数、零还是正数。
     *
     * @param other 要比较的对象。
     * @return 一个负整数、零或正整数，以指示此对象
     * 小于、等于或大于指定对象。
     * @throws NullPointerException 如果指定的对象为 null
     * @throws ClassCastException   如果指定对象的类型阻止其
     *                              与此对象进行比较。
     */
    @Override
    public int compareTo(ProfilingFlameNodeVO other) {
        return this.width.compareTo(other.width);
    }
}