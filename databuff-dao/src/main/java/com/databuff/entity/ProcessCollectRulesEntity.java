package com.databuff.entity;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ProcessCollectRulesEntity {

    // id
    private Long id;

    // 进程名称
    private String processName;

    // 进程路径
    private String processPath;

    // 匹配进程
    private String matchProcess;

    // 主机名，配合match字端进行匹配
    private String hostname;

    // 主机名，明确指定哪些主机名
    private String hostnames;

    // IP，配合match字端进行匹配
    private String ip;

    // ip，明确指定哪些ip
    private String ips;

    // 标签
    private String tags;

    // 匹配主机，包含，以..开头，以..结尾
    private String matchHost;

    // 0 白名单，1 黑名单
    private Integer type;

    // 1 启用，0 禁用
    private Integer status;

    // 1 内置 0 自定义
    private Integer inner;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
