package com.databuff.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @author:TianMing
 * @date: 2024/2/22
 * @time: 15:54
 */
@Data
@Entity
public class K8sPodEntity {
    @Column(name = "spuid", nullable = false)
    private String spuid;

    @Column(name = "clusterId", nullable = false)
    private String clusterId;
    @Column(name = "name", nullable = false)
    private String name;
    @Column(name = "clusterName", nullable = false)
    private String clusterName;

    @Column(name = "df-api-key", nullable = false)
    private String apiKey;

    @Column(name = "namespace", nullable = false)
    private String namespace;

    @Column(name = "wlName", nullable = false)
    private String wlName;

    @Column(name = "wlKind", nullable = false)
    private String wlKind;

    @Column(name = "nodeName", nullable = false)
    private String nodeName;

    @Column(name = "status", nullable = false)
    private String status;

    @Column(name = "timestamp", nullable = false)
    private long timestamp;

    @Column(name = "type")
    private Integer type;

    // Assuming JSON is mapped to a String
    @Column(name = "data")
    private String data;
}