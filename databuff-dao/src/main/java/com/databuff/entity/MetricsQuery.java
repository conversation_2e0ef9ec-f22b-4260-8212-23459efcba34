package com.databuff.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableName;
import com.databuff.entity.enums.AggregationMethodEnum;
import com.databuff.entity.enums.BasicJudgmentEnum;
import com.databuff.entity.enums.MetricSourceEnum;
import com.databuff.entity.enums.MetricTypeEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.Collection;

@ApiModel(value = "MetricsQuery", description = "指标查询实体")
@Data
@TableName("dc_databuff_metrics_query")
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MetricsQuery {

    @ApiModelProperty(value = "类型1", notes = "指标的一级分类，如基础设施、应用、业务等")
    private String type1;

    @ApiModelProperty(value = "类型2", notes = "指标的二级分类，如服务器、数据库、网络等")
    private String type2;

    @ApiModelProperty(value = "类型3", notes = "指标的三级分类，更细粒度的分类")
    private String type3;

    @ApiModelProperty(value = "应用", notes = "指标所属的应用名称")
    private String app;

    @ApiModelProperty(value = "数据库", notes = "指标数据存储的数据库名称")
    private String database;

    @ApiModelProperty(value = "测量", notes = "指标在时序数据库中的测量名称")
    private String measurement;

    @ApiModelProperty(value = "描述", notes = "指标的详细描述信息")
    private String desc;

    @ApiModelProperty(value = "维度key", notes = "指标的标签键定义，用于指标数据的多维度分析")
    private JSONObject tagKey;

    @ApiModelProperty(value = "特殊维度key", notes = "指标的特殊标签键，用于特殊场景的数据分析")
    private JSONObject keys;

    @ApiModelProperty(value = "维度value", notes = "指标的标签值定义，与标签键对应")
    private JSONObject tagValue;

    @ApiModelProperty(value = "字段value", notes = "指标的字段值定义，用于存储指标的实际数值")
    private JSONObject fieldValue;

    @ApiModelProperty(value = "主键ID", notes = "指标在数据库中的唯一标识")
    private Long id;

    @ApiModelProperty(value = "字段", notes = "指标的字段名称，用于指定要查询的指标值")
    private String field;

    @ApiModelProperty(value = "聚合类型", notes = "指标数据的聚合方式，如avg、sum、max等")
    private String aggregatorType;

    @ApiModelProperty(value = "单位", notes = "指标的单位，如ms、bytes、percent等")
    private String unit;

    @ApiModelProperty(value = "单位(中文)", notes = "指标单位的中文表示，如毫秒、字节、百分比等")
    private String unitCn;

    @ApiModelProperty(value = "公式", notes = "衣生指标的计算公式，用于从原始指标计算得出衣生指标")
    private String formula;

    @ApiModelProperty(value = "过滤条件", notes = "指标查询的过滤条件，用于筛选特定条件的数据")
    private JSONObject filter;

    @ApiModelProperty(value = "是否开启", notes = "指标是否启用，用于控制指标的可用性")
    private Boolean isOpen;

    @ApiModelProperty(value = "创建时间", notes = "指标创建的时间戳")
    private Timestamp createTime;

    @ApiModelProperty(value = "更新时间", notes = "指标最后更新的时间戳")
    private Timestamp updateTime;

    @ApiModelProperty(value = "标识符", notes = "指标的唯一标识符，用于在系统中唯一标识指标")
    private String identifier;

    @ApiModelProperty(value = "指标中文名", notes = "指标的中文名称，用于在界面上展示")
    private String metricCn;

    @ApiModelProperty(value = "是否为核心指标", notes = "标记指标是否为核心指标，核心指标通常具有更高的优先级")
    private boolean core;

    @ApiModelProperty(value = "是否内置指标", notes = "内置指标不需要用户手动创建，由系统自动创建")
    public Boolean builtin;

    @ApiModelProperty(value = "为null时，自动填充", notes = "当指标值为null时，自动填充的默认值")
    private Double autoFill;

    @ApiModelProperty(value = "指标类型", notes = "标识数据来源(原始采集/衣生计算/预测/合成等)")
    private MetricTypeEnum metricType;

    @ApiModelProperty(value = "指标来源", notes = "预置/自定义/第三方平台等")
    private MetricSourceEnum metricSource;

    @ApiModelProperty(value = "基本判断", notes = "高好/低好等指标值的判断方向")
    private BasicJudgmentEnum basicJudgment;

    @ApiModelProperty(value = "聚合方式", notes = "平均，求和，最大值，75分位等")
    private Collection<AggregationMethodEnum> aggregationMethod;
}