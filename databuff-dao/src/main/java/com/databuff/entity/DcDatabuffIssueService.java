package com.databuff.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@TableName("dc_databuff_issue_service")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DcDatabuffIssueService {
    @TableId
    private String issueId;

    @TableField
    private String service;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}