package com.databuff.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Entity;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 指标元数据定义表
 * @date 2023-11-01
 * @since 2.7.1
 */
@Setter
@Getter
@ToString
@Entity
@TableName(value = "dc_databuff_db_measure_meta")
public class DBMeta {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * db
     */
    @TableField(value = "db")
    private String db;

    /**
     * table
     */
    @TableField(value = "table")
    private String table;

    @TableField(value = "api_key")
    private String apiKey;

//    @TableField(value = "tags", typeHandler = TagMetaTypeHandler.class)
//    @ApiModelProperty("标签元数据")
private List<TagMeta> tags;

    //    @TableField(value = "fields", typeHandler = FieldMetaTypeHandler.class)
//    @ApiModelProperty("字段元数据")
    private List<FieldMeta> fields;

    @Getter
    @Setter
    @NoArgsConstructor
    @ApiModel("系统字典内容实体类")
    public static class TagMeta {
        @ApiModelProperty("键")
        private String key;
        @ApiModelProperty("名称")
        private String name;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @ApiModel("系统字典内容实体类")
    public static class FieldMeta {
        @ApiModelProperty("键")
        private String key;
        @ApiModelProperty("名称")
        private String name;
        @ApiModelProperty("单位")
        private String unit;
    }
}
