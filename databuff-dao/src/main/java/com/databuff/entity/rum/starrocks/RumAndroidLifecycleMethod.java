package com.databuff.entity.rum.starrocks;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.databuff.entity.rum.web.IRumAndroidData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("dc_rum_android_lifecycle_method")
public class RumAndroidLifecycleMethod implements IRumAndroidData {
    @ApiModelProperty(value = "方法开始时间")
    @JSONField(name = "start_time", format="yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "应用ID")
    @JSONField(name = "app_id")
    private Integer appId;

    @ApiModelProperty(value = "关联的 ID（页面、启动或操作的 ID）")
    @JSONField(name = "associated_id")
    private Long associatedId;

    @ApiModelProperty(value = "关联类型：1=启动, 2=操作, 4=页面, 0=过期")
    @JSONField(name = "type")
    private Integer type;

    @ApiModelProperty(value = "生命周期事件的唯一 spanId")
    @JSONField(name = "span_id")
    private Long spanId;

    @ApiModelProperty(value = "生命周期方法名")
    @JSONField(name = "method_name")
    private String methodName;

    @ApiModelProperty(value = "生命周期所属页面名")
    @JSONField(name = "page_name")
    private String pageName;

    @ApiModelProperty(value = "事件开始时间（纳秒）")
    @JSONField(name = "start")
    private Long start;

    @ApiModelProperty(value = "事件结束时间（纳秒）")
    @JSONField(name = "end")
    private Long end;

    @ApiModelProperty(value = "事件持续时间（纳秒）")
    @JSONField(name = "duration")
    private Long duration;

    @ApiModelProperty(value = "租户 apiKeyId")
    @JSONField(name = "df_api_key_id")
    private Integer dfApiKeyId;
}