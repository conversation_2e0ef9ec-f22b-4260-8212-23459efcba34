package com.databuff.entity.rum.web;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@ApiModel(description = "URL聚合设置")
public class UrlAggregationSettings {
    @ApiModelProperty("参数采集规则列表")
    private List<ParameterCollectionRule> parameterCollectionRules;

    @ApiModelProperty("URI聚合规则列表")
    private List<UriAggregationRule> uriAggregationRules;

    @ApiModelProperty("是否启用参数采集规则")
    private boolean enableParameterCollection;

    @ApiModelProperty("是否启用URI聚合规则")
    private boolean enableUriAggregation;

    @Data
    public static class ParameterCollectionRule {
        @ApiModelProperty("排序")
        @NotNull
        private Integer order;

        @ApiModelProperty("参数位置:  URL参数(url_param)、HTTP请求头(http_request_header)、HTTP请求体(http_request_body)" +
                "、HTTP响应头(http_response_header)、HTTP响应体(http_response_body) ")
        @NotNull
        private ParameterLocation parameterLocation;

        @ApiModelProperty("参数名称")
        @NotNull
        @Size(max = 200)
        private String parameterName;
    }

    public enum ParameterLocation {
        url_param,
        http_request_header,
        http_request_body,
        http_response_header,
        http_response_body
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UriAggregationRule {
        @ApiModelProperty("排序")
        @NotNull
        private Integer order;

        @ApiModelProperty("规则")
        @NotNull
        @Size(max = 200)
        private String rule;
    }
}
