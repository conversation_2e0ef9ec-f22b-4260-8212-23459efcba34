package com.databuff.entity.rum.starrocks;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("dc_rum_android_page_span")
public class RumAndroidPageSpan extends BaseRumAndroidSpan {
    @ApiModelProperty(value = "页面id")
    @JSONField(name = "page_id")
    private Long pageId;
}
