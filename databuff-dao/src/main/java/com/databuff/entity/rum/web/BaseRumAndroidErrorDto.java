package com.databuff.entity.rum.web;

import com.alibaba.fastjson.annotation.JSONField;
import com.databuff.entity.rum.starrocks.BaseRumAndroidInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public abstract class BaseRumAndroidErrorDto extends BaseRumAppErrorDto implements BaseRumAndroidInfo {
    @ApiModelProperty(value = "应用版本名称")
    @JSONField(name = "app_version_name")
    private String appVersionName;
}
