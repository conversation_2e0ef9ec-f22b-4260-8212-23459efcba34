package com.databuff.entity.rum.web;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class RumBaseRequest {
    @ApiModelProperty(value = "应用名称", example = "我的应用")
    private String appName;

    @ApiModelProperty(value = "应用id", example = "1")
    private Integer appId;

    @ApiModelProperty(value = "应用类型", example = "web ios")
    private String appType;

    @ApiModelProperty(value = "常用地域", example = "北京")
    private String region;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间", example = "2023-05-01 00:00:00")
    private Date fromTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间", example = "2023-05-02 00:00:00")
    private Date toTime;
}