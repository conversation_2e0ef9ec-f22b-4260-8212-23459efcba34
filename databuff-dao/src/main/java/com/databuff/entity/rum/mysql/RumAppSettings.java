package com.databuff.entity.rum.mysql;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.databuff.entity.rum.web.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("dc_rum_app_settings")
public class RumAppSettings {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "应用ID")
    private Integer id;
    @ApiModelProperty(value = "应用唯一标识符,验证是否合法")
    private String appKey;
    @ApiModelProperty(value = "应用名称")
    private String appName;
    @ApiModelProperty(value = "应用类型 ('web', 'ios', 'android')")
    private String appType;
    // "enabled"=1, "disabled"=0
    @ApiModelProperty(value = "应用状态")
    private int status;
    @ApiModelProperty(value = "数据上传地址")
    private String dataUploadUrl;
    @ApiModelProperty(value = "是否使用全局配置")
    private boolean useGlobalConfig;
    @ApiModelProperty(value = "是否使用自定义IP地域规则")
    private boolean useCustomIpRules;
    /**
     * @see CustomIpRuleDto
     */
    @ApiModelProperty(value = "自定义IP地域规则")
    private String customIpRules;
    /**
     * @see WebScoreSettings
     * @see IosScoreSettings
     */
    @ApiModelProperty(value = "评分设置")
    private String scoreSettings;
    @ApiModelProperty(value = "是否删除")
    private boolean isDeleted;
    @ApiModelProperty("租户 api key")
    private String apiKey;
    @ApiModelProperty("租户 api key id")
    private Integer apiKeyId;
    @ApiModelProperty(value = "记录创建时间")
    private Date createTime;
    @ApiModelProperty(value = "记录更新时间")
    private Date updatedTime;
    /**
     * @see WebSecuritySettings
     * @see IosSecuritySettings
     * @see AndroidScoreSettings
     */
    @ApiModelProperty(value = "安全设置")
    private String securitySettings;
    /**
     * @see ThresholdSettings
     */
    @ApiModelProperty(value = "阈值设置")
    private String thresholdSettings;
    /**
     * @see UrlAggregationSettings
     */
    @ApiModelProperty(value = "URL聚合设置")
    private String urlAggregationSettings;
}

