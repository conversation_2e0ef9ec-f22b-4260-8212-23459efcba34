package com.databuff.entity.rum.starrocks;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.databuff.entity.rum.web.IRumData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("dc_rum_web_session")
public class RumWebSession implements IRumData {

    @ApiModelProperty(value = "Hour分区 (yyyyMMddHH)", example = "2024111010")
    @JSONField(name = "hour")
    private Integer hour;

    @ApiModelProperty(value = "minute (yyyyMMddHHmm)", example = "202411101059")
    @JSONField(name = "minute")
    private Long minute;

    @ApiModelProperty(value = "会话ID", example = "1001")
    @JSONField(name = "session_id")
    private Long sessionId;

    @ApiModelProperty(value = "会话开始时间", example = "2023-10-01 12:00:00")
    @JSONField(name = "start_time", format = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "应用ID", example = "12345")
    @JSONField(name = "app_id")
    private Integer appId;

    @ApiModelProperty(value = "用户ID", example = "user_001")
    @JSONField(name = "user_id")
    private String userId;

    @ApiModelProperty(value = "会话时长(秒)", example = "3600")
    @JSONField(name = "duration")
    @TableField(exist = false)
    private Integer duration = 0;

    @ApiModelProperty(value = "会话结束时间", example = "2023-10-01 13:00:00")
    @JSONField(name = "end_time", format = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty(value = "地域", example = "Shanghai")
    @JSONField(name = "region")
    private String region;

    @ApiModelProperty(value = "运营商", example = "China Mobile")
    @JSONField(name = "isp")
    private String isp;

    @ApiModelProperty(value = "浏览器", example = "Chrome")
    @JSONField(name = "browser")
    private String browser;

    @ApiModelProperty(value = "操作系统", example = "Windows 10")
    @JSONField(name = "operating_system")
    private String operatingSystem;

    @ApiModelProperty(value = "公网IP", example = "***********")
    @JSONField(name = "ip")
    private String ip;

    @ApiModelProperty(value = "探针版本", example = "1.0.0")
    @JSONField(name = "probe_version")
    private String probeVersion;

    @ApiModelProperty(value = "租户 apiKeyId", example = "1")
    @JSONField(name = "df_api_key_id")
    private Integer dfApiKeyId;

    @ApiModelProperty(value = "交互次数", example = "10")
    @JSONField(name = "interaction_count")
    private int interactionCount = 0;

    @ApiModelProperty(value = "异常次数", example = "2")
    @JSONField(name = "exception_count")
    private int exceptionCount = 0;
}
