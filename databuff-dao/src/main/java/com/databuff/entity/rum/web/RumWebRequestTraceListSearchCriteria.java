package com.databuff.entity.rum.web;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class RumWebRequestTraceListSearchCriteria {
    @ApiModelProperty(value = "apiKey", example = "NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4")
    private String apiKey;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fromTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date toTime;

    @ApiModelProperty(value = "应用ID", example = "12345", required = true)
    private String appId;

    @ApiModelProperty(value = "用户ID", example = "user123")
    private String userId;

    @ApiModelProperty(value = "公网IP", example = "***********")
    private String ip;

    @ApiModelProperty(value = "会话ID", example = "3001")
    private String sessionId;

    @ApiModelProperty(value = "处理过的请求URL", example = "/api/v1/users", required = true)
    private String processedHttpUrl;

    // Add pagination parameters
    @ApiModelProperty(value = "页码", example = "1")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "排序字段", example = "startTime", allowableValues = "startTime,duration,serverTime,transferSize")
    private String sortField = "startTime";

    @ApiModelProperty(value = "排序方向", example = "DESC", allowableValues = "ASC,DESC")
    private String sortOrder = "DESC";

}
