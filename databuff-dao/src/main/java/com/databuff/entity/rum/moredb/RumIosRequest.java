package com.databuff.entity.rum.moredb;

import com.baomidou.mybatisplus.annotation.TableName;
import com.databuff.entity.rum.web.IRumIosData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * iOS RUM HTTP 请求指标数据模型 (用于写入TSDB)
 * 参照 RumWebRequest 结构进行精简
 */
@Data
 @TableName("rum.ios.request")
public class RumIosRequest implements IRumIosData {

    @ApiModelProperty("用户 api key")
    private String apiKey;

    @ApiModelProperty(value = "请求开始时间 (毫秒级时间戳)")
    private long startTime; // 使用 long 存储毫秒时间戳

    // Tags (索引):
    @ApiModelProperty(value = "应用id")
    private String appId;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "调用服务")
    private String service;

    // Fields (度量值):
    @ApiModelProperty(value = "请求数 (恒为1)")
    private Long requestCount = 1L;

    @ApiModelProperty(value = "服务端耗时 (纳秒), 可能为 null。严格来自 x-databuff-span-duration")
    private Long duration;
}