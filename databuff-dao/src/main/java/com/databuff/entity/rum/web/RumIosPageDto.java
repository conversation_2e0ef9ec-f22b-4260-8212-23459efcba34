package com.databuff.entity.rum.web;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("dc_rum_ios_page")
public class RumIosPageDto extends BaseRumIosDetailDto {
    @ApiModelProperty(value = "页面id")
    @JSONField(name = "page_id")
    private String pageId;

    @ApiModelProperty(value = "页面名称")
    @JSONField(name = "page_name")
    private String pageName;
}
