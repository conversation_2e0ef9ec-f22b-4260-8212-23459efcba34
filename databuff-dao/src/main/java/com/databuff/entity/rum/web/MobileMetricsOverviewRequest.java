package com.databuff.entity.rum.web;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "app应用指标概览请求")
public class MobileMetricsOverviewRequest implements MetricsOverviewRequestBase {
    private String apiKey;
    private Integer appId;
    private Date fromTime;
    private Date toTime;
    @ApiModelProperty(value = "间隔(秒)", example = "60")
    private Integer interval;

    @ApiModelProperty(value = "字段", example = "launchDuration, pageDuration, actionDuration, requestDuration, anrRate, crashRate, launchCount, deviceCount")
    private String field;

    @ApiModelProperty(value = "聚合函数", example = "launchDuration, pageDuration, actionDuration, requestDuration 支持 avg,upper ,anrRate, crashRate(默认avg) launchCount, deviceCount(默认sum)可不传")
    private String aggregation;

    @ApiModelProperty(value = "upper函数百分位", example = "当aggregation为upper时需要传入: 99,95,90,75,50")
    private Integer upperNumber;
}
