package com.databuff.entity.rum.web;

import com.databuff.entity.dto.CommonSearchParams;
import com.databuff.entity.rum.RumJsErrorStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

@ApiModel(description = "JS错误搜索条件")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class JsErrorSearchCriteria extends CommonSearchParams {

    @NotNull(message = "应用ID不能为空")
    @Positive(message = "应用ID必须为正数")
    @ApiModelProperty(value = "应用id", required = true)
    private int appId;

    @ApiModelProperty(value = "处理人")
    private String handler;

    @ApiModelProperty(value = "处理状态")
    private RumJsErrorStatusEnum status;

    @ApiModelProperty(value = "错误信息")
    private String errorMessage;

    private int offset;

    // Add enum for sort fields specific to JsErrorListItemDto
    public enum SortField {
        errorMessage,
        errorPercentage,
        errorCount,
        affectedUsers,
        handler,
        status
    }

    @Override
    public void setSortField(String sortField) {
        if (sortField != null) {
            try {
                SortField.valueOf(sortField);
                super.setSortField(sortField);
            } catch (IllegalArgumentException e) {
                throw new IllegalArgumentException("Unsupported sort field for JS errors: " + sortField);
            }
        }
    }

    public void setSortField(SortField field) {
        if (field != null) {
            super.setSortField(field.name());
        }
    }

    public void setStatus(String status) {
        if (status != null) {
            try {
                this.status = RumJsErrorStatusEnum.valueOf(status.toLowerCase());
            } catch (IllegalArgumentException e) {
                throw new IllegalArgumentException("Unsupported status: " + status);
            }
        }
    }


}
