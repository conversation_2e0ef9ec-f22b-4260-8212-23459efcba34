package com.databuff.entity.rum.web;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class InteractionDto {
    @ApiModelProperty(value = "发生时间", example = "2023-10-01 12:00:00")
    private Date startTime;

    @ApiModelProperty(value = "交互id 包括操作 页面 启动ID", example = "2597963121942891")
    private String interactionId;

    @ApiModelProperty(value = "交互类型", example = "启动、页面、操作")
    private String interactionType;

    @ApiModelProperty(value = "交互名称", example = "http://example.com")
    private String interactionName;

    @ApiModelProperty(value = "是否可跳转", example = "true")
    private boolean canJump;

    @ApiModelProperty(value = "应用ID", example = "2")
    private Integer appId;
}
