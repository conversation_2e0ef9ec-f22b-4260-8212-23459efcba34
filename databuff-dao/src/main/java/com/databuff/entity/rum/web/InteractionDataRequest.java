package com.databuff.entity.rum.web;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class InteractionDataRequest {
    @ApiModelProperty(value = "会话ID", example = "1001", required = true)
    private Long sessionId;

    @ApiModelProperty(value = "应用类型", example = "web/ios", allowableValues = "web,ios")
    private String appType;

    @ApiModelProperty(value = "开始时间", example = "2023-05-01 00:00:00", required = true)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fromTime;

    @ApiModelProperty(value = "结束时间", example = "2023-05-02 00:00:00", required = true)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date toTime;

    @ApiModelProperty(value = "页码", example = "1", required = true)
    private int pageNum = 1;

    @ApiModelProperty(value = "每页大小", example = "10", required = true)
    private int pageSize = 10;
}
