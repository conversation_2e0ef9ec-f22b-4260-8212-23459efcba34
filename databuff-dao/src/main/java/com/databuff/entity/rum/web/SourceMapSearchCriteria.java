package com.databuff.entity.rum.web;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.domain.Sort;

@ApiModel(description = "Source Map查询参数")
@Data
public class SourceMapSearchCriteria {
    @ApiModelProperty(value = "应用ID", example = "12345", required = true)
    private Integer appId;

    @ApiModelProperty(value = "排序方向", example = "DESC")
    private Sort.Direction sortOrder = Sort.Direction.DESC;

    @ApiModelProperty(value = "页码", example = "1")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "分页数", example = "20")
    private Integer pageSize = 20;

    public void setSortOrder(String sortOrder) {
        if (sortOrder != null) {
            this.sortOrder = Sort.Direction.valueOf(sortOrder.toUpperCase());
        }
    }
}
