package com.databuff.entity.rum.starrocks;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("dc_rum_action_span")
public class RumActionSpan {

    @ApiModelProperty(value = "开始时间", example = "2023-10-01 12:00:00")
    @JSONField(name = "startTime", format="yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "操作id", example = "1001")
    @JSONField(name = "action_id")
    private Long actionId;

    @ApiModelProperty(value = "操作名称", example = "click")
    @JSONField(name = "action_name")
    private String actionName;

    @ApiModelProperty(value = "traceId", example = "trace_001")
    @JSONField(name = "trace_id")
    private Long traceId;

    @ApiModelProperty(value = "spanId", example = "span_001")
    @JSONField(name = "span_id")
    private Long spanId;

    @ApiModelProperty(value = "父spanId", example = "parent_span_001")
    @JSONField(name = "parent_id")
    private Long parentId;

    @ApiModelProperty(value = "应用id", example = "12345")
    @JSONField(name = "app_id")
    private Integer appId;

    @ApiModelProperty(value = "会话id", example = "session_001")
    @JSONField(name = "session_id")
    private Long sessionId;

    @ApiModelProperty(value = "服务名", example = "service_name")
    @JSONField(name = "service")
    private String service;

    @ApiModelProperty(value = "页面来源URL", example = "http://example.com")
    @JSONField(name = "location_href")
    private String locationHref;

    @ApiModelProperty(value = "请求URL", example = "http://example.com/request")
    @JSONField(name = "http_url")
    private String httpUrl;

    @ApiModelProperty(value = "开始时间", example = "1696156800000")
    @JSONField(name = "start")
    private Long start;

    @ApiModelProperty(value = "结束时间", example = "1696156801500")
    @JSONField(name = "end")
    private Long end;

    @ApiModelProperty(value = "耗时", example = "1500")
    @JSONField(name = "duration")
    private Long duration;

    @ApiModelProperty(value = "租户 api key id", example = "1")
    @JSONField(name = "df_api_key_id")
    private Integer dfApiKeyId;

    @ApiModelProperty(value = "相对时间", example = "1500")
    private Long relativeTime;
}