package com.databuff.entity.rum.starrocks;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.databuff.entity.rum.web.LaunchType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("dc_rum_android_launch")
public class RumAndroidLaunch extends BaseRumAndroidDetail {
    @ApiModelProperty(value = "启动id")
    @JSONField(name = "launch_id")
    private Long launchId;

    /**
     * @see LaunchType
     */
    @ApiModelProperty(value = "启动类型: 冷启动(App Start), 热启动(App Start Hot), 首次启动(App Start First)")
    @JSONField(name = "launch_type")
    private String launchType;
}
