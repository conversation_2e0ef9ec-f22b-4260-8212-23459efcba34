package com.databuff.entity.rum.web;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public abstract class AbstractWebRequestSearch implements IWebRequestFilter {
    @ApiModelProperty(value = "apiKey", example = "NzhEMjlBOTk3MzkxRjk5MjQyMzMzQzI4")
    private String apiKey;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fromTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date toTime;

    @ApiModelProperty(value = "应用ID", example = "12345")
    private String appId;

    @ApiModelProperty(value = "请求类型", example = "0", notes = "0: AJAX请求, 1: 静态资源请求")
    private Integer requestType;

    @ApiModelProperty(value = "域名", example = "example.com")
    private String domain;

    @ApiModelProperty(value = "处理过的路径", example = "/api/users")
    private String processedPath;

    @ApiModelProperty(value = "运营商", example = "China Mobile")
    private String isp;

    @ApiModelProperty(value = "状态码", example = "200")
    private String statusCode;

    @ApiModelProperty(value = "调用服务", example = "UserService")
    private String service;

    @ApiModelProperty(value = "别名", example = "Get User Profile")
    private String alias;
}