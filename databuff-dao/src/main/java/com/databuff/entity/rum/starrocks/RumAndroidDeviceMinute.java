package com.databuff.entity.rum.starrocks;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.databuff.entity.rum.web.IRumAndroidData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("dc_rum_android_device_minute")
public class RumAndroidDeviceMinute implements IRumAndroidData {
    @ApiModelProperty(value = "启动开始时间")
    @JSONField(name = "start_time", format="yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "应用ID")
    @JSONField(name = "app_id")
    private Integer appId;

    @ApiModelProperty(value = "租户 apiKeyId")
    @JSONField(name = "df_api_key_id")
    private Integer dfApiKeyId;

    @ApiModelProperty(value = "设备ID")
    @JSONField(name = "device_id")
    @TableField(exist = false)
    private Long deviceId;

    @ApiModelProperty(value = "设备ID数 Bitmap聚合")
    @JSONField(name = "device_count")
    private Object deviceCount; // Using Object type for BITMAP type
}