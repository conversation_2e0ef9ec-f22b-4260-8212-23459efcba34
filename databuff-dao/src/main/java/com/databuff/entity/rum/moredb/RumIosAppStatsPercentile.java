package com.databuff.entity.rum.moredb;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

@Data
@TableName("rum.ios.app.stats.percentile")
public class RumIosAppStatsPercentile {

    @ApiModelProperty("用户 api key")
    private String dfApiKey;

    @ApiModelProperty(value = "事件发生时间")
    private long startTime;

    @ApiModelProperty(value = "应用id")
    private Integer appId;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    @ApiModelProperty(value = "launchDuration pageDuration actionDuration requestDuration")
    private String type;

    @ApiModelProperty(value = "type对应值")
    private Long metric;

    // Add other necessary fields for histogram calculation
    // These fields will be used by MoreDB to calculate percentiles
    @ApiModelProperty(value = "Histogram count")
    private Long histogramCount;

    @ApiModelProperty(value = "Histogram min")
    private Long histogramMin;

    @ApiModelProperty(value = "Histogram max")
    private Long histogramMax;

    @ApiModelProperty(value = "Histogram sum")
    private Long histogramSum;

    private Map<Integer, Long> percentileAgg;
}
