package com.databuff.entity.rum.web;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AndroidScoreSettings extends AppScoreSettings {
    public AndroidScoreSettings() {
        setLaunchDurationThreshold(1050);
        setActionDurationThreshold(340);
        setPageDurationThreshold(290);
        setRequestDurationThreshold(816);
        setAnrRateThreshold(2.59);
        setCrashRateThreshold(0.55);
    }
}
