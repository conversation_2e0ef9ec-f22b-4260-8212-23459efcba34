package com.databuff.entity.rum.web;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@TableName("dc_rum_android_action_span")
public class RumAndroidActionSpanDto extends BaseRumAndroidSpanDto {
    @ApiModelProperty(value = "操作id")
    @JSONField(name = "action_id")
    private String actionId;
}
