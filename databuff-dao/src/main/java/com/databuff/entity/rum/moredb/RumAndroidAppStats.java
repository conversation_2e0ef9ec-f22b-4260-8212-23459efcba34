package com.databuff.entity.rum.moredb;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("rum.android.app.stats")
public class RumAndroidAppStats {
    @ApiModelProperty("用户 api key")
    private String dfApiKey;

    @ApiModelProperty(value = "事件发生时间")
    private Long startTime;

    //以下Tags (索引):
    @ApiModelProperty(value = "应用id")
    private Integer appId;

    @ApiModelProperty(value = "应用名称")
    private String appName;

    //以下Fields (度量值):
    @ApiModelProperty(value = "启动时间(ns)")
    private Long launchDuration;

    @ApiModelProperty(value = "页面加载时间(ns)")
    private Long pageDuration;

    @ApiModelProperty(value = "操作时间(ns)")
    private Long actionDuration;

    @ApiModelProperty(value = "请求响应时间(ns)")
    private Long requestDuration;

    @ApiModelProperty(value = "启动次数")
    private Long launchCount;

    @ApiModelProperty(value = "页面数量")
    private Long pageCount;

    @ApiModelProperty(value = "操作次数")
    private Long actionCount;

    @ApiModelProperty(value = "请求次数")
    private Long requestCount;

    @ApiModelProperty(value = "卡顿次数")
    private Long anrCount;

    @ApiModelProperty(value = "崩溃次数")
    private Long crashCount;
}
