package com.databuff.entity.rum.web;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel("Android卡顿详情请求参数")
public class AndroidAnrDetailRequest extends AndroidBaseDetailRequest {
    @ApiModelProperty(value = "卡顿ID", required = true)
    private Long anrId;
}
