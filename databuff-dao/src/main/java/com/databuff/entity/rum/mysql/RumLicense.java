package com.databuff.entity.rum.mysql;

import com.baomidou.mybatisplus.annotation.TableName;
import com.databuff.entity.rum.web.AuthDistributionEnum;
import com.databuff.entity.rum.web.CooperationModelEnum;
import com.databuff.entity.rum.web.MonthRuleEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("RUM License配置")
@TableName("dc_rum_license")
public class RumLicense {
    @ApiModelProperty("License ID")
    private Long licenseId;

    /**
     * @see MonthRuleEnum
     */
    @ApiModelProperty(value = "月划分规则", notes = "0=自然月,1=完整月")
    private Integer monthRule;

    /**
     * @see CooperationModelEnum
     */
    @ApiModelProperty(value = "商务合作方式", notes = "0=订阅,1=买断")
    private Integer cooperationModel;

    /**
     * @see AuthDistributionEnum
     */
    @ApiModelProperty(value = "授权分配方式", notes = "0=月均,1=年包")
    private Integer authDistribution;

    @ApiModelProperty(value = "授权月活(设备)", notes = "订阅=一年内总量,买断=整期限总量")
    private Long authMau;

    @ApiModelProperty(value = "授权PV数", notes = "同authMau作用")
    private Long authPv;

    @ApiModelProperty("授权起始时间戳")
    private Long startTime;

    @ApiModelProperty("授权截止时间戳")
    private Long endTime;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;
}
