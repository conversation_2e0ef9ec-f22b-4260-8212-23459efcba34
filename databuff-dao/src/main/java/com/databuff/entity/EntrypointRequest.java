package com.databuff.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EntrypointRequest {

    private String apiKey;

    @ApiModelProperty(value = "开始时间", example = "2021-07-15 00:00:00")
    private String fromTime;

    @ApiModelProperty(value = "结束时间", example = "2021-07-15 23:59:59")
    private String toTime;

    @ApiModelProperty(value = "服务ID")
    private String serviceId;

    @ApiModelProperty(value = "组件类型")
    private String componentType;

    @ApiModelProperty(value = "服务名")
    private String service;

    @ApiModelProperty(value = "接口请求")
    private String resource;

}
