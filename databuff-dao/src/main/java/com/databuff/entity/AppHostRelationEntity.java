package com.databuff.entity;

import lombok.Data;

@Data
public class AppHostRelationEntity {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dc_app_host_relation.id
     *
     * @mbg.generated Wed Jun 29 11:02:32 CST 2022
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dc_app_host_relation.host_name
     *
     * @mbg.generated Wed Jun 29 11:02:32 CST 2022
     */
    private String hostName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dc_app_host_relation.app
     *
     * @mbg.generated Wed Jun 29 11:02:32 CST 2022
     */
    private String app;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dc_app_host_relation.api_key
     *
     * @mbg.generated Wed Jun 29 11:02:32 CST 2022
     */
    private String apiKey;

}