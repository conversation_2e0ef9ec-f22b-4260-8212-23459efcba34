package com.databuff.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 业务场景关联业务事件
 */
@ToString
@Data
@ApiModel("业务场景关联业务事件")
public class BizScenarioEvent implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 业务事件id
     */
    @ApiModelProperty("业务事件id")
    private Integer bizEventId;
    /**
     * 业务事件名称
     */
    @ApiModelProperty("业务事件名称")
    private String bizEventName;

    /**
     * 排序号
     */
    @ApiModelProperty("排序号")
    private Integer order;


    @ApiModelProperty("关联参数")
    private String params;

    public BizScenarioEvent() {
    }

}