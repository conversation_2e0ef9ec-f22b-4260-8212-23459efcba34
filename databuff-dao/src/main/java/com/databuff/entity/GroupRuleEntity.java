package com.databuff.entity;

import lombok.Data;

import java.util.Date;

@Data
public class GroupRuleEntity {
    /**
     * id
     */
    private Integer id;
    /**
     * group id
     */
    private Integer groupId;
    /**
     * 匹配类型
     */
    private String type;
    /**
     * 是否启用
     */
    private Integer enable;
    /**
     * 是否展示
     */
    private Integer hidden;
    /**
     * 匹配规则（JSON字符串）
     */
    private String params;
    /**
     * 语义化web展示的规则预览
     */
    private String formatted;
    /**
     * apikey
     */
    private String apiKey;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
}
