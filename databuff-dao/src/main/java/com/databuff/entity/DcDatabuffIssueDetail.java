package com.databuff.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.gson.annotations.Expose;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 数据缓冲问题详情实体类
 */
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "dc_databuff_issue_detail")
public class DcDatabuffIssueDetail {

    /**
     * 主键ID
     */
    @Id
    private String id;

    /**
     * 域ID
     */
    @Column(name = "gid", nullable = true)
    private String gid;

    /**
     * 根因节点
     */
    @Column(name = "root_cause_node", nullable = true)
    private String rootCauseNode;

    /**
     * 根因类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "root_cause_type", nullable = true)
    private String rootCauseType;

    /**
     * 状态
     */
    @Column(name = "status", nullable = false)
    private String status;

    /**
     * 数据来源
     */
    @Column(name = "source", nullable = false)
    private Source source;

    /**
     * 开始时间
     */
    @Column(name = "start_time", nullable = true)
    @Temporal(TemporalType.TIMESTAMP)
    private Date startTime;

    /**
     * 结束时间
     */
    @Column(name = "end_time", nullable = true)
    @Temporal(TemporalType.TIMESTAMP)
    private Date endTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = true, updatable = true)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time", nullable = true, updatable = true)
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

    /**
     * 根因分析
     */
    @Column(name = "root_analyse", nullable = true)
    private JSONArray rootAnalyse;

    /**
     * 问题ID
     */
    @Column(name = "problemId", nullable = true)
    private String problemId;

    private String suggest;

    private String suggestStatus;

    /**
     * 用来标识 IssueDetail 写库状态，不参与序列化和反序列化
     * 后续判断是否需要记录日志
     */
    @Transient
    private AnalysisStatus analysisStatus;

    /**
     * 日志列表
     */
    @Transient
    private List<DcDatabuffIssueLog> logs;

    /**
     * 分析服务列表
     */
    @Transient
    private List<String> services;

    /**
     * 问题详情
     * 临存，不参与序列化和反序列化
     */
    @Transient
    @JsonIgnore
    @Expose(serialize = false, deserialize = false)
    @JSONField(serialize = false, deserialize = false)
    private DcDatabuffProblem problem;

    /**
     * 数据来源枚举
     */
    public enum Source {
        事件触发("事件触发"),
        告警触发("告警触发"),
        手动触发("手动触发"),
        其他("其他");

        private final String value;

        Source(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 分析状态枚举
     */
    public enum AnalysisStatus {
        更新成功("更新成功"),
        写入成功("写入成功"),
        其他("其他");

        private final String value;

        AnalysisStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }
}