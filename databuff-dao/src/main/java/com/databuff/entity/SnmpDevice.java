package com.databuff.entity;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author:TianMing
 * @date: 2022/6/23
 * @time: 15:59
 */
@Data
public class SnmpDevice {
    /**
     * apikey，snmpdevice，snmphost md5加密
     */
    private String id;

    /**
     * apikey
     */
    private String apiKey;

    /**
     * 上报主机id
     */
    private String hostId;

    /**
     * 上报主机名
     */
    private String hostName;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备ip
     */
    private String deviceIp;

    private List<String> deviceIps;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 设备厂商
     */
    private String manufacturer;

    private List<String> manufacturers;

    /**
     * 设备型号
     */
    private String modelNumber;
    private List<String> modelNumbers;
    /**
     * 产品名称
     */
    private String productName;
    private List<String> productNames;

    /**
     * 序列号
     */
    private String snNumber;
    private List<String> snNumbers;

    /**
     * 配置文件
     */
    private String snmpProfile;

    /**
     * 地址
     */
    private String sysLocation;
    private List<String> sysLocations;

    /**
     * 系统对象id
     */
    private String sysObjectid;
    /**
     * 系统对象id
     */
    private List<String> sysObjectids;
    /**
     * sysservices
     */
    private String sysServices;

    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 运行时长
     */
    private Long upTime;

    private String groupByField ;

    private Date startTime;

    private Date endTime;

}
