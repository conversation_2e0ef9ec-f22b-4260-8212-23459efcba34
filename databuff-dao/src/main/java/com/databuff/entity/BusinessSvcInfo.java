package com.databuff.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.List;


@ToString
@Data
@ApiModel("业务系统子系统服务id关联表")
public class BusinessSvcInfo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 服务id
     */
    @ApiModelProperty("服务id")
    private String svcId;
    /**
     * 系统id
     */
    @ApiModelProperty("系统id")
    private Integer id;

    /**
     * 系统名称
     */
    @ApiModelProperty("系统名称")
    private String bname;

    /**
     * 父系统id
     */
    @ApiModelProperty("父系统id")
    private Integer pid;

    /**
     * 父系统名称
     */
    @ApiModelProperty("父系统名称")
    private String pbname;

    /**
     * 1顶级业务系统 2业务子系统
     */
    @ApiModelProperty("1顶级业务系统 2业务子系统")
    private Integer btype;


    public BusinessSvcInfo() {
    }

}