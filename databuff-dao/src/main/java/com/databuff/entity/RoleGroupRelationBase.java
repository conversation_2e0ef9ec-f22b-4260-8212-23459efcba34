package com.databuff.entity;

import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * 角色与域关系
 */
@Data
@Builder
public class RoleGroupRelationBase {
    private Integer roleId;
    private Integer gid;
    private Boolean configAuth; // 是否允许配置管理
    private Boolean dataAuth;  // 是否允许查看数据

    public RoleGroupRelationBase() {
    }
    public RoleGroupRelationBase(Integer roleId, Integer gid, Boolean configAuth, Boolean dataAuth) {
        this.roleId = roleId;
        this.gid = gid;
        this.configAuth = configAuth;
        this.dataAuth = dataAuth;
    }
}
