package com.databuff.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @author:TianMing
 * @date: 2021/7/14
 * @time: 10:07
 */
@Data
@TableName(value="dc_agent")
@ApiModel("agent 探针信息")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentEntity implements Serializable {

    private static final long serialVersionUID = 1L;

//    @Id
//    @GeneratedValue
    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(value="id")
    private String id;

    /**
     * 服务名
     */
    @ApiModelProperty("服务名")
    @TableField(value="server_name")
    private String serverName;

    /**
     * 主机ip
     */
    @ApiModelProperty("主机ip")
    @TableField(value="host_ip")
    private String hostIp;
    /**
     * 管理ip
     */
    @ApiModelProperty("管理ip")
    @TableField(value="manager_ipaddress")
    private String managerIpaddress;
    /**
     * 主机名
     */
    @ApiModelProperty("主机名")
    @TableField(value="host_name")
    private String hostName;

    /**
     * agentserver标识
     */
    @ApiModelProperty("agentserver标识")
    @TableField(value="api_key")
    private String apiKey;
    /**
     * agent服务
     */
    @ApiModelProperty("agent服务")
    @TableField(value="agent_server")
    private String agentServer;
    /**
     * agentserver版本
     */
    @ApiModelProperty("agentserver版本")
    @TableField(value="agent_version")
    private String agentVersion;

    /**
     * 第一次部署时间
     */
    @ApiModelProperty("第一次部署时间")
    @TableField(value="create_time")
    private Date createTime;

    /**
     * 最近同步数据时间
     */
    @ApiModelProperty("最近同步数据时间")
    @TableField(value="last_syn_data_time")
    private String lastSynDataTime;

    /**
     * 最近同步agent信息时间
     */
    @ApiModelProperty("最近同步agent信息时间")
    @TableField(value="last_syn_agent_time")
    private Date lastSynAgentTime;

    /**
     * 最新cpu数据
     */
    @ApiModelProperty("最新cpu数据")
    @TableField(value="cpu")
    private String cpu ;

    /**
     * 最新内存数据
     */
    @ApiModelProperty("最新内存数据")
    @TableField(value="memory")
    private String memory ;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @TableField(value="remark")
    private String remark;
    /**
     * 状态
     */
    @ApiModelProperty("状态")
    @TableField(value="status")
    private String status;
    /**
     * agent与服务器时间差
     */
    @TableField(value="time_diff")
    private Long timeDiff;
    /**
     * 主机CPU核数
     */
    @ApiModelProperty("主机CPU核数")
    @TableField(value="cpu_cores")
    private Integer cpuCores;
    /**
     * 数据上传量
     */
    @ApiModelProperty("数据上传量")
    @TableField(value="packets_sent")
    private Long packetsSent;


    /**
     * 所在主机操作系统
     */
    @TableField("goos")
    @ApiModelProperty("所在主机操作系统")
    private String goos;

    /**
     * 是否k8s
     */
    @TableField("is_k8s")
    @ApiModelProperty("是否k8s")
    private Boolean isK8s;

    /**
     * 配置文件内容
     */
    @TableField("config_content")
    @ApiModelProperty("配置文件内容")
    private String configContent;

    /**
     * 配置文件更新状态
     */
    @TableField("config_status")
    @ApiModelProperty("配置文件更新状态")
    private Integer configStatus;

    /**
     * 配置文件更新信息提示
     */
    @TableField("config_msg")
    @ApiModelProperty("配置文件更新信息提示")
    private String configMsg;
    /**
     * 配置文件修改时间
     */
    @TableField("config_time")
    private Date configTime;

    @TableField("last_version")
    @ApiModelProperty("安装包最新版本")
    private String lastVersion;


    @ApiModelProperty("操作 0更新，1重启，2停止，3启动")
    private Integer operation;

    @ApiModelProperty("操作状态 0待更新/重启/停止/启动，1更新/重启/停止/启动成功，2更新/重启/停止/启动失败，3更新/重启/停止/启动中")
    private Integer operationStatus;

    @ApiModelProperty("操作开始时间")
    private Date operationStartTime;
    @ApiModelProperty("操作结束时间")
    private Date operationEndTime;
    @ApiModelProperty("操作信息")
    private String operationMsg;
//    public AgentEntity() {
//    }


//    public AgentEntity(String serverName, String hostIp, String hostName, String apiKey, String agentServer
//            , String agentVersion, Date createTime, Date lastSynAgentTime, Integer cpuCores,Long packetsSent) {
//        this.serverName = serverName ;
//        this.hostIp = hostIp ;
//        this.hostName = hostName ;
//        this.apiKey = apiKey ;
//        this.agentServer = agentServer ;
//        this.agentVersion = agentVersion ;
//        this.createTime = createTime ;
//        this.lastSynAgentTime = lastSynAgentTime ;
//        this.cpuCores = cpuCores ;
//        this.packetsSent = packetsSent ;
//    }
//    public AgentEntity(String id, String hostIp, String hostName, String apiKey, String agentServer
//            ,  Date lastSynAgentTime, long timeDiff) {
//        this.id = id ;
//        this.hostIp = hostIp ;
//        this.hostName = hostName ;
//        this.apiKey = apiKey ;
//        this.agentServer = agentServer ;
//        this.lastSynAgentTime = lastSynAgentTime ;
//        this.timeDiff = timeDiff ;
//    }
}
