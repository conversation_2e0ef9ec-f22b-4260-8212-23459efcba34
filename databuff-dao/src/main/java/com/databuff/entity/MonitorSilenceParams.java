package com.databuff.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

/**
 * @description  
 * <AUTHOR>
 * @date 2021-08-30 
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MonitorSilenceParams {

	/**
	 * 计划名称
	 */
	private String name;

	/**
	 * 监控范围
	 */
	private Boolean enabled ;

    /**
     * cycle周期性计划，single一次性计划
     */
    private String type;
    /**
     * 当前时间,传了当前时间则代表查询当前时间激活的计划
     */
	private Long thisTime;

    private String apiKey;

    private List<Long> userIds;

    private List<Long> silenceIds;

    @ApiModelProperty("排序字段")
    private String sortField = "UPDATE_TIME";
    @ApiModelProperty("排序方式 asc desc")
    private String sortOrder = "DESC";

}
