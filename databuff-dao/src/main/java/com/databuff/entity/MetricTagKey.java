package com.databuff.entity;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MetricTagKey {

    @ApiModelProperty(value = "是否启用")
    private boolean enabled;

    @ApiModelProperty(value = "标签key")
    @JSONField(serialize = false, deserialize = false)
    @JsonIgnore
    private String tagKey;

    @ApiModelProperty(value = "标签名称")
    @JSONField(serialize = false, deserialize = false)
    @JsonIgnore
    private Set<String> names;

    @ApiModelProperty(value = "标签所属指标")
    private Set<String> metrics;

    @ApiModelProperty(value = "标签维度value")
    @JSONField(serialize = false, deserialize = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private JSONObject tagValue;

    @ApiModelProperty(value = "标签维度名称列表")
    public String getName() {
        return String.join("|", names);
    }

}