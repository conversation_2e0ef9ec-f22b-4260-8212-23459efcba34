package com.databuff.entity;

public class InfluenceConvergence {

    private int majorEvent;
    private int minorEvent;
    private int noDataEvent;
    private int majorAlarm;
    private int minorAlarm;
    private int noDataAlarm;
    private int problem;
    private double convergenceRatio;

    public InfluenceConvergence() {
    }

    public InfluenceConvergence(int majorEvent, int minorEvent, int noDataEvent, int majorAlarm, int minorAlarm, int noDataAlarm, int problem, double convergenceRatio) {
        this.majorEvent = majorEvent;
        this.minorEvent = minorEvent;
        this.noDataEvent = noDataEvent;
        this.majorAlarm = majorAlarm;
        this.minorAlarm = minorAlarm;
        this.noDataAlarm = noDataAlarm;
        this.problem = problem;
        this.convergenceRatio = convergenceRatio;
    }

    public int getNoDataEvent() {
        return noDataEvent;
    }

    public int getNoDataAlarm() {
        return noDataAlarm;
    }

    public int getMinorEvent() {
        return minorEvent;
    }

    public int getMajorEvent() {
        return majorEvent;
    }

    public int getMinorAlarm() {
        return minorAlarm;
    }

    public int getMajorAlarm() {
        return majorAlarm;
    }

    public int getProblem() {
        return problem;
    }

    public double getConvergenceRatio() {
        return convergenceRatio;
    }
}
