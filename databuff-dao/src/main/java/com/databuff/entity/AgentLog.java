package com.databuff.entity;

import lombok.Data;

import java.util.Date;


/**
 * agent上传日志管理表
 */
@Data
public class AgentLog {
    private Integer id;
    /**
     * 日志文件名称
     */
    private String logName;
    /**
     * 日志文件路径
     */
    private String path;
    /**
     * 主机名
     */
    private String host;
    /**
     * 是否新上传
     */
    private Integer isNew;
    /**
     * 上传时间
     */
    private Date uploadTime;
    /**
     * 是否新上传
     */
    private Long logFileSize;
    /**
     * 日志文件创建时间
     */
    private Date logFileCreateTime;
    /**
     * 日志文件更新时间
     */
    private Date logFileUpdateTime;
    private String apiKey;
}
