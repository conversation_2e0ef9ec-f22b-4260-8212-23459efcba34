package com.databuff.entity.enums;

import lombok.Getter;

/**
 * 基本判断枚举
 * 指标值的判断方向
 */
@Getter
public enum BasicJudgmentEnum {
    
    // 基本判断
    HIGH_GOOD("HIGH_GOOD", "高好"),           // 值越高越好，如：吞吐量、成功率、可用性
    LOW_GOOD("LOW_GOOD", "低好"),             // 值越低越好，如：延迟、错误率、资源消耗
    
    // 范围判断
    RANGE_GOOD("RANGE_GOOD", "范围内好"),     // 在特定范围内为好，如：CPU使用率(不能太低也不能太高)
    TARGET_GOOD("TARGET_GOOD", "接近目标好"), // 接近特定目标值为好，如：温度控制
    
    // 趋势判断
    STABLE_GOOD("STABLE_GOOD", "稳定好"),     // 值保持稳定为好，如：网络延迟波动
    TREND_UP_GOOD("TREND_UP_GOOD", "上升趋势好"), // 上升趋势为好，如：用户增长率
    TREND_DOWN_GOOD("TREND_DOWN_GOOD", "下降趋势好"); // 下降趋势为好，如：成本降低趋势
    
    private final String code;
    private final String name;
    
    BasicJudgmentEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    /**
     * 根据编码获取枚举
     */
    public static BasicJudgmentEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (BasicJudgmentEnum judgment : values()) {
            if (judgment.getCode().equals(code)) {
                return judgment;
            }
        }
        return null;
    }
    
    /**
     * 根据名称获取枚举
     */
    public static BasicJudgmentEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        for (BasicJudgmentEnum judgment : values()) {
            if (judgment.getName().equals(name)) {
                return judgment;
            }
        }
        return null;
    }
}
