package com.databuff.entity.domainObj;

import lombok.Data;

@Data
public class ServiceListObj extends ServiceObj{

    private String subSystem ;
    private String system ;

    public ServiceListObj() {
    }
    public ServiceListObj(ServiceObj serviceObj) {
        this.setServiceId(serviceObj.getServiceId());
        this.setService(serviceObj.getService());
        this.setName(serviceObj.getName());
        this.setApikey(serviceObj.getApikey());
        this.setCustomTags(serviceObj.getCustomTags());
    }
}
