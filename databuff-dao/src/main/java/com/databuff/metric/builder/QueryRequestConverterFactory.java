package com.databuff.metric.builder;

import com.databuff.common.tsdb.builder.QueryBuilderX;
import com.databuff.common.tsdb.dto.CompositeCondition;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.common.tsdb.dto.preview.OrderDTO;
import com.databuff.common.tsdb.model.*;
import com.databuff.entity.MetricsQuery;
import com.databuff.service.DomainManagerObjService;
import com.databuff.service.MetricsQueryService;
import com.databuff.tsdb.metric.moredb.TSDBSQLParser;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.databuff.common.constants.Constant.DF_API_KEY_VALUE;

@Slf4j
@Service
public class QueryRequestConverterFactory {

    private static final Integer DEFAULT_INTERVAL = 60;
    @Autowired
    private MetricsQueryService metricsQueryService;

    @Autowired
    private DomainManagerObjService domainManagerObjService;

    /**
     * 从给定的复合条件集合中查找第一个有效的逻辑操作符。
     * 该方法按以下步骤处理：
     * 1. 过滤掉集合中为null的CompositeCondition对象。
     * 2. 提取每个有效CompositeCondition的connector属性。
     * 3. 过滤掉这些connector中为null的值。
     * 4. 返回第一个非空的connector；若所有connector均为null或集合为空，则返回默认值LogicalOperator.AND。
     *
     * @param collection 包含CompositeCondition对象的集合，可能包含null元素
     * @return 第一个有效的LogicalOperator值，或默认的AND
     */
    private static LogicalOperator getFirstValidConnector(Collection<CompositeCondition> collection) {
        return collection.stream()
                .filter(Objects::nonNull)
                .map(CompositeCondition::getConnector)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(LogicalOperator.AND);
    }

    public QueryBuilderX buildQueryBuilderX(QueryRequest queryRequest) {
        // 1. 参数提取
        Collection<String> queryRequestMetrics = queryRequest.getMetrics();

        final String metricName = queryRequest.getMetric();
        final String tb = queryRequest.getTb();
        final String db = queryRequest.getDb();
        if (StringUtils.isBlank(metricName) && (StringUtils.isBlank(tb) || StringUtils.isBlank(db))) {
            log.warn("metricName和tb不能同时为空");
            return null;
        }

        Integer interval = queryRequest.getInterval();
        interval = Optional.ofNullable(interval)
                .filter(i -> i > 0)
                .orElse(DEFAULT_INTERVAL);

        Collection<String> by = queryRequest.getBy();
        OrderDTO order = queryRequest.getOrder();
        String apiKey = Optional.ofNullable(queryRequest.getApiKey()).orElse(DF_API_KEY_VALUE);

        // 提前获取 metricsQuery 以便后续使用
        MetricsQuery metricsQuery = metricsQueryService.findOpenByIdentifier(metricName);

        if (metricsQuery == null) {
            log.warn("构建查询时metricsQuery为null，可能导致查询失败。metricName={}, apiKey={}, db={}, tb={}, 请求参数={}",
                    metricName, apiKey, db, tb, queryRequest);
        }

        // 2. 构建基础查询结构
        QueryBuilderX builder = queryRequest.convert();
        builder.setMetric(metricName);

        // 5. 设置数据库名称（结合API密钥和指标编码）
        String encodedDatabase = metricsQueryService.encodedDatabaseName(apiKey, metricName);
        if (encodedDatabase == null) {
            encodedDatabase = queryRequest.getDb();
        }
        builder.setDatabaseName(encodedDatabase);

        // 处理 measurement
        if (metricsQuery != null) {
            final String measurement = metricsQuery.getMeasurement();
            if (measurement == null) {
                throw new IllegalArgumentException("measurement无效：" + metricName);
            }
            builder.setMeasurement(measurement);
        } else {
            if (tb == null) {
                log.warn("找不到指标时必须提供measurement");
                return null;
            }
            builder.setMeasurement(tb);
        }

        // 6. 处理WHERE条件（使用TSDBSQLParser统一处理）
        Boolean allPermission = queryRequest.getAllPermission();
        Boolean domainManagerStatusOpen = queryRequest.getDomainManagerStatusOpen();
        Collection<String> gids = queryRequest.getGids();

        // 指标tag白名单（修复空指针风险）
        Collection<String> whiteKeys = queryRequest.getWhiteKeys();
        if (CollectionUtils.isEmpty(whiteKeys)) {
            whiteKeys = Optional.ofNullable(metricsQuery)
                    .map(mq -> mq.getTagKey())
                    .map(Map::keySet)
                    .orElse(Collections.emptySet());
        }

        final Collection<CompositeCondition> from = queryRequest.getFrom();
        final Collection<CompositeCondition> ext = queryRequest.getFromExt();
        int initialSize = 0;
        if (CollectionUtils.isNotEmpty(from)) {
            initialSize += 1;
        }
        if (CollectionUtils.isNotEmpty(ext)) {
            initialSize += 1;
        }

        final Collection<CompositeCondition> conditions = new ArrayList<>(initialSize);

        // 兼容from和fromExt
        if (CollectionUtils.isNotEmpty(from)) {
            // 获取第一个connector，兼容前端格式
            final LogicalOperator firstConnector = getFirstValidConnector(from);
            conditions.add(CompositeCondition.builder().connector(firstConnector).left(from).build());
        }

        if (CollectionUtils.isNotEmpty(ext)) {
            // 获取第一个connector，兼容前端格式
            final LogicalOperator firstConnector = getFirstValidConnector(ext);
            conditions.add(CompositeCondition.builder().connector(firstConnector).left(ext).build());
        }

        QueryBuilderX whereBuilder = TSDBSQLParser.parseWhere(
                allPermission,
                domainManagerStatusOpen,
                gids,
                conditions,
                whiteKeys
        );
        builder.addAllWhere(whereBuilder.getWheres());

        // 7. 设置分组与聚合（修复空指针风险）
        if (by != null && !by.isEmpty()) {
            builder.addAllGroupBy(by);
        }

        builder.setInterval(interval.intValue());

        final List<Aggregation> aggregations = queryRequest.getAggregations();
        if (CollectionUtils.isNotEmpty(aggregations)) {
            builder.addAllAgg(aggregations);
        }

        if (CollectionUtils.isEmpty(queryRequestMetrics)) {
            queryRequestMetrics = Lists.newArrayList(queryRequest.getMetric());
        }
        for (String metric : queryRequestMetrics) {
            final MetricsQuery metricMeta = metricsQueryService.findOpenByIdentifier(metric);
            if (metricMeta == null) {
                log.warn("在处理指标聚合时找不到指标元数据，该指标将被跳过。metric={}, apiKey={}, 请求参数={}",
                        metric, queryRequest.getApiKey(), queryRequest);
                continue;
            }
            String aggs = queryRequest.getAggs();
            if (StringUtils.isBlank(aggs)) {
                aggs = metricMeta.getAggregatorType();
            }
            if (StringUtils.isBlank(aggs)) {
                aggs = "avg";
            }
            FieldExpressionResult result = getFieldExpressionResult(metric, interval, aggs, metricMeta);
            builder.addAgg(new Aggregation(result.getFunction(), result.getField(), result.getAlias()));
        }

        if (order == null) {
            return builder;
        }
        final String code = order.getCode();
        if (code == null) {
            return builder;
        }
        final String func = order.getFunc();
        boolean isAsc = true;
        if ("top".equalsIgnoreCase(func)) {
            isAsc = false;
        }
        if (StringUtils.isNotBlank(code) && queryRequestMetrics.contains(code)) {
            builder.addOrderBy(new OrderBy(code, isAsc));
        }
        final Integer limit = order.getLimit();
        if (limit != null && limit > 0) {
            builder.setLimit(limit);
        }
        return builder;
    }


    /**
     * 根据复合条件构建查询条件对象列表
     * @param conditions 需要解析的复合条件集合，用于生成查询条件语句
     * @return 包含解析后查询条件的Where对象列表
     */
    public List<Where> buildWhere(Collection<CompositeCondition> conditions) {
        return buildWhere(conditions, null);
    }

    public List<Where> buildWhere(Collection<CompositeCondition> conditions, Collection<String> whiteKeys) {

        // 使用TSDB SQL解析器根据权限信息和条件构建查询条件对象
        QueryBuilderX whereBuilder = TSDBSQLParser.parseWhere(
                domainManagerObjService.hasAllEntityPermission(),
                domainManagerObjService.getDomainManagerStatusOpen(),
                domainManagerObjService.getGidFromThread(),
                conditions,
                whiteKeys
        );
        return whereBuilder.getWheres();
    }

    /**
     * 生成并返回带有别名的字段表达式
     *
     * @param metricCode   指标代码，用于标识具体的指标
     * @param interval     时间间隔，单位取决于具体业务场景
     * @param aggs         聚合方式，指定数据聚合的计算类型（如avg/sum等）
     * @param metricsQuery 包含查询参数的MetricsQuery对象
     * @return 处理后的字段表达式，已附加指标代码作为别名
     */
    protected String getFieldExpression(String metricCode, long interval, String aggs, MetricsQuery metricsQuery) {
        // 生成原始字段表达式
        String rawField = buildFieldExpression(metricCode, interval, aggs, metricsQuery);
        // 为字段表达式附加指标代码作为别名
        return addAlias(rawField, metricCode);
    }

    /**
     * 生成并返回字段表达式结果对象，包含聚合函数、字段和别名
     *
     * @param metricCode   指标代码，用于标识具体的指标
     * @param interval     时间间隔，单位取决于具体业务场景
     * @param aggs         聚合方式，指定数据聚合的计算类型（如avg/sum等）
     * @param metricsQuery 包含查询参数的MetricsQuery对象
     * @return 包含聚合函数、字段和别名的FieldExpressionResult对象
     */
    protected FieldExpressionResult getFieldExpressionResult(String metricCode, long interval, String aggs, MetricsQuery metricsQuery) {
        if (metricsQuery == null) {
            log.warn("在getFieldExpressionResult中metricsQuery为null，可能导致字段表达式生成失败。metricCode={}, interval={}, aggs={}",
                    metricCode, interval, aggs);
        }

        // 生成原始字段表达式
        String rawField = buildFieldExpression(metricCode, interval, aggs, metricsQuery);

        if (rawField == null) {
            log.warn("生成的字段表达式为null，可能影响查询结果。metricCode={}, interval={}, aggs={}, metricsQuery={}",
                    metricCode, interval, aggs, metricsQuery);
        }

        // 解析聚合函数和字段
        FieldExpressionResult result = parseFieldExpression(rawField);

        // 如果别名为空，则使用指标代码作为别名
        if (result.getAlias() == null || result.getAlias().isEmpty()) {
            result.setAlias(metricCode);
        }

        // core表指标强制使用聚合函数（增加null检查防止空指针异常）
        if (result.getFunction() == null && metricsQuery != null && metricsQuery.isCore()) {
            result.setFunction(AggFun.fromString(aggs));
        } else if (result.getFunction() == null && metricsQuery == null) {
            // 当metricsQuery为null时，尝试使用默认聚合函数
            log.info("由于metricsQuery为null，尝试使用默认聚合函数。metricCode={}, aggs={}", metricCode, aggs);
            result.setFunction(AggFun.fromString(aggs));
        }

        return result;
    }


    /**
     * 构建指标字段表达式
     *
     * @param metricCode   指标代码
     * @param interval     时间间隔（单位由上下文决定）
     * @param aggs         聚合类型（如"mean", "sum"等）
     * @param metricsQuery 包含指标查询参数的对象
     * @return 生成的字段表达式字符串，或null
     */
    protected String buildFieldExpression(String metricCode, long interval, String aggs, MetricsQuery metricsQuery) {
        // 处理基础情况：当查询对象为空时直接返回null
        if (metricsQuery == null) {
            // 增强日志：记录详细信息以协助排查
            log.warn("构建字段表达式时metricsQuery为null，无法生成有效表达式。metricCode={}, interval={}, aggs={}, 调用堆栈={}",
                    metricCode, interval, aggs, Thread.currentThread().getStackTrace());
            return null;
        }
        String formula = metricsQuery.getFormula();
        boolean isCore = metricsQuery.isCore();
        String field;

        // 根据公式和核心指标状态生成字段表达式
        if (StringUtils.isNotBlank(formula) && (!isCore || StringUtils.isEmpty(aggs))) {
            field = formula;
        } else if (isCore && StringUtils.isNotBlank(aggs) && StringUtils.isNotBlank(metricsQuery.getField())) {
            // 使用指定的聚合类型和字段生成表达式
//            field = SQLParser.getAggsBuilder(aggs, metricsQuery.getField(), metricCode);
            field = metricsQuery.getField();
        } else {
            // 递归处理默认聚合类型（如 "avg"）
            return buildFieldExpression(metricCode, interval, "avg", metricsQuery);
        }

        // 替换间隔参数占位符为实际值或默认值
        field = field.trim().replace("${interval}",
                String.valueOf(interval > 0 ? interval : DEFAULT_INTERVAL));
        return field;
    }


    /**
     * 为字段或表达式添加别名，仅当字段不包含逗号或AS关键字时生效。
     *
     * @param field      需要添加别名的原始字段或表达式
     * @param metricCode 作为新列标识的别名代码
     * @return 处理后的字段字符串，满足条件时附加AS和双引号包裹的metricCode，否则返回原字段
     */
    protected String addAlias(String field, String metricCode) {
        // 仅在未包含逗号或 AS 关键字时添加别名
        if (!field.contains(",")
                && !field.contains("AS")
                && !field.contains("as")) {
            return String.format("%s AS \"%s\"", field, metricCode);
        }
        return field;
    }

    /**
     * 解析字段表达式，提取聚合函数、字段和别名
     *
     * @param expression 字段表达式
     * @return 包含聚合函数、字段和别名的FieldExpressionResult对象
     */
    protected FieldExpressionResult parseFieldExpression(String expression) {
        FieldExpressionResult result = new FieldExpressionResult();

        // 如果表达式为空，返回空结果
        if (expression == null || expression.trim().isEmpty()) {
            return result;
        }
        result.setField(expression.trim());
        return result;
    }

    /**
     * 字段表达式结果类，用于存储解析后的聚合函数、字段和别名
     */
    @Data
    public static class FieldExpressionResult {
        private AggFun function;
        private String field;
        private String alias;
    }
}
