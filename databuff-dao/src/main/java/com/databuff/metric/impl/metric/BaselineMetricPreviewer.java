package com.databuff.metric.impl.metric;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.metric.WrapData;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.common.tsdb.dto.preview.RuleDTO;
import com.databuff.metric.MetricAggregator;
import com.databuff.metric.MetricPreviewer;
import com.databuff.metric.impl.alarmV2.CalculateBaseline;
import com.databuff.service.JedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Component("baselineMetricPreviewer")
public class BaselineMetricPreviewer implements MetricPreviewer {

    private final static String SEPARATOR = ":";
    private final static String REDIS_PREFIX = "monitor:metric:";

    @Autowired
    private CalculateBaseline calculateBaseline;

    @Autowired
    @Lazy
    private MetricAggregator metricAggregator;

    @Resource
    protected JedisService jedisService;


    @Override
    public List<WrapData> preview(List<WrapData> wrapData, RuleDTO rule, Collection<QueryRequest> queries) {

        final double baselineScope;
        final String comparison;
        if (rule == null) {
            baselineScope = 1.0D;
            comparison = ">";
        } else {
            baselineScope = rule.getBaselineScope();
            comparison = rule.getComparison();
        }

        for (WrapData wrapDatum : wrapData) {
            if (wrapDatum == null) {
                continue;
            }
            final Map<String, String> tags = wrapDatum.getTags();
            final String group = tags.entrySet().stream()
                    .filter(Objects::nonNull)
                    .map(i -> i.getKey() + SEPARATOR + i.getValue())
                    .reduce((a, b) -> a + SEPARATOR + b)
                    .orElse(null);
            if (group == null) {
                log.warn("找不到触发对象，不计算动态基线");
                continue;
            }
            final String keyStr = REDIS_PREFIX + "baseline" + SEPARATOR + group + SEPARATOR + comparison + SEPARATOR + baselineScope;
            final String baselineJson = jedisService.getJson(keyStr);
            JSONObject baselineJSON;
            if (baselineJson != null) {
                baselineJSON = JSONObject.parseObject(baselineJson);
            } else {
                baselineJSON = calculateBaseline.process(keyStr, queries, tags, metricAggregator, comparison, baselineScope);
            }
            if (baselineJSON == null) {
                log.warn("监控[{}]计算基线失败", keyStr);
                continue;
            }
            final double num = baselineJSON.getDoubleValue("num");
            if (num < 2016) {
                log.debug("监控[{}]计算基线点数{}，低于所需数据量(分钟粒度下，至少需要满足 7d*20%*24h*60min=2016 的数据量)不满足要求", keyStr, num);
            } else {
                double baseline = baselineJSON.getDoubleValue("baseline");
                for (List<Number> value : wrapDatum.getValues()) {
                    value.add(baseline);
                }
                String[] columns = wrapDatum.getColumns();
                List<String> newColumnsList = new ArrayList<>(Arrays.asList(columns));
                newColumnsList.add("baseline_" + (Objects.equals(comparison, "<") ? "low" : "high"));
                String[] newColumns = newColumnsList.toArray(new String[0]);
                wrapDatum.setColumns(newColumns);

                final String[] units = wrapDatum.getUnits();
                List<String> newUnitsList = new ArrayList<>(Arrays.asList(units));
                newUnitsList.add(units[units.length - 1]);
                String[] newUnits = newUnitsList.toArray(new String[0]);
                wrapDatum.setUnits(newUnits);
            }
        }
        return wrapData;
    }
}
