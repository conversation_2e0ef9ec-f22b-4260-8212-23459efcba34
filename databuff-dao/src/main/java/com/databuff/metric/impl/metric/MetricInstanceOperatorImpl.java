package com.databuff.metric.impl.metric;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.entity.MetricsQuery;
import com.databuff.metric.MetricAggregator;
import com.databuff.metric.MetricInstanceOperator;
import com.databuff.metric.moredb.SQLParser;
import com.databuff.service.MetricsQueryService;
import com.google.common.collect.Sets;
import com.sun.istack.NotNull;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.databuff.metric.moredb.SQLParser.CONDITIONS;
import static com.databuff.metric.moredb.SQLParser.OR;

@Component("metricInstanceOperatorImpl")
public class MetricInstanceOperatorImpl implements MetricInstanceOperator {

//    @Autowired
//    private MetricMoreDBOperateUtil moreDBUtil;

    @Autowired
    private MetricsQueryService metricsQueryService;

    @Autowired
    private MetricAggregator metricAggregator;

//    @Override
//    public Set<String> showDatabases() throws Exception {
//        Set<String> dbs = new HashSet<String>();
//        List<Result> ret = moreDBUtil.showDatabases();
//        if (ret == null) {
//            return dbs;
//        }
//        for (Series series : ret.get(0).getSeries()) {
//            if (series == null) {
//                continue;
//            }
//            dbs.addAll(series.getValues().stream().filter(Objects::nonNull).map(i -> (String) i.get(0)).collect(Collectors.toSet()));
//        }
//        return dbs;
//    }

//    @Override
//    public List<JSONObject> listMetricNames(Set<String> metric, Set<JSONObject> tags, String apiKey, Integer offset, Integer size) throws Exception {
//        List<JSONObject> ret = new ArrayList<>();
//        for (String database : this.showDatabases()) {
//            if (database == null || apiKey == null || !database.contains(apiKey)) {
//                continue;
//            }
//            ret.addAll(this.listMetricNames(database, null, metric, tags, null, null, offset, size));
//        }
//        return ret;
//    }


    /**
     * @param database
     * @param measurement
     * @param keys
     * @param whereCondition
     * @param start
     * @param end
     * @param gids
     * @return
     * @throws Exception
     */
//    @Override
//    public Map<String, Set<String>> listTagKeyValues(String database, String measurement, Set<String> keys, Set<JSONObject> whereCondition, Timestamp start, Timestamp end, Collection<String> gids) throws Exception {
//        Map<String, Set<String>> ret = new HashMap<>();
//        Set<String> tagKeys = extractTagKeys(null, database, measurement);
//        if (CollectionUtils.isEmpty(tagKeys)) {
//            return ret;
//        }
//
//        if (CollectionUtils.isNotEmpty(keys) && tagKeys.contains(SQLParser.METRIC)) {
//            Set<JSONObject> metricsCondition = keys.stream()
//                    .filter(key -> !metricsQueryService.isDerivedMetric(key))
//                    .map(key -> new JSONObject().fluentPut(OR, true).fluentPut(SQLParser.NAME, SQLParser.METRIC).fluentPut(SQLParser.OPERATOR, SQLParser.EQ).fluentPut(SQLParser.VALUE, key))
//                    .collect(Collectors.toSet());
//            whereCondition.add(new JSONObject().fluentPut(OR, false).fluentPut(CONDITIONS, metricsCondition));
//        }
//
//        List<Result> results = moreDBUtil.showTagValues(database, measurement, tagKeys, whereCondition, start, end, 0, 0, false, gids);
//        for (Result result : results) {
//            if (result == null || result.getSeries() == null) {
//                continue;
//            }
//            for (Series series : result.getSeries()) {
//                for (List<Object> value : series.getValues()) {
//                    if (CollectionUtils.isEmpty(value) || value.size() < 2) {
//                        continue;
//                    }
//                    final String tagKey = (String) value.get(0);
//                    // tag key为metric算指标，不能作为标签
//                    if (tagKey == null || tagKey.equalsIgnoreCase(SQLParser.METRIC)) {
//                        continue;
//                    }
//                    final String tagValue = tagKey + ":" + value.get(1);
//                    // 按照tagkey对tagvalue做分组
//                    Set<String> set = ret.putIfAbsent(tagKey, Sets.newHashSet(tagValue));
//                    if (set != null) {
//                        set.add(tagValue);
//                    }
//                }
//            }
//        }
//        return ret;
//    }

    /**
     * 查询指定数据库、指标和标签范围内的标签名
     *
     * @param database 要查询的数据库名称集合
     * @param metric   要查询的指标名称集合
     * @param tags     要查询的标签条件集合，每个JSONObject表示一个标签名和值的对应关系
     * @param apiKey   访问密钥
     * @param start    查询起始时间戳
     * @param end      查询结束时间戳
     * @param tagOr    标签条件之间是否使用或逻辑，默认为否
     * @param gids
     * @return
     * @throws Exception
     */
//    @Override
//    @Deprecated
//    public Map<String, Set<String>> listTagKeyValues(Set<String> database, Set<String> metric, Set<JSONObject> tags, String apiKey, Timestamp start, Timestamp end, boolean tagOr, Collection<String> gids) throws Exception {
//        Map<String, Set<String>> ret = new HashMap<>();
//        List<JSONObject> query = listTagNames(database, metric, tags, apiKey, start, end, tagOr);
//        for (JSONObject o : query) {
//            if (o == null) {
//                continue;
//            }
//            List<Result> results = moreDBUtil.showTagValues(o.getString(SQLParser.DB), o.getString("table"), o.getObject("keys", Set.class), o.getObject(CONDITIONS, Set.class), o.getObject("start", Timestamp.class), o.getObject("end", Timestamp.class), 0, 0, false, gids);
//            for (Result result : results) {
//                if (result == null || result.getSeries() == null) {
//                    continue;
//                }
//                for (Series series : result.getSeries()) {
//                    for (List<Object> value : series.getValues()) {
//                        if (CollectionUtils.isEmpty(value) || value.size() < 2) {
//                            continue;
//                        }
//                        final String tagKey = (String) value.get(0);
//                        // tag key为metric算指标，不能作为标签
//                        if (tagKey == null || tagKey.equalsIgnoreCase(SQLParser.METRIC)) {
//                            continue;
//                        }
//                        final String tagValue = tagKey + ":" + value.get(1);
//                        // 按照tagkey对tagvalue做分组
//                        Set<String> set = ret.putIfAbsent(tagKey, Sets.newHashSet(tagValue));
//                        if (set != null) {
//                            set.add(tagValue);
//                        }
//                    }
//                }
//            }
//        }
//        return ret;
//    }

//    @Override
//    public List<JSONObject> listTagNames(Set<String> database, Set<String> metrics, Set<JSONObject> tags, String apiKey, Timestamp start, Timestamp end, boolean tagOr) throws Exception {
//        //构建指标查询条件
//        Set<JSONObject> conditions = buildConditions(metrics, tags, false);
//        if (CollectionUtils.isEmpty(database)) {
//            database = this.showDatabases();
//        }
//
//        return buildQueryParams(database, metrics, apiKey, start, end, conditions);
//    }

//    @NotNull
//    private List<JSONObject> buildQueryParams(Set<String> databases, Set<String> metrics, String apiKey, Timestamp start, Timestamp end, Set<JSONObject> conditions) throws Exception {
//        List<JSONObject> query = new ArrayList<>();
//        // 由于数据结构的差异 APM相关指标标签需要做特殊处理
//        for (String metric : metrics) {
//            if (metric == null) {
//                continue;
//            }
//            MetricsQuery metricDetail = metricsQueryService.findOpenByIdentifier(metric);
//            if (metricDetail == null) {
//                continue;
//            }
//
//            final boolean derivedMetric = metricsQueryService.isDerivedMetric(metric);
//            if (derivedMetric) {
//                continue;
//            }
//            String db = metricDetail.getDatabase();
//            if (!DATABASE_NAME_DATABUFF_SYSTEM.equals(db) && db != null) {
//                db = apiKey + "_" + db;
//            }
//            final String tb = metricDetail.getMeasurement();
//            Set<String> tagKeys = extractTagKeys(apiKey, db, tb);
//            if (CollectionUtils.isEmpty(tagKeys)) {
//                continue;
//            }
//            query.add(new JSONObject()
//                    .fluentPut(SQLParser.DB, db)
//                    .fluentPut("table", tb)
//                    .fluentPut(CONDITIONS, conditions)
//                    .fluentPut("start", start)
//                    .fluentPut("end", end)
//                    .fluentPut("keys", tagKeys));
//        }
//
//        for (String db : databases) {
//            Map<String, Set<String>> tbTagsMap = extractTagKeys(apiKey, db);
//            if (tbTagsMap == null) {
//                continue;
//            }
//            for (Map.Entry<String, Set<String>> entry : tbTagsMap.entrySet()) {
//                if (entry == null || entry.getValue() == null) {
//                    continue;
//                }
//                query.add(new JSONObject()
//                        .fluentPut(SQLParser.DB, db)
//                        .fluentPut("table", entry.getKey())
//                        .fluentPut(CONDITIONS, conditions)
//                        .fluentPut("start", start)
//                        .fluentPut("end", end)
//                        .fluentPut("keys", entry.getValue()));
//            }
//        }
//        return query;
//    }

    /**
     * 从给定的数据库和表中提取标签键。
     * 它过滤掉不包含提供的 apiKey 的数据库。
     *
     * @param apiKey 用于过滤数据库的apiKey。如果为空，则不执行任何过滤。
     * @param db     从中提取标签键的数据库的名称。
     * @param tb     从中提取标签键的表的名称。
     * @return 从指定数据库和表中提取的一组标签键。
     * @throws Exception 如果操作过程中发生错误。
     */
//    private Set<String> extractTagKeys(String apiKey, String db, String tb) throws Exception {
//        if (db == null || (apiKey != null && !db.contains(apiKey))) {
//            return Collections.emptySet();
//        }
//
//        return moreDBUtil.extractTagKeys(db, tb);
//    }

    /**
     * 从给定的数据库和表中提取标签键。
     * 它过滤掉不包含提供的 apiKey 的数据库。
     *
     * @param apiKey 用于过滤数据库的apiKey。如果为空，则不执行任何过滤。
     * @param db     从中提取标签键的数据库的名称。
     * @return 从指定数据库和表中提取的一组标签键。
     * @throws Exception 如果操作过程中发生错误。
     */
//    private Map<String, Set<String>> extractTagKeys(String apiKey, String db) throws Exception {
//        if (db == null || (apiKey != null && !db.contains(apiKey))) {
//            return new HashMap<>();
//        }
//
//        return moreDBUtil.extractTagKeys(db);
//    }

//    @Override
//    public List<JSONObject> listMetricNames(String db, String measurements, Set<String> metric, Set<JSONObject> tags, Timestamp start, Timestamp end, Integer offset, Integer size) throws Exception {
//        List<JSONObject> ret = new ArrayList<>();
//        Set<JSONObject> conditions = buildConditions(metric, tags, true);
//        List<Result> results = moreDBUtil.showTagValues(db, measurements, Sets.newHashSet(SQLParser.METRIC), conditions, start, end, offset, size, false, null);
//        for (Result result : results) {
//            if (result == null || result.getSeries() == null) {
//                continue;
//            }
//            for (Series series : result.getSeries()) {
//                for (List<Object> value : series.getValues()) {
//                    if (CollectionUtils.isEmpty(value) || value.size() < 2) {
//                        continue;
//                    }
//                    ret.add(new JSONObject().fluentPut((String) value.get(0), value.get(1)));
//                }
//            }
//        }
//        return ret;
//    }

    @NotNull
    private Set<JSONObject> buildConditions(Set<String> metric, Set<JSONObject> tags, boolean tagOr) {
        Set<JSONObject> conditions = new HashSet<>();
        if (CollectionUtils.isNotEmpty(tags)) {
            conditions.add(new JSONObject().fluentPut(OR, tagOr).fluentPut(CONDITIONS, tags));
        }
        if (CollectionUtils.isNotEmpty(metric)) {
            for (String m : metric) {
                if (StringUtils.isBlank(m)) {
                    continue;
                }
                final boolean derivedMetric = metricsQueryService.isDerivedMetric(m);
                if (derivedMetric) {
                    continue;
                }
                JSONObject metricTag = new JSONObject();
                metricTag.put(SQLParser.NAME, SQLParser.METRIC);
                metricTag.put(SQLParser.VALUE, m);
                metricTag.put(SQLParser.OPERATOR, SQLParser.EQ);
                conditions.add(new JSONObject().fluentPut(OR, false).fluentPut(CONDITIONS, Sets.newHashSet(metricTag)));
            }
        }
        return conditions;
    }

    @Override
    public Object findMetricDetail(@NotNull QueryRequest queryJson, boolean needHostCount, boolean needTags) throws Exception {
        Map<String, Object> resMap = new HashMap<>(3);
        // 指标字典中查询单位和描述
        String metric = queryJson.getMetric();
        String apiKey = queryJson.getApiKey();
        final Long start = queryJson.getStart();
        final Long end = queryJson.getEnd();
        final MetricsQuery metricsQuery = metricsQueryService.load(metric);
        if (metricsQuery == null) {
            return resMap;
        }
        resMap.put("metricDetail", metricsQuery);
        // 查询标签数
        if (needTags) {
            // 根据指标名称、时间条件查询主机数、标签
            Map<String, Set<String>> tagValues = metricAggregator.showTagValuesResult(apiKey, metric, null, null, start, end);
            resMap.put("tagMap", tagValues);
            resMap.put("tagSize", tagValues.size());
        }
//        // 查询主机数
//        if (needHostCount) {
//            // 设置域ID
//            Collection<String> gids = Optional.ofNullable(queryJson.getGids()).orElseGet(ArrayList::new);
//            resMap.put("hostSize", totalMetric(Sets.newHashSet(database), table, Sets.newHashSet("host"), Sets.newHashSet(metric), Sets.newHashSet(), apiKey, gids));
//        }
        return resMap;
    }
}
