package com.databuff.metric.impl.alarmV2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.databuff.common.tsdb.dto.CompositeCondition;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.common.tsdb.dto.preview.ThresholdsDTO;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.entity.EventEntity;
import com.databuff.entity.MetricsQuery;
import com.databuff.metric.AlarmCheckOperatorV2;
import com.databuff.metric.MetricAggregator;
import com.databuff.metric.moredb.SQLParser;
import com.databuff.service.JedisService;
import com.databuff.service.MetricsQueryService;
import com.databuff.util.LocalCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.databuff.common.constants.TSDBIndex.DATABASE_NAME_DATABUFF_SYSTEM;

/**
 * @author:TianMing
 * @date: 2023/10/17
 * @time: 17:52
 */
@Component
@Slf4j
public abstract class BaseAlarmCheckV2 implements AlarmCheckOperatorV2 {
    @Resource
    protected JedisService jedisService;

    @Autowired
    protected MetricAggregator metricAggregator;

    @Autowired
    protected MetricsQueryService metricsQueryService;

    @Value("${alarm.needMoreTag.enable:false}")
    private boolean needMoreTag;

    /**
     * 数据完整性检查
     *
     * @param period   检测周期（单位秒）
     * @param lineData 分组和数值的映射，键为标签的Map，值为时间戳和数值的有序Map
     * @return 如果检测周期内数据完整，返回true，否则返回false
     * <p>
     * 输入：period = 120, entry = {city=Beijing}={1634726400=10.0, 1634730000=null}
     * 输出：false
     * <p>
     * 输入：period = 120, entry = {city=Shanghai}={1634726400=8.0, 1634730000=9.0}
     * 输出：true
     */
    protected boolean dataIntegrityCheck(long period, Map.Entry<Map, Map<Object, Double>> lineData) {
        // 周期(秒)/60=期望的数据点（取整）
        long expectCount = period / 60L;

        if (lineData == null) {
            return false;
        }
        Map<Object, Double> pointMap = lineData.getValue();
        if (CollectionUtils.isEmpty(pointMap)) {
            return false;
        }
        //检测周期内有数据的count
        long pointCount = pointMap.values().stream().filter(i -> i != null && i >= 0).count();

        // 正常的话，周期内pointCount与expectCount相等
        if (pointCount >= expectCount) {
            return true;
        }
        log.warn(String.format("已开启数据完整性校验，对象%s在检测周期内数据不完整，期望：%d个，实际：%d个。", JSON.toJSONString(lineData.getKey()), expectCount, pointCount));
        return false;
    }

    /**
     * 根据时间聚合器和比较符获取分组的聚合值
     *
     * @param timeAggregator 时间聚合器，可以是avg（平均值）、max（最大值）、min（最小值）、last（最后一个值）、least（至少有n个）、always（始终满足比较符的值）
     * @param aggTimeSeries  分组和时间序列的映射，键为标签的Map，值为时间戳和数值的有序Map
     * @param comparison     比较符，可以是>、>=、<、<=，仅在时间聚合器为always时有效
     * @return 一个Map，键为标签的Map，值为聚合后的数值
     * 输入：timeAggregator = “avg”, aggTimeSeries = {{city=Beijing}={1634726400=10.0, 1634730000=12.0}, {city=Shanghai}={1634726400=8.0, 1634730000=9.0}}, comparison = null
     * 输出：result = {{city=Beijing}=11.0, {city=Shanghai}=8.5}
     */
    protected Map<Map, Map.Entry> getAggValue(final String timeAggregator, final Map<Map, Map<Object, Double>> aggTimeSeries,
                                              final String comparison, Boolean continuous, Integer continuous_n, ThresholdsDTO thresholds) {
        Map<Map, Map.Entry> result = new HashMap<>();
        if (timeAggregator == null) {
            log.warn("找不到timeAggregator参数");
            return result;
        }
        for (Map.Entry<Map, Map<Object, Double>> lineData : aggTimeSeries.entrySet()) {
            if (lineData == null || lineData.getValue() == null || CollectionUtils.isEmpty(lineData.getValue().values())) {
                continue;
            }
            final Map<Object, Double> lineDataValue = lineData.getValue();
            if (!(lineDataValue instanceof TreeMap)) {
                continue;
            }
            final TreeMap<String, Double> sortedTimeValues = (TreeMap) lineDataValue;
            // 最近一个值非null的时间节点（秒）
            final Long lastTimestamp;
            final String lastNonNullValueKey = getLastNonNullValueKey(sortedTimeValues);
            if (lastNonNullValueKey == null) {
                lastTimestamp = System.currentTimeMillis() / 1000L;
            } else {
                lastTimestamp = Long.valueOf(lastNonNullValueKey);
            }

            final Collection<Double> values = lineDataValue.values();
            /**
             * lineDataKey 指的是时间序列对应的标签集合（触发对象）
             * @see {"host":"host193","source":"DataBuff","host_id":"fa9880f4582d8076a2c2a76290a8ed71"}
             */
            final Map lineDataKey = lineData.getKey();
            switch (timeAggregator) {
                case "avg":
                case "mean":
                    final Double mean =
                            values.stream().filter(Objects::nonNull).collect(Collectors.averagingDouble(t -> t));
                    result.put(lineDataKey, new AbstractMap.SimpleEntry<>(lastTimestamp, mean));
                    break;
                case "sum":
                    final double sum = values.stream().filter(Objects::nonNull).mapToDouble(t -> t).sum();
                    result.put(lineDataKey, new AbstractMap.SimpleEntry<>(lastTimestamp, sum));
                    break;
                case "max":
                    final double max = values.stream().filter(Objects::nonNull).mapToDouble(t -> t).max().getAsDouble();
                    result.put(lineDataKey, new AbstractMap.SimpleEntry<>(lastTimestamp, max));
                    break;
                case "min":
                    final double min = values.stream().filter(Objects::nonNull).mapToDouble(t -> t).min().getAsDouble();
                    result.put(lineDataKey, new AbstractMap.SimpleEntry<>(lastTimestamp, min));
                    break;
                //至少有n个必须连续的点大于阈值
                case "least":
                    if (continuous_n == null || thresholds == null || !(lineDataValue instanceof TreeMap)) {
                        log.warn("错误的参数配置：continuous_n或thresholds或lineDataValue");
                        break;
                    }
                    final TreeMap<Long, Double> treeMap = new TreeMap<>();
                    for (Map.Entry<Object, Double> entry : lineDataValue.entrySet()) {
                        if (entry.getKey() instanceof String) {
                            try {
                                Long key = Long.parseLong((String) entry.getKey());
                                treeMap.put(key, entry.getValue());
                            } catch (NumberFormatException e) {
                                // 处理转换错误
                                log.error("Key conversion error: " + entry.getKey(), e);
                            }
                        }
                    }

                    final Double critical = thresholds.getCritical() == null ? null : thresholds.getCritical().doubleValue();
                    final Double warning = thresholds.getWarning() == null ? null : thresholds.getWarning().doubleValue();
                    if (critical == null && warning == null) {
                        log.warn("阈值配置错误：critical和warning至少需要配置一个");
                        return null;
                    }

                    Map.Entry<Long, Double> realTimeValue = this.getEntryWithValue(treeMap, Double.valueOf(continuous_n), critical, continuous, comparison);
                    if (realTimeValue == null) {
                        realTimeValue = this.getEntryWithValue(treeMap, Double.valueOf(continuous_n), warning, continuous, comparison);
                    }
                    if (realTimeValue != null) {
                        result.put(lineDataKey, realTimeValue);
                    }
                    break;
                case "last":
                    Collection objects = Lists.newArrayList(lineDataValue.keySet().toArray());
                    Object maxKey = Collections.max(objects, new Comparator<Object>() {
                        @Override
                        public int compare(Object o1, Object o2) {
                            if (o1 == null || o2 == null) {
                                return 0;
                            }
                            String o1Str = o1.toString();
                            String o2Str = o2.toString();
                            if (!NumberUtils.isCreatable(o1Str) || !NumberUtils.isCreatable(o2Str)) {
                                return 0;
                            }
                            return Long.compare(Long.parseLong(o1Str), Long.parseLong(o2Str));
                        }
                    });
                    result.put(lineDataKey, new AbstractMap.SimpleEntry<>(Long.valueOf((String) maxKey), lineDataValue.get(maxKey)));
                    break;
                case "always":
                    if (comparison == null) {
                        log.warn("找不到comparison参数");
                        break;
                    }
                    switch (comparison) {
                        case ">":
                        case ">=":
                            AbstractMap.SimpleEntry<Long, Double> value = new AbstractMap.SimpleEntry<>(lastTimestamp,
                                    values.stream().filter(Objects::nonNull).mapToDouble(t -> t).min().getAsDouble());
                            result.put(lineDataKey, value);
                            break;
                        case "<":
                        case "<=":
                            value = new AbstractMap.SimpleEntry<>(lastTimestamp,
                                    values.stream().filter(Objects::nonNull).mapToDouble(t -> t).max().getAsDouble());
                            result.put(lineDataKey, value);
                            break;
                        default:
                            log.warn("comparison不在处理范围内：>/>=/</<=");
                            break;
                    }
                    break;
                default:
                    log.warn("timeAggregator不在处理范围内：avg/mean/sum/max/min/last/always");
                    break;
            }
        }
        return result;
    }


    /**
     * 获取 TreeMap 中最后一个非空值的键。
     * @param sortedTimeValues
     * @return
     */
    public String getLastNonNullValueKey(TreeMap<String, Double> sortedTimeValues) {
        if (sortedTimeValues == null) {
            return null;
        }
        for (Map.Entry<String, Double> entry : sortedTimeValues.descendingMap().entrySet()) {
            if (entry != null && entry.getValue() != null) {
                return entry.getKey();
            }
        }
        return null;
    }

    /**
     * 根据提供的比较运算符比较两个 Double 值。
     *
     * @param value          要比较的值。
     * @param thresholdValue 要比较的阈值。
     * @param comparison     作为字符串的比较运算符。支持的运营商有：
     *                       ">" - 大于
     *                       ">= - 大于或等于
     *                       "<" - 小于
     *                       “<=” - 小于或等于
     * @如果满足比较条件则返回 true，否则返回 false。
     */
    private boolean compareValues(Double value, Double thresholdValue, String comparison) {
        if (value == null || thresholdValue == null || comparison == null) {
            return false;
        }
        switch (comparison) {
            case ">":
                return value > thresholdValue;
            case ">=":
                return value >= thresholdValue;
            case "<":
                return value < thresholdValue;
            case "<=":
                return value <= thresholdValue;
            default:
                return false;
        }
    }

    /**
     * 检索TreeMap 中满足指定比较条件的最后一个条目。
     *
     * @param treeMap        包含要评估的条目的 TreeMap。
     * @param n              必须满足条件的连续条目的数量。
     * @param thresholdValue 要比较的阈值。
     * @param continuous     如果为 true，则条目必须是连续的；否则，它们可能是不连续的。
     * @param comparison     作为字符串的比较运算符。支持的运营商有：
     *                       ">" - 大于
     *                       ">= - 大于或等于
     *                       "<" - 小于
     *                       “<=” - 小于或等于
     * @return 满足指定条件的最后一个条目，如果不存在这样的条目则返回 null。
     */
    protected Map.Entry<Long, Double> getEntryWithValue(
            TreeMap<Long, Double> treeMap, Double n, Double thresholdValue, Boolean continuous, String comparison) {
        Map.Entry<Long, Double> result = null;
        if (thresholdValue == null || treeMap == null) {
            return result;
        }

        Boolean findMax = false;
        int count = 0;
        switch (comparison) {
            case ">":
            case ">=":
                findMax = true;
                break;
            case "<":
            case "<=":
                findMax = false;
                break;
            default:
                break;
        }
        // 获取最大时间戳
        final Long timeKey = treeMap.lastKey();
        // 获取最大或最小值
        Double extremeValue = findMax ?
                treeMap.values().stream().filter(Objects::nonNull).max(Double::compareTo).orElse(Double.MIN_VALUE)
                : treeMap.values().stream().filter(Objects::nonNull).min(Double::compareTo).orElse(Double.MAX_VALUE);

        for (Map.Entry<Long, Double> entry : treeMap.entrySet()) {
            if (entry == null) {
                continue;
            }
            final Double value = entry.getValue();
            if (compareValues(value, thresholdValue, comparison)) {
                count++;
                extremeValue = findMax ? Math.max(extremeValue, value) : Math.min(extremeValue, value);
                if (n != null && count >= n) {
                    result = new AbstractMap.SimpleEntry<>(timeKey, extremeValue);
                }
            } else if (continuous != null && continuous) {
                count = 0;
            }
        }

        return result;
    }

    @Override
    public Map<Object, Object> needMoreTags(Map<Object, Object> eventsMap, Collection<QueryRequest> queries, Collection<String> gids) {
        if (!needMoreTag) {
            return eventsMap;
        }
        if (CollectionUtils.isEmpty(eventsMap)) {
            return eventsMap;
        }
        for (QueryRequest query : queries) {
            if (query == null || query.getMetric() == null) {
                continue;
            }
            final MetricsQuery metricsQuery = metricsQueryService.findOpenByIdentifier(query.getMetric());
            if (metricsQuery == null) {
                continue;
            }

            final String apiKey = query.getApiKey();
            final int period = query.getPeriod();

            String database = metricsQuery.getDatabase();
            if (!DATABASE_NAME_DATABUFF_SYSTEM.equals(database) && database != null) {
                database = apiKey + "_" + database;
            }

            final Collection<CompositeCondition> from = query.getFrom();
            final Timestamp start = new Timestamp(System.currentTimeMillis() - period * 1000L);
            final Timestamp end = new Timestamp(System.currentTimeMillis());

            for (Map.Entry<Object, Object> entry : eventsMap.entrySet()) {
                if (entry == null) {
                    continue;
                }
                final Map<String, String> eventTag = (Map) entry.getKey();
                final EventEntity eventEntity = (EventEntity) entry.getValue();
                if (eventEntity == null || eventEntity.getStatus() <= 0) {
                    OtelMetricUtil.logCounter("monitor.needMoreTags.event.skip", eventTag, 1);
                    continue;
                }
                OtelMetricUtil.logCounter("monitor.needMoreTags.event.cnt", eventTag, 1);
                Collection<JSONObject> collection = eventTag.entrySet().stream()
                        .map(e -> new JSONObject()
                                .fluentPut(SQLParser.LEFT, e.getKey())
                                .fluentPut(SQLParser.RIGHT, e.getValue())
                                .fluentPut(SQLParser.OPERATOR, SQLParser.EQ)
                                .fluentPut(SQLParser.CONNECTOR, SQLParser.AND))
                        .collect(Collectors.toList());
                HashSet<JSONObject> whereCondition = Sets.newHashSet(new JSONObject()
                        .fluentPut(SQLParser.LEFT, from)
                        .fluentPut(SQLParser.OPERATOR, SQLParser.AND)
                        .fluentPut(SQLParser.RIGHT, collection));

                String redisKey = metricsQuery.getIdentifier() + ":" + JSON.toJSONString(whereCondition);
                final Map<String, String> cacheIfPresent = LocalCache.localTagCache.getIfPresent(redisKey);
                if (cacheIfPresent != null) {
                    eventTag.putAll(cacheIfPresent);
                    OtelMetricUtil.logCounter("monitor.needMoreTags.cache.hit", eventTag, 1);
                } else {
//                    try {
//                        Map<String, Set<String>> map = metricInstanceOperator.listMetricMonitorTagValues(database, metricsQuery.getMeasurement(), null, whereCondition, start, end, gids);
//                        if (map != null && !map.isEmpty()) {
//                            map.entrySet().stream()
//                                    .filter(setEntry -> setEntry != null && setEntry.getValue() != null && setEntry.getValue().stream().filter(Objects::nonNull).count() <= 1)
//                                    .forEach(setEntry -> eventTag.put(setEntry.getKey(), String.join(",", setEntry.getValue())));
//                        }
//                        OtelMetricUtil.logCounter("monitor.needMoreTags.cache.missed", eventTag, 1);
//                        LocalCache.localTagCache.put(redisKey, eventTag);
//                    } catch (Exception e) {
//                        log.error("seriesResult error", e);
//                    }
                }
            }
        }
        return eventsMap;
    }


    /**
     * 过滤 aggTimeSeries 中 value.value 有值的 count 大于 key count 的 30% 的 entry
     *
     * @param aggTimeSeries 需要过滤的时间序列数据
     */
    protected Map<Map, Map<Object, Double>> filterAggTimeSeries(Map<Map, Map<Object, Double>> aggTimeSeries) {
        for (Iterator<Map.Entry<Map, Map<Object, Double>>> iterator = aggTimeSeries.entrySet().iterator(); iterator.hasNext(); ) {
            Map.Entry<Map, Map<Object, Double>> entry = iterator.next();
            if (entry == null || entry.getKey() == null || entry.getValue() == null) {
                iterator.remove();
                continue;
            }

            Map<Object, Double> valueMap = entry.getValue();
            if (CollectionUtils.isEmpty(valueMap)) {
                iterator.remove();
                continue;
            }

            // 计算有值的 count
            long nonNullCount = valueMap.values().stream().filter(Objects::nonNull).count();
            // 计算 key count
            long keyCount = valueMap.size();

            // 过滤条件：有值的 count 大于 key count 的 30%
            if (nonNullCount <= keyCount * 0.3) {
                iterator.remove();
            }
        }
        return aggTimeSeries;
    }

}
