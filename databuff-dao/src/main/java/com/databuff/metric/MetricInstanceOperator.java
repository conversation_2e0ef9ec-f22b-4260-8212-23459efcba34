package com.databuff.metric;

import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.sun.istack.NotNull;

/**
 * 指标元数据操作接口
 *
 * @version 1.0
 * @since 2.7.0
 */
public interface MetricInstanceOperator {
    /**
     * 查找特定指标的详细信息。
     *
     * @param queryJson     一个包含度量查询参数的JSONObject。这个JSONObject的结构将取决于度量的特定需求。
     * @param needHostCount 布尔值，表示结果中是否需要主机计数。如果为true，结果将包含主机计数。
     * @param needTags      布尔值，表示结果中是否需要这些标签。如果为true，结果将包含标签。
     * @返回一个包含指标细节的对象。这个对象的结构将取决于指标的具体要求。 如果在方法执行过程中发生任何错误，@throws异常。
     **/
    Object findMetricDetail(@NotNull QueryRequest queryJson, boolean needHostCount, boolean needTags) throws Exception;
}
