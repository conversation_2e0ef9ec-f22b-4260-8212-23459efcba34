package com.databuff.metric.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@ApiModel(description = "按组分组的指标图表数据")
public class MetricByGroupGraph {

    @ApiModelProperty(value = "描述信息列表", example = "[\"描述1\", \"描述2\"]")
    private List<String> describeCns;

    @ApiModelProperty(value = "列名列表", example = "[\"列1\", \"列2\"]")
    private List<String> columns;

    @ApiModelProperty(value = "值列表", example = "[[\"值1\", \"值2\"], [\"值3\", \"值4\"]]")
    private List<List<Object>> values;

    @ApiModelProperty(value = "图表名称", example = "示例图表")
    private String name;

    @ApiModelProperty(value = "单位列表", example = "[\"单位1\", \"单位2\"]")
    private List<String> units;

    @ApiModelProperty(value = "标签映射", example = "{\"标签1\": \"值1\", \"标签2\": \"值2\"}")
    private Map<String, String> tags;

    @ApiModelProperty(value = "数据的过期时间", example = "1627849200000")
    private long expiredAt;
}