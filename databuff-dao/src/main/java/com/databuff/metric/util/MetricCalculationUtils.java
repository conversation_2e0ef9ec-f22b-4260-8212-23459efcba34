package com.databuff.metric.util;

import com.databuff.entity.dto.TimeValue;
import com.databuff.metric.dto.MetricByGroupGraph;

import java.util.*;

public class MetricCalculationUtils {


    /**
     * 计算两个指标的比率工具方法
     *
     * @param numeratorData 分子数据,Map结构,key为分组条件,value为MetricByGroupGraph对象
     *                     MetricByGroupGraph中的values为时序数据,格式为[[timestamp, value],...]
     * @param denominatorData 分母数据,格式同numeratorData
     * @param isDenominatorSum 是否需要将分子值加到分母上
     *                        true: 最终计算公式为 100 * 分子/(分母+分子)
     *                        false: 最终计算公式为 100 * 分子/分母
     * @return List<TimeValue> 计算后的时序数据列表
     *         TimeValue.ts 为时间戳(毫秒)
     *         TimeValue.value 为计算后的比率值(百分比)
     *
     * 使用示例:
     * Map<Map<String, String>, MetricByGroupGraph> errorCount = queryErrorCount();
     * Map<Map<String, String>, MetricByGroupGraph> pv = queryPV();
     * // 计算错误率: 100 * errorCount/(pv + errorCount)
     * List<TimeValue> errorRate = calculateRateFromTwoMetrics(errorCount, pv, true);
     *
     * 注意事项:
     * 1. 输入的两个指标必须具有相同的时间粒度
     * 2. 当分母为0时该时间点会被过滤掉
     * 3. 返回结果按时间戳升序排序
     */
    public static List<TimeValue> calculateRateFromTwoMetrics(
            Map<Map<String, String>, MetricByGroupGraph> numeratorData,
            Map<Map<String, String>, MetricByGroupGraph> denominatorData,
            boolean isDenominatorSum) {

        TreeMap<Long, Double> numeratorMap = new TreeMap<>();
        TreeMap<Long, Double> denominatorMap = new TreeMap<>();

        // Process numerator data
        for (MetricByGroupGraph graph : numeratorData.values()) {
            for (List<Object> value : graph.getValues()) {
                Long timestamp = ((Number) value.get(0)).longValue() * 1000L;
                Double metricValue = value.get(1) != null ? ((Number) value.get(1)).doubleValue() : 0.0;
                numeratorMap.put(timestamp, metricValue);
            }
        }

        // Process denominator data and ensure all timestamps exist
        for (MetricByGroupGraph graph : denominatorData.values()) {
            for (List<Object> value : graph.getValues()) {
                Long timestamp = ((Number) value.get(0)).longValue() * 1000L;
                Double metricValue = value.get(1) != null ? ((Number) value.get(1)).doubleValue() : 0.0;
                denominatorMap.put(timestamp, metricValue);
                // Ensure numerator has all timestamps
                if (!numeratorMap.containsKey(timestamp)) {
                    numeratorMap.put(timestamp, 0.0);
                }
            }
        }

        // Calculate final rate for all timestamps
        List<TimeValue> result = new ArrayList<>();
        Set<Long> allTimestamps = new TreeSet<>(numeratorMap.keySet());
        allTimestamps.addAll(denominatorMap.keySet());

        for (Long timestamp : allTimestamps) {
            Double numeratorValue = numeratorMap.getOrDefault(timestamp, 0.0);
            Double denominatorValue = denominatorMap.getOrDefault(timestamp, 0.0);

            if (isDenominatorSum) {
                denominatorValue += numeratorValue;
            }

            TimeValue timeValue = new TimeValue();
            timeValue.setTs(timestamp);
            timeValue.setValue(denominatorValue > 0 ? 100 * numeratorValue / denominatorValue : 0.0);
            result.add(timeValue);
        }

        return result;
    }

    /**
     * 计算两个指标的比率工具方法,返回MetricByGroupGraph格式
     *
     * @param numeratorData 分子数据,Map结构,key为分组条件,value为MetricByGroupGraph对象
     *                     MetricByGroupGraph中的values为时序数据,格式为[[timestamp, value],...]
     * @param denominatorData 分母数据,格式同numeratorData
     * @param isDenominatorSum 是否需要将分子值加到分母上
     *                        true: 最终计算公式为 100 * 分子/(分母+分子)
     *                        false: 最终计算公式为 100 * 分子/分母
     * @return List<MetricByGroupGraph> 计算后的图表数据列表
     *
     * 使用示例:
     * Map<Map<String, String>, MetricByGroupGraph> errorCount = queryErrorCount();
     * Map<Map<String, String>, MetricByGroupGraph> pv = queryPV();
     * // 计算错误率: 100 * errorCount/(pv + errorCount)
     * List<MetricByGroupGraph> errorRate = calculateRateMetricGraph(errorCount, pv, true);
     */
    public static List<MetricByGroupGraph> calculateRateMetricGraph(
            Map<Map<String, String>, MetricByGroupGraph> numeratorData,
            Map<Map<String, String>, MetricByGroupGraph> denominatorData,
            boolean isDenominatorSum) {

        List<TimeValue> timeValues = calculateRateFromTwoMetrics(numeratorData, denominatorData, isDenominatorSum);

        MetricByGroupGraph resultGraph = new MetricByGroupGraph();
        List<List<Object>> resultValues = new ArrayList<>();

        // Copy metadata from source
        MetricByGroupGraph sourceGraph = numeratorData.values().iterator().next();
        resultGraph.setColumns(sourceGraph.getColumns());
        resultGraph.setDescribeCns(sourceGraph.getDescribeCns());
        resultGraph.setUnits(Arrays.asList("%"));

        // Convert TimeValue list to MetricByGroupGraph format
        for (TimeValue tv : timeValues) {
            resultValues.add(Arrays.asList(tv.getTs()/1000, tv.getValue()));
        }

        resultGraph.setValues(resultValues);
        return Collections.singletonList(resultGraph);
    }


}