package com.databuff.handler;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.databuff.entity.DBMeta;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.util.Collection;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2.7.1
 */

@MappedJdbcTypes({JdbcType.VARCHAR})
@MappedTypes({DBMeta.FieldMeta.class})
public class FieldMetaTypeHandler extends AbstractJsonTypeHandler<Collection<DBMeta.FieldMeta>> {
    /**
     * @param json
     * @return
     */
    @Override
    protected Collection<DBMeta.FieldMeta> parse(String json) {
        return JSONArray.parseArray(json, DBMeta.FieldMeta.class);
    }

    /**
     * @param obj
     * @return
     */
    @Override
    protected String toJson(Collection<DBMeta.FieldMeta> obj) {
        return JSONArray.toJSONString(obj);
    }
}
