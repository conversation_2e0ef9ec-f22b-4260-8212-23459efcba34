package com.databuff.kafka;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.databuff.common.annotation.LogExecutionTime;
import com.databuff.common.utils.OtelMetricUtil;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.databuff.common.constants.Constant.Trace.DTS_RECEIVE_TIME;
import static com.databuff.common.constants.MetricName.KAFKA_SEND_SUCCESS_COST;
import static com.databuff.common.constants.MetricName.KAFKA_SEND_SUCCESS_COUNT;

@Component
public class KafkaSender {

    private static final Log LOG = LogFactory.getLog(KafkaSender.class);
    public static final String KAFKA_DATA_SIZE = "kafka.data.size";
    @Autowired
    @Qualifier("otherKafkaProducerPool")
    private KafkaProducerPool producerPool;
    @Autowired
    private KafkaProducerConfig config;

    @SneakyThrows
    @LogExecutionTime(tag = {"topic"})
    public void data2Kafka(JSONObject data, String topic) {
        data2Kafka(data, topic, null);
    }

    @SneakyThrows
    @LogExecutionTime(tag = {"topic"})
    public void data2Kafka(JSONObject data, String topic, String key) {
        KafkaProducer<String, String> kafkaProducer = null;
        try {
            kafkaProducer = producerPool.borrowObject();
            final String jsonString = JSON.toJSONString(data, SerializerFeature.DisableCircularReferenceDetect);
            ProducerRecord<String, String> record = new ProducerRecord(topic, key, jsonString);
            kafkaProducer.send(record);
        } catch (Exception e) {
            LOG.error("发送kafka信息失败: " + e.getMessage(), e);
            throw e;
        } finally {
            if (kafkaProducer != null) {
                producerPool.returnObject(kafkaProducer);
            }
            OtelMetricUtil.logCounter(KAFKA_DATA_SIZE, new HashMap<String, String>() {{
                put("topic", topic);
            }}, JSONObject.toJSONBytes(data, SerializerFeature.DisableCircularReferenceDetect).length);
        }
    }

    public void data2Kafka(List<JSONObject> datas, String topic) {
        KafkaProducer<String, String> kafkaProducer = null;
        try {
            long start = System.currentTimeMillis();
            kafkaProducer = producerPool.borrowObject();
            for (JSONObject data : datas) {
                data.put(DTS_RECEIVE_TIME, start);
                ProducerRecord<String, String> record = new ProducerRecord(topic, null, data.toJSONString());
                kafkaProducer.send(record);
            }
            long duration = System.currentTimeMillis() - start;
            Map<String, String> tags = new HashMap<>();
            tags.put("topic", topic);
            OtelMetricUtil.logHistogram(KAFKA_SEND_SUCCESS_COST, tags, duration);
            OtelMetricUtil.logCounter(KAFKA_SEND_SUCCESS_COUNT, tags, datas.size());
        } catch (Exception e) {
            LOG.error("发送kafka信息失败: " + e.getMessage(), e);
            OtelMetricUtil.logException("data2Kafka", e);
        } finally {
            if (kafkaProducer != null) {
                producerPool.returnObject(kafkaProducer);
            }
        }
    }

    public void data2Kafka(List<JSONObject> datas, String keyField, String topic, String saveIndex) {
        KafkaProducer<String, String> kafkaProducer = null;
        try {
            long start = System.currentTimeMillis();
            kafkaProducer = producerPool.borrowObject();
            for (JSONObject data : datas) {
                if (StringUtils.isNotBlank(saveIndex)) {
                    data.put("saveIndex", saveIndex);
                }
                String key = null;
                if (StringUtils.isNotBlank(keyField)) {
                    key = data.getString(keyField);
                }
                data.put(DTS_RECEIVE_TIME, start);
                ProducerRecord<String, String> record = new ProducerRecord(topic, key, data.toJSONString());
                kafkaProducer.send(record);
            }
            long duration = System.currentTimeMillis() - start;
            Map<String, String> tags = new HashMap<>();
            tags.put("topic", topic);
            OtelMetricUtil.logHistogram(KAFKA_SEND_SUCCESS_COST, tags, duration);
            OtelMetricUtil.logCounter(KAFKA_SEND_SUCCESS_COUNT, tags, datas.size());
        } catch (Exception e) {
            LOG.error("发送kafka信息失败: " + e.getMessage(), e);
            OtelMetricUtil.logException("data2Kafka", e);
        } finally {
            if (kafkaProducer != null) {
                producerPool.returnObject(kafkaProducer);
            }
        }
    }

    public void data2Kafka(List<JSONObject> datas, String topic, String saveIndex) {
        KafkaProducer<String, String> kafkaProducer = null;
        try {
            long start = System.currentTimeMillis();
            kafkaProducer = producerPool.borrowObject();
            for (JSONObject data : datas) {
                data.put("saveIndex", saveIndex);
                data.put(DTS_RECEIVE_TIME, start);
                ProducerRecord<String, String> record = new ProducerRecord(topic, null, data.toJSONString());
                kafkaProducer.send(record);
            }
            long duration = System.currentTimeMillis() - start;
            Map<String, String> tags = new HashMap<>();
            tags.put("topic", topic);
            OtelMetricUtil.logHistogram(KAFKA_SEND_SUCCESS_COST, tags, duration);
            OtelMetricUtil.logCounter(KAFKA_SEND_SUCCESS_COUNT, tags, datas.size());
        } catch (Exception e) {
            LOG.error("发送kafka信息失败: " + e.getMessage(), e);
            OtelMetricUtil.logException("data2Kafka-saveIndex", e);
        } finally {
            if (kafkaProducer != null) {
                producerPool.returnObject(kafkaProducer);
            }
        }
    }

    @SneakyThrows
    @LogExecutionTime(tag = {"topic"})
    public void data2Kafka(String data, String topic) {
        data2Kafka(data, topic, null);
    }


    @SneakyThrows
    @LogExecutionTime(tag = {"topic"})
    public void data2Kafka(String data, String topic, String key) {
        if (data == null) {
            return;
        }
        KafkaProducer<String, String> kafkaProducer = null;
        try {
            kafkaProducer = producerPool.borrowObject();
            ProducerRecord<String, String> record = new ProducerRecord(topic, key, data);
            kafkaProducer.send(record);
        } catch (Exception e) {
            LOG.error("发送kafka信息失败: " + e.getMessage(), e);
            throw e;
        } finally {
            if (kafkaProducer != null) {
                producerPool.returnObject(kafkaProducer);
            }
            OtelMetricUtil.logCounter(KAFKA_DATA_SIZE, new HashMap<String, String>() {{
                put("topic", topic);
            }}, data.getBytes().length);
        }
    }


    @LogExecutionTime(tag = {"topic"})
    public void data2Kafka(KafkaProducer producer, String data, String topic) {
        if (data == null) {
            return;
        }
        try {
            ProducerRecord<String, String> record = new ProducerRecord(topic, null, data);
            producer.send(record);
        } catch (Exception e) {
            LOG.error("发送kafka信息失败: " + e.getMessage(), e);
            throw e;
        } finally {
            OtelMetricUtil.logCounter(KAFKA_DATA_SIZE, new HashMap<String, String>() {{
                put("topic", topic);
            }}, data.getBytes().length);
        }
    }

    @LogExecutionTime(tag = {"topic"})
    @SneakyThrows
    public void data2Kafka(KafkaProducer producer, JSONObject data, String topic) {
        try {
            final String jsonString = JSON.toJSONString(data, SerializerFeature.DisableCircularReferenceDetect);
            ProducerRecord<String, String> record = new ProducerRecord(topic, null, jsonString);
            producer.send(record);
        } catch (Exception e) {
            LOG.error("发送kafka信息失败: " + e.getMessage(), e);
            throw e;
        }
    }

    @LogExecutionTime(tag = {"hosts"})
    public KafkaProducer getProducer(String hosts, String user, String password) {

        Map<String, Object> props = new HashMap<>();
        props.putAll(config.producerConfigs());
        props.put("bootstrap.servers", hosts);
        if (StringUtils.isNotBlank(user) && StringUtils.isNotBlank(password)) {
            String jaasTemplate = "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"%s\" password=\"%s\";";
            String jaasCfg = String.format(jaasTemplate, user, password);
            props.put("security.protocol", "SASL_PLAINTEXT");
            props.put("sasl.mechanism", "PLAIN");
            props.put("sasl.jaas.config", jaasCfg);
        }
        KafkaProducer kafkaProducer = new KafkaProducer<>(props);
        return kafkaProducer;
    }

}
