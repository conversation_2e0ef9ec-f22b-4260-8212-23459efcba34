package com.databuff.dao.mysql.topo;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.databuff.entity.dto.topo.TopologyNode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.List;

@Mapper
@Repository
@Api(tags = "拓扑节点Mapper接口")
public interface TopologyNodeMapper extends BaseMapper<TopologyNode> {

    /**
     * 根据ID查询拓扑节点。
     *
     * @param id 节点ID
     * @return 拓扑节点
     */
    @Select("SELECT * FROM topology_node WHERE id = #{id}")
    @Results({
            @Result(property = "upstreamNodes", column = "upstream_nodes", javaType = JSONArray.class),
            @Result(property = "downstreamNodes", column = "downstream_nodes", javaType = JSONArray.class),
            @Result(property = "metricMetaData", column = "metric_meta_data", javaType = JSONArray.class)
    })
    @Override
    @ApiOperation(value = "根据ID查询拓扑节点", notes = "根据节点ID查询拓扑节点信息")
    TopologyNode selectById(@ApiParam(value = "节点ID", required = true) @Param("id") Serializable id);

    @Select("<script>" +
            "SELECT * FROM topology_node " +
            "<if test='ew != null'> " +
            "${ew.customSqlSegment} " +
            "</if> " +
            "</script>")
    @Results({
            @Result(property = "upstreamNodes", column = "upstream_nodes", javaType = JSONArray.class),
            @Result(property = "downstreamNodes", column = "downstream_nodes", javaType = JSONArray.class),
            @Result(property = "metricMetaData", column = "metric_meta_data", javaType = JSONArray.class)
    })
    @Override
    @ApiOperation(value = "查询拓扑节点列表", notes = "根据条件查询拓扑节点列表")
    List<TopologyNode> selectList(@ApiParam(value = "查询条件包装器", required = false) @Param(Constants.WRAPPER) Wrapper<TopologyNode> queryWrapper);

    @Select("<script>" +
            "SELECT id,`name`,upstream_nodes,downstream_nodes,metric_meta_data,node_type FROM topology_node " +
            "<if test='ew != null'> " +
            "${ew.customSqlSegment} " +
            "</if> " +
            "</script>")
    @Results({
            @Result(property = "upstreamNodes", column = "upstream_nodes", javaType = JSONArray.class),
            @Result(property = "downstreamNodes", column = "downstream_nodes", javaType = JSONArray.class),
            @Result(property = "metricMetaData", column = "metric_meta_data", javaType = JSONArray.class)
    })
    @ApiOperation(value = "查询拓扑图", notes = "根据条件查询拓扑图信息")
    List<TopologyNode> graph(@ApiParam(value = "查询条件包装器", required = false) @Param(Constants.WRAPPER) Wrapper<TopologyNode> queryWrapper);

    @Select("<script>" +
            "SELECT id,`name` FROM topology_node " +
            "</script>")
    @ApiOperation(value = "查询所有节点映射", notes = "查询所有节点的ID和名称映射")
    List<TopologyNode> allNodeMap();

    @Update("UPDATE topology_node SET `name` = #{et.name}, `group_id` = #{et.groupId}, node_type = #{et.nodeType}, description = #{et.description}, upstream_nodes = #{et.upstreamNodes}, downstream_nodes = #{et.downstreamNodes}, metric_meta_data = #{et.metricMetaData} WHERE id = #{et.id}")
    @Override
    @ApiOperation(value = "根据ID更新拓扑节点", notes = "根据节点ID更新拓扑节点信息")
    int updateById(@ApiParam(value = "拓扑节点实体", required = true) @Param(Constants.ENTITY) TopologyNode entity);


    @Update("UPDATE topology_node SET `group_id` = null WHERE group_id = #{groupId}")
    @ApiOperation(value = "重置groupId为null", notes = "重置groupId为null")
    int ResetGroupId(@Param("groupId") String groupId);
}