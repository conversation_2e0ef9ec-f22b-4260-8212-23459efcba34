package com.databuff.dao.mysql;

import com.databuff.entity.Resources;
import com.databuff.entity.SaasOtherConfine;
import com.databuff.entity.SaasTenant;
import com.databuff.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * @package com.databuff.webapp.admin.mapper
 * @company: dacheng
 * @author: zlh
 * @createDate: 2021/11/1
 */
@Mapper
@Repository
public interface SaasCustomerMapper {
    /**
     * 根据账号查询用户
     * @param account
     * @return
     */
    User getSaasUserInfo(@Param("account") String account);

    /**
     * 根据apikey查询租户信息
     * @param apiKey
     * @return
     */
    SaasTenant getTenantByApiKey(@Param("apiKey") String apiKey);

    /**
     * 根据id修改是否首次登陆
     * @param id
     * @param firstLogin
     */
    void updateFirstLoginById(@Param("id") Long id, @Param("firstLogin") int firstLogin);

    /**
     * 根据id修改用户密码
     * @param userDto
     */
    void updatePwById(User userDto);

    /**
     * 根据api key修改公司
     * @param apiKey
     * @param companyName
     */
    void updateTenantCompanyByApiKey(@Param("apiKey") String apiKey, @Param("companyName") String companyName);

    /**
     * 根据api key修改公司规模
     * @param apiKey
     * @param companyScale
     */
    void updateTenantCompanyScaleByApiKey(@Param("apiKey") String apiKey, @Param("companyScale") String companyScale);

    /**
     * 根据id修改昵称
     * @param user
     */
    void updateUserNickNameById(User user);

    /**
     * 根据id修改是否邀请
     * @param user
     */
    void updateUserAllowInvitationById(User user);

    /**
     * 根据id修改电话
     * @param user
     */
    void updateUserPhoneById(User user);

    /**
     * 根据id修改邮件
     * @param user
     */
    void updateUserEmailById(User user);

    /**
     * 根据api key修改租户电话
     * @param memberPhone
     * @param apiKey
     */
    void updateTenantPhoneByApiKey(@Param("memberPhone") String memberPhone, @Param("apiKey") String apiKey);

    /**
     * 根据api key修改租户邮箱
     * @param emailAddr
     * @param apiKey
     */
    void updatetenantEmailByApiKey(@Param("emailAddr") String emailAddr, @Param("apiKey") String apiKey);

    /**
     * 查询所有租户
     * @param tenant
     * @return
     */
    List<SaasTenant> findAllTenant(SaasTenant tenant);

    /**
     * 根据apiKey查询菜单资源
     * @param apiKey
     * @return
     */
    List<Resources> findResourceByApiKey(@Param("apiKey") String apiKey);

    /**
     * 根据授权是否过期查询apiKey
     * @param licEndTime
     * @param timeOut
     * @return
     */
    List<SaasTenant> findAllApiKeyByTimeOut(@Param("licEndTime") Date licEndTime, @Param("timeOut") int timeOut);

    /**
     * 根据apiKey查询限制
     * @param apiKey
     * @return
     */
    SaasOtherConfine getConfineByApiKey(@Param("apiKey") String apiKey);

    /**
     * 根据条件查询user id
     * @param apiKey
     * @param userName
     * @return
     */
    Long getUserIdByUserApiKey(@Param("apiKey") String apiKey,@Param("userName") String userName);

    int getTenantIdByApiKey(@Param("apiKey") String apiKey);

}
