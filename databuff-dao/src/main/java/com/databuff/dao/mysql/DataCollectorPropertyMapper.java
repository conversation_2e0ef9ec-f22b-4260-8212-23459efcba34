package com.databuff.dao.mysql;

import com.databuff.entity.DataCollectorPropertyEntity;
import com.databuff.entity.DataCollectorSourceEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DataCollectorPropertyMapper {

    void addDataCollectorProperty(DataCollectorPropertyEntity property);

    void addDataCollectorSource(DataCollectorSourceEntity source);

    void editDataCollectorProperty(DataCollectorPropertyEntity property);

    void deleteDataCollectorSourceByPropertyId(@Param("propertyId") Long propertyId);

    List<DataCollectorPropertyEntity> list(@Param("property") String property);

    DataCollectorPropertyEntity getById(@Param("id") Long id);

    List<DataCollectorSourceEntity> getByPropertyId(Long id);

    void updateState(@Param("id") Long id, @Param("state") boolean state);

    List<DataCollectorSourceEntity> listAllSources();

    List<DataCollectorPropertyEntity> listAllProperties();

    void deleteDataCollectorProperty(@Param("id") Long id);

    /**
     * 根据服务ID和资源名称查询所有活动的、已启用的数据采集源配置。
     * @param serviceId    服务ID (对应 dc_data_collector_source.service_id)
     * @param resourceName 资源名称 (对应 dc_data_collector_source.resource)
     * @return 匹配的、已启用的数据采集源实体 ({@link DataCollectorSourceEntity}) 列表。
     */
    List<DataCollectorSourceEntity> findActiveSourcesByServiceIdAndExactResource(
            @Param("serviceId") String serviceId,
            @Param("resourceName") String resourceName
    );

}
