package com.databuff.dao.mysql;

import com.databuff.entity.HostInfoEntity;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Mapper
@Repository
public interface HostInfoEntityMapper {

    /**
     * 更新主机最新指标数据
     */
    int updateHostMetrics(@Param("hostName") String hostName,@Param("apiKey") String apiKey,@Param("lastreportmem") String lastreportmem,@Param("lastreportcpu") String lastreportcpu,@Param("lastreportmemusedpercent") String lastreportmemusedpercent);

    /**
     * 根据主机id查询主机名
     */
    List<HostInfoEntity> listNameByIds(@Param("ids") List<Integer> ids, @Param("apiKey") String apiKey);

    /**
     * 根据起始时间查询存在的主机名
     */
    List<String> listNameByTime(@Param("startTime") String startTime,@Param("endTime") String endTime,@Param("apiKey") String apiKey);

    List<String> getDistinctHostOs(@Param("startTime") String startTime,@Param("endTime") String endTime,@Param("apiKey") String apiKey);


    @MapKey("host")
    Map<String, Map<String, String>> hostGOOS(@Param("hostNames") List<String> hostNames, @Param("apiKey") String apiKey);

    List<HostInfoEntity> listHosts(@Param("isFuzzy") int isFuzzy, @Param("hostIp") String hostIp, @Param("hostName") String hostName,
                                   @Param("os") String os, @Param("filterByAppOrTag") boolean filterByAppOrTag, @Param("filteredHostNames") Set<String> filteredHostNames,
                                   @Param("from")String fromTime, @Param("to") String toTime, @Param("apiKey") String apiKey, @Param("sortOrder") String sortOrder, @Param("sortField") String sortField);

    HostInfoEntity selectByName(@Param("hostName") String hostName,@Param("from") String startTime,@Param("to") String endTime,@Param("apiKey") String apiKey);

    HostInfoEntity selectById(@Param("id") Integer id,@Param("apiKey") String apiKey);

    List<HostInfoEntity> listNameByHostIds(@Param("hostIds") List<String> ids, @Param("apiKey") String apiKey);

    HostInfoEntity selectByHostId(@Param("hostId") String hostId,@Param("apiKey") String apiKey);

    HostInfoEntity selectByHostIdOrName(@Param("hostId") String hostId,@Param("hostName") String hostName,@Param("apiKey")  String apiKey);

    void insertHost(HostInfoEntity hostInfo);
    int updateHost(HostInfoEntity hostInfo);
}