package com.databuff.dao.mysql;

import com.databuff.entity.AppHostRelationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface AppHostRelationEntityMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table dc_app_host_relation
     *
     * @mbg.generated Wed Jun 29 11:02:32 CST 2022
     */
    int insert(AppHostRelationEntity record);


    List<String> getDistinctAppsByHostNames(@Param("hostNames") List<String> hostNames, @Param("apiKey") String apiKey);

    List<String> getHostNamesByApp(@Param("app")String app,@Param("apiKey") String apiKey);

    List<AppHostRelationEntity> listAppByHosts(@Param("hostNames") List<String> hostNames,@Param("apiKey") String apiKey);

    void deleteOldAppsById( @Param("id") Integer id);
}