package com.databuff.dao.mysql;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.databuff.entity.Business;
import com.databuff.entity.BusinessSvcInfo;
import com.databuff.entity.dto.KeyValue;
import com.databuff.entity.extend.BusinessSearch;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 业务系统与子系统信息表
 * @date 2022-08-24
 */
@Mapper
public interface BusinessMapper {

    Integer insert(Business business);

    int bachAddRelation(@Param("id") Integer id, @Param("serviceIds") JSONArray serviceIds);

    /**
     * 批量删除业务系统与服务的关联关系
     * @param sysId 业务系统ID
     * @param serviceIds 要删除关联的服务ID列表
     * @return 受影响的行数
     */
    int batchRemoveRelation(@Param("sysId") Integer sysId, @Param("serviceIds") Set<String> serviceIds);

    List<Business> getBusinessList(BusinessSearch business);

    /**
     * 根据父级名称列表获取子业务系统列表
     *
     * @param apiKey 用于身份验证的API密钥。
     * @param names  父级名称的集合，用于查询相关的业务列表。
     * @return 返回与给定父级名称列表相关的业务列表。如果未找到匹配的业务，则返回空列表。
     */
    List<Business> getBusinessListByParentNameList(@Param("apiKey") String apiKey, @Param("names") Collection<String> names);

    /**
     * 根据单个父级名称获取子业务系统列表
     *
     * @param apiKey 用于身份验证的API密钥。
     * @param name   父级名称，用于查询相关的业务列表。
     * @return 返回与给定父级名称相关的业务列表。如果未找到匹配的业务，则返回空列表。
     */
    List<Business> getBusinessListByParentName(@Param("apiKey") String apiKey, @Param("name") String name);

    /**
     * 根据列表中搜索的名称，查询相关服务，子系统，业务系统及其上级系统
     *
     * @param apiKey
     * @param listnameQuery
     * @return
     */
    List<Business> getAllSysByQueryName(@Param("apiKey") String apiKey, @Param("listnameQuery") String listnameQuery);

    /**
     * 只查当前系统下的服务id（包括子系统下的）
     *
     * @param sysId
     * @return
     */
    List<String> getServiceIdsBySysId(@Param("sysId") Integer sysId);

    /**
     * 获取和业务系统无任何关系的服务列表
     *
     * @return
     */
    List<String> getServiceIdsNotInBusiness(@Param("apiKey") String apiKey);

    List<String> getServiceIdsNotInBusinessWithTime(@Param("apiKey") String apiKey, @Param("fromTime") String fromTime);

    /**
     * 查当前系统下层所有的服务id（包括子系统下的）
     *
     * @param sysId
     * @return
     */
    Set<String> getAllServiceIdsBySysId(@Param("sysId") Integer sysId);


    Set<String> getAllServiceIdsBySysIdWithTime(@Param("sysId") Integer sysId, @Param("fromTime") String fromTime);

    List<KeyValue> getAllServiceIdsAndTypeByBusinessHierarchyWithTime(@Param("sysId") Integer sysId, @Param("fromTime") String fromTime);

    /**
     * 查当前系统下层所有的服务显示名（包括子系统下的）
     *
     * @param sysId
     * @return
     */
    Set<String> getAllServiceNamesBySysId(@Param("sysId") Integer sysId);

    Business getBusinessById(@Param("id") Integer id, @Param("apiKey") String apiKey);

    List<Business> getBusinessByIds(@Param("ids") List<Integer> ids, @Param("apiKey") String apiKey);

    List<BusinessSvcInfo> getBusinessSvcByIds(@Param("svcIds") List<String> svcIds,@Param("bNames") List<String> bNames);

    int updateSys(Business business);

    int delSys(@Param("id") Integer id, @Param("pid") Integer pid);

    int delRelation(@Param("sysId") Integer sysId, @Param("serviceId") String serviceId);

    int delRelationBySysIds(@Param("sysIds") List<Integer> sysIds);

    /**
     * 删除不在服务列表中得业务系统与服务关系数据
     *
     * @return
     */
    int delRelationOffService();

    int getSubSysCnt(@Param("sysId") Integer sysId);

    int getServiceCnt(@Param("sysId") Integer sysId);

    int getSysCnt(BusinessSearch business);

    int getAnomalySysCnt(BusinessSearch business);

    List<KeyValue> getBusinessRelations(@Param("sysIds") List<Integer> sysIds);
    List<KeyValue> getAllBusinessRelations();

    List<Business> getBusiness();

    List<Business> getAllSysByQueryNameAndAccount(@Param("apiKey") String apiKey, @Param("listnameQuery") String listnameQuery, @Param("account") String account, @Param("sysIds") List<Integer> sysIds);

    void batchUpdateDefaultDeptIdByPid(@Param("pid") Integer pid, @Param("deptId") Integer deptId);

    List<Business> getBusinessByServiceId(@Param("service_id") String serviceId);

    List<Business> getSysBusinessByServiceId(@Param("service_id") String serviceId);

    List<JSONObject> getServicesMapBySysId(@Param("sysId") Integer sysId);

    List<Business> listAll();

    List<Integer> getBusIdsBySvcId(@Param("svcId") String serviceId);
    List<KeyValue> getBusSvcIds();

    List<Business> findBusiness(@Param("apiKey") String apiKey);

    Set<Integer> getSubSystemIdsByServiceIds(List<String> serviceIds);

    Set<Integer> getSubSystemIdsByPId(@Param("pid") Integer pid);

}
