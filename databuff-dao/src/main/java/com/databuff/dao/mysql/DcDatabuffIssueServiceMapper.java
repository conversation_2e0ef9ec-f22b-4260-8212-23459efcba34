package com.databuff.dao.mysql;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.databuff.entity.DcDatabuffIssueService;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface DcDatabuffIssueServiceMapper extends BaseMapper<DcDatabuffIssueService> {

    @Select("SELECT service FROM dc_databuff_issue_service WHERE issue_id = #{issueId}")
    List<String> selectServicesByIssueId(String issueId);


    @Insert({
            "<script>",
            "INSERT INTO dc_databuff_issue_service (issue_id, service) VALUES (#{issueId}, #{service}) ",
            "</script>"
    })
    default int upsert(DcDatabuffIssueService detail) {
        try {
            return insert(detail);
        } catch (Exception e) {
            return 0;
        }
    }
}