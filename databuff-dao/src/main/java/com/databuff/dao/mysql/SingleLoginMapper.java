package com.databuff.dao.mysql;

import com.databuff.entity.HuaweiImcAdmin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @author:TianMing
 * @date: 2022/2/18
 * @time: 17:26
 */
@Mapper
@Repository
public interface SingleLoginMapper {

    /**
     * 新增
     * <AUTHOR>
     * @date 2022/02/18
     **/
    int insertHuaweiImcAdmin(HuaweiImcAdmin huaweiImcAdmin);

    /**
     * 刪除
     * <AUTHOR>
     * @date 2022/02/18
     **/
    int deleteHuaweiImcAdmin(String tenant);

    /**
     * 更新
     * <AUTHOR>
     * @date 2022/02/18
     **/
    int updateHuaweiImcAdmin(HuaweiImcAdmin huaweiImcAdmin);

    /**
     * 查询 根据tenant 查询
     * <AUTHOR>
     * @date 2022/02/18
     **/
    HuaweiImcAdmin loadHuaweiImcAdminByTenant(@Param("tenant") String tenant, @Param("isdelete") Integer isDelete);

}
