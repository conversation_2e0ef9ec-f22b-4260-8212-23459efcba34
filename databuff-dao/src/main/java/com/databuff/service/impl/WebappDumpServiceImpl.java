package com.databuff.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.databuff.common.audit.AuditEntity;
import com.databuff.common.tsdb.model.Where;
import com.databuff.dao.mysql.AgentDumpMapper;
import com.databuff.dao.mysql.TraceServiceMapper;
import com.databuff.entity.TraceServiceEntity;
import com.databuff.entity.dump.AgentDump;
import com.databuff.entity.dump.AgentDumpSearchCriteria;
import com.databuff.metric.MetricAggregator;
import com.databuff.service.DomainManagerObjService;
import com.databuff.service.WebappDumpService;
import com.databuff.util.IDConfigManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import dto.DConf;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.audit4j.core.annotation.DatabuffAudit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.databuff.common.constants.Constant.DF_API_KEY_VALUE;
import static com.databuff.common.constants.Constant.Trace.*;

@Slf4j
@Service
public class WebappDumpServiceImpl extends ServiceImpl<AgentDumpMapper, AgentDump> implements WebappDumpService {

    public static final String SERVICE_INSTANCES = "serviceInstances";
    public static final String HEAP_DUMP = "heap_dump";

    @Autowired
    private MetricAggregator metricAggregator;

    @Autowired
    protected IDConfigManager<DConf> dConfigManager;

    @Autowired
    @Qualifier("traceServiceMapper")
    private TraceServiceMapper traceServiceMapper;

    @Autowired
    private DomainManagerObjService domainManagerObjService;


    @Override
    public List<AgentDump> searchAgentDumps(AgentDumpSearchCriteria criteria) {
        final boolean allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        final Collection<String> gids = domainManagerObjService.getGidFromThread();
        if (!allEntityPermission) {
            if (CollectionUtils.isEmpty(gids)) {
                return Lists.newArrayList();
            } else {
                criteria.setGids(gids);
            }
        }
        return baseMapper.searchAgentDumps(criteria);
    }


    @DatabuffAudit(action = "重新执行", entityType = "Dump任务")
    @Transactional
    @Override
    public Long reDump(Long id) {
        final AgentDump agentDump = baseMapper.selectById(id);
        if (agentDump == null) {
            throw new IllegalArgumentException("任务不存在");
        }
        // 初始化任务数据
        agentDump.setStatus(0);
        agentDump.setProgress("新建任务");
        agentDump.setOperation(5);
        baseMapper.updateById(agentDump);

        AuditEntity.builder().id(id.toString()).name(agentDump.getService()).add();

        final String service = agentDump.getService();
        final String serviceInstance = agentDump.getServiceInstance();
        updateConfig(service, serviceInstance, System.currentTimeMillis());
        return id;
    }

    @SneakyThrows
    @DatabuffAudit(action = "新增", entityType = "Dump任务")
    @Transactional
    @Override
    public Long addAgentDump(AgentDump agentDump) {
        final String serviceId = agentDump.getServiceId();
        if (serviceId == null) {
            throw new IllegalArgumentException("服务 ID 为空");
        }
        TraceServiceEntity serviceEntity = traceServiceMapper.serviceInfo(serviceId);
        if (serviceEntity == null) {
            throw new IllegalArgumentException("服务不存在");
        }
        final String service = serviceEntity.getService();
        final String serviceInstance = agentDump.getServiceInstance();
        if (serviceInstance == null) {
            throw new IllegalArgumentException("服务实例为空");
        }
        final String hostname = getHostName(serviceEntity.getName(), serviceInstance);
        // 初始化任务数据
        agentDump.setService(service);
        agentDump.setHost(hostname);
        agentDump.setCreateTime(new Date());
        agentDump.setStatus(0);
        agentDump.setProgress("新建任务");
        agentDump.setOperation(5);
        baseMapper.insert(agentDump);

        final Long id = agentDump.getId();
        AuditEntity.builder().id(id.toString()).name(service).add();

        updateConfig(agentDump.getService(), serviceInstance, System.currentTimeMillis());
        return agentDump.getId();
    }

    private String getHostName(String serviceEntityName, String serviceInstance) {
        final long currentTimeMillis = System.currentTimeMillis();
        final Long fromTimeMs = currentTimeMillis - 1000 * 60 * 60 * 24;
        final Long toTimeMs = currentTimeMillis;

        List<Where> wheres = new ArrayList<>();
        wheres.add(Where.and(Lists.newArrayList(Where.eq(SERVICE, serviceEntityName), Where.eq(SERVICE_INSTANCE, serviceInstance))));

        final Map<String, Set<String>> tagValuesResult = metricAggregator.showTagValuesResult(DF_API_KEY_VALUE, SERVICE_INSTANCE_METRIC, wheres, Lists.newArrayList(HOST_NAME), fromTimeMs, toTimeMs);
        if (tagValuesResult == null || tagValuesResult.isEmpty()) {
            throw new IllegalArgumentException("未找到主机信息");
        }
        final Set<String> hostnames = tagValuesResult.get(HOST_NAME);
        if (hostnames == null) {
            throw new IllegalArgumentException("主机名为空");
        }
        return hostnames.stream()
                .filter(Objects::nonNull)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("未找到主机信息"));
    }

    @SneakyThrows
    private void updateConfig(String service, String serviceInstance, long currentTimeMillis) {
        final String path = "/javaagent/" + service;
        DConf oldDConf = dConfigManager.getData(path);
        JSONObject serviceConf = (oldDConf != null) ? (JSONObject) oldDConf.getValue() : new JSONObject();
        JSONObject heapDump = serviceConf.getJSONObject(HEAP_DUMP);
        if (heapDump == null) {
            heapDump = new JSONObject().fluentPut(SERVICE_INSTANCES, Sets.newHashSet(serviceInstance));
            serviceConf.put(HEAP_DUMP, heapDump);
        } else {
            final Collection<String> serviceInstanceList = heapDump.getObject(SERVICE_INSTANCES, Collection.class);
            if (serviceInstanceList == null) {
                heapDump.put(SERVICE_INSTANCES, Sets.newHashSet(serviceInstance));
            } else {
                serviceInstanceList.add(serviceInstance);
                heapDump.put(SERVICE_INSTANCES, Sets.newHashSet(serviceInstanceList));
            }
        }
        heapDump.put("time", currentTimeMillis);

        DConf<JSONObject> newDConf = new DConf<>();
        newDConf.setKey(service);
        newDConf.setValue(serviceConf);
        newDConf.setPath(path);
        newDConf.setBuiltIn(false);
        dConfigManager.saveNode(path, newDConf, true);
    }

    @DatabuffAudit(action = "更新Dump任务", entityType = "Dump任务")
    @Transactional
    @Override
    public Long updateAgentDump(AgentDump agentDump) {
        final Long id = agentDump.getId();
        if (id == null) {
            throw new IllegalArgumentException("id is null");
        }
        final AgentDump old = baseMapper.selectById(id);
        baseMapper.updateByIdSelective(agentDump);
        AuditEntity.builder()
                .id(id.toString())
                .name(old.getService())
                .beforeValue(old.getMsg())
                .afterValue(agentDump.getMsg())
                .add();
        return id;
    }

    @DatabuffAudit(action = "删除", entityType = "Dump任务")
    @Override
    @Transactional
    public void deleteAgentDumpById(Long id) {
        final AgentDump agentDump = baseMapper.selectById(id);
        if (agentDump == null) {
            return;
        }
        baseMapper.deleteById(id);
        AuditEntity.builder()
                .id(agentDump.getId().toString())
                .name(agentDump.getService())
                .add();
    }

    @Override
    public AgentDump getAgentDumpById(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 获取下一个任务
     *
     * @param host
     * @return
     */
    @Override
    public AgentDump getNextTask(String host) {
        if (host == null || baseMapper == null) {
            return null;
        }
        try {
            final AgentDump nextTask = baseMapper.getNextTask(host);
            if (nextTask != null) {
                nextTask.sanitizeServiceNames();
            }
            return nextTask;
        } catch (Exception e) {
            // 记录日志，便于问题排查
            log.error("Failed to get next task for host: " + host, e);
            return null;
        }
    }
}