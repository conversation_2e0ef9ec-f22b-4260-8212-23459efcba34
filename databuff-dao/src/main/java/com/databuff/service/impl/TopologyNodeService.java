package com.databuff.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.databuff.dao.mysql.topo.TopologyNodeMapper;
import com.databuff.entity.dto.topo.TopologyNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class TopologyNodeService {

    @Autowired
    private TopologyNodeMapper mapper;

    public Map<String, String> allNodeMap() {
        final List<TopologyNode> allNodeMap = mapper.allNodeMap();
        if (allNodeMap == null) {
            return new HashMap<>();
        }
        Map<String, String> nodeMap = new HashMap<>();
        for (TopologyNode topologyNodeDAO : allNodeMap) {
            if (topologyNodeDAO == null) {
                continue;
            }
            nodeMap.put(topologyNodeDAO.getId(), topologyNodeDAO.getName());
        }
        return nodeMap;
    }

    public List<TopologyNode> search(Wrapper<TopologyNode> queryWrapper) {
        return mapper.selectList(queryWrapper);
    }

    public List<TopologyNode> graph(Wrapper<TopologyNode> queryWrapper) {
        final List<TopologyNode> graph = mapper.graph(queryWrapper);
        if (graph == null) {
            return new ArrayList<>();
        }
        return graph;
    }

    public Optional<TopologyNode> findById(String nodeId) {
        return Optional.ofNullable(mapper.selectById(nodeId));
    }

    public TopologyNode save(TopologyNode node) {
        mapper.insert(node);
        return node;
    }

    public TopologyNode update(TopologyNode node) {
        mapper.updateById(node);
        return node;
    }

    public void deleteById(String nodeId) {
        mapper.deleteById(nodeId);
    }
}