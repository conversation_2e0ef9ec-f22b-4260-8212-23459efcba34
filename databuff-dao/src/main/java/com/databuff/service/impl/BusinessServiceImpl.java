package com.databuff.service.impl;

import com.databuff.dao.mysql.BusinessMapper;
import com.databuff.dao.mysql.TraceServiceMapper;
import com.databuff.entity.Business;
import com.databuff.entity.TraceServiceEntity;
import com.databuff.service.BusinessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Date 2022/10/12
 * @Description:
 */
@Component("metricBusinessServiceImpl")
@Slf4j
public class BusinessServiceImpl implements BusinessService {

    @Autowired
    private BusinessMapper businessMapper;
    @Autowired
    @Qualifier("traceServiceMapper")
    private TraceServiceMapper traceServiceMapper;

    @Override
    public Map<Integer, Business> getBusNameMap() {
        List<Business> busines = businessMapper.listAll();
        return busines.stream().collect(Collectors.toMap(s -> s.getId(), s -> s));
    }
    /**
     * 根据所属业务系统获取数据权限下的服务列表
     * fromTime传null获取全部，传时间则获取指定时间后的服务
     */
    @Override
    public List<String> getDeptAuthServiceIdsByAccount(String account, String apiKey, String fromTime, String serviceName) {
        try {
            // 根据account获取用户部门信息
            List<Business> buss = businessMapper.getBusiness();
            Set<String> allServiceIdsInBus = new HashSet<>();
            for (Business bus : buss) {
                // 只获取一级业务系统，sql中会处理子系统
                if (bus.getPid() == 0) {
                    Integer busId = bus.getId();
                    // 根据部门id获取对应的服务
                    Set<String> serviceIds = businessMapper.getAllServiceIdsBySysIdWithTime(busId, fromTime);
                    allServiceIdsInBus.addAll(serviceIds);
                }
            }
            // 最外层没有分配的服务
            List<String> notInBusinessServiceIds = businessMapper.getServiceIdsNotInBusinessWithTime(apiKey, fromTime);
            allServiceIdsInBus.addAll(notInBusinessServiceIds);

            return new ArrayList<>(allServiceIdsInBus);
        } catch (Exception e) {
            log.error("getServiceIdsByUser error", e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据筛选的服务ids列表获取服务列表
     * fromTime传null获取全部，传时间则获取指定时间后的服务
     */
    @Override
    public List<TraceServiceEntity> getDeptAuthServicesByAccount(String account, String apiKey, String fromTime, String serviceName) {
        List<String> serviceIds = getDeptAuthServiceIdsByAccount(account, apiKey, fromTime, serviceName);
        if (serviceIds == null || serviceIds.size() == 0) {
            return new ArrayList<>();
        }
        return traceServiceMapper.getServicesByIds(apiKey, serviceIds);
    }


}
