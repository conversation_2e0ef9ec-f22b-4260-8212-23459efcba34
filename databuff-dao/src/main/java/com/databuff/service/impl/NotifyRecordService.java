package com.databuff.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.databuff.dao.mysql.NotifyRecordMapper;
import com.databuff.entity.NotifyRecord;
import com.databuff.entity.NotifyRecordParams;
import com.databuff.service.DomainManagerObjService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

@Service
public class NotifyRecordService {

    @Autowired
    private NotifyRecordMapper notifyRecordMapper;

    @Autowired
    private DomainManagerObjService domainManagerObjService;

    public List<NotifyRecord> pageAll(NotifyRecordParams queryParamDTO) {
        final boolean allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        final boolean domainManagerStatusOpen = domainManagerObjService.getDomainManagerStatusOpen();
        final Collection<String> gids = domainManagerObjService.getGidFromThread();
        return notifyRecordMapper.pageAll(queryParamDTO, allEntityPermission, domainManagerStatusOpen, gids);
    }

    public NotifyRecord selectOne(QueryWrapper<NotifyRecord> eq) {
        final boolean allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        final Collection<String> gids = domainManagerObjService.getGidFromThread();
        if (allEntityPermission) {
            return notifyRecordMapper.selectOne(eq);
        }
        if (gids == null || gids.size() <= 0) {
            eq.isNull("gid");
        } else {
            eq.and(wrapper -> wrapper.isNull("gid").or().in("gid", gids));
        }
        return notifyRecordMapper.selectOne(eq);
    }

    public void insert(NotifyRecord notifyRecord) {
        if (notifyRecord == null) {
            return;
        }
        if (notifyRecord.getGid() != null) {
            // 通知记录的gid不为空，直接插入
            notifyRecordMapper.insert(notifyRecord);
            return;
        }
        final boolean allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        final Collection<String> gids = domainManagerObjService.getGidFromThread();
        if (allEntityPermission) {
            // 有所有实体权限, gid为空
            notifyRecordMapper.insert(notifyRecord);
            return;
        }
        if (gids == null || gids.size() <= 0) {
            // 没有实体权限，gid为空
            notifyRecordMapper.insert(notifyRecord);
        } else {
            for (String gid : gids) {
                // 有实体权限，gid不为空
                notifyRecord.setGid(gid);
                notifyRecordMapper.insert(notifyRecord);
            }
        }
    }

    public void updateById(NotifyRecord record) {
        notifyRecordMapper.updateById(record);
    }
}