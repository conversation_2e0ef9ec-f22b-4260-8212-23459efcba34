package com.databuff.service.impl;

import com.databuff.audit.DcAudit;
import com.databuff.dao.mysql.DcAuditMapper;
import com.databuff.entity.dto.AuditSearchCriteria;
import com.databuff.service.DcAuditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class DcAuditServiceImpl implements DcAuditService {

    @Autowired
    private DcAuditMapper dcAuditMapper;

    @Override
    public List<DcAudit> search(AuditSearchCriteria criteria) {
        return dcAuditMapper.search(criteria);
    }

    @Override
    public List<DcAudit> findAll() {
        return dcAuditMapper.selectList(null);
    }

    @Override
    public Map<String, Set<String>> getAuditTags(AuditSearchCriteria criteria) {
        List<Map<String, String>> results = dcAuditMapper.getAuditTags(criteria);
        Map<String, Set<String>> tags = new HashMap<>();
        String[] keys = {"actor", "action", "entityType", "entityName", "outcome"};

        for (String key : keys) {
            tags.put(key, new HashSet<>());
        }

        for (Map<String, String> result : results) {
            for (String key : keys) {
                String value = result.get(key);
                if (value != null) {
                    tags.get(key).add(value);
                }
            }
        }

        return tags;
    }
}