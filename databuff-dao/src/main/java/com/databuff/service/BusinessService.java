package com.databuff.service;

import com.alibaba.fastjson.JSONObject;
import com.databuff.entity.Business;
import com.databuff.entity.TraceServiceEntity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/10/12
 * @Description:
 */
public interface BusinessService {


    Map<Integer, Business> getBusNameMap();

    /**
     * 根据所属业务系统获取数据权限下的服务列表
     * fromTime传null获取全部，传时间则获取指定时间后的服务
     */
    List<String> getDeptAuthServiceIdsByAccount(String account, String apiKey, String fromTime, String serviceName);

    /**
     * 根据筛选的服务ids列表获取服务列表
     * fromTime传null获取全部，传时间则获取指定时间后的服务
     */
    List<TraceServiceEntity> getDeptAuthServicesByAccount(String account, String apiKey, String fromTime, String serviceName);

}
