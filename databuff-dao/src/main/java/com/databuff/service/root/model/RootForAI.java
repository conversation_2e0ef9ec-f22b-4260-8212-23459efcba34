package com.databuff.service.root.model;

import java.util.List;
import java.util.Map;

public class RootForAI {

    private String caseType;
    private String caseTypeDesc;
    private List<Map<String, String>> roots;

    public String getCaseType() {
        return caseType;
    }

    public void setCaseType(String caseType) {
        this.caseType = caseType;
    }

    public String getCaseTypeDesc() {
        return caseTypeDesc;
    }

    public void setCaseTypeDesc(String caseTypeDesc) {
        this.caseTypeDesc = caseTypeDesc;
    }

    public List<Map<String, String>> getRoots() {
        return roots;
    }

    public void setRoots(List<Map<String, String>> roots) {
        this.roots = roots;
    }
}
