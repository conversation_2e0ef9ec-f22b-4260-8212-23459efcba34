package com.databuff.service.root.service;

import com.alibaba.fastjson.JSON;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.service.root.model.OpenAIMessage;
import com.databuff.service.root.model.OpenAIResponse;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.databuff.common.constants.MetricName.QUERY_OPEN_AI_COST;

@Service
public class OpenAIService {

    private CloseableHttpClient httpClient = HttpClients.createDefault();

    public OpenAIResponse sendMessage(String openAIUrl, String openAIApiKey, String openAIModel, List<OpenAIMessage> openAIMessages) {
        long startTime = System.currentTimeMillis();
        // 构建 HTTP POST 请求
        HttpPost httpPost = new HttpPost(openAIUrl);

        // 设置请求头
        httpPost.setHeader("Content-Type", "application/json");
        httpPost.setHeader("Authorization", "Bearer " + openAIApiKey); // 替换你的API密钥

        // 构造请求体 JSON（手动拼接字符串）
        Map<String, Object> body = new HashMap<>();
        body.put("model", openAIModel);
        body.put("messages", openAIMessages);
        body.put("stream", false);

        // 设置请求体
        httpPost.setEntity(new StringEntity(JSON.toJSONString(body), "UTF-8"));
        CloseableHttpResponse response = null;
        try {
            // 执行请求并获取响应
            response = httpClient.execute(httpPost);

            // 获取状态码
            int statusCode = response.getStatusLine().getStatusCode();

            // 读取响应体
            HttpEntity entity = response.getEntity();
            String responseBody = EntityUtils.toString(entity, "UTF-8");
            if (statusCode == 200) {
                try {
                    Map<String, Object> retMap = JSON.parseObject(responseBody);
                    List<Map<String, Object>> choices = (List<Map<String, Object>>) retMap.get("choices");
                    Map<String, Object> firstChoices = choices.get(0);
                    Map<String, Object> message = (Map<String, Object>) firstChoices.get("message");
                    OpenAIResponse openAIResponse = new OpenAIResponse();
                    openAIResponse.setContent((String) message.get("content"));
                    openAIResponse.setReasoningContent((String) message.get("reasoning_content"));
                    return openAIResponse;
                } catch (Exception e) {
                    throw new RuntimeException("响应格式不对: " + responseBody, e);
                }
            } else {
                throw new RuntimeException("请求失败: 状态码 " + statusCode + " " + responseBody);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
            long diff = System.currentTimeMillis() - startTime;
            OtelMetricUtil.logHistogram(QUERY_OPEN_AI_COST, diff);
        }
    }

}
