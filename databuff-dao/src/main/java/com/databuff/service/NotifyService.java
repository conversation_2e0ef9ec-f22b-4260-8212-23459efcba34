package com.databuff.service;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.utils.dingtalk.NotifyDingTalkConfig;
import com.databuff.common.utils.email.NotifyEmailConfig;
import com.databuff.common.utils.sms.NotifySmsConfig;
import com.databuff.common.utils.socket.NotifySockerConfig;
import com.databuff.common.utils.wechat.NotifyWeChatConfig;
import com.databuff.common.utils.webhook.NotifyWebhookConfig;
import com.databuff.entity.NotifyRecord;
import com.databuff.entity.RcvUser;

import java.util.List;
import java.util.Map;

/**
 * @author:TianMing
 * @date: 2021/12/23
 * @time: 13:37
 */
public interface NotifyService {
    /**
     *设置邮件配置
     * @param notifyConfig
     * @return
     */
    int setEmailConfig(NotifyEmailConfig notifyConfig);
    /**
     *获取邮件配置
     * @param apiKey
     * @param notifyType
     * @return
     */
    NotifyEmailConfig getEmailConfig(String apiKey,String notifyType);
    /**
     *设置短信配置
     * @param notifyConfig
     * @return
     */
    int setSmsConfig(NotifySmsConfig notifyConfig);
    /**
     *获取短信配置
     * @param apiKey
     * @param notifyType
     * @return
     */
    NotifySmsConfig getSmsConfig(String apiKey, String notifyType);
    /**
     *设置钉钉配置
     * @param notifyConfig
     * @return
     */
    int setDingTalkConfig(NotifyDingTalkConfig notifyConfig);
    /**
     *获取钉钉配置
     * @param apiKey
     * @param notifyType
     * @return
     */
    NotifyDingTalkConfig getDingTalkConfig(String apiKey,String notifyType);

    /**
     *设置微信配置
     * @param notifyConfig
     * @return
     */
    int setWeChatConfig(NotifyWeChatConfig notifyConfig);
    /**
     *获取微信配置
     * @param apiKey
     * @param notifyType
     * @return
     */
    NotifyWeChatConfig getWeChatConfig(String apiKey,String notifyType);


    int setWebhookConfig(NotifyWebhookConfig notifyConfig);


    NotifyWebhookConfig getWebhookConfig(String apiKey, String notifyType);


    int setSocketConfig(NotifySockerConfig notifyConfig);

    NotifySockerConfig getSocketConfig(String apiKey, String notifyType);

    /**
     * 测试邮件发送
     * @param apiKey
     * @param info
     * @return
     */
    String testEmail(String apiKey, JSONObject info);
    /**
     * 测试短信发送
     * @param apiKey
     * @param info
     * @return
     */
    String testSms(String apiKey, JSONObject info);

    /**
     * 测试钉钉机器人发送
     * @param apiKey
     * @return
     */
    String testDingTalk(String apiKey,JSONObject params);

    /**
     * 通过手机号测试钉钉机器人发送
     * @param apiKey
     * @return
     */
    String testDingTalkByPhone(String apiKey, String phone);
    /**
     * 测试微信机器人发送
     * @param apiKey
     * @return
     */
    String testWeChat(String apiKey,JSONObject params);
    /**
     * 测试微信机器人发送
     * @param apiKey
     * @return
     */
    String testWeChatByPhone(String apiKey, String phone);

    /**
     * webhook机器人发送
     * @param apiKey
     * @return
     */
    String testWebhook(String apiKey);

    /**
     * 测试Socket发送
     */
    String testSocket(NotifySockerConfig notifyConfig,StringBuffer rets);



    /**
     * 测试自定义webhook机器人发送
     */
    String testCustomWebhook(NotifyWebhookConfig notifyConfig);

    /**
     * @param apiKey
     * @param content   对象包含eventName message
     * @param phoneList 手机列表（可为空）
     * @return
     * @throws Exception
     */
    String dingTalkAlarm(String apiKey, String content, List<String> phoneList) throws Exception;
    /**
     * @param notifyDingTalkConfig
     * @param apiKey
     * @param content   对象包含eventName message
     * @param phoneList 手机列表（可为空）
     * @return
     * @throws Exception
     */
    String dingTalkAlarm(NotifyDingTalkConfig notifyDingTalkConfig , String apiKey, String content, List<String> phoneList) throws Exception;

    String dingTalkAlarm(String dingSecret,String dingWebhook , String apiKey, String content, List<String> phoneList) throws Exception;

    /**
     *
     * @param notifyConfig
     * @param apiKey
     * @param text 对象包含eventName message
     * @param textType 发送类型
     * @param useridList 用户id列表
     * @return
     */
    String dingTalkAlarmToOne(NotifyDingTalkConfig notifyConfig, String apiKey, String text, String textType, List<String> useridList) throws Exception;

    /**
     *
     * @param apiKey
     * @param content 对象包含eventName message
     * @param phoneList 手机列表（可为空）
     * @param useridList 用户id列表（可为空）
     * @return
     */
    String weChatAlarm(String apiKey, String content, List<String> phoneList, List<String> useridList) throws Exception;

    /**
     * @param notifyConfig
     * @param apiKey
     * @param content 对象包含eventName message
     * @param phoneList 手机列表（可为空）
     * @param useridList 用户id列表（可为空）
     * @return
     */
    String weChatAlarm(NotifyWeChatConfig notifyConfig,String apiKey, String content, List<String> phoneList, List<String> useridList) throws Exception;

    String weChatAlarm(String webhookUrl,String apiKey, String content, List<String> phoneList, List<String> useridList) throws Exception;

    /**
     *
     * @param notifyConfig
     * @param apiKey
     * @param text 对象包含eventName message
     * @param textType 发送类型
     * @param useridList 用户id列表
     * @return
     */
    String weChatAlarmToOne(NotifyWeChatConfig notifyConfig,String apiKey, String text, String textType,List<String> useridList) throws Exception;
    /**
     *
     * @param apiKey
     * @param emailTitle 邮件标题
     * @param msgs 对象包含eventName message
     * @param emailList 邮箱逗号分隔
     * @return
     */
    String emailAlarm(String apiKey, String emailTitle, List<JSONObject> msgs, List<String> emailList) throws Exception;
    /**
     *
     * @param notifyConfig
     * @param apiKey
     * @param emailTitle 邮件标题
     * @param msgs 对象包含eventName message
     * @param emailList 邮箱逗号分隔
     * @return
     */
    String emailAlarm(NotifyEmailConfig notifyConfig,String apiKey, String emailTitle, List<JSONObject> msgs, List<String> emailList) throws Exception;

    /**
     * 告警短信发送
     * @param apiKey
     * @param contents
     * @param phoneList
     * @param smsNotifyType
     * @return
     * @throws Exception
     */
    String smsAlarm(String apiKey, JSONObject contents, List<String> phoneList, String smsNotifyType);
    /**
     * 告警短信发送
     * @param notifyConfig
     * @param apiKey
     * @param contents
     * @param phoneList
     * @param smsNotifyType
     * @return
     * @throws Exception
     */
    String smsAlarm(NotifySmsConfig notifyConfig,String apiKey, JSONObject contents, List<String> phoneList, String smsNotifyType) throws Exception;

    JSONObject getUpgradeConfig();


    String webhookAlarm(String apiKey, String contentMsg, Map<String, String> params) throws Exception;

    String webhookAlarm(NotifyWebhookConfig notifyConfig,String apiKey, String contentMsg, Map<String, String> params) throws Exception;
    String webhookAlarm(String webhookUrl,String webhookMethod,String headers,String apiKey, String contentMsg, Map<String, String> params) throws Exception;

    String socketAlarm(NotifySockerConfig notifyConfig,String apiKey, String contents) throws Exception;
    String socketAlarm(String apiKey, String contents) throws Exception;

}
