package com.databuff.service;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.utils.HttpUtil;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.common.utils.TimeUtil;
import com.databuff.entity.AlarmSearchParams;
import com.databuff.entity.dto.AlarmTriggerIdStatusCount;
import com.databuff.moredb.model.util.Pair;
import com.databuff.processor.apm.ProcessorUtil;
import com.databuff.util.RootUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import static com.databuff.common.constants.Constant.Trace.SERVICE_ID;

@Service
public class RootEngineService {

    private static final String ENCODE = StandardCharsets.UTF_8.toString();
    private static final String PATH_ANALYSE = "/analyse?";
    private static final String PATH_INFLUENCE = "/influence";
    private static final String PATH_AI_ANALYSE = "/aiAnalyse?";
    private static final String PATH_AI_FETCH_RESULT = "/aiFetchResult?";
    private static final String PARAMS_SERVICE_TYPE = "serviceType";
    private static final String PARAMS_SERVICE = "service";
    private static final String PARAMS_FROM_TIME = "fromTime";
    private static final String PARAMS_TO_TIME = "toTime";
    private static final String PARAMS_DISABLE_EXPAND = "disableExpand";
    private static final String PARAMS_RETRY = "retry";
    private static final String PARAMS_ABNORMAL_DETAIL = "abnormalDetail";

    @Value("${root-engine.url:http://root-engine:18666/root}")
    private String rootEngineUrl;

    @Resource
    private ServiceSyncService serviceSyncService;
    @Autowired
    private DcAlarmService dcAlarmService;

    public void aiAnalyse(String service, long fromTime, long toTime, Boolean disableExpand, Boolean retry) {
        if (disableExpand == null) {
            disableExpand = Boolean.FALSE;
        }
        if (retry == null) {
            retry = Boolean.FALSE;
        }
        try {
            Map<String, String> params = new HashMap<>();
            params.put(PARAMS_SERVICE, service);
            params.put(PARAMS_FROM_TIME, String.valueOf(fromTime));
            params.put(PARAMS_TO_TIME, String.valueOf(toTime));
            params.put(PARAMS_DISABLE_EXPAND, String.valueOf(disableExpand));
            params.put(PARAMS_RETRY, String.valueOf(retry));

            String paramsPath = initParams(params);
            String url = rootEngineUrl + PATH_AI_ANALYSE + paramsPath;
            HttpUtil.getJson(url, null, TimeUtil.ONE_MINUTE_MS);
        } catch (Exception e) {
            OtelMetricUtil.logException("根因分析异常：", e);
            throw new RuntimeException(e);
        }
    }

    public JSONObject aiFetchResult(String service, long fromTime, long toTime, Boolean disableExpand) {
        if (disableExpand == null) {
            disableExpand = Boolean.FALSE;
        }
        try {
            Map<String, String> params = new HashMap<>();
            params.put(PARAMS_SERVICE, service);
            params.put(PARAMS_FROM_TIME, String.valueOf(fromTime));
            params.put(PARAMS_TO_TIME, String.valueOf(toTime));
            params.put(PARAMS_DISABLE_EXPAND, String.valueOf(disableExpand));

            String paramsPath = initParams(params);
            String url = rootEngineUrl + PATH_AI_FETCH_RESULT + paramsPath;
            String response = HttpUtil.getJson(url, null, TimeUtil.ONE_MINUTE_MS);
            return JSONObject.parseObject(response);
        } catch (Exception e) {
            OtelMetricUtil.logException("根因分析异常：", e);
            throw new RuntimeException(e);
        }
    }

    protected List<JSONObject> analyses(String apiKey, String service, long fromTime, long toTime, Boolean disableExpand) {
        if (disableExpand == null) {
            disableExpand = Boolean.FALSE;
        }
        try {
            Map<String, String> params = new HashMap<>();
            params.put(PARAMS_SERVICE, service);
            params.put(PARAMS_FROM_TIME, String.valueOf(fromTime));
            params.put(PARAMS_TO_TIME, String.valueOf(toTime));
            params.put(PARAMS_DISABLE_EXPAND, String.valueOf(disableExpand));

            String paramsPath = initParams(params);
            String url = rootEngineUrl + PATH_ANALYSE + paramsPath;
            String retString = HttpUtil.getJson(url, null, TimeUtil.ONE_MINUTE_MS);
            return convert(apiKey, retString);
        } catch (Exception e) {
            OtelMetricUtil.logException("根因分析异常：", e);
            throw new RuntimeException(e);
        }
    }

    protected List<JSONObject> influence(String apiKey, String serviceType, String service, long fromTime, long toTime,
                                         Map<String, Object> abnormalDetail) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put(PARAMS_SERVICE_TYPE, serviceType);
            params.put(PARAMS_SERVICE, service);
            params.put(PARAMS_FROM_TIME, String.valueOf(fromTime));
            params.put(PARAMS_TO_TIME, String.valueOf(toTime));
            params.put(PARAMS_ABNORMAL_DETAIL, abnormalDetail);

            String url = rootEngineUrl + PATH_INFLUENCE;
            String response = HttpUtil.postJson(url, null, params, TimeUtil.ONE_MINUTE_MS);
            return convert(apiKey, response);
        } catch (Exception e) {
            OtelMetricUtil.logException("根因分析异常：", e);
            throw new RuntimeException(e);
        }
    }

    private List<JSONObject> convert(String apiKey, String response) {
        List<JSONObject> jsonObjects = JSONObject.parseArray(response, JSONObject.class);

        Map<Pair<Long, Long>, Set<String>> timeRangeServiceIdSet = RootUtil.collectTimeRangeServiceIdSet(serviceSyncService, jsonObjects);
        Map<Pair<Long, Long>, Map<String, Integer>> timeRangeServiceIdAlarms = queryTimeRangeServiceIdAlarms(timeRangeServiceIdSet, dcAlarmService, apiKey);

        ProcessorUtil.processList(apiKey, serviceSyncService, jsonObjects, timeRangeServiceIdAlarms);
        return jsonObjects;
    }

    private Map<Pair<Long, Long>, Map<String, Integer>> queryTimeRangeServiceIdAlarms(Map<Pair<Long, Long>, Set<String>> timeRangeServiceIdSet, DcAlarmService dcAlarmService, String apiKey) {
        Map<Pair<Long, Long>, Map<String, Integer>> map = new ConcurrentHashMap<>();
        if (!timeRangeServiceIdSet.isEmpty()) {
            timeRangeServiceIdSet.entrySet().parallelStream().forEach(pairSetEntry -> {
                final Pair<Long, Long> pairSetEntryKey = pairSetEntry.getKey();
                AlarmSearchParams alarmSearchParams = AlarmSearchParams.builder()
                        .apiKey(apiKey)
                        .start(pairSetEntryKey.getKey())
                        .end(pairSetEntryKey.getValue())
                        .trigger(new JSONObject().fluentPut(SERVICE_ID, pairSetEntry.getValue()))
                        .build();
                List<AlarmTriggerIdStatusCount> ret = dcAlarmService.countByServiceId(alarmSearchParams);
                if (ret == null) {
                    return;
                }
                Map<String, Integer> serviceIdAlarms = map.computeIfAbsent(pairSetEntryKey, k -> new ConcurrentHashMap<>());
                for (AlarmTriggerIdStatusCount serviceIdCount : ret) {
                    if (serviceIdCount == null) {
                        continue;
                    }
                    final String serviceId = serviceIdCount.getTriggerId();
                    Integer value = serviceIdCount.getCnt();
                    if (value == null || serviceId == null) {
                        continue;
                    }
                    serviceIdAlarms.put(serviceId, value);
                }
            });
        }
        return map;
    }

    private String initParams(Map<String, String> params) {
        StringBuilder sb = new StringBuilder();
        boolean first = true;
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (entry == null || entry.getKey() == null || entry.getValue() == null) {
                continue;
            }
            try {
                String encodeValue = URLEncoder.encode(entry.getValue(), ENCODE);
                if (!first) {
                    sb.append("&");
                } else {
                    first = false;
                }
                sb.append(entry.getKey()).append("=").append(encodeValue);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return sb.toString();
    }
}
