package com.databuff.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.databuff.common.exception.ExceptionHandlingRunnable;
import com.databuff.dao.mysql.BizObservabilityMapper;
import com.databuff.entity.BizEvent;
import com.databuff.entity.BizEventReqs;
import com.databuff.entity.BizSearch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static com.databuff.common.constants.Constant.Trace.BIZ_EVENT_ID;
import static com.databuff.common.constants.Constant.Trace.BIZ_EVENT_NAME;

/**
 * @author:TianMing
 * @date: 2024/8/30
 * @time: 11:11
 */
@Service
@Slf4j
public class BizInfoService {
    @Autowired
    private BizObservabilityMapper bizMapper;
    private ScheduledExecutorService scheduled = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r);
        t.setName("biz-info-thread");
        return t;
    });

    @Value("${scheduler.enabled.bizInfo:true}")
    private Boolean schedulerOpen;
    private Map<String, Set<JSONObject>> bizSvcInfos = new ConcurrentHashMap<>();
    private Map<String, Set<JSONObject>> bizResourceInfos = new ConcurrentHashMap<>();
    private Map<Integer, BizEvent> bizEventIdMap = new ConcurrentHashMap<>();
    private Map<String, BizEvent> bizEventNameMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        if (schedulerOpen) {
            syncBizInfo();
            scheduled.scheduleAtFixedRate(new ExceptionHandlingRunnable(() -> syncBizInfo()), 1, 1, TimeUnit.MINUTES);
        }else{
            log.info("bizInfo scheduler is not enabled");
        }
   }

    private void syncBizInfo() {
        BizSearch bizSearch = new BizSearch();
        bizSearch.setEnabled(true);

        Map<String, Set<JSONObject>> svcTmp = new ConcurrentHashMap<>();
        Map<String, Set<JSONObject>> svcResourceTmp = new ConcurrentHashMap<>();
        Map<Integer, BizEvent> bizEventIdMapTmp = new ConcurrentHashMap<>();
        Map<String, BizEvent> bizEventNameMapTmp = new ConcurrentHashMap<>();
        final List<BizEvent> bizEventList = bizMapper.getBizEventList(bizSearch);
        for (BizEvent bizEvent : bizEventList) {
            final Integer bizType = bizEvent.getBizType();
            final Integer bizSubType = bizEvent.getBizSubType();
            JSONObject bizEventJson = new JSONObject();
            bizEventJson.put(BIZ_EVENT_ID,bizEvent.getId());
            bizEventJson.put(BIZ_EVENT_NAME,bizEvent.getBizName());
            bizEventIdMapTmp.put(bizEvent.getId(),bizEvent);
            bizEventNameMapTmp.put(bizEvent.getBizName(),bizEvent);
            final String bizReqs = bizEvent.getBizReqs();
            if (StringUtils.isBlank(bizReqs)){
                continue;
            }
            List<BizEventReqs> bizEventReqs = JSON.parseArray(bizReqs, BizEventReqs.class);
            if (bizType==2){
                //后端业务
                for (BizEventReqs bizEventReq : bizEventReqs) {
                    final String svcId = bizEventReq.getSvcId();
                    if (bizSubType==1){
                        //服务级
                        if (svcTmp.containsKey(svcId)){
                            svcTmp.get(svcId).add(bizEventJson);
                        }else{
                            Set<JSONObject> set = ConcurrentHashMap.newKeySet();
                            set.add(bizEventJson);
                            svcTmp.put(svcId,set);
                        }
                    }else{
                        //接口级
                        final List<String> resources = bizEventReq.getResources();
                        for(String resource:resources){
                            String key = svcId+"_"+resource;
                            if (svcResourceTmp.containsKey(key)){
                                svcResourceTmp.get(key).add(bizEventJson);
                            }else{
                                Set<JSONObject> set = ConcurrentHashMap.newKeySet();
                                set.add(bizEventJson);
                                svcResourceTmp.put(key,set);
                            }
                        }
                    }
                }
            }
            //前端业务
        }
        this.bizSvcInfos = svcTmp;
        this.bizResourceInfos = svcResourceTmp;
        this.bizEventIdMap = bizEventIdMapTmp;
        this.bizEventNameMap = bizEventNameMapTmp;
    }

    public Set<JSONObject> getBizSvcInfos(String svcId){
        return bizSvcInfos.get(svcId);
    }
    public Set<JSONObject> getBizResourceInfos(String svcId,String resource){
        return bizResourceInfos.get(svcId+"_"+resource);
    }
    public BizEvent getBizEventById(Integer bizEventId){
        return bizEventIdMap.get(bizEventId);
    }
    public BizEvent getBizEventByName(String bizEventName){
        return bizEventNameMap.get(bizEventName);
    }
}
