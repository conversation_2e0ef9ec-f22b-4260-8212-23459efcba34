package com.databuff.sink.ringbuffer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class StarRocksResponse {
    @J<PERSON>NField(name = "TxnId")
    private long txnId;

    @J<PERSON><PERSON>ield(name = "Label")
    private String label;

    @J<PERSON><PERSON>ield(name = "Status")
    private String status;

    @JSO<PERSON>ield(name = "Message")
    private String message;

    @JSONField(name = "NumberTotalRows")
    private int numberTotalRows;

    @JSONField(name = "NumberLoadedRows")
    private int numberLoadedRows;

    @JSONField(name = "NumberFilteredRows")
    private int numberFilteredRows;

    @JSONField(name = "NumberUnselectedRows")
    private int numberUnselectedRows;

    @JSONField(name = "LoadBytes")
    private long loadBytes;

    @JSONField(name = "LoadTimeMs")
    private long loadTimeMs;

    @JSONField(name = "ErrorURL")
    private String errorURL;



    public String getDatabaseTable() {
        String[] labelParts = label.split("-");
        return labelParts[1] + "-" + labelParts[2];
    }

    public static StarRocksResponse fromJson(String json) {
        return JSON.parseObject(json, StarRocksResponse.class);
    }
}
