package com.databuff.tsdb.metric;

import com.alibaba.fastjson.JSONObject;
import com.databuff.entity.MetricsQuery;
import com.sun.istack.NotNull;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 指标元数据操作接口
 *
 * @version 1.0
 * @since 2.7.0
 */
public interface MetricInstanceOperator {

    /**
     * 查看所有数据库名称
     *
     * @return 数据库名称列表
     * @throws Exception 如果发生任何错误
     */
    Set<String> showDatabases() throws Exception;

    /**
     * 查询指定数据库、指标和标签范围内的标签值
     *
     * @param database       要查询的数据库名称集合
     * @param measurements   一个字符串，表示要查询的数据表的名称
     * @param keys           一个字符串集合，表示要查询的标签键的名称。
     * @param whereCondition 一个JSONObject集合，表示要查询的条件，每个JSONObject包含一个字段名、一个操作符和一个值
     * @param start          查询起始时间戳
     * @param end            查询结束时间戳
     * @return 一个映射，键为标签名，值为该标签名对应的所有可能的标签值集合
     * @throws Exception 如果发生任何错误
     */
    Map<String, Set<String>> listTagValues(String database, String measurements, Set<String> keys, Set<JSONObject> whereCondition, Timestamp start, Timestamp end) throws Exception;

    //单指标多指标监控查询tag
    Map<String, Set<String>> listMetricMonitorTagValues(String database, String measurements, Collection<String> keys, Collection<JSONObject> whereCondition, Timestamp start, Timestamp end, Collection<String> gids) throws Exception;
    Map<String, Set<String>> lastLastMetricMonitorTagValues(String database, MetricsQuery metricsQuery, Collection<String> keys, List whereCondition, Timestamp start, Timestamp end) throws Exception;

    Map<String, Set<String>> listTagValues(String database, String measurements, Set<String> keys, Set<JSONObject> whereCondition, Timestamp start, Timestamp end, Integer offset, Integer size) throws Exception;


    /**
     * 查询指定数据库、指标和标签范围内的标签值
     *
     * @param database    要查询的数据库名称集合
     * @param measurement 度量值名称
     * @param keys        要查询的标签条件集合，每个JSONObject表示一个标签名和值的对应关系
     * @param start       查询起始时间戳
     * @param end         查询结束时间戳
     * @param gids
     * @return 一个映射，键为标签名，值为该标签名对应的所有可能的标签值集合
     * @throws Exception 如果发生任何错误
     * @deprecated {@link #listTagKeyValues(String, String, Set, Set, Timestamp, Timestamp, Collection)}
     */
    Map<String, Set<String>> listTagKeyValues(String database, String measurement, Set<String> keys, Set<JSONObject> whereCondition, Timestamp start, Timestamp end, Collection<String> gids) throws Exception;

    /**
     * 查询指定数据库、指标和标签范围内的标签值
     *
     * @param database 要查询的数据库名称集合
     * @param metric   要查询的指标名称集合
     * @param tags     要查询的标签条件集合，每个JSONObject表示一个标签名和值的对应关系
     * @param apiKey   访问密钥
     * @param start    查询起始时间戳
     * @param end      查询结束时间戳
     * @param tagOr    标签条件之间是否使用或逻辑，默认为否
     * @param gids
     * @return 一个映射，键为标签名，值为该标签名对应的所有可能的标签值集合
     * @throws Exception 如果发生任何错误
     * @deprecated {@link #listTagKeyValues(String, String, Set, Set, Timestamp, Timestamp, Collection)}
     */
    @Deprecated
    Map<String, Set<String>> listTagKeyValues(Set<String> database, Set<String> metric, Set<JSONObject> tags, String apiKey, Timestamp start, Timestamp end, boolean tagOr, Collection<String> gids) throws Exception;

    /**
     * 查询指定数据库、指标和标签范围内的标签名
     *
     * @param database 要查询的数据库名称集合
     * @param metric   要查询的指标名称集合
     * @param tags     要查询的标签条件集合，每个JSONObject表示一个标签名和值的对应关系
     * @param apiKey   访问密钥
     * @param start    查询起始时间戳
     * @param end      查询结束时间戳
     * @param tagOr    标签条件之间是否使用或逻辑，默认为否
     * @return 一个列表，每个元素是一个JSONObject，表示一个标签名和其对应的数据类型（String或Number）
     * @throws Exception 如果发生任何错误
     */
    List<JSONObject> listTagNames(Set<String> database, Set<String> metric, Set<JSONObject> tags, String apiKey, Timestamp start, Timestamp end, boolean tagOr) throws Exception;

    /**
     * 统计指定数据库、指标和标签范围内的指标总数
     *
     * @param database     要统计的数据库名称集合
     * @param measurements 要统计的测量值名称，如果为null，则表示统计所有测量值
     * @param keys         要统计的键名称集合，如果为空，则表示统计所有键
     * @param metric       要统计的指标名称集合
     * @param tags         要统计的标签条件集合，每个JSONObject表示一个标签名和值的对应关系
     * @param apiKey       访问密钥
     * @param gids
     * @return 指标总数，如果没有匹配的数据，则返回0.0
     * @throws Exception 如果发生任何错误
     */
    Double totalMetric(Set<String> database, String measurements, Set<String> keys, Set<String> metric, Set<JSONObject> tags, String apiKey, Collection<String> gids) throws Exception;

    /**
     * 分页查询指定指标和标签范围内的指标名列表（不区分数据库）
     *
     * @param metric 要查询的指标名称集合，如果为空，则表示查询所有指标名
     * @param tags   要查询的标签条件集合，每个JSONObject表示一个标签名和值的对应关系
     * @param apiKey 访问密钥
     * @param offset 查询起始偏移量，从0开始
     * @param size   查询每页大小，如果为null，则表示不分页
     * @return 一个列表，每个元素是一个JSONObject，表示一个指标名和其对应的数据库名列表
     * @throws Exception 如果发生任何错误
     */
    List<JSONObject> listMetricNames(Set<String> metric, Set<JSONObject> tags, String apiKey, Integer offset, Integer size) throws Exception;

    /**
     * 分页查询指定数据库、测量值、指标和标签范围内的指标名列表
     *
     * @param database     要查询的数据库名称集合，如果为空，则表示查询所有数据库
     * @param measurements 要查询的测量值名称，如果为null，则表示查询所有测量值
     * @param metric       要查询的指标名称集合，如果为空，则表示查询所有指标名
     * @param tags         要查询的标签条件集合，每个JSONObject表示一个标签名和值的对应关系
     * @param start        查询起始时间戳，如果为null，则表示不限制起始时间
     * @param end          查询结束时间戳，如果为null，则表示不限制结束时间
     * @param offset       查询起始偏移量，从0开始
     * @param size         查询每页大小，如果为null，则表示不分页
     * @return 一个列表，每个元素是一个JSONObject，表示一个指标名和其对应的数据库名列表
     * @throws Exception 如果发生任何错误
     */
    List<JSONObject> listMetricNames(Set<String> database, String measurements, Set<String> metric, Set<JSONObject> tags, Timestamp start, Timestamp end, Integer offset, Integer size) throws Exception;

    /**
     * 分页查询指定数据库、测量值、指标和标签范围内的指标名列表（单个数据库）
     *
     * @param db           要查询的数据库名称，不能为空
     * @param measurements 要查询的测量值名称，如果为null，则表示查询所有测量值
     * @param metric       要查询的指标名称集合，如果为空，则表示查询所有指标名
     * @param tags         要查询的标签条件集合，每个JSONObject表示一个标签名和值的对应关系
     * @param start        查询起始时间戳，如果为null，则表示不限制起始时间
     * @param end          查询结束时间戳，如果为null，则表示不限制结束时间
     * @param offset       查询起始偏移量，从0开始
     * @param size         查询每页大小，如果为null，则表示不分页
     * @return 一个列表，每个元素是一个JSONObject，表示一个指标名和其对应的数据库名列表（只有一个元素）
     * @throws Exception 如果发生任何错误
     */
    List<JSONObject> listMetricNames(String db, String measurements, Set<String> metric, Set<JSONObject> tags, Timestamp start, Timestamp end, Integer offset, Integer size) throws Exception;

    /**
     * 查找特定指标的详细信息。
     *
     * @param queryJson     一个包含度量查询参数的JSONObject。这个JSONObject的结构将取决于度量的特定需求。
     * @param needHostCount 布尔值，表示结果中是否需要主机计数。如果为true，结果将包含主机计数。
     * @param needTags      布尔值，表示结果中是否需要这些标签。如果为true，结果将包含标签。
     * @返回一个包含指标细节的对象。这个对象的结构将取决于指标的具体要求。 如果在方法执行过程中发生任何错误，@throws异常。
     **/
    Object findMetricDetail(@NotNull JSONObject queryJson, boolean needHostCount, boolean needTags) throws Exception;

    /**
     * 查找特定查询的标签值。
     *
     * @param queryJson 一个包含标签查询参数的JSONObject。这个JSONObject的结构将取决于标签的特定要求。
     * @param dbs       将搜索标签的一组数据库名称。
     * @param all       布尔值，表示是否应该返回所有标签值。如果为true，则返回与查询匹配的所有标签值。
     * @返回一个包含标签值的对象。这个对象的结构将取决于标签的特定要求。 如果在方法执行过程中发生任何错误，@throws异常。
     **/
    Object findTagValues(JSONObject queryJson, Set<String> dbs, boolean all) throws Exception;
}
