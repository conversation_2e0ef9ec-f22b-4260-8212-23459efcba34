package com.databuff.tsdb.metric;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.tsdb.builder.QueryBuilder;
import com.databuff.common.tsdb.builder.QueryBuilderX;
import com.databuff.common.tsdb.model.TSDBResult;
import com.databuff.common.tsdb.model.TSDBResultSet;
import com.databuff.common.tsdb.model.TSDBSeries;
import com.databuff.metric.MetricSQLBuilder;
import com.databuff.tsdb.metric.dto.MetricByGroupGraph;
import lombok.SneakyThrows;

import java.util.List;
import java.util.Map;

/**
 * 数据指标操作接口，提供时间序列数据库查询和统计功能。
 */
public interface TSDBMetricOperator extends MetricSQLBuilder {

    @SneakyThrows
    QueryBuilderX aggBuilder(JSONObject queryJson);

    /**
     * 执行查询并返回结果的通用映射结构。
     * @param builder 查询构建器，定义查询条件和参数
     * @return 嵌套Map结构，外层键为指标标签的Map，值为对应的原始数据对象
     */
    Map<Map<String, String>, TSDBSeries> queryResult(QueryBuilderX builder);

    /**
     * 执行按组分组的指标查询，返回结构化图表数据。
     * @param builder 查询构建器，需包含分组条件
     * @return 嵌套Map结构，外层键为分组标签的Map，值为对应的MetricByGroupGraph图表数据对象
     */
    Map<Map<String, String>, MetricByGroupGraph> query(QueryBuilderX builder);

    /**
     * 执行查询并返回完整结果集对象，包含元数据和原始数据。
     * @param builder 查询构建器，定义具体查询逻辑
     * @return TSDBResultSet结果集对象
     * @throws Exception 数据库连接异常或查询执行异常
     */
    TSDBResultSet executeQuery(QueryBuilder builder) throws Exception;

    /**
     * 执行统计查询，返回指标统计结果列表。
     * @param builder 包含统计条件的查询构建器
     * @return 包含统计结果的TSDBResult列表
     */
    List<TSDBResult> count(QueryBuilder builder);

}
