//package com.databuff.dss;
//
//import static com.databuff.common.constants.Constant.Kafka.TRACE_TOPIC;
//import static com.databuff.common.constants.OlapDB.OLAP_DATABASE;
//import static com.databuff.dss.constant.Constant.FORWARD_SPAN;
//import static com.databuff.dss.constant.Constant.OLAP_FORWARD_DATABASE;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.databuff.common.utils.TimeUtil;
//import com.databuff.sink.OlapTableWriter;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.kafka.clients.consumer.ConsumerRecord;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.kafka.annotation.KafkaListener;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//import java.util.Optional;
//
///**
// * @author:TianMing
// * @date: 2024/4/8
// * @time: 20:30
// */
//@Slf4j
////@Component()
//public class TraceSyncTask {
//    @Autowired
//    private OlapTableWriter olapTableWriter;
//
//    @KafkaListener( id = "trace_sync",containerFactory = "kafkaListenerContainerFactory", groupId = "trace_sync", topics = TRACE_TOPIC)
//    public void onMessageToEs(List<ConsumerRecord<String, String>> records) {
//        this.kafka2Starrocks(records);
//    }
//
//    public void kafka2Starrocks(List<ConsumerRecord<String, String>> records) {
//        for (ConsumerRecord<String, String> record : records) {
//            Optional<?> kafkaMsg = Optional.ofNullable(record.value());
//            if (kafkaMsg.isPresent()) {
//                String traceStr = record.value();
//                JSONObject traceJson = JSONObject.parseObject(traceStr);
//                JSONArray spans = traceJson.getJSONArray("spans");
//                for (Object spanObj : spans) {
//                    JSONObject span = (JSONObject) spanObj;
//                    long startTime = span.getLongValue("startTime");
//                    span.put("startTime", TimeUtil.formatLongToString(startTime));
//                    olapTableWriter.write(span.toString().getBytes(), OLAP_FORWARD_DATABASE, FORWARD_SPAN);
//                }
//            }
//        }
//    }
//}
