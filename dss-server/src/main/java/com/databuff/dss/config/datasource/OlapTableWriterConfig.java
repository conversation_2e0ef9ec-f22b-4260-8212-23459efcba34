package com.databuff.dss.config.datasource;

import com.databuff.sink.OlapTableWriter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Configuration
@Component
public class OlapTableWriterConfig {

    @Value("${spring.olap.feIpPorts}")
    private String feIpPorts;

    @Value("${spring.olap.username}")
    private String username;

    @Value("${spring.olap.password}")
    private String password;

    @Value("${spring.olap.maxFilterRatio:1}")
    private String maxFilterRatio;

    @Bean
    public OlapTableWriter getOlapTableWriter() {
        return new OlapTableWriter(feIpPorts, username, password, maxFilterRatio);
    }

}
