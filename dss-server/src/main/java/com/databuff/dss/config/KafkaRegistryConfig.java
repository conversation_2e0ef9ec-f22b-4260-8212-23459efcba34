package com.databuff.dss.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.listener.MessageListenerContainer;
import org.springframework.stereotype.Component;

/**
 * @author:TianMing
 * @date: 2024/5/24
 * @time: 10:37
 */
@Component
public class KafkaRegistryConfig implements ApplicationListener<ContextRefreshedEvent> {
    @Autowired
     KafkaListenerEndpointRegistry registry;


    @Value("${databuff.i6000.trace.enable:false}")
    private Boolean i6000TraceOpen;

    @Value("${databuff.sync.trace.enable:false}")
    private Boolean traceSyncOpen;

    public void startConsuming(String listenerId) {
        MessageListenerContainer container = registry.getListenerContainer(listenerId);
        if (container!=null && !container.isRunning()) {
            container.start();
        }
    }
    public void stopConsuming(String listenerId) {
        MessageListenerContainer container = registry.getListenerContainer(listenerId);
        if (container!=null && container.isRunning()) {
            container.stop();
        }
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        if (i6000TraceOpen) {
            startConsuming("trace_forward_i6000");
        }else{
            stopConsuming("trace_forward_i6000");
        }

        if (traceSyncOpen) {
            startConsuming("trace_sync");
        }else{
            stopConsuming("trace_sync");
        }
    }
}
