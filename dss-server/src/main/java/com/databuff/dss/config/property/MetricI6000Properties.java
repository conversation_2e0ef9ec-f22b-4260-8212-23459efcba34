package com.databuff.dss.config.property;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@ConfigurationProperties(prefix = "databuff.i6000.metric")
@Data
@RefreshScope
public class MetricI6000Properties {
    private String version = "1.0.0";
    private String serviceCode = "Databuff";
    private String userId = "221000000000036885";
    private String bgId = "C8D953A0-FFFC-491A-86AD-3AAEF940FCBC";
    private String SecZoneId = "40003102";
    private JSONObject producer = new JSONObject();
    private boolean enable = true;
    private JSONObject query = new JSONObject();
    private Map<String, String> serviceExceptionConfig = new HashMap<>();
    private Map<String, String> serviceConfig = new HashMap<>();
    private Map<String, String> serviceInstanceConfig = new HashMap();
    private Map<String, String> serviceEndpointConfig = new HashMap();
    private Map<String, String> serviceDBConfig = new HashMap();
    private Map<String, String> serviceDBSQLConfig = new HashMap();

    private Map<String, Double> serviceDefValue = new HashMap<>();
    private Map<String, Double> serviceInstanceDefValue = new HashMap();
    private Map<String, Double> serviceEndpointDefValue = new HashMap();
    private Map<String, Double> serviceDBDefValue = new HashMap();
    private Map<String, Double> serviceDBSQLDefValue = new HashMap();
}