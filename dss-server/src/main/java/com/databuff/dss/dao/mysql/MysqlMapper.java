package com.databuff.dss.dao.mysql;

import com.databuff.entity.Business;
import com.databuff.entity.HostInfoEntity;
import com.databuff.entity.TraceServiceEntity;
import com.databuff.entity.dto.KeyValue;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface MysqlMapper {
    /**
     * 根据起始时间查询存在的主机信息
     */
    List<HostInfoEntity> listHostByTime(@Param("startTime") String startTime, @Param("endTime") String endTime);
    List<TraceServiceEntity> listServiceByTime(@Param("startTime") String startTime);
    List<Business> listBusinessAll();
    List<KeyValue> getBusSvcIds();

}