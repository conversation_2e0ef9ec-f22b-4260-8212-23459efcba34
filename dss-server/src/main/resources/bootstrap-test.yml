spring:
  application:
    name: DSS
  cloud:
    zookeeper:
      connection-timeout: 30000  # Zookeeper 客户端的连接超时时间，单位是毫秒。
      session-timeout: 120000  # Zookeeper 客户端的会话超时时间，单位是毫秒。
      config:
        enabled: true
        root: databuff
        profileSeparator: ','
      discovery:
        enabled: true
      connect-string: 192.168.50.110:12181
      base-sleep-time-ms: 1000  # 重试策略的基础睡眠时间
      max-retries: 3  # 重试策略的最大重试次数
      max-sleep-ms: 3000

#javaagent 全局配置
databuff:
  javaagent:
    global:
      print_logs: false
      service_sample_rate: 1
      databuff:
        slf4j:
          simpleLogger:
            defaultLogLevel: info