SET global time_zone = 'Asia/Shanghai' ;

CREATE DATABASE IF NOT EXISTS forward;

use forward;

CREATE TABLE IF NOT EXISTS forward.forward_span
(
    `startTime` DATETIME NOT NULL COMMENT "开始时间",
    `serviceId` VARCHAR(255) NOT NULL COMMENT "服务id",
    `serviceInstance` VARCHAR(255) NULL COMMENT "服务实例",
    `resource` VARCHAR(500) NOT NULL COMMENT "资源",
    `clientService` VARCHAR(255) NULL COMMENT "客户端服务",
    `client_service_id` VARCHAR(255) NULL COMMENT "客户端服务id",
    `clientServiceInstance` VARCHAR(255) NULL COMMENT "客户端服务实例",
    `clientReportService` VARCHAR(255) COMMENT "客户端上报服务名",
    `end` BIGINT NOT NULL COMMENT "结束时间",
    `hostName` VARCHAR(255) COMMENT "主机名",
    `error` INT COMMENT "错误数",
    `type` VARCHAR(50) COMMENT "类型",
    `duration` BIGINT COMMENT "耗时",
    `isIn` TINYINT COMMENT "是否入口",
    `df-hostname` VARCHAR(255) COMMENT "主机名",
    `trace_id` VARCHAR(50) COMMENT "traceId",
    `span_id` VARCHAR(50) COMMENT "spanId",
    `df-api-key` VARCHAR(255) COMMENT "apiKey",
    `start` BIGINT COMMENT "开始时间",
    `host_id` VARCHAR(100) COMMENT "主机id",
    `reportService` VARCHAR(255) COMMENT "上报服务名",
    `service` VARCHAR(255) COMMENT "服务名",
    `parent_id` VARCHAR(50) COMMENT "父spanId",
    `meta`  VARCHAR(10000) COMMENT "meta信息",
    `name` VARCHAR(255) COMMENT "name",
    `isOut` TINYINT COMMENT "是否出口",
    `metrics` VARCHAR(1000) COMMENT "metrics信息",
    `user-agent` VARCHAR(255) COMMENT "agent信息",
    INDEX index_trace_id (trace_id) USING BITMAP COMMENT "加速trace_id 精确匹配查询"
    )
    PARTITION BY date_trunc('hour', startTime)
    DISTRIBUTED BY HASH(`serviceId`,`serviceInstance`,`resource`)
    PROPERTIES (
                   "replication_num" = "1",
                   "partition_live_number" = "192"
               );


CREATE TABLE IF NOT EXISTS forward.forward_service_instance (
    `hostIp` varchar(50) COMMENT '主机ip',
    `k8sContainerId` varchar(255) COMMENT '容器id',
    `virtualService` varchar(20) NOT NULL COMMENT '是否虚拟服务',
    `pgId` varchar(255) COMMENT '进程唯一id',
    `pid` varchar(255) COMMENT '进程pid',
    `pgName` varchar(255) COMMENT '进程名',
    `timestamp` DATETIME NOT NULL COMMENT '更新时间',
    `k8sNamespace`  varchar(255) COMMENT 'k8s命名空间',
    `hostname`  varchar(255) COMMENT '主机名',
    `service`  varchar(255) COMMENT '服务名',
    `k8sClusterId`  varchar(255) COMMENT 'k8s集群id',
    `serviceInstance`  varchar(255) NOT NULL COMMENT '服务实例',
    `serviceId`  varchar(255)  NOT NULL COMMENT '服务id',
    `k8sPodName`  varchar(255) COMMENT 'k8s pod名称'
    )
    PARTITION BY date_trunc('hour', timestamp)
    DISTRIBUTED BY HASH(`serviceId`,`serviceInstance`)
    PROPERTIES (
    "replication_num" = "1"
               );

CREATE TABLE IF NOT EXISTS forward.forward_container (
    `spuid` VARCHAR(100) NOT NULL COMMENT "唯一标识",
    `id` VARCHAR(100) NOT NULL COMMENT "容器id",
    `name` VARCHAR(200) NOT NULL COMMENT "container名称",
    `hostName` VARCHAR(100) COMMENT "node名称",
    `totalPct` DOUBLE COMMENT "cpu使用率",
    `memRss` BIGINT COMMENT "内存使用量",
    `netSentBps` DOUBLE COMMENT "网络发送速率",
    `netRcvdBps` DOUBLE COMMENT "网络接收速率",
    `state` VARCHAR(50) COMMENT "状态",
    `health` VARCHAR(50) COMMENT "健康状态",
    `started` BIGINT COMMENT "启动时间",
    `df-api-key` VARCHAR(100) COMMENT "apiKey",
    `timestamp` BIGINT NOT NULL COMMENT "上报时间",
    `tags` VARCHAR(400) COMMENT "tags",
    `data` JSON NULL COMMENT "container数据"
    )
    UNIQUE KEY(spuid)
    DISTRIBUTED BY HASH(spuid) BUCKETS 1
    PROPERTIES (
    "replication_num" = "1"
               );


CREATE TABLE IF NOT EXISTS forward.forward_process (
    `spuid` VARCHAR(100) NOT NULL COMMENT "唯一标识",
    `pname` VARCHAR(200) NOT NULL COMMENT "进程名",
    `pid` BIGINT NULL COMMENT "进程pid",
    `command` VARCHAR(65533) NOT NULL COMMENT "进程启动命令",
    `containerId` VARCHAR(200) COMMENT "容器id",
    `hostName` VARCHAR(100) COMMENT "node名称",
    `cpu` VARCHAR(65533) COMMENT "cpu信息",
    `ioStat` VARCHAR(65533) COMMENT "io信息",
    `memory` VARCHAR(65533) COMMENT "内存信息",
    `df-api-key` VARCHAR(100) NOT NULL COMMENT "apiKey",
    `timestamp` BIGINT NOT NULL COMMENT "上报时间",
    `tags` VARCHAR(400) COMMENT "tags",
    `data` JSON NULL COMMENT "进程数据"
    )
    UNIQUE KEY(spuid)
    DISTRIBUTED BY HASH(spuid) BUCKETS 1
    PROPERTIES (
    "replication_num" = "1"
               );


CREATE TABLE IF NOT EXISTS forward.forward_host (
    `hostName` varchar(255) NOT NULL COMMENT '主机名',
    `apiKey` varchar(255) NOT NULL COMMENT 'apiKey',
    `ipaddress` varchar(255) DEFAULT NULL COMMENT 'ip地址',
    `ipaddressv6` varchar(255) DEFAULT NULL COMMENT 'ipv6地址',
    `memTotal` varchar(255) DEFAULT NULL COMMENT '总内存',
    `memSwapTotal` varchar(255) DEFAULT NULL COMMENT '总Swap内存',
    `osVersion` varchar(255) DEFAULT NULL COMMENT '操作系统版本',
    `macaddress` varchar(255) DEFAULT NULL COMMENT 'mac地址',
    `cpu` varchar(1024) DEFAULT NULL COMMENT '主机cpu信息',
    `filesystem` VARCHAR(65533) COMMENT '主机文件系统信息',
    `platform` varchar(1024) DEFAULT NULL COMMENT '主机硬件平台信息',
    `interfaces` text COMMENT '主机网卡信息',
    `timestamp` BIGINT NOT NULL COMMENT '最近一次更新时间',
    `hostId` varchar(32) DEFAULT NULL COMMENT '探针生成的hostId信息',
    `lastReportMemUsedPercent` varchar(255) DEFAULT NULL COMMENT '最新内存百分比',
    `lastReportMem` varchar(255) DEFAULT NULL COMMENT '最新内存数据',
    `lastReportCpu` varchar(255) DEFAULT NULL COMMENT '最新cpu数据',
    `totalDisk` varchar(255) DEFAULT NULL COMMENT '总磁盘大小'
    )
    UNIQUE KEY(hostName)
    DISTRIBUTED BY HASH(`hostName`) BUCKETS 1
    PROPERTIES (
    "replication_num" = "1"
               );


CREATE TABLE IF NOT EXISTS forward.forward_business (
    `id` BIGINT NOT NULL COMMENT "唯一id",
    `name` VARCHAR(100) NOT NULL COMMENT "系统名称",
    `type` INT NOT NULL COMMENT '1顶级业务系统 2业务子系统',
    `remark` VARCHAR(300) COMMENT "注释",
    `pid` BIGINT NOT NULL COMMENT "父级id（顶层为0，子系统则关联父系统id）",
    `apiKey` VARCHAR(100) COMMENT "apiKey",
    `createTime` DATETIME NOT NULL COMMENT '创建时间',
    `updateTime` DATETIME NOT NULL COMMENT '更新时间'
    )
    UNIQUE KEY(id)
    DISTRIBUTED BY HASH(id) BUCKETS 1
    PROPERTIES (
    "replication_num" = "1"
               );

CREATE TABLE IF NOT EXISTS forward.forward_service (
    `id` varchar(255) NOT NULL COMMENT '服务id',
    `name` varchar(255) DEFAULT NULL COMMENT '服务展示名称',
    `service` varchar(255) DEFAULT NULL COMMENT '采集的服务名称',
    `service_type` varchar(255) DEFAULT NULL COMMENT '服务大类 web db cache custom',
    `apikey` varchar(255) DEFAULT NULL COMMENT 'apikey',
    `custom_tags` VARCHAR(5000) COMMENT '自定义标签',
    `type` varchar(255) DEFAULT NULL COMMENT '服务类别',
    `fqdn` varchar(255) DEFAULT NULL COMMENT 'cmdb fqdn',
    `source` varchar(255) DEFAULT NULL COMMENT '服务来源如k8s或vm',
    `describe` VARCHAR(5000) COMMENT '服务描述',
    `container_service` varchar(255) DEFAULT NULL COMMENT 'K8s服务的容器名',
    `virtual_service` INT DEFAULT NULL COMMENT '根据调用关系虚拟出来的服务',
    `update_time` DATETIME NULL DEFAULT NULL COMMENT '服务的更新时间',
    `technology` varchar(255) DEFAULT NULL COMMENT '服务使用到的技术',
    `busIds` varchar(5000) DEFAULT NULL COMMENT '业务系统ids'
    )
    UNIQUE KEY(id)
    DISTRIBUTED BY HASH(`id`) BUCKETS 1
    PROPERTIES (
    "replication_num" = "1"
               );


CREATE TABLE IF NOT EXISTS forward.forward_service_relation (
    `srcServiceId` varchar(255) NOT NULL COMMENT '来源服务id',
    `srcService` varchar(255) NOT NULL COMMENT '来源服务',
    `srcServiceInstance` varchar(255) NOT NULL COMMENT '来源服务实例',
    `serviceId` varchar(255) NOT NULL COMMENT '目标服务id',
    `service` varchar(255) NOT NULL COMMENT '目标服务',
    `serviceInstance` varchar(255) NOT NULL COMMENT '目标服务实例',
    `timestamp` DATETIME NOT NULL COMMENT "统计时间",
    `allTime` BIGINT NOT NULL COMMENT '总耗时（ns）',
    `errCnt` BIGINT NOT NULL COMMENT '错误数',
    `allCnt` BIGINT NOT NULL COMMENT '总调用数',
    `avgLatency` DOUBLE NOT NULL COMMENT '平均相应时间',
    `errRate` DOUBLE  NOT NULL COMMENT '错误率'
    )
    PARTITION BY date_trunc('hour', timestamp)
    DISTRIBUTED BY HASH(`srcServiceId`,`srcServiceInstance`,`serviceId`,`serviceInstance`)
    PROPERTIES (
                   "replication_num" = "1"
               );


-- forward.forward_metric_detail definition
CREATE TABLE IF NOT EXISTS `forward_metric_detail` (
  `time` datetime NULL COMMENT "时间戳（字符串表示）",
  `id` varchar(32) NULL COMMENT "指标id",
  `type1` varchar(255) NULL COMMENT "类型1",
  `type2` varchar(255) NULL COMMENT "类型2",
  `type3` varchar(255) NULL COMMENT "类型3",
  `aggregatorType` varchar(255) NULL COMMENT "聚合类型",
  `formula` varchar(255) NULL COMMENT "计算公式",
  `identifier` varchar(255) NULL COMMENT "指标标识",
  `measurement` varchar(255) NULL COMMENT "度量名称",
  `metricValue` double NULL COMMENT "指标值",
  `unitCn` varchar(32) NULL COMMENT "单位（中文）",
  `interval` SMALLINT NULL COMMENT "聚合粒度（秒）",
  `latency` bigint(20) NULL COMMENT "延迟(秒)",
  `tag` json NULL COMMENT "标签（JSON格式）"
) ENGINE=OLAP
DUPLICATE KEY(`time`, `id`)
DISTRIBUTED BY HASH(`time`, `id`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);