package com.databuff.tasks.log;

import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.sink.OlapTableWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;

import static com.databuff.common.constants.MetricName.*;
import static com.databuff.common.constants.OlapDB.DC_LOGS;
import static com.databuff.common.constants.OlapDB.OLAP_DATABASE;

/**
 * @author:TianMing
 * @date: 2022/11/25
 * @time: 16:34
 */
@Slf4j
@Component()
public class LogsSinkStarrocks {

    @Autowired
    private OlapTableWriter olapTableWriter;

    @KafkaListener(containerFactory = "kafkaListenerContainerFactory", groupId = "dc_log_to_sr", topics = "dc_databuff_log")
    public void onMessageToEs(List<ConsumerRecord<String, String>> records) {
        long start = System.currentTimeMillis();
        long oldTime = start;
        try {
            OtelMetricUtil.logCounter(LOG_CONSUME, records.size());
            oldTime = records.get(0).timestamp();
            this.kafka2Starrocks(records);
        } finally {
            long duration = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(LOG_COST, duration);
            OtelMetricUtil.logHistogram(LOG_DELAY, start - oldTime);
        }
    }

    public void kafka2Starrocks(List<ConsumerRecord<String, String>> records) {
        for (ConsumerRecord<String, String> record : records) {
            Optional<?> kafkaMsg = Optional.ofNullable(record.value());
            if (kafkaMsg.isPresent()) {
                String message = record.value();
                olapTableWriter.write(message.getBytes(StandardCharsets.UTF_8), OLAP_DATABASE, DC_LOGS);
                OtelMetricUtil.logCounter(LOG_TO_SR, 1);

            }
        }
    }
}
