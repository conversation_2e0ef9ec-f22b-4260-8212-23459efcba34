package com.databuff.tasks.cmdb.task;

import static com.databuff.common.constants.MetricName.*;
import com.alibaba.fastjson.JSONObject;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.dao.mysql.CmdbMapper;
import com.databuff.dao.mysql.TraceServiceMapper;
import com.databuff.entity.CmdbEntity;
import com.databuff.entity.TraceServiceEntity;
import com.databuff.service.JedisService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @author:TianMing
 * @date: 2023/5/4
 * @time: 18:39
 */
@Component()
@Slf4j
public class CmdbSynData {

    @Getter
    private Map<String, JSONObject> serviceFqdnMap = new ConcurrentHashMap<>();
    @Getter
    private Map<String, JSONObject> hostFqdnMap = new ConcurrentHashMap<>();

    @Resource
    private CmdbMapper cmdbMapper;
    @Resource
    private TraceServiceMapper serviceMapper;
    @Resource
    private JedisService jedisService;
    @Value("${cmdb.env:}")
    private String env;
    @PostConstruct
    public void initSyncServiceName() {
        //启动的时候把fqdn加载到内存，供外发是获取
        log.info("cacheFqdn ing......");
        cacheFqdn();
    }

    @XxlJob("syn-cmdb-data-task")
    public void start() {
        String fqdnSyndata = jedisService.getJson("fqdn:syndata");
        LocalDateTime now = LocalDateTime.now();
        int minute = now.getMinute();
        try{
            if (org.apache.commons.lang3.StringUtils.isNotBlank(fqdnSyndata) && "1".equals(fqdnSyndata)) {
                log.info("task syncServiceName ing......");
                //同步服务中文名称
                syncServiceName("task");
                //同步完后设为0
                jedisService.setJson("fqdn:syndata", "0");
            }else if(minute == 38){
                //每小时的38分也同步一次服务名称，避免新上线的服务没有更改名字
                //同步服务中文名称
                log.info("每小时 syncServiceName ing......");
                syncServiceName("timer");
            }
        }catch (Exception e){
            log.error("syncServiceName error",e);
        }
        //缓存fqdn到redis
        cacheFqdn();
    }

    private void cacheFqdn() {
        List<TraceServiceEntity> services = serviceMapper.fqdnSvcs();
        if (!CollectionUtils.isEmpty(services)) {
            Map<String, JSONObject> serviceFqdnMap = new ConcurrentHashMap<>();
            for (TraceServiceEntity service : services) {
                String fqdn = service.getFqdn();
                String name = service.getName();
                JSONObject info = new JSONObject();
                info.put("fqdn", fqdn);
                info.put("name", name);
                if (org.apache.commons.lang3.StringUtils.isNotBlank(fqdn)) {
                    serviceFqdnMap.put(service.getId(), info);
                }
            }

            this.serviceFqdnMap = serviceFqdnMap;
        }
        //虚拟机
        List<CmdbEntity> hostCmdbEntities = cmdbMapper.listHostCmdb();
        if (!CollectionUtils.isEmpty(hostCmdbEntities)) {
            Map<String, JSONObject> hostFqdnMap = new ConcurrentHashMap<>();
            for (CmdbEntity cmdbEntity : hostCmdbEntities) {
                String fqdn = cmdbEntity.getFqdn();
                String hostName = cmdbEntity.getName();
                JSONObject info = new JSONObject();
                info.put("fqdn", fqdn);
                info.put("name", hostName);
                hostFqdnMap.put(hostName, info);
            }
            this.hostFqdnMap = hostFqdnMap;
        }
    }

    /**
     * 同步服务中文名称
     */
    private void syncServiceName(String type) {
        Map<String, String> tags = new HashMap<>();
        tags.put("type", type);
        tags.put("env", env);
        //根据环境取出需要变更名称的服务，fqdn等信息
        List<TraceServiceEntity> serviceEntities = serviceMapper.listCmdbService(env);
        OtelMetricUtil.logCounter(CMDB_TASK_SYNC_CMDB_SVC_COUNTS,tags,serviceEntities.size());
        if (!CollectionUtils.isEmpty(serviceEntities)) {
            //非虚拟机服务
            List<TraceServiceEntity> services = serviceMapper.notVirtualSvcs();
            OtelMetricUtil.logCounter(CMDB_TASK_SYNC_ALL_SVC_COUNTS,tags,services.size());

            Set<String> nameSet = services.stream().filter(s -> !StringUtils.isEmpty(s.getName())).map(s -> ("" + s.getApikey() + s.getName())).collect(Collectors.toSet());
            for (TraceServiceEntity service : serviceEntities) {
                try{
                    String name = service.getName();
                    if (nameSet.contains("" + service.getApikey() + name)) {
                        name = getServiceNumberName(nameSet, name, service.getApikey());
                    }
                    TraceServiceEntity updateEntity = new TraceServiceEntity();
                    updateEntity.setName(name);
                    updateEntity.setId(service.getId());
                    updateEntity.setFqdn(service.getFqdn());
                    serviceMapper.updateServiceName(updateEntity);
                    OtelMetricUtil.logCounter(CMDB_TASK_SYNC_CMDB_SUCCESS_COUNTS,tags,1);
                    nameSet.add("" + service.getApikey() + name);
                }catch (Exception e){
                    Map<String, String> tags2 = new HashMap<>();
                    tags2.put("type", type);
                    tags2.put("env", env);
                    tags2.put("service", service.getName());
                    tags2.put("serviceId", service.getId());
                    tags2.put("fqdn", service.getFqdn());
                    tags2.put("msg", e.getMessage());
                    OtelMetricUtil.logCounter(CMDB_TASK_SYNC_CMDB_ERR_COUNTS,tags2,1);
                    log.error("syncServiceName error",e);
                }
            }
        }
    }

    /**
     * 若存在重复名称，则在服务名称后增加数字
     *
     * @param nameSet
     * @param name
     * @param apikey
     * @return
     */
    private String getServiceNumberName(Set<String> nameSet, String name, String apikey) {
        List<String> duplicatedNames = nameSet.stream().filter(s -> s.startsWith("" + apikey + name)).collect(Collectors.toList());
        Integer max = duplicatedNames.stream().map(s -> s.substring(("" + apikey + name).length())).map(this::convertToNum).max(new Comparator<Integer>() {
            @Override
            public int compare(Integer o1, Integer o2) {
                return o1 - o2;
            }
        }).get();
        return name + "(" + (max + 1) + ")";
    }

    private int convertToNum(String num) {
        if (StringUtils.isEmpty(num)) {
            return 0;
        }
        if (!(num.startsWith("(") && num.endsWith(")"))) {
            return 0;
        }
        num = num.replace("(", "").replace(")", "");
        if (!NumberUtils.isNumber(num)) {
            return 0;
        }
        return Integer.valueOf(num);
    }

}
