package com.databuff.tasks.kafka.stream.convergence;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.databuff.common.utils.TimeUtil;
import com.databuff.entity.ConvergencePolicy;
import com.databuff.entity.TraceServiceEntity;
import com.databuff.entity.dto.DCAlarmDto;
import com.databuff.entity.dto.DcAlarmAggregate;
import com.databuff.entity.dto.EventConvergenceDTO;
import com.databuff.metric.RuleTypeEnum;
import com.databuff.service.DomainManagerObjService;
import com.databuff.tasks.config.TaskRefreshScopeConfig;
import com.databuff.tasks.convergence.ConvergenceType;
import com.databuff.tasks.util.DelayTaskUtil;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.HashSet;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.databuff.common.constants.Constant.HOST;
import static com.databuff.common.constants.Constant.Trace.*;
import static com.databuff.common.constants.KafkaTopicConstant.ALARM_TOPIC;
import static com.databuff.common.constants.OlapDB.*;
import static com.databuff.common.utils.TimeUtil.ONE_MIN_MS_LONG;
import static com.databuff.common.utils.TimeUtil.ONE_MIN_SEC_INT;
import static com.databuff.tasks.convergence.ConvergenceType.END_JUDGMENT_WINDOW;
import static com.databuff.tasks.convergence.ConvergenceType.FIXED_DURATION_WINDOW;
import static com.databuff.tasks.convergence.ConvergenceUtil.extractStrDateFromAlarmId;

@Slf4j
@Component
@Api(value = "CustomConvergence", tags = "自定义收敛策略实现类")
public class CustomConvergence extends ConvergenceBase implements CustomConvergenceBatchInvoker {

    @Autowired
    private DomainManagerObjService domainManagerObjService;
    @Autowired
    private TaskRefreshScopeConfig taskRefreshScopeConfig;

    /**
     * @param trigger
     * @param events
     * @param convergencePolicy
     */
    @Override
    public void invokeBatchConvergence(JSONObject trigger, Collection<EventConvergenceDTO> events, ConvergencePolicy convergencePolicy) {
        if (trigger == null || trigger.isEmpty()
                || CollectionUtils.isEmpty(events)) {
            return;
        }
        final String service = trigger.getString(SERVICE);
        final String serviceId = trigger.getString(SERVICE_ID);
        final String apiKey = convergencePolicy.getApiKey();
        if (service != null && serviceId == null) {
            TraceServiceEntity traceServiceEntity = serviceSyncService.getTraceServiceEntityByChineseName(service);
            if (traceServiceEntity != null) {
                trigger.put(SERVICE_ID, traceServiceEntity.getId());
            }
        }

        ConvergenceType convergenceType;
        final Integer fixedDurationWindow = convergencePolicy.getFixedDurationWindow();
        final Integer endJudgmentWindow = convergencePolicy.getEndJudgmentWindow();
        Long expireTimeMs;
        final Boolean convergencePolicyDef = convergencePolicy.getDef();
        if (endJudgmentWindow != null && endJudgmentWindow > 0) {
            convergenceType = END_JUDGMENT_WINDOW;
            expireTimeMs = endJudgmentWindow * ONE_MIN_MS_LONG;
        } else if (fixedDurationWindow != null && fixedDurationWindow > 0) {
            convergenceType = FIXED_DURATION_WINDOW;
            expireTimeMs = fixedDurationWindow * ONE_MIN_MS_LONG;
        } else if (Boolean.TRUE.equals(convergencePolicyDef)) {
            // 默认收敛策略,默认5分钟
            convergenceType = ConvergenceType.DEFAULT;
            expireTimeMs = 5 * ONE_MIN_MS_LONG;
        } else {
            log.warn("收敛策略参数无效，继续");
            return;
        }

        final Integer policyId = convergencePolicy.getId();
        final String gid = convergencePolicy.getGid();
        final String key = String.format("convergence:%d:%s:%s", policyId, gid, trigger.toJSONString());
        final String lockKey = "/" + key + ":lock";

        AtomicBoolean minuteFirst = new AtomicBoolean(false);
        final Callable<Collection<DCAlarmDto>> customMethod = () -> lockAndProcess(events, convergencePolicy, trigger, key, apiKey, expireTimeMs, convergenceType, policyId, minuteFirst);
        final Collection<DCAlarmDto> alarmDtos = dConfigLockOperator.acquireLockAndExecuteCallback(lockKey, 2, customMethod, customMethod);
        if (alarmDtos == null || alarmDtos.isEmpty()) {
            return;
        }

        for (DCAlarmDto alarmDto : alarmDtos) {
            alarmDto.setConvergenceType(convergenceType);

            final String alarmId = alarmDto.getId();
            final long triggerTime = alarmDto.getTimestamp();

            log.debug("告警[{}]发送到 starRocks", alarmId);

            // 更新告警描述, 如果是默认收敛策略, 则使用第一个事件的描述
            if (Boolean.TRUE.equals(convergencePolicyDef)) {
                events.stream().findFirst().ifPresent(event -> {
                    final String message = event.getMessage();
                    if (message != null) {
                        alarmDto.setDescription(message);
                    }
                });
            } else {
                alarmDto.resetDescription();
            }
            olapTableWriter.write(JSONObject.toJSONBytes(alarmDto, SerializerFeature.DisableCircularReferenceDetect), OLAP_DATABASE, DC_ALARM);
            final int eventCnt = alarmDto.getCurrentMinEventCnt().get();
            final DcAlarmAggregate alarmAggregate = DcAlarmAggregate.builder()
                    .time(TimeUtil.formatLongToString(triggerTime))
                    .apiKey(alarmDto.getApiKey())
                    .id(alarmDto.getId())
                    .gid(alarmDto.getGid())
                    .eventCnt(eventCnt)
                    .build();
            olapTableWriter.write(JSONObject.toJSONBytes(alarmAggregate, SerializerFeature.DisableCircularReferenceDetect), OLAP_DATABASE, DC_AGG_ALARM);
            if (minuteFirst.get()) {
                DelayTaskUtil.add(() -> {
                    try {
                        kafkaUtil.producerSend(ALARM_TOPIC, alarmId, (JSONObject) JSONObject.toJSON(alarmDto));
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }, taskRefreshScopeConfig.getRootEngineTriggerDelay());
            }
        }
    }

    @ApiOperation(value = "锁定并处理", notes = "锁定资源并处理收敛逻辑")
    protected Collection<DCAlarmDto> lockAndProcess(Collection<EventConvergenceDTO> events, ConvergencePolicy convergencePolicy, JSONObject trigger, String key, String apiKey, Long expireTimeMs, ConvergenceType convergenceType, Integer policyId, AtomicBoolean minuteFirst) {
        if (convergencePolicy == null) {
            log.warn("收敛策略不存在，继续");
            return null;
        }

        // 如果 gid 为空，则从收敛策略中获取 gid
        String gid = convergencePolicy.getGid();
        final Boolean def = convergencePolicy.getDef();
        final boolean allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        final boolean domainManagerStatusOpen = domainManagerObjService.getDomainManagerStatusOpen();
        if (domainManagerStatusOpen) {
            final String finalGid = gid;
            final boolean shouldFilterByGid = (def == null || !def) && (gid != null && !allEntityPermission);
            final Predicate<EventConvergenceDTO> statusOpenFilter = event ->
                    event.getGid() != null && (!shouldFilterByGid || Objects.equals(finalGid, event.getGid()));

            events = events.stream()
                    .filter(statusOpenFilter)
                    .collect(Collectors.toList());
            // 如果没有域ID，则从事件数据中获取
            if (gid == null) {
                final EventConvergenceDTO eventConvergenceDTO = events.stream()
                        .filter(i -> i != null && i.getGid() != null)
                        .findFirst()
                        .get();
                if (eventConvergenceDTO != null) {
                    gid = eventConvergenceDTO.getGid();
                }
            }
        } else {
            // 关闭域时只处理没有域ID的事件数据
            final Predicate<EventConvergenceDTO> statusCloseFilter = event -> event.getGid() == null;

            events = events.stream()
                    .filter(statusCloseFilter)
                    .collect(Collectors.toList());
        }

        if (events.isEmpty()) {
            return null;
        }

        final Long maxTriggerTime = EventConvergenceTimeRange.getMaxTriggerTime(events).get();
        final Long minTriggerTime = EventConvergenceTimeRange.getMinTriggerTime(events).get();
        Long triggerTime = maxTriggerTime;

        DCAlarmDto alarmDto = JSONObject.parseObject(jedisService.getJson(key), DCAlarmDto.class);
        if (alarmDto != null) {
            // 更新最早触发时间,交换最早触发时间和当前触发时间
            long startTriggerTime = alarmDto.getStartTriggerTime();
            if (startTriggerTime > minTriggerTime) {
                // 更新触发时间
                triggerTime = Math.max(triggerTime, startTriggerTime);
                // 更新最早触发时间
                alarmDto.setStartTriggerTime(Math.min(startTriggerTime, minTriggerTime));
                // 更新过期时间
                long expiredAt = alarmDto.getStartTriggerTime() + expireTimeMs;
                if (convergenceType.equals(END_JUDGMENT_WINDOW)) {
                    expiredAt = triggerTime + expireTimeMs;
                }
                alarmDto.setExpiredAt(expiredAt);
            }
            if (alarmDto.getExpiredAt() < triggerTime) {
                log.warn(String.format("告警ID【%s】已过期，删除并创建新的告警", alarmDto.getId()));
                alarmDto = dcAlarmCleanService.cleanAlarm(key, triggerTime, alarmDto);
            }
        }

        if (alarmDto == null) {
            alarmDto = new DCAlarmDto();
            String alarmId = generateId(null, false);
            alarmDto.setId(alarmId);
            alarmDto.setCreateDt(extractStrDateFromAlarmId(alarmId));
            alarmDto.setSameRootCause(false);
            alarmDto.setFirst(true);
            alarmDto.setLast(false);
            alarmDto.setType("convergence");
            final Long creatorId = convergencePolicy.getCreatorId();
            if (creatorId != null) {
                alarmDto.setCreatorId(creatorId.toString());
            }
            final Long editorId = convergencePolicy.getEditorId();
            if (editorId != null) {
                alarmDto.setEditorId(editorId.toString());
            }
            alarmDto.setAlarmStatus("未处理");
            alarmDto.setStatus(0);
            alarmDto.setLevel(0);
            alarmDto.setStartTriggerTime(triggerTime);
            alarmDto.setApiKey(apiKey);
            alarmDto.setEventId(new HashSet<>());
            alarmDto.setServiceId(new HashSet<>());
            alarmDto.setServiceInstance(new HashSet<>());
            alarmDto.setHost(new HashSet<>());
            alarmDto.setDeviceName(new HashSet<>());
            alarmDto.setClassification(new HashSet<>());
            alarmDto.setUpdateTime(System.currentTimeMillis());
        } else {
            alarmDto.setFirst(false);
            alarmDto.setLast(false);
        }
        long expiredAt = alarmDto.getStartTriggerTime() + expireTimeMs;
        if (convergenceType.equals(END_JUDGMENT_WINDOW)) {
            expiredAt = triggerTime + expireTimeMs;
        }
        // 告警域ID
        String alarmGid = null;
        if (domainManagerStatusOpen && gid != null) {
            alarmGid = gid;
        }

        alarmDto.setGid(alarmGid);
        alarmDto.setExpiredAt(expiredAt);
        alarmDto.setPolicyId(policyId);

        final String policyName = convergencePolicy.getPolicyName();
        if (def != null && def) {
            alarmDto.setPolicyName("");
        } else {
            alarmDto.setPolicyName(policyName);
        }
        trigger.put("policyName", alarmDto.getPolicyName());
        trigger.put("convergenceType", convergenceType.getNameZh());
        alarmDto.setTrigger(trigger);
        alarmDto.setTime(TimeUtil.formatLongToString(triggerTime));
        alarmDto.setTimestamp(triggerTime);
        alarmDto.setDuration((triggerTime - alarmDto.getStartTriggerTime()) / 1000 + ONE_MIN_SEC_INT);
        alarmDto.setUpdateTime(System.currentTimeMillis());

        for (EventConvergenceDTO event : events) {
            alarmDto.incrementCurrentMinEventCnt(triggerTime, minuteFirst);
            // 设置告警最高等级
            final Integer eventDtoLevel = event.getLevel();
            final Integer alarmDtoLevel = alarmDto.getLevel();
            if (alarmDtoLevel != null && eventDtoLevel != null && alarmDtoLevel < eventDtoLevel) {
                alarmDto.setLevel(eventDtoLevel);
            }
            event.getPolicyIds().add(policyId);
            alarmDto.setTags(getTags(event.getTags(), alarmDto.getTags()));
            // 添加事件ID
            alarmDto.getEventId().add(event.getEventId());

            final RuleTypeEnum classification = event.getClassification();
            if (classification != null) {
                alarmDto.getClassification().add(classification.name());
            }
        }

        if (trigger.getString(SERVICE_ID) != null) {
            alarmDto.getServiceId().add(trigger.getString(SERVICE_ID));
        }
        if (trigger.getString(SERVICE_INSTANCE) != null) {
            alarmDto.getServiceInstance().add(trigger.getString(SERVICE_INSTANCE));
        }
        if (trigger.getString(HOST) != null) {
            alarmDto.getHost().add(trigger.getString(HOST));
        }
        if (trigger.getString("device_name") != null) {
            alarmDto.getDeviceName().add(trigger.getString("device_name"));
        }

        final EventConvergenceDTO firstEvent = getFirstEvent(events);
        updatePattern(firstEvent, convergencePolicy, alarmDto);

        for (EventConvergenceDTO event : events) {
            updateVariableMap(event, convergencePolicy, alarmDto);
        }
        final int expiretime = Long.valueOf(expireTimeMs).intValue() / 1000 + ONE_MIN_SEC_INT;
        jedisService.setJson(key, JSONObject.toJSONString(alarmDto), expiretime);
        return Lists.newArrayList(alarmDto);
    }


}