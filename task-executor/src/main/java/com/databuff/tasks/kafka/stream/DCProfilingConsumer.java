package com.databuff.tasks.kafka.stream;

import com.alibaba.fastjson.JSONException;
import com.databuff.common.constants.Constant;
import com.databuff.sink.OlapTableWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;

import static com.databuff.common.constants.OlapDB.OLAP_DATABASE;

/**
 * @package com.databuff.tasks.kafka.stream
 * @company: dacheng
 * @author: yuzhili
 * @createDate: 2024/11/04
 * @description: Profiling消费者
 */
@Slf4j
@Component
public class DCProfilingConsumer {

    @Autowired
    private OlapTableWriter olapTableWriter;

    @KafkaListener(containerFactory = "kafkaListenerContainerFactory", groupId = Constant.Kafka.PROFILING_STACK_BASE_TOPIC, topics = Constant.Kafka.PROFILING_STACK_BASE_TOPIC, concurrency = "1")
    public void onStackBaseNotify(List<ConsumerRecord<String, String>> records) {
        for (ConsumerRecord<String, String> record : records) {
            if (record == null || record.value() == null) {
                continue;
            }
            try {
                olapTableWriter.write(record.value().getBytes(), OLAP_DATABASE, "dc_profiling_stack_base", null);
            } catch (JSONException e) {
                log.error("Unexpected error in onStackNotify", e);
            }
        }
    }

    @KafkaListener(containerFactory = "kafkaListenerContainerFactory", groupId = Constant.Kafka.PROFILING_STACK_TOPIC, topics = Constant.Kafka.PROFILING_STACK_TOPIC, concurrency = "1")
    public void onStackNotify(List<ConsumerRecord<String, String>> records) {
        for (ConsumerRecord<String, String> record : records) {
            if (record == null || record.value() == null) {
                continue;
            }
            try {
                olapTableWriter.write(record.value().getBytes(StandardCharsets.UTF_8), OLAP_DATABASE, "dc_profiling_stack", null);
            } catch (JSONException e) {
                log.error("Unexpected error in onStackNotify", e);
            }
        }
    }

    @KafkaListener(containerFactory = "kafkaListenerContainerFactory", groupId = Constant.Kafka.PROFILING_HOTSPOT_TOPIC, topics = Constant.Kafka.PROFILING_HOTSPOT_TOPIC, concurrency = "3")
    public void onHotSpotNotify(List<ConsumerRecord<String, String>> records) {
        for (ConsumerRecord<String, String> record : records) {
            if (record == null || record.value() == null) {
                continue;
            }
            try {
                olapTableWriter.write(record.value().getBytes(StandardCharsets.UTF_8), OLAP_DATABASE, "dc_profiling_hotspot", null);
            } catch (JSONException e) {
                log.error("Unexpected error in onHotSpotNotify", e);
            }
        }
    }
}
