package com.databuff.tasks.service.impl;

import com.databuff.common.constants.Constant;
import com.databuff.dao.mysql.MonitorMapper;
import com.databuff.entity.DatabuffMonitor;
import com.databuff.entity.dto.DatabuffMonitorView;
import com.databuff.service.DomainManagerObjService;
import com.databuff.service.JedisService;
import com.databuff.tasks.service.MonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @package com.databuff.webapp.monitor.service.impl
 * @company: dacheng
 * @author: zlh
 * @createDate: 2021/8/26
 */
@Slf4j
@Service
public class MonitorServiceImpl implements MonitorService {

    @Autowired
    private MonitorMapper monitorMapper;
    @Autowired
    private JedisService jedisService;

    @Autowired
    private DomainManagerObjService domainManagerObjService;

    @Override
    public List<DatabuffMonitorView> findAllMonitorByClassfy(String monitorClassify, Boolean enabled) {
        // 查出所有未过期的api key，缓存中获取
        Set<String> keys = jedisService.keysS(Constant.SAAS_API_KEY_CATCH);
        List<String> apiKeys = new ArrayList<>(keys.size());
        for (String key : keys) {
            String apiKey = key.split(":")[2];
            apiKeys.add(apiKey);
        }

        final Boolean domainManagerStatus = domainManagerObjService.getDomainManagerStatusOpen();

        // 查出所有监控规则, 不通过域ID过滤
        List<DatabuffMonitorView> allMonitors = monitorMapper.findAllMonitorByClassfy(monitorClassify, enabled, domainManagerStatus, true, null);
        List<DatabuffMonitorView> monitors = new ArrayList<>(allMonitors.size());
        // 引擎专用查询
        //过期的apikey监控规则排除 , 场景规则id填充
        for (DatabuffMonitorView m : allMonitors) {
            if (!apiKeys.contains(m.getApiKey())) {
                continue;
            }
            monitors.add(m);
        }
        return monitors;
    }

    /**
     * 获取由管理员创建的DatabuffMonitor列表
     * <p>
     * 该方法从数据库中检索所有由管理员（creator字段为'Admin'）创建的DatabuffMonitor记录。
     *
     * @return 由管理员创建的DatabuffMonitor列表
     */
    @Override
    public List<DatabuffMonitor> findMonitorsByAdminCreator() {
        return monitorMapper.findMonitorsByAdminCreator();
    }

}
