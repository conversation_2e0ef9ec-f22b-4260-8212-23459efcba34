package com.databuff.tasks.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.databuff.common.annotation.LogExecutionTime;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.entity.dto.DCAlarmDto;
import com.databuff.service.JedisService;
import com.databuff.sink.OlapTableWriter;
import com.databuff.tasks.config.TaskRefreshScopeConfig;
import com.databuff.util.KafkaUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static com.databuff.common.constants.KafkaTopicConstant.ALARM_TOPIC;
import static com.databuff.common.constants.OlapDB.DC_ALARM;
import static com.databuff.common.constants.OlapDB.OLAP_DATABASE;
import static com.databuff.common.utils.TimeUtil.roundDownToMinute;

/**
 * @package com.databuff.tasks.kafka.stream
 * @company: dacheng
 * @author: yuzhili
 * @createDate: 2024/10/17
 * @description: 告警过期清理任务
 */
@Slf4j
@Component
public class DCAlarmCleanService {

    @Autowired
    private OlapTableWriter olapTableWriter;

    @Autowired
    private TaskRefreshScopeConfig taskRefreshScopeConfig;

    @Autowired
    private JedisService jedisService;

    @Autowired
    private KafkaUtil kafkaUtil;

    @LogExecutionTime
    public void clean() {
        Long nowTime = System.currentTimeMillis();
        // 计算最后触发时间
        Long lastTriggerTime = roundDownToMinute(nowTime) - (taskRefreshScopeConfig.getEventDefTimeOffset() - 60) * 1000;

        Set<String> allAlarmKeys = jedisService.keysS("convergence:");
        if (allAlarmKeys == null) {
            return;
        }
        // 并行清理过期告警
        allAlarmKeys.stream()
                .parallel()
                .forEach(alarmKey -> cleanExpiredAlarm(alarmKey, lastTriggerTime));
    }

    private void cleanExpiredAlarm(final String alarmKey, final Long lastTriggerTime) {
        if (alarmKey == null || lastTriggerTime == null) {
            return;
        }
        final String json = jedisService.getJson(alarmKey);
        if (json == null) {
            return;
        }
        DCAlarmDto cacheAlarm = JSONObject.parseObject(json, DCAlarmDto.class);
        if (cacheAlarm == null) {
            return;
        }
        final long expireTime = cacheAlarm.getExpiredAt();
        if (expireTime >= lastTriggerTime) {
            return;
        }
        cleanAlarm(alarmKey, lastTriggerTime, cacheAlarm);
    }

    @LogExecutionTime
    public DCAlarmDto cleanAlarm(final String alarmKey, final Long ts, DCAlarmDto cacheAlarm) {
        if (alarmKey == null) {
            return null;
        }
        if (cacheAlarm == null) {
            return null;
        }
        final String alarmId = cacheAlarm.getId();
        // 1.清理过期告警
        log.info("clean alarm id: {}. alarm key: {}", alarmId, alarmKey);
        jedisService.delKey(alarmKey);

        // 2.如果告警信息不为空，标记为最后一个告警
        // 2.1.更新告警信息到starRocks
        cacheAlarm.setFirst(false);
        cacheAlarm.setLast(true);
        // 设置告警结束时间
        cacheAlarm.setEndTriggerTime(System.currentTimeMillis() - (taskRefreshScopeConfig.getEventDefTimeOffset() - 60) * 1000);
        // 重新计算告警持续时间
//        cacheAlarm.setDuration((cacheAlarm.getEndTriggerTime() - cacheAlarm.getStartTriggerTime()) / 1000);
        cacheAlarm.resetDescription();
        olapTableWriter.write(JSONObject.toJSONBytes(cacheAlarm, SerializerFeature.DisableCircularReferenceDetect), OLAP_DATABASE, DC_ALARM);

        // 3.发送告警到Kafka
        try {
            kafkaUtil.producerSend(ALARM_TOPIC, alarmId, (JSONObject) JSONObject.toJSON(cacheAlarm));
        } catch (Exception e) {
            log.error("send alarm to kafka error, alarmId: {}", alarmId, e);
        }

        // 4.自监控数据上报
        Map<String, Number> fields = new HashMap<>();
        fields.put("trigger.cnt", 1L);
        fields.put("duration", cacheAlarm.getDuration());
        fields.put("eventCnt", cacheAlarm.getEventId().size());
        Map<String, String> tags = new HashMap<>();
        final JSONObject trigger = cacheAlarm.getTrigger();
        if (trigger != null) {
            trigger.forEach((k, v) -> tags.put(k, v.toString()));
        }
        tags.put("policyName", cacheAlarm.getPolicyName());
        tags.put("type", cacheAlarm.getType());
        OtelMetricUtil.logOriginalData("alarm.clean", fields, tags, ts);
        return null;
    }
}
