package com.databuff.tasks.service.impl;

import com.databuff.entity.dump.AgentDump;
import com.databuff.entity.dump.AgentDumpSearchCriteria;
import com.databuff.service.WebappDumpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Calendar;
import java.util.List;

/**
 * DCDumpFileCleanService 类用于清理 AgentDump 文件。
 */
@Slf4j
@Component
@RefreshScope
public class DCDumpFileCleanService {

    @Autowired
    private WebappDumpService webappDumpService;

    /**
     * 清理任务的时间周期，单位为小时。默认值为 168 小时（7 天）。
     */
    @Value("${databuff.clean-task.period-hours:168}")
    private int periodHours;

    /**
     * 执行定时清理任务，删除过期的AgentDump记录及其关联文件
     *
     * 功能说明：
     * 1. 根据periodHours配置的时间范围，清理早于该时间点的历史记录
     * 2. 删除流程包含两个步骤：
     *    - 删除文件系统中存储的dump文件
     *    - 删除数据库中对应的记录
     * 3. 采用防御性编程策略，处理过程中发生的异常不会中断整体清理流程
     *
     * 实现逻辑：
     * - 使用动态计算的时间点(endTime = now - periodHours)作为清理截止时间
     * - 对每个清理记录进行文件删除和数据库删除的原子操作
     * - 采用多层异常捕获机制保证任务可靠性
     */
    public void clean() {
        try {
            /* 构建带时间条件的查询参数 */
            AgentDumpSearchCriteria criteria = new AgentDumpSearchCriteria();
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.HOUR_OF_DAY, -periodHours);
            criteria.setEndTime(calendar.getTime());

            /* 查询需要清理的历史记录 */
            List<AgentDump> agentDumps = webappDumpService.searchAgentDumps(criteria);
            if (agentDumps == null || agentDumps.isEmpty()) return;

            /* 批量处理清理任务 */
            for (AgentDump agentDump : agentDumps) {
                try {
                    // 防御性空值检查
                    if (agentDump == null) {
                        log.warn("Found null AgentDump record during cleanup");
                        continue;
                    }

                    /* 文件系统清理模块 */
                    final String path = agentDump.getPath();
                    if (path != null) {
                        try {
                            Path filePath = Paths.get(path);
                            boolean deleted = Files.deleteIfExists(filePath);
                            if (deleted) {
                                log.info("Successfully deleted file: {}", path);
                            }
                        } catch (IOException e) {
                            log.error("Failed to delete file: {} - {}", path, e.getMessage());
                        }
                    }

                    /* 数据库记录清理 */
                    webappDumpService.deleteAgentDumpById(agentDump.getId());
                } catch (Exception e) {
                    /* 单个记录处理异常捕获 */
                    log.error("Error processing AgentDump ID {}: {}",
                            agentDump != null ? agentDump.getId() : "unknown", e.getMessage());
                }
            }
        } catch (Exception e) {
            /* 全局异常处理保障定时任务持续运行 */
            log.error("Scheduled cleanup task failed: {}", e.getMessage(), e);
        }
    }
}