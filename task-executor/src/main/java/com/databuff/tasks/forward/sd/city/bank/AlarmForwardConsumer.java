package com.databuff.tasks.forward.sd.city.bank;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.databuff.common.constants.KafkaTopicConstant;
import com.databuff.common.utils.HttpUtil;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.entity.dto.DCAlarmDto;
import com.databuff.tasks.forward.sd.city.bank.config.CityBankConfig;
import com.databuff.tasks.forward.sd.city.bank.dto.AlarmCityBankDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * AlarmForwardConsumer 负责从 Kafka 消费告警消息，处理它们，并将其转发到指定的 URL。
 *
 * @package com.databuff.tasks.forward.sd.city.bank
 * @company: 乘云数字 x 山东城商行
 * @author: yuzhili
 * @createDate: 2024/11/12
 */
@Slf4j
@Component
public class AlarmForwardConsumer {
    private static final String RESPONSE_CODE = "code";

    @Autowired
    private CityBankConfig cityBankConfig;
    private static final String METHOD_TAG = "method";


    /**
     * 检查配置中是否启用了告警转发。
     *
     * @return 如果启用则返回 true，否则返回 false。
     */
    private boolean isAlarmEnabled() {
        return cityBankConfig.getEnabled() != null && cityBankConfig.getEnabled();
    }
    private static final String PROCESS_RECORD_METHOD = "processRecord";
    private static final String CNT_FIELD = "cnt";
    private static final String COST_MS_FIELD = "costMs";
    private static final String SUCCESS = "success";

    /**
     * 监听 Kafka 主题以获取告警通知。
     *
     * @param records 包含告警消息的 ConsumerRecord 列表。
     */
    @KafkaListener(containerFactory = "kafkaListenerContainerFactory", groupId = "dc_alarm_sdcsh", topics = KafkaTopicConstant.ALARM_TOPIC)
    public void onAlarmNotify(List<ConsumerRecord<String, String>> records) {
        long start = System.currentTimeMillis();
        log.debug("开始处理Kafka告警消息，记录数量：{}", Optional.ofNullable(records).map(List::size).orElse(0));

        // 检查记录是否为空或告警是否未启用
        if (records == null || records.isEmpty() || !isAlarmEnabled()) {
            log.debug("告警处理未启用或消息为空，跳过本次处理");
            return;
        }

        records.stream().forEach(record -> {
            try {
                processRecord(record);
            } catch (JSONException e) {
                log.error("JSON解析错误，消息内容：key={}, value={}", record.key(), record.value(), e);
            } catch (Exception e) {
                log.error("处理消息时发生错误，消息内容：key={}, value={}", record.key(), record.value(), e);
            }
        });

        long cost = System.currentTimeMillis() - start;
        log.debug("告警处理完成，总耗时：{}ms", cost);
    }
    
    /**
     * 处理接收到的Kafka消费者记录，执行告警信息的解析、验证及发送操作。
     * @param record 消费者记录，包含键和需要处理的告警消息值
     */
    private void processRecord(ConsumerRecord<String, String> record) {
        long start = System.currentTimeMillis();
        String value = record.value();

        /*
         * 处理无效的记录值：当值为null或空字符串时记录日志并提前终止处理
         */
        if (value == null) {
            log.debug("记录值为null，跳过处理");
            return;
        }
        if (value.isEmpty()) {
            log.debug("记录值为空字符串，跳过处理");
            return;
        }

        /*
         * 反序列化JSON字符串为DCAlarmDto对象，禁用循环引用检测以提高安全性
         */
        try {
            DCAlarmDto alarm = JSON.parseObject(value, DCAlarmDto.class, Feature.DisableCircularReferenceDetect);

            /*
             * 验证API密钥有效性：当apiKey为空时记录日志并终止处理
             */
            if (alarm.getApiKey() == null) {
                log.debug("API密钥为空，跳过处理");
                return;
            }

            /*
             * 判断告警是否符合处理条件：若符合则格式化并发送告警，否则跳过
             */
            if (shouldProcessAlarm(alarm)) {
                AlarmCityBankDTO alarmCityBankDTO = AlarmCityBankDTO.formatAlarm(alarm, cityBankConfig);
                log.debug("格式化告警: {}", alarmCityBankDTO);
                sendAlarm(alarmCityBankDTO);
            } else {
                log.debug("告警不符合处理条件，跳过处理");
            }
        } catch (JSONException e) {
            /*
             * 捕获JSON解析异常，记录错误信息并标记处理失败
             */
            log.error("JSON解析失败，记录值: {}", value, e);
        } catch (Exception e) {
            /*
             * 捕获其他异常，记录错误信息并标记处理失败
             */
            log.error("处理告警时发生错误", e);
        } finally {
            /*
             * 记录处理耗时及状态指标：统计处理时间、成功与否等信息并上报
             */
            long cost = System.currentTimeMillis() - start;
            Map<String, Number> fields = new HashMap<>();
            fields.put(CNT_FIELD, 1L);
            fields.put(COST_MS_FIELD, cost);
            Map<String, String> tags = new HashMap<>();
            tags.put(METHOD_TAG, PROCESS_RECORD_METHOD);
            OtelMetricUtil.logOriginalData("alarm.forward.consumer.process", fields, tags, System.currentTimeMillis());
        }
    }


    private boolean shouldProcessAlarm(DCAlarmDto alarm) {
        final Boolean firstEnabled = cityBankConfig.getFirstEnabled();
        final Boolean lastEnabled = cityBankConfig.getLastEnabled();
        final Boolean allEnabled = cityBankConfig.getAllEnabled();

        // 检查告警处理条件
        boolean shouldProcess = allEnabled || (firstEnabled && alarm.getFirst()) || (lastEnabled && alarm.getLast());
        log.debug("告警处理条件：allEnabled={}, firstEnabled={}, lastEnabled={}, shouldProcess={}",
                allEnabled, firstEnabled, lastEnabled, shouldProcess);
        return shouldProcess;
    }

    private void sendAlarm(AlarmCityBankDTO alarmCityBankDTO) {
        long start = System.currentTimeMillis();
        List<AlarmCityBankDTO> alarmList = Lists.newArrayList(alarmCityBankDTO);
        String url = cityBankConfig.getUrl();
        Map<String, String> headers = cityBankConfig.getHeaders();

        // 检查URL是否为空
        if (url == null) {
            log.error("目标URL为空");
            return;
        }

        // 验证URL有效性
        if (!isValidURL(url)) {
            log.error("无效的目标URL：{}", url);
            return;
        }

        try {
            // 设置HTTP超时参数（假设方法支持）
            String response = HttpUtil.postString(url, headers, JSON.toJSONString(alarmList));
            JSONObject jsonResponse = JSONObject.parseObject(response);
            boolean success = jsonResponse != null && jsonResponse.containsKey(RESPONSE_CODE)
                    && jsonResponse.getIntValue(RESPONSE_CODE) == 200;
            if (success) {
                log.debug("告警发送成功，响应：{}", jsonResponse);
            } else {
                log.error("告警发送失败，响应：{}", jsonResponse);
            }

            long cost = System.currentTimeMillis() - start;
            recordMetrics(success, cost, alarmCityBankDTO, System.currentTimeMillis());
    
        } catch (Exception e) {
            log.error("发送告警时发生异常，URL: {}", url, e);
            long cost = System.currentTimeMillis() - start;
            recordMetrics(false, cost, alarmCityBankDTO, System.currentTimeMillis());
        }
    }

    private void recordMetrics(boolean success, long cost, AlarmCityBankDTO alarmDTO, long timestamp) {
        Map<String, Number> fields = new HashMap<>();
        fields.put("cnt", 1L);
        fields.put("costMs", cost);
        if (!success) {
            fields.put("errorCnt", 1L);
        }

        Map<String, String> tags = new HashMap<>();
        tags.put("method", "sendAlarm");
        tags.put("success", Boolean.toString(success));
        tags.put("sourceCIName", Optional.ofNullable(alarmDTO.getSourceCIName()).orElse("N/A"));
        tags.put("severity", Optional.ofNullable(alarmDTO.getSeverity()).orElse("N/A"));
        tags.put("sourceSeverity", Optional.ofNullable(alarmDTO.getSourceSeverity()).orElse("N/A"));
        OtelMetricUtil.logOriginalData("alarm.forward.consumer.sendAlarm", fields, tags, timestamp);
    }


    private boolean isValidURL(String url) {
        try {
            new URL(url);
            return true;
        } catch (MalformedURLException e) {
            return false;
        }
    }
}
