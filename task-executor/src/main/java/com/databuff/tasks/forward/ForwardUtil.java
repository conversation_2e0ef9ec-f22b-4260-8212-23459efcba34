package com.databuff.tasks.forward;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * @author:TianMing
 * @date: 2024/9/6
 * @time: 15:20
 */
public class ForwardUtil {

    public static Boolean isAllowForward(String sendFrequencyStr, JSONObject msg) {
        if (StringUtils.isBlank(sendFrequencyStr)) {
            sendFrequencyStr = "1";
        }
        List<String> sendFrequencys = Arrays.asList(sendFrequencyStr.split(","));
        for (String frequency : sendFrequencys) {
            Integer sendFrequency = Integer.valueOf(frequency);
            if (sendFrequency == 0 && msg.getBooleanValue("last")) {
                //最后一条消息发送
                return true;
            } else if (sendFrequency == 1 && msg.getBooleanValue("first")) {
                //第一条发送
                return true;
            } else if (sendFrequency == 2){
                //每条都发
                return true;
            }
        }
        return false ;
    }

}
