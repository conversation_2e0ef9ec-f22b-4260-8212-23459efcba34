package com.databuff.tasks.config;

import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR>
 * @Date 2022/9/29
 * @Description:
 */
@Configuration
public class KafkaConfig {

    @Value("${kafka.bootstrap-servers:kafka:9092}")
    public String kafkaIp;

    @Bean
    public KafkaProducer kafkaProducer() {
        Properties props = new Properties();

        props.putAll(producerConfigs());
        //发送端初始化
        return new KafkaProducer<String, String>(props);
    }

    private Map<String, Object> producerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put("bootstrap.servers", kafkaIp);
        /**
         * 消息存储是是否需要应答
         */
        props.put("acks", "1");
        props.put("retries", 0);
        props.put("batch.size", 16384);
        props.put("linger.ms", 500);
        props.put("buffer.memory", 33554432);
        // 指定key value的序列化方式
        props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "lz4");
        return props;
    }

}

