package com.databuff.tasks.scheduler;

import com.databuff.tasks.monitor.MultipleMetricMonitorTimer;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 多指标监控调度器。
 * 此类负责调度多指标监控任务。
 * 注意：此bean不能使用 @Value + @RefreshScope 一起使用，否则会导致定时任务失效。
 */
@Component
public class MultipleMetricMonitorScheduler {

    @Autowired
    private MultipleMetricMonitorTimer multipleMetricMonitorTimer;

//    @Scheduled(cron = "*/10 * * * * ?")
    @XxlJob("sharding-multipleMetricThresholdMonitor-task")
    public void scheduleMetricMonitor() {
        multipleMetricMonitorTimer.multipleMetricMonitorTimer();
    }
}