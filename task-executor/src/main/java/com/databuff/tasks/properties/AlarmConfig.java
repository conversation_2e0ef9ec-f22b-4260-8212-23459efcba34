package com.databuff.tasks.properties;

import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@ConfigurationProperties(prefix = "alarm")
@Component
@Data
@RefreshScope
public class AlarmConfig {

    /**
     * 压测事件开关
     */
    @Value("${databuff.test.event.enabled:false}")
    private boolean eventStressTestEnabled;

    /**
     * triggerTime过期20分钟，不执行【预置】智能AI收敛
     */
    @Value("${expiredOffset:20}")
    private long expiredOffset;

    /**
     * 事件过期时间（单位：秒），超过此时间的事件将被过滤掉不进行处理
     */
    @Value("${databuff.event.expireTime:300}")
    private long eventExpireTime;

    @Autowired
    private Cron cron;
    @Autowired
    private Subscribe subscribe;

    @Component
    @Data
    @RefreshScope
    public static class Cron {
        @Value("${enabled:false}")
        private boolean enabled;

        public boolean isEnabled() {
            return this.enabled;
        }

        public boolean getEnabled() {
            return this.enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
    }

    @Component
    @Data
    @RefreshScope
    public static class Subscribe {
        @Value("${enabled:true}")
        private boolean enabled;

        /**
         * AI告警过期时间（单位：秒）
         */
        @Value("${expireTime:180}")
        private Long expireTime;

        public boolean isEnabled() {
            return this.enabled;
        }

        public boolean getEnabled() {
            return this.enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
    }

}
