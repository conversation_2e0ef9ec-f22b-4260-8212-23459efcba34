package com.databuff.tasks.cleanTask;

import com.databuff.tasks.service.XxlJobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
@EnableScheduling
public class CleanXxlJobTimer {

    @Resource
    private XxlJobService xxlJobService;
    @Scheduled(cron = "0 0 0 * * ?")
    public void cleanXxlJobLog(){
        long startTime=System.currentTimeMillis();
        log.info("定时清理xxl-job log 任务开始");
        xxlJobService.clearLog();
        log.info("定时清理xxl-job log 任务结束,耗时:{} ms",System.currentTimeMillis()-startTime);
    }
}
