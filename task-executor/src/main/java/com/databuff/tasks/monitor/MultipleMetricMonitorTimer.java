package com.databuff.tasks.monitor;

import com.alibaba.fastjson.JSONObject;
import com.databuff.common.exception.RecordTimeExceptionHandlingRunnable;
import com.databuff.common.threadLocal.NowTimeThreadLocal;
import com.databuff.common.threadLocal.ThreadLocalUtil;
import com.databuff.common.tsdb.dto.detect.DetectQueryRequest;
import com.databuff.common.tsdb.dto.detect.MultiDetectQueryRequest;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.common.tsdb.dto.preview.ThresholdsDTO;
import com.databuff.common.tsdb.model.LogicalOperator;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.entity.DatabuffMonitor;
import com.databuff.entity.EventEntity;
import com.databuff.entity.dto.DatabuffMonitorView;
import com.databuff.metric.AlarmCheckOperatorV2;
import com.databuff.tasks.service.MonitorService;
import com.databuff.tasks.util.ShardingUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.databuff.common.constants.Constant.CURRENT_MONITOR_COUNT;
import static com.databuff.common.utils.TimeUtil.ONE_MIN_MS_LONG;
import static com.databuff.common.utils.TimeUtil.roundDownToMinute;
import static com.databuff.metric.moredb.SQLParser.*;

/**
 * 指标监控规则计算
 *
 * @package com.databuff.webapp.task.monitor
 * @company: dacheng
 * @author: zlh
 * @createDate: 2021/9/22
 */
@Component
@Slf4j
public class MultipleMetricMonitorTimer extends MonitorTimerBase {
    @Autowired
    private MonitorService monitorService;
    @Autowired
    private MonitorTaskPool monitorTaskPool;
    /**
     * 当前统计时间 毫秒
     */
    private long currentStatTime;


    public void multipleMetricMonitorTimer() {
        int shardIndex = XxlJobHelper.getShardIndex();
        int shardTotal = XxlJobHelper.getShardTotal();
        log.info("sharding-multipleMetricThresholdMonitor-task 开始多指标监控引擎 ,分片参数：当前分片序号 = {}, 总分片数 = {}", shardIndex, shardTotal);

        // 当前统计时间
        long curTime = System.currentTimeMillis();
        //1、计算查询起始时间
        currentStatTime = (curTime / 1000 - curTime / 1000 % 60) * 1000;
        // 根据条件查询监控
        List<DatabuffMonitorView> monitorList = monitorService.findAllMonitorByClassfy("multipleMetric", true);
        List<DatabuffMonitorView> shardingMonitors = ShardingUtil.sharding(monitorList, shardIndex, shardTotal);
        if (CollectionUtils.isEmpty(shardingMonitors)) {
            return;
        }

        // 当前正在执行监控的count
        int monitoringCount = shardingMonitors.size();
        OtelMetricUtil.logCounter(CURRENT_MONITOR_COUNT, monitoringCount);
        log.info("sharding-multipleMetricThresholdMonitor-task 当前正在执行监控的count = {}", monitoringCount);

        for (DatabuffMonitorView shardingMonitor : shardingMonitors) {
            Runnable task = new RecordTimeExceptionHandlingRunnable(() -> {
                NowTimeThreadLocal.setNowTime(curTime);
                try {
                    // 参数：截取的每个线程需要处理的监控，主机list,标签及apiKey
                    ThreadLocalUtil.doWithThreadLocal(() -> doMonitor(shardingMonitor));
                } catch (Exception e) {
                    log.error("multipleMetric mid:{},doMonitor error:", shardingMonitor.getId(), e);
                }
            });
            monitorTaskPool.submitWorker(task);
        }
        log.info("---执行多指标监控引擎任务消耗了 ：" + (System.currentTimeMillis() - curTime) + "毫秒");
    }

    /**
     * 监控事件计算
     *
     * @param m
     */
    private void doMonitor(DatabuffMonitorView m) {
        List<JSONObject> events = new ArrayList<>();
        final MultiDetectQueryRequest allQuery = m.getQuery();

        Map<String, Map<Object, Object>> allMetricCheakRets = new HashMap<>(5);
        // 计算静默情况
        Set<Object> groupSet = new HashSet<>(16);
        int ruleSize = 0;
        log.debug("【多指标】开始处理多指标监控：{}=============", m.getRuleName());
        final Set<String> by = new HashSet<>();

        // 多指标监控最多只能配置5个指标
        final Set<String> metricIndex = Sets.newHashSet("1", "2", "3", "4", "5");

        Map<String, Set<Map<String, String>>> delayListMap = new HashMap<>();
        final String ruleName = m.getRuleName();
        final String gid = m.getGid();
        final Collection<String> metrics = allQuery.findMetrics();

        for (Map.Entry<String, DetectQueryRequest> detectQueryRequestEntry : allQuery.buildNonNullQueriesMap().entrySet()) {
            Map<Object, Object> checkDataRets = new HashMap<>();
            final String mKey = detectQueryRequestEntry.getKey();
            final DetectQueryRequest detectQueryRequest = detectQueryRequestEntry.getValue();
            if (detectQueryRequest == null) {
                continue;
            }
            if (!metricIndex.contains(mKey)) {
                continue;
            }
            log.debug("【多指标】开始处理多指标监控：{}下指标{}=============", ruleName, mKey);

            final String way = detectQueryRequest.getWay();
            if (way == null) {
                log.warn("【多指标】多指标监控：{}下指标{}找不到检测方法，结束", ruleName, mKey);
                return;
            }
            final EventEntity.DetectionType detectionType = EventEntity.DetectionType.valueOf(way);
            AlarmCheckOperatorV2 checkOperator = getAlarmCheckOperatorV2(way);
            ruleSize++;

            Integer evaluationDelay = detectQueryRequest.getEvaluationDelay();
            Integer noDataTimeframe = detectQueryRequest.getNoDataTimeframe();
            if (noDataTimeframe > 0) {
                log.debug("【多指标】多指标监控：{}下指标{}开始无数据结果集查询===============", m.getRuleName(), mKey);
                // 无数据结果集获取
                Set<Map<String, String>> noDataResult = metricAggregator.noDataResult(m, detectQueryRequest.getNonNullQueries());
                log.debug("【多指标】多指标监控：{}下指标{}无数据结果集数量：{}，并开始无数据结果集告警等级判断===============", m.getRuleName(), mKey, noDataResult.size());
                //无数据判断是否生成
                checkDataRets.putAll(checkOperator.afterCheckNoDataResult(new DatabuffMonitor(m), mKey, noDataResult, detectQueryRequest));
            }

            if (evaluationDelay > 0) {
                delayListMap.put(mKey, metricAggregator.delayResult(m, detectQueryRequest.getNonNullQueries(), evaluationDelay));
            }

            final String expr = detectQueryRequest.getExpr();

            final Collection<QueryRequest> queryRequests = detectQueryRequest.getNonNullQueries();
            if (queryRequests == null) {
                continue;
            }

            final Collection<String> gids = Lists.newArrayList(m.getGid());

            for (QueryRequest queryRequest : queryRequests) {
                if (queryRequest == null) {
                    continue;
                }

                // 如果是系统类型的检测规则，则可以查询所有对象
                final boolean allPermission = gid == null || m.getSystem();

                //将当前统计时间放入
                queryRequest.setCurrentStatTime(currentStatTime);
                Integer period = queryRequest.getPeriod();
                // 变异检测周期为1小时
                if (detectionType == EventEntity.DetectionType.changePoint) {
                    period = 60 * 60;
                } else if (period == null) {
                    // 检测周期 秒,默认5分钟
                    period = 300;
                }
                queryRequest.setApiKey(m.getApiKey());
                queryRequest.setPeriod(period);
                queryRequest.setTimeOffset(taskRefreshScopeConfig.getEventDefTimeOffset());
                queryRequest.setGids(gids);
                queryRequest.setAllPermission(allPermission);
                queryRequest.setDomainManagerStatusOpen(domainManagerObjService.getDomainManagerStatusOpen());

                final Collection<String> groupBy = queryRequest.getBy();
                if (groupBy != null) {
                    for (Map.Entry<String, String> entry : keyValueMap.entrySet()) {
                        if (groupBy.contains(entry.getKey())) {
                            groupBy.add(entry.getValue());
                        }
                    }
                }
            }

            log.debug("【多指标】多指标监控：{}下指标{}开始获取查询结果===============", ruleName, mKey);
            final Map<String, QueryRequest> queriesMap = detectQueryRequest.getNonNullQueriesMap();
            Map<Map, Map<Object, Double>> aggTimeSeries = metricAggregator.aggResult(expr, queriesMap);
            if (CollectionUtils.isEmpty(aggTimeSeries) && checkDataRets.isEmpty()) {
                log.warn("【多指标】多指标监控：{}下指标{}获取结果为空", ruleName, mKey);
                return;
            }
            // 是否周期内完整数据，不完整不评估
            //阈值比较
            log.debug("【多指标】多指标监控：{}下指标{}数据结果集数量：{}，并开始数据结果集告警等级判断===============", m.getRuleName(), mKey, aggTimeSeries.size());
            Map<Object, Object> checkRets = checkOperator.afterCheckResult(new DatabuffMonitor(m), aggTimeSeries, detectQueryRequest, metricAggregator, queriesMap.values());
            log.debug("【多指标】多指标监控：{}下指标{}数据结果集数量：{}，告警等级结果集数量：{}===============", m.getRuleName(), mKey, aggTimeSeries.size(), checkRets.size());
            // 是否周期内完整数据，不完整不评估
            //阈值比较
            if (checkRets != null) {
                checkRets.forEach((key, value) -> checkDataRets.putIfAbsent(key, value));
            }
//            checkOperator.needMoreTags(checkDataRets, queryRequests, gid == null ? Lists.newArrayList() : Lists.newArrayList(gid));
            //获取所有分组
            groupSet.addAll(checkDataRets.keySet());
            allMetricCheakRets.put(mKey, checkDataRets);
        }

        log.debug("【多指标】多指标监控：{} 各指标告警等级结果集数量：{}===============", m.getRuleName(), allMetricCheakRets.size());
        //3
        LogicalOperator criticalConnector = allQuery.getCritical();
        //2
        LogicalOperator warningConnector = allQuery.getWarning();
        //1
        LogicalOperator noDataConnector = allQuery.getNoData();

        for (Object groupObj : groupSet) {
            try {
                int criticalCount = 0;
                List<EventEntity> criticalEventList = new ArrayList<>();
                int warnCount = 0;
                List<EventEntity> warnEventList = new ArrayList<>();
                int noDataCount = 0;
                List<EventEntity> noDataEventList = new ArrayList<>();
                List<EventEntity> normalEventList = new ArrayList<>();
                for (Map.Entry<String, Map<Object, Object>> entry : allMetricCheakRets.entrySet()) {
                    String mKey = entry.getKey();
                    final Set<Map<String, String>> delayList = delayListMap.get(mKey);
                    if (delayList != null && delayList.contains(groupObj)) {
                        log.debug("【多指标】多指标监控：{}下指标{}分组{}存在延迟数据，跳过===============", m.getRuleName(), mKey, groupObj);
                        continue;
                    }
                    Map<Object, Object> checkDataRets = entry.getValue();
                    EventEntity eventEntity = (EventEntity) checkDataRets.get(groupObj);
                    if (eventEntity == null) {
                        //这个对象的这个指标一直没有这个分组
                        continue;
                    }
                    eventEntity.setMKey(mKey);
                    int status = eventEntity.getStatus();
                    if (status == 3) {
                        //critical
                        criticalCount++;
                        criticalEventList.add(eventEntity);
                        //如果是重要，这里判断肯定也触发了次要的判断数量也得+1
                        warnCount++;
                        EventEntity warnEventEntity = EventEntity.builder()
                                .way(eventEntity.getWay())
                                .threshold(eventEntity.getThreshold())
                                .existsEvent(eventEntity.isExistsEvent())
                                .status(2)
                                .group(eventEntity.getGroup())
                                .value(eventEntity.getValue())
                                .mKey(mKey)
                                .build();
                        warnEventList.add(warnEventEntity);
                    } else if (status == 2) {
                        //warning
                        warnCount++;
                        warnEventList.add(eventEntity);
                    } else if (status == 1) {
                        //nodata
                        noDataCount++;
                        noDataEventList.add(eventEntity);
                    } else {
                        normalEventList.add(eventEntity);
                    }
                }

                JSONObject trigger = new JSONObject();
                if (groupObj != null && groupObj instanceof Map) {
                    Map<?, ?> tempMap = (Map<?, ?>) groupObj;
                    for (Map.Entry<?, ?> entry : tempMap.entrySet()) {
                        Object key = entry.getKey();
                        if (key instanceof String) {
                            String strKey = (String) key;
                            if (strKey != null) {
                                trigger.put(strKey, entry.getValue());
                            }
                        }
                    }
                }

                log.debug("【多指标】多指标监控：{} 对象：{},总检测指标数:{},符合重要数:{},符合次要数:{},符合无数据数:{},===============", m.getRuleName(), trigger.toJSONString(), criticalCount, warnCount, noDataCount);


                List<EventEntity> allEventEntitys = new ArrayList<>();
                allEventEntitys.addAll(criticalEventList);
                allEventEntitys.addAll(warnEventList);
                allEventEntitys.addAll(noDataEventList);
                allEventEntitys.addAll(normalEventList);
                if (CollectionUtils.isEmpty(allEventEntitys)) {
                    continue;
                }
                List<EventEntity> triggerEventEntitys = new ArrayList<>();
                int status = 0;
                if (LogicalOperator.AND.equals(criticalConnector)) {
                    if (criticalCount >= ruleSize) {
                        //重要的条数大于等于总条数，说明所有的都是重要的
                        //生成重要事件
                        triggerEventEntitys.addAll(criticalEventList);
                        status = 3;
                    }
                } else if (LogicalOperator.OR.equals(criticalConnector)) {
                    if (criticalCount > 0) {
                        //重要的条数大于1条
                        //生成重要事件
                        triggerEventEntitys.addAll(criticalEventList);
                        status = 3;
                    }
                }
                //只有状态还是0的时候才计算次要和无数据
                if (status == 0 && LogicalOperator.AND.equals(warningConnector)) {
                    if (warnCount >= ruleSize) {
                        //次要的条数大于等于总条数
                        //生成次要事件
                        triggerEventEntitys.addAll(warnEventList);
                        status = 2;
                    }
                } else if (status == 0 && LogicalOperator.OR.equals(warningConnector)) {
                    if (warnCount > 0) {
                        //次要的条数大于1条
                        //生成次要事件
                        triggerEventEntitys.addAll(warnEventList);
                        status = 2;
                    }
                }
                //只有状态还是0的时候才计算次要和无数据
                if (status == 0 && LogicalOperator.AND.equals(noDataConnector)) {
                    if (noDataCount >= ruleSize) {
                        //无数据的条数大于等于总条数
                        //生成无数据事件
                        triggerEventEntitys.addAll(noDataEventList);
                        status = 1;
                    }
                } else if (status == 0 && LogicalOperator.OR.equals(noDataConnector)) {
                    if (noDataCount > 0) {
                        //无数据的条数大于0
                        //生成无数据事件
                        triggerEventEntitys.addAll(noDataEventList);
                        status = 1;
                    }
                }
                if (status == 0) {
                    //正常事件处理
                    triggerEventEntitys.addAll(normalEventList);
                }
                if (triggerEventEntitys.isEmpty()) {
                    log.debug("【多指标】多指标监控：{} 对象：{} eventEntitys为空 ===============", m.getRuleName(), trigger.toJSONString());
                    continue;
                }
                // 事件记录
                JSONObject event = this.getEventLogJson(allEventEntitys, status, m, allQuery, trigger);
                event.put(TAGS, trigger);
                event.put("metrics", metrics);
                event.put(BY, by);
                log.debug("【多指标】多指标监控：{} 触发对象：{} 生成事件：{} ===============", m.getRuleName(), trigger.toJSONString(), event);
                events.add(event);
            } catch (Exception e) {
                log.error(String.format("【多指标】多指标监控：%s 对象：%s 判断告警异常", m.getRuleName(), groupObj), e);
            }
        }
        //事件告警，故障场景，连续性，以及变量填充后发送库及队列
        monitorUtils.eventSinkDBMq(events, m);

    }


    /**
     * 生成事件记录
     *
     * @param eventEntitys
     * @param status          当前状态
     * @param monitor         当前监控
     * @return 事件记录
     */
    private JSONObject getEventLogJson(List<EventEntity> eventEntitys, int status, DatabuffMonitorView monitor, MultiDetectQueryRequest allQuery, JSONObject trigger) {

        final Long nowTime = NowTimeThreadLocal.getNowTime();
        // 触发时间 = endTime-1分钟
        final long triggerTime = roundDownToMinute(nowTime) - taskRefreshScopeConfig.getEventDefTimeOffset() * 1000L - ONE_MIN_MS_LONG;

        // 初始化事件记录json
        JSONObject event = monitorUtils.initMultipleMetricEventJson(monitor, status, trigger, triggerTime);
        // 多指标监控最多只能配置5个指标
        final Set<String> metricIndex = Sets.newHashSet("1", "2", "3", "4", "5");
        Map<String, ThresholdsDTO> mKeyQueryMap = new HashMap<>(5);
        final Map<String, DetectQueryRequest> nonNullQueriesMap = allQuery.buildNonNullQueriesMap();
        for (Map.Entry<String, DetectQueryRequest> entry : nonNullQueriesMap.entrySet()) {
            final String mKey = entry.getKey();
            final DetectQueryRequest detectQueryRequest = entry.getValue();
            if (!metricIndex.contains(mKey) || detectQueryRequest == null) {
                continue;
            }
            ThresholdsDTO thresholds = detectQueryRequest.getThresholds();
            thresholds.setComparison(detectQueryRequest.getComparison());
            thresholds.setUnit(detectQueryRequest.getUnit());
            thresholds.setCheckName(mKey);
            String exprName = detectQueryRequest.getExprName();
            if (StringUtils.isNotBlank(exprName)) {
                //表达式名称 后续无数据生成告警 用
                thresholds.setMName(exprName);
            } else {
                //指标名称 后续无数据生成告警 用
                thresholds.setMName(detectQueryRequest.findMetrics().toString());
            }
            mKeyQueryMap.put(mKey, thresholds);
        }

        for (EventEntity eventEntity : eventEntitys) {
            if (eventEntity == null) {
                continue;
            }
            final ThresholdsDTO thresholds = mKeyQueryMap.get(eventEntity.getMKey());

            if (!eventEntity.isNoData()) {
                thresholds.setValue(eventEntity.getValue());
            }

            if (eventEntity.getComparison() != null) {
                thresholds.setComparison(eventEntity.getComparison());
            }

            if (status == 1) {
                // 无数据不需要阈值判断
                thresholds.setTrgTrd("无数据");
                thresholds.setTrgValue("空");
            } else {
                String threshold = eventEntity.getThreshold();
                // 单位格式化
                threshold = monitorUtils.unitFormat(threshold, thresholds.getUnit(), thresholds.getUnit());
                //此处产品更改1.30
                //阈值：即监控中设置的阈值；显示的单位优先以阈值设置时的单位为基准；当是静态阈值时，显示为“静态阈值X”；当是动态基线时，显示为“动态基线Y”；
                final EventEntity.DetectionType way = eventEntity.getWay();
                if (way != null) {
                    thresholds.setTrgTrd(way.getName() + threshold);
                } else {
                    thresholds.setTrgTrd(EventEntity.DetectionType.threshold.getName() + threshold);
                }
                String value = eventEntity.getValue() + "";
                value = monitorUtils.unitFormat(value, thresholds.getUnit(), thresholds.getUnit());
                thresholds.setTrgValue("实际值" + value);
            }
            mKeyQueryMap.put(eventEntity.getMKey(), thresholds);
        }
        // todo 算法不在需要 multithresholds, thresholds.
        event.put("multithresholds", mKeyQueryMap);

        // 状态大于0生成事件
        if (status > 0) {
            event.put("triggerObjType", String.join(",", trigger.keySet()));
            event.put(TRIGGER, trigger);
            String eventMessage = monitor.getMessage();
            if (allQuery != null) {
                // todo 前端还需要query对象去查询指标趋势图，暂不能删除
                event.put("query", allQuery);
            }
            String triggerThreshold = "无数据";
            if (status == 1) {
                // 无数据不需要阈值判断
                event.put("trgTrd", triggerThreshold);
                event.put("message", eventMessage);
                // 无数据事件封装完成返回
                return event;
            }
            event.put("message", eventMessage);
        }
        return event;
    }
}
