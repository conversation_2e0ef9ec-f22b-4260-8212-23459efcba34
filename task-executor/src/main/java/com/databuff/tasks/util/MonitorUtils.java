package com.databuff.tasks.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.databuff.common.constants.KafkaTopicConstant;
import com.databuff.common.constants.RedisKeyConstants;
import com.databuff.common.exception.ExceptionHandlingRunnable;
import com.databuff.common.tsdb.dto.CompositeCondition;
import com.databuff.common.tsdb.dto.detect.DetectQueryRequest;
import com.databuff.common.tsdb.dto.detect.MultiDetectQueryRequest;
import com.databuff.common.tsdb.dto.explore.QueryRequest;
import com.databuff.common.tsdb.dto.preview.ThresholdsDTO;
import com.databuff.common.tsdb.model.TSDBSeries;
import com.databuff.common.tsdb.model.WhereOp;
import com.databuff.common.utils.ConditionalVerifyUtil;
import com.databuff.common.utils.EventIdGenerator;
import com.databuff.common.utils.JSONUtils;
import com.databuff.common.utils.UnitFormatUtil;
import com.databuff.dao.mysql.BusinessMapper;
import com.databuff.dao.mysql.HostInfoEntityMapper;
import com.databuff.dao.mysql.MonitorMapper;
import com.databuff.entity.*;
import com.databuff.entity.dto.DatabuffMonitorView;
import com.databuff.entity.dto.KeyValue;
import com.databuff.metric.MetricAggregator;
import com.databuff.service.*;
import com.databuff.tasks.config.TaskRefreshScopeConfig;
import com.databuff.tasks.util.expiry.HostInfoExpiry;
import com.databuff.util.KafkaUtil;
import com.databuff.util.MetricsUtil;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.databuff.common.constants.CommonConstants.TEST_TAG;
import static com.databuff.common.constants.Constant.API_KEY2;
import static com.databuff.common.constants.Constant.DF_API_KEY_VALUE;
import static com.databuff.common.constants.Constant.Kafka.METRIC_TOPIC;
import static com.databuff.common.constants.Constant.Trace.*;
import static com.databuff.common.constants.TSDBIndex.METRIC_DATABUFF_ALARM_BUS;
import static com.databuff.entity.dto.DCEventDto.*;
import static com.databuff.metric.moredb.SQLParser.*;

/**
 * 监控通用方法抽取
 *
 * @package com.databuff.webapp.task.util
 * @company: dacheng
 * @author: zlh
 * @createDate: 2021/10/14
 */
@Component
@Slf4j
public class MonitorUtils {

    @Autowired
    private TaskRefreshScopeConfig taskRefreshScopeConfig;

    @Autowired
    private MetricAggregator metricsAggregator;

    public static final String K_8_S_NAMESPACE = "k8sNamespace";

    /**
     * 用于存储主机名的缓存。
     * 该缓存是使用 Guava 的 CacheBuilder 构建的，配置如下：
     * - 最大大小：缓存可以容纳的最大条目数为 9999。一旦达到此大小，缓存将开始根据其逐出策略逐出条目。
     * - 访问后过期：缓存中的每个条目将在 2 分钟没有读取或写入访问后过期并被删除。
     * - 写入后过期：缓存中的每个条目将在 4 分钟无写访问后过期并被删除。
     * - 弱键：缓存的键是弱引用。这意味着如果没有其他对该键的引用，垃圾收集器可以回收它，并且缓存中的相应条目将被删除。
     * - 软值：缓存的值是软引用。这意味着如果内存不足，垃圾收集器可以回收它们。
     * - 记录统计数据：缓存被配置为记录有关其活动的统计数据，例如命中率、逐出计数等。
     */
    private final Cache<String, HostInfoEntity> cacheHostNames = Caffeine.newBuilder()
            .expireAfter(new HostInfoExpiry())
            .maximumSize(9999)
            .weakKeys()
            .softValues()
            .recordStats()
            .build();

    private final Cache<String, String> cacheServiceNameSpace = Caffeine.newBuilder()
            .maximumSize(9999)
            .expireAfterAccess(2, TimeUnit.MINUTES)
            .expireAfterWrite(4, TimeUnit.MINUTES)
            .weakKeys()
            .softValues()
            .recordStats()
            .build();

    private final Cache<String, Set<Integer>> svcIdBusIdMap = Caffeine.newBuilder()
            .maximumSize(9999)
            .recordStats()
            .build();

    private final Cache<Integer, Business> busMap = Caffeine.newBuilder()
            .maximumSize(9999)
            .recordStats()
            .build();

    private final String colon = ":";
    @Autowired
    private KafkaUtil kafkaUtil;

    @Autowired
    private JedisService jedisService;

    @Autowired
    private MonitorMapper monitorMapper;

    @Autowired
    private BizInfoService bizInfoService;

    @Autowired
    private MetricsQueryService metricsQueryService;

    @Autowired
    private HostInfoEntityMapper hostInfoEntityMapper;

    @Autowired
    protected BusinessService businessService;
    @Autowired
    private BusinessMapper businessMapper;

    @Autowired
    private DomainManagerObjService domainManagerObjService;

    private final ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread thread = new Thread(r);
        thread.setName("Monitor-init-Thread");
        return thread;
    });

    @PostConstruct
    private void init() {
        loadBusMap();
        scheduledExecutorService.scheduleAtFixedRate(
                new ExceptionHandlingRunnable(() -> loadBusMap()), 1, 1, TimeUnit.MINUTES);
    }

    private void loadBusMap() {
        List<KeyValue> svcIdBusIds = businessMapper.getBusSvcIds();
        Map<String, Set<Integer>> svcIdBusIdMap = svcIdBusIds.stream()
                .collect(Collectors.groupingBy(
                        kv -> kv.getKey(),
                        Collectors.mapping(kv -> (Integer) kv.getValue(), Collectors.toSet())));
        if (!CollectionUtils.isEmpty(svcIdBusIdMap)) {
            // 清除缓存中的所有条目
            this.svcIdBusIdMap.invalidateAll();
            this.svcIdBusIdMap.putAll(svcIdBusIdMap);
        }

        Map<Integer, Business> busNameMap = businessService.getBusNameMap();
        if (!CollectionUtils.isEmpty(busNameMap)) {
            // 清除缓存中的所有条目
            this.busMap.invalidateAll();
            this.busMap.putAll(busNameMap);
        }
    }

    /**
     * 初始化事件记录json
     *
     * @param monitor
     * @param status
     * @param value
     * @param options
     * @param groupValue
     * @return
     */
    public JSONObject initEventLogJson(DatabuffMonitorView monitor, int status, double value,
                                       String unit, String groupValue, long triggerTime) {
        // 是否静默判断
        int silence = 0;
        // 组装事件记录
        JSONObject event = new JSONObject();
        if (monitor != null) {
            event.put(CLASSIFICATION, monitor.getClassification());
            event.put(API_KEY2, monitor.getApiKey());
            event.put(MONITOR_ID, monitor.getId());
            event.put(RULE_NAME, monitor.getRuleName());
            event.put("type", monitor.getType());
            event.put("monitorTags", JSONArray.parseArray(monitor.getTags()));
            event.put("priority", monitor.getPriority());
        }
        event.put("silence", silence);
        event.put("source", "DataBuff");
        event.put("group", groupValue);
        event.put("status", status);
        event.put("triggerTime", triggerTime);
        //事件记录实际值
        event.put("value", value);
        event.put("unit", unit);
        return event;
    }

    /**
     * 初始化事件记录json
     *
     * @param monitor
     * @param status
     * @return
     */
    public JSONObject initMultipleMetricEventJson(DatabuffMonitorView monitor, int status,
                                                  JSONObject trigger, long triggerTime) {
        // 是否静默判断
        int silence = 0;
        // 组装事件记录
        JSONObject event = new JSONObject();
        event.put(RULE_NAME, monitor.getRuleName());
        event.put(CLASSIFICATION, monitor.getClassification());
        event.put("silence", silence);
        event.put(API_KEY2, monitor.getApiKey());
        event.put("source", "DataBuff");
        event.put("group", JSONUtils.jsonObjectToString(trigger));
        event.put(MONITOR_ID, monitor.getId());
        event.put("status", status);
        event.put("monitorTags", JSONArray.parseArray(monitor.getTags()));
        event.put("triggerTime", triggerTime);
        event.put("priority", monitor.getPriority());
        return event;
    }


    /**
     * 获取状态中文值
     *
     * @param status
     * @return
     */
    public String getStatusCn(int status) {
        switch (status) {
            case 3:
                return "重要";
            case 2:
                return "次要";
            case 1:
                return "无数据";
            default:
                return "正常";
        }
    }

    public String unitFormat(String value, String unit, String format) {
        if (StringUtils.isBlank(value)) {
            return value;
        }
        return UnitFormatUtil.humanReadableFormat(Double.valueOf(value), unit, format);
    }

    public String unitFormat(Double value, String unit, String format) {
        if (value == null) {
            return UnitFormatUtil.humanReadableFormat(0D, unit, format);
        }
        return UnitFormatUtil.humanReadableFormat(Double.valueOf(value), unit, format);
    }

    /**
     * 变量替换
     *
     * @return
     */
//    public JSONObject replaceParam(JSONObject event) {
//        if (event == null) {
//            return null;
//        }
//
//        if (event.getInteger("status") == 0) {
//            return event;
//        }
//        final JSONObject tags = event.getJSONObject(TAGS);
//        final Collection<String> by = event.getObject(BY, Collection.class);
//        final StringBuilder obj = triggerObjToCn(tags, by);
//
//        Map<String, String> trgTrdMap = new HashMap<>();
//        Map<String, String> trgValueMap = new HashMap<>();
//        String type = event.getString("type");
//        final String classification = event.getString(CLASSIFICATION);
//        switch (classification) {
//            case "multipleMetric":
//                //多指标监控
//                Map<String, ThresholdsDTO> multithresholds = event.getObject("multithresholds", Map.class);
//                final JSONObject query = event.getJSONObject("query");
//                for (Map.Entry<String, ThresholdsDTO> entry : multithresholds.entrySet()) {
//                    String mKey = entry.getKey();
//                    ThresholdsDTO thresholds = entry.getValue();
//                    final double value = thresholds.getValue().doubleValue();
//                    final String thresholdValue = thresholds.getTrgValue();
//                    String unit = thresholds.getUnit();
//                    String viewUnit = StringUtils.isBlank(thresholds.getUnit()) ? unit : thresholds.getUnit();
//                    String trgTrd = thresholds.getTrgTrd();
//                    String trgValue = thresholds.getTrgValue();
//                    final int status = event.getIntValue("status");
//                    final String levelStr = getStatusCn(status);
//
//                    trgTrdMap.put("\\{\\{指标" + mKey + "阈值}}", trgTrd);
//                    trgValueMap.put("\\{\\{指标" + mKey + "实际值}}", trgValue);
//                    if (trgTrd != null) {
//                        final String comparison = thresholds.getComparison();
//                        final JSONObject queryMetric = (JSONObject) query.get(mKey);
//                        if (queryMetric == null) {
//                            continue;
//                        }
//                        type = queryMetric.getString(WAY);
//                        if (type != null) {
//                            switch (type) {
//                                case "threshold":
//                                    thresholds.setMsg(String.format(" 指标%s当前%s%s%s",
//                                            thresholds.getMName(), unitFormat(value, unit, viewUnit),
//                                            comparison == null ? "=" : comparison, trgTrd));
//                                    break;
//                                case "baseline":
//                                    thresholds.setMsg(String.format(" 指标%s当前%s%s%s",
//                                            thresholds.getMName(), unitFormat(value, unit, viewUnit),
//                                            comparison == null ? "=" : comparison, trgTrd));
//                                    break;
//                                case "mutation":
//                                    thresholds.setMsg(String.format(" 指标%s当前%s%s%s",
//                                            thresholds.getMName(), unitFormat(value, unit, viewUnit),
//                                            comparison == null ? "=" : comparison, trgTrd));
//                                    break;
//                                case "stateThreshold":
//                                    String stateThresholdMsg = String.format("指标%s至少有%s分钟, 状态检测为%s",
//                                            thresholds.getMName(),
//                                            unitFormat(value, unit, viewUnit),
//                                            thresholdValue);
//                                    thresholds.setMsg(stateThresholdMsg);
//                                    break;
//                                case "changePoint":
//                                    String msgTemplate = "指标%s当前值为%s, 相对过去1小时%s%s, %s突变检测%s阈值%s%s";
//                                    String change = comparison.equals(">") ? "提升" : "下降";
//                                    String limit = comparison.equals(">") ? "上限" : "下限";
//                                    String direction = comparison.equals(">") ? "高于" : "小于";
//                                    thresholds.setMsg(String.format(msgTemplate,
//                                            thresholds.getMName(),
//                                            unitFormat(Math.abs(value), unit, viewUnit),
//                                            change,
//                                            unitFormat(Math.abs(value), unit, viewUnit),
//                                            direction,
//                                            levelStr,
//                                            limit,
//                                            thresholdValue));
//                                    break;
//                                default:
//                                    thresholds.setMsg(String.format(" 指标%s当前%s%s%s",
//                                            thresholds.getMName(), unitFormat(value, unit, viewUnit),
//                                            comparison == null ? "=" : comparison, trgTrd));
//                                    break;
//                            }
//                        }
//                    }
//                }
//                break;
//            case "singleMetric":
//                String unit = event.getString("unit");
//
//                String viewUnit = StringUtils.isBlank(event.getString("view_unit")) ? unit : event.getString("view_unit");
//                final ThresholdsDTO thresholds = event.getObject("thresholds", ThresholdsDTO.class);
//                final Number value = thresholds.getValue();
//                final double doubleValue = value.doubleValue();
//                final String thresholdValue = thresholds.getTrgValue();
//                final int status = event.getIntValue("status");
//                final String levelStr = getStatusCn(status);
//                trgValueMap.put("\\{\\{指标实际值}}", status == 1 ? "无数据" : unitFormat(doubleValue, unit, viewUnit));
//                String trgTrd = event.getString("trgTrd");
//                trgTrdMap.put("\\{\\{阈值}}", trgTrd);
//                String comparison = thresholds.getComparison();
//                if (comparison == null) {
//                    comparison = "=";
//                }
//                if (type != null) {
//                    switch (type) {
//                        case "threshold":
//                            thresholds.setMsg(String.format(" 指标%s当前%s%s%s",
//                                    thresholds.getMName(), unitFormat(doubleValue, unit, viewUnit),
//                                    comparison == null ? "=" : comparison, trgTrd));
//                            break;
//                        case "baseline":
//                            thresholds.setMsg(String.format(" 指标%s当前%s%s%s",
//                                    thresholds.getMName(), unitFormat(doubleValue, unit, viewUnit),
//                                    comparison == null ? "=" : comparison, trgTrd));
//                            break;
//                        case "mutation":
//                            thresholds.setMsg(String.format(" 指标%s当前%s%s%s",
//                                    thresholds.getMName(), unitFormat(doubleValue, unit, viewUnit),
//                                    comparison == null ? "=" : comparison, trgTrd));
//                            break;
//                        case "stateThreshold":
//                            String stateThresholdMsg = String.format("指标%s至少有%s分钟, 状态检测为%s",
//                                    thresholds.getMName(),
//                                    unitFormat(doubleValue, unit, viewUnit),
//                                    thresholdValue);
//                            thresholds.setMsg(stateThresholdMsg);
//                            break;
//                        case "changePoint":
//                            String msgTemplate = "指标%s当前值为%s, 相对过去1小时%s%s, %s突变检测%s阈值%s%s";
//                            String change = comparison.equals(">") ? "提升" : "下降";
//                            String limit = comparison.equals(">") ? "上限" : "下限";
//                            String direction = comparison.equals(">") ? "高于" : "小于";
//                            thresholds.setMsg(String.format(msgTemplate,
//                                    thresholds.getMName(),
//                                    unitFormat(Math.abs(doubleValue), unit, viewUnit),
//                                    change,
//                                    unitFormat(Math.abs(doubleValue), unit, viewUnit),
//                                    direction,
//                                    levelStr,
//                                    limit,
//                                    thresholdValue));
//                            break;
//                        default:
//                            thresholds.setMsg(String.format(" 指标%s当前%s%s%s",
//                                    thresholds.getMName(), unitFormat(doubleValue, unit, viewUnit),
//                                    comparison == null ? "=" : comparison, trgTrd));
//                            break;
//                    }
//                }
//                break;
//            default:
//                throw new IllegalStateException("Unexpected value: " + classification);
//        }
//        Long triggerTime = event.getLong("triggerTime");
//
//        Long cycle = event.getLong("cycle");
//        if (cycle == null) {
//            cycle = 60L;
//        }
//        //一般事件的持续时间都为触发时间-周期
//        long duration = cycle;
//        Long startTriggerTime = triggerTime - cycle;
//        Integer status = event.getInteger("status");
//        if (status != null && status == 1) {
//            //无数据
//            if ("multipleMetric".equals(classification) || "singleMetric".equals(classification)) {
//                //多指标监控无数据 单指标监控无数据
//                String msg = getMultipleSingleMetricNoDataMsg(obj.toString(), classification, event);
//                event.put(MESSAGE, msg);
//                return event;
//            }
//        }
//        String statusCn = getStatusCn(status);
//
//        final String metrics = event.getString("metrics");
//
//        // 事件描述
//        event.put(MESSAGE, this.replaceStr(statusCn, obj.toString(), trgValueMap, startTriggerTime, duration, trgTrdMap, metrics, event));
//        return event;
//    }


    private static final String TAGS = "tags";
    private static final String BY = "by";
    private static final String CLASSIFICATION = "classification";
    private static final String MESSAGE = "message";
    private static final String WAY = "way";

    /**
     * 执行告警事件中的变量替换处理
     *
     * @param event 输入的JSON事件对象，包含告警状态、标签、指标等数据
     * @return 处理后的JSON对象，包含替换变量后的完整告警消息
     * <p>
     * 处理流程：
     * 1. 前置检查：验证事件有效性，过滤无需处理的状态
     * 2. 构建基础信息：转换标签数据为中文描述对象
     * 3. 分类处理：根据指标类型初始化阈值映射表
     * 4. 时间参数计算：确定告警触发时间窗口
     * 5. 消息生成：根据状态和指标类型组装最终告警信息
     */
    public JSONObject replaceParam(JSONObject event) {
        // 前置有效性校验：过滤空事件和非活动状态
        if (event == null) return null;

        Integer status = event.getInteger("status");
        if (status == null || status == 0) return event;

        // 转换标签数据为中文描述对象
        JSONObject tags = event.getJSONObject(TAGS);
        Collection<String> by = event.getObject(BY, Collection.class);
        StringBuilder obj = triggerObjToCn(tags, by);

        // 初始化阈值存储结构（用于后续变量替换）
        Map<String, String> trgTrdMap = new HashMap<>();
        Map<String, String> trgValueMap = new HashMap<>();
        String classification = event.getString(CLASSIFICATION);

        // 分类处理指标数据
        try {
            switch (classification) {
                case "multipleMetric":
                    processMultipleMetric(event, trgTrdMap, trgValueMap);
                    break;
                case "singleMetric":
                    processSingleMetric(event, trgTrdMap, trgValueMap);
                    break;
                default:
                    throw new IllegalStateException("非法的分类类型: " + classification);
            }
        } catch (NullPointerException e) {
            throw new IllegalArgumentException("必要字段缺失", e);
        }

        // 计算告警时间窗口（触发时间前推周期时长）
        Long triggerTime = event.getLong("triggerTime");
        Long cycle = event.getLong("cycle");
        if (cycle == null) cycle = 60L;
        long duration = cycle;
        Long startTriggerTime = triggerTime != null ? triggerTime - cycle : null;

        // 处理无数据状态的特殊消息格式
        if (status == 1) {
            if (classification.matches("multipleMetric|singleMetric")) {
                event.put(MESSAGE, getMultipleSingleMetricNoDataMsg(obj.toString(), classification, event));
                return event;
            }
        }

        // 执行最终消息模板替换
        String statusCn = getStatusCn(status);
        String metrics = event.getString("metrics");
        event.put(MESSAGE, replaceStr(statusCn, obj.toString(), trgValueMap,
                startTriggerTime, duration, trgTrdMap, metrics, event));
        return event;
    }


    /**
     * 处理多指标检测逻辑，遍历阈值配置并执行检测处理
     *
     * @param event       包含查询参数和阈值配置的JSON对象。需要包含query字段和multithresholds字段
     * @param trgTrdMap   输出参数，用于存储触发器ID与阈值关系的映射结果（键值对会被修改）
     * @param trgValueMap 输出参数，用于存储触发器ID与指标值关系的映射结果（键值对会被修改）
     */
    private void processMultipleMetric(JSONObject event,
                                       Map<String, String> trgTrdMap, Map<String, String> trgValueMap) {

        // 从事件对象中提取多指标检测查询请求
        MultiDetectQueryRequest query = event.getObject("query", MultiDetectQueryRequest.class);
        if (query == null) return;

        // 获取多指标阈值配置，映射格式为<指标键, 阈值配置DTO>
        Map<String, ThresholdsDTO> multithresholds = event.getObject("multithresholds",
                new TypeReference<Map<String, ThresholdsDTO>>() {
                });
        if (multithresholds == null) return;

        // 构建非空的检测查询请求映射表
        final Map<String, DetectQueryRequest> detectQueryRequestMap = query.buildNonNullQueriesMap();

        // 遍历所有阈值配置项进行处理
        for (Map.Entry<String, ThresholdsDTO> entry : multithresholds.entrySet()) {
            String mKey = entry.getKey();
            ThresholdsDTO thresholds = entry.getValue();
            if (thresholds == null) continue;

            // 对单个指标进行阈值检测处理
            processThreshold(mKey, thresholds, event.getIntValue("status"), detectQueryRequestMap, trgTrdMap);
        }
    }


    /**
     * 处理单个指标的阈值检测及数据格式化
     *
     * @param event       事件对象，包含指标查询参数、阈值配置和状态信息
     * @param trgTrdMap   目标阈值结果映射表，用于存储处理后的阈值检测结果（输出参数）
     * @param trgValueMap 目标指标值映射表，用于存储格式化后的指标实际值（输出参数）
     */
    private void processSingleMetric(JSONObject event, Map<String, String> trgTrdMap, Map<String, String> trgValueMap) {
        // 从事件对象中解析多指标检测请求配置
        MultiDetectQueryRequest multiDetectQueryRequest = event.getObject("query", MultiDetectQueryRequest.class);
        final Map<String, DetectQueryRequest> detectQueryRequestMap = multiDetectQueryRequest.buildNonNullQueriesMap();

        // 获取阈值配置，无配置时直接返回
        ThresholdsDTO thresholds = event.getObject("thresholds", ThresholdsDTO.class);
        if (thresholds == null) return;

        // 处理单位转换：优先使用视图显示单位，默认使用原始单位
        String unit = event.getString("unit");
        String viewUnit = Optional.ofNullable(event.getString("view_unit"))
                .filter(StringUtils::isNotBlank)
                .orElse(unit);

        // 获取阈值实际数值，处理空值情况
        double value = Optional.ofNullable(thresholds.getValue())
                .map(Number::doubleValue)
                .orElse(0.0);

        // 根据状态生成指标值显示内容：状态1表示无数据，否则显示格式化后的单位数值
        trgValueMap.put("\\{\\{指标实际值}}", event.getIntValue("status") == 1 ? "无数据" : unitFormat(value, unit, viewUnit));

        // 执行阈值检测逻辑（级别1的阈值检测）
        processThreshold("1", thresholds, event.getIntValue("status"), detectQueryRequestMap, trgTrdMap);
    }


    private void processThreshold(String mKey, ThresholdsDTO thresholds,
                                  int status, Map<String, DetectQueryRequest> detectQueryRequestMap,
                                  Map<String, String> trgTrdMap) {

        final DetectQueryRequest detectQueryRequest = detectQueryRequestMap.get(mKey);
        String type = detectQueryRequest.getWay();
        if (type == null) return;

        final String comparison = Optional.ofNullable(thresholds.getComparison()).orElse("=");

        switch (type) {
            case "stateThreshold":
                buildStateThresholdMsg(mKey, thresholds, trgTrdMap);
                break;
            case "changePoint":
                buildChangePointMsg(mKey, thresholds, status, comparison, trgTrdMap);
                break;
            case "threshold":
            case "baseline":
            case "mutation":
            default:
                buildDefaultMsg(mKey, thresholds, comparison, trgTrdMap);
        }
    }

    private void buildDefaultMsg(String mKey, ThresholdsDTO thresholds,
                                 String comparison, Map<String, String> trgTrdMap) {
        String keySuffix = mKey != null ? mKey + "阈值" : "阈值";
        String formattedValue = unitFormat(
                thresholds.getValue().doubleValue(),
                thresholds.getUnit(),
                thresholds.getUnit()
        );

        String msg = String.format("指标%s %s %s %s",
                thresholds.getMName(),
                formattedValue,
                comparison,
                thresholds.getTrgValue());

        thresholds.setMsg(msg);
        trgTrdMap.put("\\{\\{" + keySuffix + "}}", comparison + formattedValue);
    }

    private void buildChangePointMsg(String mKey, ThresholdsDTO thresholds,
                                     int status, String comparison,
                                     Map<String, String> trgTrdMap) {
        if (thresholds == null) {
            log.warn("thresholds must not be null");
            return;
        }
        if (thresholds.getValue() == null) {
            log.warn("thresholds.getValue() must not be null");
            return;
        }
        // 1. 校验comparison合法性
        if (!">".equals(comparison) && !"<!".equals(comparison)) {
            log.warn("comparison must be '>' or '<'");
            return;
        }

        boolean isGreaterThan = ">".equals(comparison);
        String trend = isGreaterThan ? "提升" : "下降";
        String limit = isGreaterThan ? "上限" : "下限";
        String direction = isGreaterThan ? "高于" : "小于";

        final String levelStr = getStatusCn(status);

        // 2. 处理mKey为null的情况
        String keySuffix = (mKey != null) ? mKey + "变更点阈值" : "变更点阈值";

        // 3. 防止thresholds.getValue()为null
        Double value = thresholds.getValue().doubleValue();
        if (value == null) {
            log.warn("thresholds.getValue() must not be null");
            return;
        }

        final String formattedValue = unitFormat(value.doubleValue(), thresholds.getUnit(), thresholds.getUnit());

        final String msg = String.format("指标%s当前值为%s, 相对过去1小时%s, %s突变检测%s阈值%s",
                thresholds.getMName(),
                formattedValue,
                trend,
                direction,
                levelStr,
                limit);
    
        thresholds.setMsg(msg);

        // 4. 处理trgTrdMap的null值
        String trgValue = thresholds.getTrgValue() != null ? thresholds.getTrgValue() : "";
        trgTrdMap.put("{{" + keySuffix + "}}", comparison + trgValue);
    }

    private void buildStateThresholdMsg(String mKey, ThresholdsDTO thresholds,
                                        Map<String, String> trgTrdMap) {

        String keySuffix = mKey != null ? mKey + "阈值" : "阈值";
        String msg = String.format("指标%s至少有%s分钟, 状态检测为%s",
                thresholds.getMName(),
                unitFormat(
                        thresholds.getValue().doubleValue(),
                        thresholds.getUnit(),
                        thresholds.getUnit()
                ),
                thresholds.getTrgValue());
        thresholds.setMsg(msg);
        trgTrdMap.put("\\{\\{" + keySuffix + "}}", thresholds.getTrgTrd());
    }

    //{检测指标1}、{检测指标2}、{检测指标3}在{触发对象}上无数据
    private String getMultipleSingleMetricNoDataMsg(String obj, String classification, JSONObject event) {
        Set<String> metrics = new HashSet<>();
        if ("multipleMetric".equals(classification)) {
            //多指标监控无数据
            Map<String, ThresholdsDTO> multithresholds = event.getObject("multithresholds",
                    new TypeReference<Map<String, ThresholdsDTO>>() {
                    });
            for (Map.Entry<String, ThresholdsDTO> entry : multithresholds.entrySet()) {
                if (entry == null || entry.getValue() == null) {
                    continue;
                }
                if (entry.getValue().getValue() != null) {
                    metrics.add(entry.getValue().getMName());
                }
            }
        } else if ("singleMetric".equals(classification)) {
            //单指标监控无数据
            final ThresholdsDTO thresholds = event.getObject("thresholds", ThresholdsDTO.class);
            metrics.add(thresholds.getMName());
        }
        StringBuilder builder = new StringBuilder();
        builder.append("【");
        for (String metric : metrics) {
            builder.append(metric).append("、");
        }
        builder.deleteCharAt(builder.length() - 1);
        builder.append("】");
        builder.append("在");
        builder.append(obj);
        builder.append("上无数据");
        return builder.toString();
    }

    /**
     * 触发对象拼接翻译中文
     *
     * @return 拼接后的中文字符串
     */
    private StringBuilder triggerObjToCn(JSONObject trigger, Collection<String> by) {
        final NavigableMap<String, MetricTagKey> openTagKeys = metricsQueryService.findOpenTagKeys();
        StringBuilder obj = new StringBuilder();
        obj.append("【");
        if (trigger != null) {
            // 定义键名替换规则
            Map<String, String> keyReplacements = new HashMap<>();
            keyReplacements.put(SERVICE_ID, SERVICE);
            keyReplacements.put(SRC_SERVICE_ID, SRC_SERVICE);

            // 处理空by集合的情况
            Collection<String> processedBy = CollectionUtils.isEmpty(by) ?
                    trigger.keySet() : new ArrayList<>(by);

            // 使用StringJoiner管理分隔符
            StringJoiner contentJoiner = new StringJoiner(",");
            for (String key : processedBy) {
                // 应用键名替换规则
                key = keyReplacements.getOrDefault(key, key);

                final MetricTagKey info = openTagKeys.get(key);
                if (info == null) continue;

                // 使用optString避免空指针
                String value = trigger.getString(key);
                if (StringUtils.isBlank(value)) {
                    value = null;
                }
                if (value == null || value.isEmpty()) continue;

                contentJoiner.add(info.getName() + colon + value);
            }

            if (contentJoiner.length() > 0) {
                obj.append(contentJoiner);
            }
        }
        obj.append("】");
        return obj;
    }


    private String replaceStr(String statusCn, String obj, Map<String, String> trgValueMap, long start, long duration, Map<String, String> trgTrdMap, String metrics, JSONObject event) {
//        String content = "{{触发对象}}的指标{{指标名称}}出现异常，";
        String content = "{{触发对象}}的";
        if (StringUtils.isNotBlank(metrics)) {
            content = content.replaceAll("\\{\\{指标名称}}", metrics);
        }
        if (StringUtils.isNotBlank(statusCn)) {
            content = content.replaceAll("\\{\\{告警等级}}", statusCn);
        }
        if (StringUtils.isNotBlank(obj)) {
            obj = obj.replaceAll("\\$", "\\\\\\$")
                    .replaceAll("\\\\", "\\\\\\\\");
//            content = content.replaceAll("\\{\\{触发对象}}", obj);
            content = content.replace("{{触发对象}}", obj);
        }
        if (StringUtils.isNotBlank(trgValueMap.get("\\{\\{指标实际值}}"))) {
            content = content.replaceAll("\\{\\{指标实际值}}", trgValueMap.get("\\{\\{指标实际值}}"));
        }
        if (StringUtils.isNotBlank(trgTrdMap.get("\\{\\{阈值}}"))) {
            content = content.replaceAll("\\{\\{阈值}}", trgTrdMap.get("\\{\\{阈值}}"));
        }
        for (int i = 1; i < 6; i++) {
            if (StringUtils.isNotBlank(trgValueMap.get("\\{\\{指标" + i + "实际值}}"))) {
                content = content.replaceAll("\\{\\{指标" + i + "实际值}}", trgValueMap.get("\\{\\{指标" + i + "实际值}}"));
            } else {
                content = content.replaceAll("\\{\\{指标" + i + "实际值}}", "指标" + i + "未检测到数据");
            }
            if (StringUtils.isNotBlank(trgTrdMap.get("\\{\\{指标" + i + "阈值}}"))) {
                content = content.replaceAll("\\{\\{指标" + i + "阈值}}", trgTrdMap.get("\\{\\{指标" + i + "阈值}}"));
            } else {
                content = content.replaceAll("\\{\\{指标" + i + "阈值}}", "");
            }
        }

        if (event.get("multithresholds") != null) {
            Map<String, ThresholdsDTO> multithresholds = event.getObject("multithresholds",
                    new TypeReference<Map<String, ThresholdsDTO>>() {
                    });
            for (Map.Entry<String, ThresholdsDTO> entry : multithresholds.entrySet()) {
                ThresholdsDTO valueObj = entry.getValue();
                if (valueObj == null) {
                    continue;
                }
                final String valueObjString = valueObj.getMsg();
                if (valueObjString == null) {
                    continue;
                }
                content = content + valueObjString + ",";
            }
        } else {
            final ThresholdsDTO thresholds = event.getObject("thresholds", ThresholdsDTO.class);
            final String msg = thresholds.getMsg();
            if (msg != null) {
                content = content + msg;
            }
        }
        return content;
    }

    public void eventSinkDBMq(List<JSONObject> events, DatabuffMonitorView m) {
        if (events == null || m == null) {
            return;
        }
        // 丰富事件属性和标签，创建事件id
        for (JSONObject event : events) {
            if (event == null) continue;
            // todo 未来 status 需要改为 level
            final int eventLevel = event.getIntValue("status");
            if (eventLevel == 0) continue;
            Long creatorId = m.getCreatorId();
            Long editorId = m.getEditorId();
            event.put(LEVEL, eventLevel);
            event.put("creatorId", creatorId);
            event.put("editorId", editorId);
            final String eventId = generateEventId(m.getClassification());
            event.put("id", eventId);
            event.put(EVENT_ID, eventId);
            event.put("createDt", EventIdGenerator.extractStrDateFromEventId(eventId));
            event.put("createTime", System.currentTimeMillis());
            //替换事件变量
            event = this.replaceParam(event);

            JSONObject tags = Optional.ofNullable(event.getJSONObject(TAGS))
                    .map(JSONObject::new) // 创建新实例
                    .orElseGet(JSONObject::new);
            event.put(TAGS, tags); // 替换原对象

            // 添加标签是为后续告警筛选和收敛用的。
            // 添加 事件等级，事件描述，检测规则，检测类型标签
            tags.put(LEVEL, eventLevel);
            tags.put(RULE_NAME, event.getString(RULE_NAME));
            tags.put(CLASSIFICATION, event.getString(CLASSIFICATION));
        }

        // 执行静默打标
        markSilenceEvent(events, m);

        events = events.stream().filter(e -> e.getIntValue("silence") == 0 && e.getIntValue("status") != 0)
                .collect(Collectors.toList());

        if (events == null || events.isEmpty()) {
            return;
        }

        // 丰富事件标签，主机IP，服务命名空间，业务信息，业务名称
        final boolean allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        final String gid = m.getGid();
        for (JSONObject event : events) {
            if (event == null) {
                continue;
            }
            final String apiKey = m.getApiKey();
            final JSONObject tags = event.getJSONObject(TAGS);
            if (tags == null || apiKey == null) {
                continue;
            }
            richHostIP(tags, apiKey);
            richServiceNamespace(tags, apiKey);
            richBizInfo(tags);
            richBusName(event, tags, m);
            if (allEntityPermission) {
                // 增加域ID信息
                event.put("gid", null);
            } else {
                // 增加域ID信息
                event.put("gid", gid);
            }

            // bugfix: 事件中的API_KEY2字段需要转换为API_KEY字段
            if (!event.containsKey(API_KEY) && event.containsKey(API_KEY2)) {
                event.put(API_KEY, event.getString(API_KEY2));
            }

        }

        final Boolean system = m.getSystem();
        String topic = system ? KafkaTopicConstant.EVENT_SYSTEM_TOPIC : KafkaTopicConstant.EVENT_TOPIC;
        final boolean eventStressTestEnabled = taskRefreshScopeConfig.isEventStressTestEnabled();
        // 压测逻辑
        if (eventStressTestEnabled) {
            int stressTestCount = taskRefreshScopeConfig.getEventStressTestCount();
            for (int i = 0; i < stressTestCount; i++) {
                for (JSONObject originalEvent : events) {
                    JSONObject eventCopy = new JSONObject(originalEvent); // 深拷贝隔离数据
                    JSONObject trigger = eventCopy.getJSONObject(TRIGGER);
                    if (trigger == null) {
                        continue;
                    }
                    trigger.put(TEST_TAG, i);
                    try {
                        kafkaUtil.producerSend(eventCopy, topic);
                    } catch (Exception e) {
                        log.error("event send to kafka error, topic: {}", topic, e);
                    }
                }
            }
        } else {
            try {
                kafkaUtil.producerSend(events, topic);
            } catch (Exception e) {
                log.error("event send to kafka error, topic: {}", topic, e);
            }
        }
    }

    private void richHostIP(JSONObject tags, String apiKey) {
        String host = tags.getString("host");
        if (host == null) return;

        HostInfoEntity hostInfoEntity = cacheHostNames.getIfPresent(host);
        if (hostInfoEntity == null) {
            hostInfoEntity = Optional.ofNullable(hostInfoEntityMapper.selectByHostIdOrName(null, host, apiKey))
                    .orElseGet(HostInfoEntity::new);
            cacheHostNames.put(host, hostInfoEntity);
        }

        tags.put("ip", hostInfoEntity.getIpaddress());
        tags.put("hostIp", hostInfoEntity.getIpaddress());
        tags.put("ipv6", hostInfoEntity.getIpaddressv6());
    }

    /**
     * 丰富事件标签中的业务名称
     *
     * @param event 事件
     * @param tags  事件标签
     * @param m
     */
    private void richBusName(JSONObject event, JSONObject tags, DatabuffMonitorView m) {
        if (event == null || tags == null) {
            return;
        }
        final JSONObject trigger = event.getJSONObject(TRIGGER);
        if (trigger == null) {
            return;
        }
        final String serviceId = trigger.getString(SERVICE_ID);
        final String srcServiceId = trigger.getString(SRC_SERVICE_ID);
        final String busN = trigger.getString(BUS_NAME);
        final Set<Integer> allBusIds = new HashSet<>();
        final Set<String> busNameList = new HashSet<>();

        if (busN != null) {
            allBusIds.add(this.getbusId(busN));
            busNameList.add(busN);
        }
        final String gid = m.getGid();

        if (!domainManagerObjService.hasAllEntityPermission() && !m.getSystem() && gid != null) {
            final Integer iGid = Integer.valueOf(gid);
            Map<String, Set<Integer>> svcIdBusIdMap = new HashMap<>();

            if (serviceId != null || srcServiceId != null) {
                svcIdBusIdMap = domainManagerObjService.getServiceBusMap(iGid);
            }

            if (serviceId != null) {
                final Set<Integer> busIds = svcIdBusIdMap.get(serviceId);
                if (!CollectionUtils.isEmpty(busIds)) {
                    allBusIds.addAll(busIds);
                }
            }

            if (srcServiceId != null) {
                final Set<Integer> busIds = svcIdBusIdMap.get(srcServiceId);
                if (!CollectionUtils.isEmpty(busIds)) {
                    allBusIds.addAll(busIds);
                }
            }
        } else {
            if (serviceId != null) {
                final Set<Integer> busIds = svcIdBusIdMap.getIfPresent(serviceId);
                if (!CollectionUtils.isEmpty(busIds)) {
                    allBusIds.addAll(busIds);
                }
            }

            if (srcServiceId != null) {
                final Set<Integer> busIds = svcIdBusIdMap.getIfPresent(srcServiceId);
                if (!CollectionUtils.isEmpty(busIds)) {
                    allBusIds.addAll(busIds);
                }
            }
        }

        if (!CollectionUtils.isEmpty(allBusIds)) {
            allBusIds.addAll(getAllParentIdsByChildList(allBusIds));
            tags.put(BUS_ID, allBusIds);
            tags.put(GID, gid == null ? "" : gid);
            busNameList.addAll(getBusNameList(allBusIds));
            if (busNameList != null && !busNameList.isEmpty()) {
                event.put(BUS_NAME, busNameList);
                tags.put(BUS_NAME, busNameList);
                for (String busName : busNameList) {
                    this.sendBusMetric(event, tags, busName);
                }
            }
        }
    }

    protected List<String> getBusNameList(Set<Integer> busIds) {
        List<String> busNameList = new ArrayList<>();
        if (busIds == null) {
            return busNameList;
        }
        final ConcurrentMap<@NonNull Integer, @NonNull Business> concurrentMap = busMap.asMap();
        for (Object busId : busIds) {
            if (busId == null) {
                continue;
            }
            busNameList.addAll(getBusNamesRecursively((Integer) busId, concurrentMap));
        }
        return busNameList;
    }

    protected Integer getbusId(String busName) {
        final ConcurrentMap<Integer, Business> concurrentMap = busMap.asMap();
        for (Map.Entry<Integer, Business> entry : concurrentMap.entrySet()) {
            Business business = entry.getValue();
            if (business != null && busName.equals(business.getName())) {
                return entry.getKey();
            }
        }
        return null;
    }
    /**
     * 递归获取业务名称
     *
     * @param busId
     * @param busNameMap
     * @return
     */
    private Set<String> getBusNamesRecursively(Integer busId, Map<Integer, Business> busNameMap) {
        // Set要求元素唯一，且有序
        Set<String> busNames = new LinkedHashSet<>();
        Business business = busNameMap.get(busId);
        if (business != null) {
            final String businessName = business.getName();
            // 防止循环引用
            if (busNames.contains(businessName)) {
                return busNames;
            }
            Integer pid = business.getPid();
            if (pid != null) {
                busNames.addAll(getBusNamesRecursively(pid, busNameMap));
            }
            busNames.add(businessName);
            busNames.add(String.join("-", busNames));
        }
        return busNames;
    }


    protected Set<Integer> getBusIdList(Set<Integer> busIds) {
        if (busIds == null) {
            return Collections.emptySet();
        }
        Set<Integer> result = new LinkedHashSet<>(); // 保留插入顺序且自动去重
        final ConcurrentMap<Integer, Business> businessConcurrentMap = busMap.asMap();
        for (Object busId : busIds) {
            if (busId == null) {
                continue;
            }
            result.addAll(getBusIdsRecursively((Integer) busId, businessConcurrentMap));
        }
        return result;
    }

    /**
     * 递归获取指定子节点ID向上追溯的所有祖先节点ID链。
     *
     * @param childId 起始子节点的ID，用于查找其所有祖先节点
     * @return 包含从直接父节点到最高层父节点的ID列表，按层级从低到高排列。若childId无效或无祖先节点则返回空列表
     */
    private List<Integer> getAncestorBusIds(Integer childId) {
        List<Integer> ancestorIds = new ArrayList<>();
        final ConcurrentMap<Integer, Business> concurrentMap = busMap.asMap();
        Business current = concurrentMap.get(childId);

        // 递归遍历父节点直至根节点或无效父ID
        while (current != null) {
            Integer parentId = current.getPid();

            // 若父ID无效（null或0），终止递归
            if (parentId == null || parentId == 0) {
                break;
            }
            ancestorIds.add(parentId);
            current = concurrentMap.get(parentId);
        }
        return ancestorIds;
    }

    // 新增批量处理方法
    public Set<Integer> getAllParentIdsByChildList(Set<Integer> childIds) {
        if (CollectionUtils.isEmpty(childIds)) {
            return new HashSet<>();
        }
        Set<Integer> parentSet = new HashSet<>();
        for (Integer childId : childIds) {
            parentSet.addAll(getAncestorBusIds(childId));
        }
        return parentSet;
    }

    private Set<Integer> getBusIdsRecursively(Integer busId, Map<Integer, Business> busMap) {
        // Set要求元素唯一，且有序
        Set<Integer> busIds = new LinkedHashSet<>();
        Business business = busMap.get(busId);
        if (business != null) {
            final Integer businessId = business.getId();
            // 防止循环引用
            if (busIds.contains(businessId)) {
                return busIds;
            }
            Integer pid = business.getPid();
            if (pid != null) {
                busIds.addAll(getBusIdsRecursively(pid, busMap));
            }
            busIds.add(businessId);
        }
        return busIds;
    }


    private void richServiceNamespace(JSONObject tags, String apiKey) {
        String serviceId = tags.getString(SERVICE_ID);
        if (serviceId == null) return;

        String namespace = cacheServiceNameSpace.get(serviceId, key -> getServiceNamespace(serviceId, apiKey));
        if (StringUtils.isBlank(namespace)) return;

        tags.put(K_8_S_NAMESPACE, namespace);
    }

    private void richBizInfo(JSONObject tags) {
        String serviceId = tags.getString(SERVICE_ID);
        if (serviceId == null) return;

        String resource = tags.getString(RESOURCE);
        Set<JSONObject> bizSvcInfos = (resource == null) ?
                bizInfoService.getBizSvcInfos(serviceId) :
                bizInfoService.getBizResourceInfos(serviceId, resource);

        if (CollectionUtils.isEmpty(bizSvcInfos)) return;

        Collection<Integer> bizEventIds = new HashSet<>();
        Collection<String> bizEventNames = new HashSet<>();
        for (JSONObject bizSvcInfo : bizSvcInfos) {
            Integer bizEventId = bizSvcInfo.getInteger(BIZ_EVENT_ID);
            String bizEventName = bizSvcInfo.getString(BIZ_EVENT_NAME);
            if (bizEventId != null) bizEventIds.add(bizEventId);
            if (StringUtils.isNotBlank(bizEventName)) bizEventNames.add(bizEventName);
        }

        if (!tags.containsKey(BIZ_EVENT_ID)) tags.put(BIZ_EVENT_ID, bizEventIds);
        if (!tags.containsKey(BIZ_EVENT_NAME)) tags.put(BIZ_EVENT_NAME, bizEventNames);
    }

    private String getServiceNamespace(String serviceId, String apiKey) {
        QueryRequest queryRequest = new QueryRequest();
        queryRequest.setApiKey(apiKey);
        queryRequest.setMetric(SERVICE_INSTANCE_METRIC);
        queryRequest.setBy(Lists.newArrayList(SERVICE_ID, K_8_S_NAMESPACE));
        queryRequest.setFrom(Lists.newArrayList(CompositeCondition.builder().left(SERVICE_ID).right(serviceId).operator(WhereOp.EQ.getSymbol()).build()));

        final Map<Map<String, String>, TSDBSeries> result = metricsAggregator.seriesResult(queryRequest);

        if (result == null || result.isEmpty()) {
            return StringUtils.EMPTY;
        }
        return result.keySet().stream().findFirst().map(o -> {
            if (o instanceof Map) {
                final Map<?, ?> map = o;
                final String namespace = (String) map.get(K_8_S_NAMESPACE);
                return namespace == null ? StringUtils.EMPTY : namespace;
            }
            return StringUtils.EMPTY;
        }).orElse(StringUtils.EMPTY);
    }

    private Collection<?> getCollectionFromTags(JSONObject tags, String key) {
        if (key == null || tags == null) {
            return null;
        }
        Object o = tags.get(key);
        if (o instanceof Collection) {
            return (Collection<?>) o;
        } else if (o instanceof String) {
            return Collections.singletonList(o);
        }
        return null;
    }

    /**
     * 将事件标记为“沉默”。
     * 它检查事件列表是否不为空并且监视器视图对象是否不为空。
     * 然后它会创建一个带有监视器名称和分类的地图。
     * 它获取当前时间（以秒为单位）并检索静默监视器列表。
     * 如果列表不为空，则迭代列表并检查每个静默对象是否不为空及其规则条件和对象条件是否不为空。
     * 如果满足这些条件，它将根据规则条件评估监控图。
     * 如果评估为真，则将当前事件标记为静默。
     *
     * @param events 要检查静默条件的事件列表。
     * @param m      包含规则名称和分类的监控视图对象。
     */
    private void markSilenceEvent(List<JSONObject> events, DatabuffMonitorView m) {
        //是否开启静默开关
        if (!taskRefreshScopeConfig.isEventSilenceEnabled()) {
            log.debug("静默开关未开启");
            return;
        }
        if (CollectionUtils.isEmpty(events) || m == null) {
            return;
        }
        final Map<String, Object> monitorMap = new HashMap<>();
        // 检测规则名称
        monitorMap.put(RULE_NAME, m.getRuleName());
        // 检测类型
        monitorMap.put(CLASSIFICATION, m.getClassification());

        long thisTime = System.currentTimeMillis() / 1000;

        final MonitorSilenceParams params = MonitorSilenceParams.builder()
                .enabled(true)
                .apiKey(m.getApiKey())
                .thisTime(thisTime)
                .build();

        final boolean allEntityPermission = domainManagerObjService.hasAllEntityPermission();
        final Collection<String> gids = domainManagerObjService.getGidFromThread(m.getGid());
        final boolean domainManagerStatusOpen = domainManagerObjService.getDomainManagerStatusOpen();
        final List<DatabuffMonitorSilence> silences = monitorMapper.findMonitorSilence(params, domainManagerStatusOpen, allEntityPermission, gids);
        if (CollectionUtils.isEmpty(silences)) {
            return;
        }
        for (DatabuffMonitorSilence silence : silences) {
            if (silence == null) {
                continue;
            }
            final JSONArray ruleConditions = silence.getRuleConditions();
            final JSONArray objConditions = silence.getObjConditions();
            final JSONArray cancelMid = silence.getCancelMid();
            final JSONArray cancelHost = silence.getCancelHost();
            final JSONArray cancelSvcid = silence.getCancelSvcid();
            boolean evaluate = ConditionalVerifyUtil.evaluate(monitorMap, ruleConditions);
            // 如果没有规则条件，全部检测规则都被屏蔽
            if (ruleConditions == null || ruleConditions.size() == 0) {
                evaluate = true;
            }
            // 如果objConditions，则静默所有事件
            final boolean evaluateAllObj = objConditions == null || objConditions.size() == 0;

            if (evaluate) {
                //当前事件被屏蔽
                events.stream().forEach(e -> {
                    final JSONObject tags = e.getJSONObject(TAGS);
                    if (tags == null) {
                        return;
                    }
                    Object trigger = e.getIntValue(MONITOR_ID);
                    final String eventId = e.getString(EVENT_ID);
                    final Long silenceId = silence.getId();
                    final String silenceName = silence.getSilenceName();
                    if (cancelMid != null && cancelMid.contains(trigger)) {
                        log.info("事件ID【{}】的触发对象【{}】在【{}-{}】的白名单中，不执行静默", eventId, trigger, silenceId, silenceName);
                        return;
                    }
                    trigger = tags.getString("host");
                    if (cancelHost != null && cancelHost.contains(trigger)) {
                        log.info("事件ID【{}】的触发对象【{}】在【{}-{}】的白名单中，不执行静默", eventId, trigger, silenceId, silenceName);
                        return;
                    }
                    trigger = tags.getString("serviceId");
                    if (cancelSvcid != null && cancelSvcid.contains(trigger)) {
                        log.info("事件ID【{}】的触发对象【{}】在【{}-{}】的白名单中，不执行静默", eventId, trigger, silenceId, silenceName);
                        return;
                    }
                    if (evaluateAllObj || ConditionalVerifyUtil.evaluate(tags, objConditions)) {
                        log.info("事件ID【{}】触发静默计划【{}-{}】", eventId, silenceId, silenceName);
                        // 静默1
                        e.put("silence", 1);
                    }
                });
            }
        }
    }

    public void sendBusMetric(JSONObject event, Map<String, Object> tags, String busName) {
        Map<String, Object> localValue = new HashMap<>();
        Map<String, Object> localTag = new HashMap<>();
        localValue.put("trigger.cnt", 1L);
        localTag.put(BUS_NAME, busName);
        localTag.put(GID, tags.get(GID));
        localTag.put("service", tags.get("service"));
        localTag.put("serviceId", tags.get("serviceId"));
        localTag.put(RULE_NAME, event.getString(RULE_NAME));
        localTag.put(MONITOR_ID, event.getString(MONITOR_ID));
        localTag.put(LEVEL, event.getString("status"));
        JSONObject metricInfo = MetricsUtil.generateMetric(DF_API_KEY_VALUE, event.getLongValue("triggerTime"), "databuff", METRIC_DATABUFF_ALARM_BUS, localTag, localValue);

        try {
            kafkaUtil.producerSend(METRIC_TOPIC, null, metricInfo);
        } catch (Exception e) {
            log.error("发送告警到Kafka时出错", e);
        }
    }

    public String generateEventId(String classification) {
        return EventIdGenerator.generateEventId(dt -> {
            final String firstLettersInUpperCase = EventIdGenerator.getFirstLettersInUpperCase(classification);
            return "E10" + firstLettersInUpperCase + dt + jedisService.generateId(RedisKeyConstants.EVENT_ID + ":" + dt);
        });
    }
}