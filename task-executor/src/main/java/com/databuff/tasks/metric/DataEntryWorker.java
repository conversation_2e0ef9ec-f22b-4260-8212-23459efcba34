package com.databuff.tasks.metric;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.databuff.common.tsdb.TSDBOperateUtil;
import com.databuff.common.tsdb.model.TSDBPoint;
import com.databuff.common.utils.OtelMetricUtil;
import com.databuff.service.JedisService;
import com.databuff.tasks.util.CollectionUtils;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.util.*;

import static com.databuff.common.constants.Constant.Metric.*;
import static com.databuff.common.constants.MetricName.*;

/**
 * @author:TianMing
 * @date: 2023/4/20
 * @time: 14:50
 */
@Slf4j
public class DataEntryWorker implements Runnable {
    private final static Set<String> GC_FIELDS = Sets.newHashSet("minor_collection_count", "minor_collection_time", "major_collection_time", "major_collection_count");
    private final List<ConsumerRecord<String, String>> records;
    private final JedisService jedisService;

    // 保留原有的TSDBOperateUtil用于兼容现有代码
    private final TSDBOperateUtil tsdbOperateUtil;

    public DataEntryWorker(List<ConsumerRecord<String, String>> records,
                          TSDBOperateUtil tsdbOperateUtil,
                           JedisService jedisService) {
        this.records = records;
        this.tsdbOperateUtil = tsdbOperateUtil; // 保留原有的MoreDB操作工具
        this.jedisService = jedisService;
    }

    @Override
    public void run() {
        try {
            // 外发事件数据到 kafka
            long start = System.currentTimeMillis();
            long oldTime = start;
            if (records.size() > 0) {
                Map<String, List<TSDBPoint>> dbPoints = new HashMap<>(256);
                for (ConsumerRecord<String, String> record : records) {
                    Optional<?> kafkaMsg = Optional.ofNullable(record.value());
                    if (kafkaMsg.isPresent()) {
                        oldTime = Math.min(oldTime, record.timestamp());
                        String message = record.value();
                        try {
                            JSONObject metric = JSON.parseObject(message);
                            JSONObject fieldsObj = metric.getJSONObject(FIELDS);
                            if (fieldsObj == null || fieldsObj.size() == 0) {
                                log.error("metric fields null {}", message);
                                continue;
                            }
                            String apiKey = metric.getString(METRIC_API_KEY);
                            String database = metric.getString(DATABUFF_DATABASE);
                            String measurement = metric.getString(DATABUFF_MEASUREMENT);
                            JSONObject tagsObj = metric.getJSONObject(TAG);
                            boolean skip = tagsObj.getBooleanValue(SKIP);
                            if (skip) {
                                continue;
                            }
                            long timestamp = metric.getLongValue(TIMESTAMP);
                            debugMetric(measurement, tagsObj, fieldsObj, timestamp);
                            generateTagAndHostRelation(apiKey, tagsObj);

                            Map<String, String> tags = new HashMap<>();
                            Map<String, Object> fields = new HashMap<>();

                            for (Map.Entry<String, Object> entry : tagsObj.entrySet()) {
                                String tagKey = entry.getKey();
                                String tagValue = "";
                                if (entry.getValue() != null) {
                                    tagValue = entry.getValue().toString();
                                }
                                tags.put(tagKey, tagValue);
                            }
                            boolean gcMetric = measurement.equals("jvm.gc");
                            for (Map.Entry<String, Object> entry : fieldsObj.entrySet()) {
                                Object value = entry.getValue();
                                if (gcMetric && GC_FIELDS.contains(entry.getKey())) {
                                    value = ((Number) value).longValue();
                                }
                                fields.put(entry.getKey(), value);
                            }
                            TSDBPoint tsdbPoint = new TSDBPoint(database, measurement, timestamp, tags, fields);
                            List<TSDBPoint> points = dbPoints.get(database);
                            if (points == null) {
                                points = dbPoints.computeIfAbsent(database, k -> new ArrayList<>());
                            }
                            points.add(tsdbPoint);
                            if (points.size() > 1999) {
                                // 使用异步写入工具同时写入MoreDB和OpenGemini
                                tsdbOperateUtil.writePoints(database, points);
                                points.clear();
                            }
                        } catch (Exception e) {
                            log.error("MetricSinkToMoreDB Exception ,message:{}, error:{}", message, e);
                        }
                    }
                }

                for (Map.Entry<String, List<TSDBPoint>> entry : dbPoints.entrySet()) {
                    if (CollectionUtils.isNotEmpty(entry.getValue())) {
                        tsdbOperateUtil.writePoints(entry.getKey(), entry.getValue());
                    }
                }
            }
            long duration = System.currentTimeMillis() - start;
            OtelMetricUtil.logHistogram(METRIC_DATA_ENTRY_WORKER_COST, duration);
            OtelMetricUtil.logHistogram(METRIC_KAFKA_DELAY, start - oldTime);
        } catch (Throwable e) {
            e.printStackTrace();
            log.error("worker process error", e);
        }
    }

    private void debugMetric(String measurement, JSONObject tags, JSONObject fields, long timestamp) {
        if ("system.cpu".equals(measurement) && fields.containsKey("usage")) {
            Map<String, String> metricNameTags = new HashMap<>();
            metricNameTags.put("agentHost", tags.getString("host"));
            metricNameTags.put("metricName", "system.cpu.usage");
            double usage = fields.getDoubleValue("usage");
            OtelMetricUtil.logOriginalData(METRIC_ORIGINAL_TIME, usage, metricNameTags, timestamp);
        }
    }

    private void generateTagAndHostRelation(String apiKey, JSONObject tagsObj) {
        String identifier = tagsObj.getString("apm");
        if (StringUtils.isNotBlank(identifier)) {
            //用于后续 服务安装在不同主机上关联原始安装主机  需要
            String originalHostKey = "trace:serviceOriginalHost:" + apiKey + ":" + identifier;
            if (!jedisService.exists(originalHostKey) || jedisService.getTTL(originalHostKey) < 60 * 60) {
                JSONObject hostInfo = new JSONObject();
                String host = tagsObj.getString("host");
                hostInfo.put("hostName", host);
                jedisService.setExpire(originalHostKey, hostInfo.toJSONString(), 60 * 60 * 24);
            }
        }
    }

}
