spring:
  application:
    name: task-executor  # 应用名称
  cloud:
    zookeeper:
      connection-timeout: 30000  # Zookeeper 客户端的连接超时时间，单位是毫秒。
      session-timeout: 120000  # Zookeeper 客户端的会话超时时间，单位是毫秒。
      config:
        enabled: true  # 是否启用ZooKeeper配置
        root: databuff  # ZooKeeper的根路径
        profileSeparator: ','  # 配置文件分隔符
      discovery:
        enabled: true  # 是否启用ZooKeeper服务发现
      connect-string: 192.168.50.193:12181  # ZooKeeper服务器地址
      base-sleep-time-ms: 1000  # 重试策略的基础睡眠时间，单位为毫秒
      max-retries: 3  # 重试策略的最大重试次数
      max-sleep-ms: 3000  # 重试策略的最大睡眠时间，单位为毫秒

#task-executor 全局配置
databuff:
  task-executor:
    policy:
      # 是否启用默认收敛策略
      enabled: false